'use client';

import { UserProfile } from '@clerk/nextjs';
import { useTheme } from 'next-themes';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

const SecurityTab = () => {
  const { theme, resolvedTheme } = useTheme();

  // Determine the current theme (resolvedTheme handles 'system' theme)
  const currentTheme = resolvedTheme || theme;

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Account Management</CardTitle>
          <CardDescription>
            Manage your profile, security preferences, authentication
            methods, and connected devices.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="clerk-user-profile">
            <UserProfile
              routing="hash"
              appearance={{
                variables: {
                  colorPrimary:
                    currentTheme === 'dark' ? 'white' : 'black',
                  colorText:
                    currentTheme === 'dark' ? 'white' : 'black',
                },
                elements: {
                  rootBox: {
                    boxShadow: 'none',
                    width: '100%',
                  },
                  card: {
                    border: 'none',
                    boxShadow: 'none',
                    width: '100%',
                  },
                  cardBox: {
                    width: '100%',
                    backgroundColor: 'transparent',
                    border: 'none',
                    boxShadow: 'none',
                  },
                  scrollBox: {
                    backgroundColor:
                      currentTheme === 'dark'
                        ? 'transparent'
                        : 'white',
                    width: '100%',
                  },
                  navbar: {
                    background:
                      currentTheme === 'dark'
                        ? 'transparent'
                        : 'white',
                  },
                },
              }}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SecurityTab;
