// Comprehensive analytics types for production-ready financial dashboard

export interface FinancialMetrics {
    // Core Financial Metrics
    totalRevenue: number;
    totalExpenses: number;
    netProfitLoss: number;
    grossProfit: number;
    grossMargin: number;

    // Accounts
    accountsReceivable: number;
    accountsPayable: number;

    // Cash Flow
    cashFlow: number;
    operatingCashFlow: number;

    // Tax Information
    totalTaxPayable: number;
    totalTaxClaimable: number;
    netTaxPosition: number;

    // Payment Metrics
    averageDaysToPayment: number;
    paymentRate: number;

    // Counts
    totalInvoices: number;
    salesInvoiceCount: number;
    purchaseInvoiceCount: number;
    overdueInvoiceCount: number;
    paidInvoiceCount: number;

    // Trends
    monthOverMonthRevenue: number;
    monthOverMonthExpenses: number;
    monthOverMonthProfit: number;

    // Additional Metrics
    averageInvoiceValue: number;
    averageSaleValue: number;
    averagePurchaseValue: number;
    activeCustomers: number;
    activeSuppliers: number;
}

export interface RevenueAnalysis {
    totalRevenue: number;
    monthlyRevenue: MonthlyData[];
    topCustomers: CustomerAnalysis[];
    revenueByCategory: CategoryData[];
    revenueForecast: ForecastData[];
    growthRate: number;
    seasonalTrends: SeasonalData[];
}

export interface ExpenseAnalysis {
    totalExpenses: number;
    monthlyExpenses: MonthlyData[];
    expensesByCategory: CategoryData[];
    topSuppliers: SupplierAnalysis[];
    expenseForecast: ForecastData[];
    expenseGrowthRate: number;
    costOptimizationOpportunities: OptimizationOpportunity[];
}

export interface CashFlowAnalysis {
    netCashFlow: number;
    monthlyInflows: MonthlyData[];
    monthlyOutflows: MonthlyData[];
    cashFlowForecast: ForecastData[];
    paymentTimingAnalysis: PaymentTiming[];
    liquidityRatio: number;
    burnRate: number;
}

export interface AccountsAnalysis {
    accountsReceivable: {
        total: number;
        aging: AgingData[];
        overdueAmount: number;
        averageCollectionDays: number;
        topDebtors: DebtorData[];
    };
    accountsPayable: {
        total: number;
        aging: AgingData[];
        overdueAmount: number;
        averagePaymentDays: number;
        topCreditors: CreditorData[];
    };
}

export interface TaxAnalysis {
    totalTaxPayable: number;
    totalTaxClaimable: number;
    netTaxPosition: number;
    taxByType: TaxTypeData[];
    monthlyTaxLiability: MonthlyData[];
    complianceStatus: ComplianceStatus;
    upcomingTaxDeadlines: TaxDeadline[];
}

// Supporting interfaces
export interface MonthlyData {
    month: string;
    year: number;
    value: number;
    count?: number;
    growth?: number;
}

export interface CategoryData {
    id: string;
    name: string;
    value: number;
    percentage: number;
    count: number;
    color?: string;
}

export interface CustomerAnalysis {
    id: string;
    name: string;
    totalRevenue: number;
    invoiceCount: number;
    averageInvoiceValue: number;
    lastInvoiceDate: Date;
    paymentBehavior: 'EXCELLENT' | 'GOOD' | 'AVERAGE' | 'POOR';
    averagePaymentDays: number;
}

export interface SupplierAnalysis {
    id: string;
    name: string;
    totalSpend: number;
    invoiceCount: number;
    averageInvoiceValue: number;
    lastInvoiceDate: Date;
    paymentTerms: string;
    relationshipScore: number;
}

export interface ForecastData {
    period: string;
    predicted: number;
    confidence: number;
    upperBound: number;
    lowerBound: number;
}

export interface SeasonalData {
    quarter: string;
    averageValue: number;
    trend: 'INCREASING' | 'DECREASING' | 'STABLE';
}

export interface OptimizationOpportunity {
    category: string;
    currentSpend: number;
    potentialSavings: number;
    recommendation: string;
    priority: 'HIGH' | 'MEDIUM' | 'LOW';
}

export interface PaymentTiming {
    period: string;
    averageDaysToPayment: number;
    onTimePaymentRate: number;
    latePaymentRate: number;
}

export interface AgingData {
    range: string;
    amount: number;
    count: number;
    percentage: number;
}

export interface DebtorData {
    customerId: string;
    customerName: string;
    amount: number;
    oldestInvoiceDate: Date;
    daysPastDue: number;
}

export interface CreditorData {
    supplierId: string;
    supplierName: string;
    amount: number;
    oldestInvoiceDate: Date;
    daysPastDue: number;
}

export interface TaxTypeData {
    type: string;
    rate: number;
    payable: number;
    claimable: number;
    net: number;
}

export interface ComplianceStatus {
    status: 'COMPLIANT' | 'WARNING' | 'NON_COMPLIANT';
    issues: string[];
    recommendations: string[];
}

export interface TaxDeadline {
    type: string;
    dueDate: Date;
    estimatedAmount: number;
    status: 'UPCOMING' | 'DUE' | 'OVERDUE';
}

// AI Insights
export interface AIInsights {
    financialHealth: {
        score: number;
        status: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR';
        factors: string[];
    };
    recommendations: AIRecommendation[];
    predictions: AIPrediction[];
    alerts: AIAlert[];
    trends: AITrend[];
}

export interface AIRecommendation {
    id: string;
    type: 'COST_OPTIMIZATION' | 'REVENUE_GROWTH' | 'CASH_FLOW' | 'TAX_PLANNING';
    title: string;
    description: string;
    impact: 'HIGH' | 'MEDIUM' | 'LOW';
    effort: 'HIGH' | 'MEDIUM' | 'LOW';
    estimatedSavings?: number;
    estimatedRevenue?: number;
}

export interface AIPrediction {
    type: 'REVENUE' | 'EXPENSES' | 'CASH_FLOW' | 'PROFIT';
    period: string;
    predicted: number;
    confidence: number;
    factors: string[];
}

export interface AIAlert {
    id: string;
    type: 'WARNING' | 'OPPORTUNITY' | 'RISK';
    title: string;
    description: string;
    severity: 'HIGH' | 'MEDIUM' | 'LOW';
    actionRequired: boolean;
}

export interface AITrend {
    metric: string;
    direction: 'INCREASING' | 'DECREASING' | 'STABLE';
    strength: 'STRONG' | 'MODERATE' | 'WEAK';
    description: string;
}

// Chart data interfaces
export interface ChartData {
    name: string;
    value: number;
    [key: string]: unknown;
}

export interface TimeSeriesData {
    date: string;
    value: number;
    category?: string;
    [key: string]: unknown;
}

// Filter and query interfaces
export interface AnalyticsFilters {
    dateRange: {
        from: Date;
        to: Date;
    };
    invoiceType?: 'PURCHASE' | 'PAYMENT' | 'ALL';
    status?: string;
    vendor?: string;
    category?: string;
    currency?: string;
    amountRange?: [number, number];
}

export interface AnalyticsQuery {
    metrics: string[];
    filters: AnalyticsFilters;
    groupBy?: string[];
    orderBy?: string;
    limit?: number;
}