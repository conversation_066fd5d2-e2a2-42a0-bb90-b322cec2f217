import { <PERSON><PERSON><PERSON>, Loader2 } from "lucide-react";
import { 
  <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import { DataSourceCard } from "./DataSourceCard";
import { FormData, DataSource } from "../custom-report-builder";

interface DataSelectionStepProps {
  formData: FormData;
  dataSources: DataSource[];
  dataLoading: boolean;
  getCurrentDataSource: () => DataSource;
  handleSelectChange: (id: string, value: string) => void;
  handleFieldToggle: (fieldName: string) => void;
  onBack: () => void;
  onNext: () => void;
}

export function DataSelectionStep({
  formData,
  dataSources,
  dataLoading,
  getCurrentDataSource,
  handleSelectChange,
  handleFieldToggle,
  onBack,
  onNext
}: DataSelectionStepProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Data Selection</CardTitle>
        <CardDescription>
          Select the data sources and fields for your report
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {dataLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">Loading data sources...</span>
          </div>
        ) : (
          <>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium">Data Sources</h3>
              </div>

              <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                {dataSources.map((source) => (
                  <DataSourceCard
                    key={source.name}
                    title={source.label}
                    description={source.description}
                    icon={source.icon}
                    selected={formData.selectedDataSource === source.name}
                    onClick={() => handleSelectChange('selectedDataSource', source.name)}
                  />
                ))}
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium">Selected Fields</h3>
              </div>

              <div className="border rounded-md">
                <div className="bg-muted p-3 flex items-center justify-between">
                  <h4 className="text-sm font-medium">{getCurrentDataSource().label}</h4>
                  <Badge>Primary Source</Badge>
                </div>
                <div className="p-3 space-y-2">
                  {getCurrentDataSource().fields.map((field) => (
                    <div key={field.name} 
                      className="flex items-center justify-between p-2 bg-muted/50 rounded-md">
                      <div className="flex items-center gap-2">
                        <Checkbox 
                          id={`field-${field.name}`}
                          checked={formData.selectedFields.includes(field.name)}
                          onCheckedChange={() => handleFieldToggle(field.name)}
                        />
                        <label htmlFor={`field-${field.name}`} className="text-sm font-medium cursor-pointer">
                          {field.label}
                        </label>
                      </div>
                      <Badge variant="outline">{field.type}</Badge>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={onBack}>
          Back
        </Button>
        <Button 
          onClick={onNext}
          disabled={dataLoading}
        >
          Next
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  );
} 