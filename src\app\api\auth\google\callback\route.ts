import { NextRequest, NextResponse } from "next/server";
import { auth, currentUser } from "@clerk/nextjs/server";
import { connectGmailAccount } from "@/lib/services/gmail-service";
import db from "@/db/db";
import { nanoid } from "nanoid";

export async function GET(request: NextRequest) {
  const { userId: clerkUserId } = await auth();
  const searchParams = request.nextUrl.searchParams;
  
  // Get the authorization code and state from the query parameters
  const code = searchParams.get("code");
  
  // We're still parsing state but not using it - keeping for backward compatibility
  const state = searchParams.get("state");
  if (state) {
    try {
      JSON.parse(Buffer.from(state, 'base64').toString());
      // We don't actually need to use the state anymore since we're getting user from Clerk
    } catch (error) {
      console.error("Error parsing state parameter:", error);
    }
  }
  
  if (!clerkUserId) {
    return NextResponse.redirect(new URL("/sign-in", request.url));
  }
  
  if (!code) {
    return new Response("Authorization code missing", { status: 400 });
  }
  
  try {
    // Get current Clerk user to get email
    const clerkUser = await currentUser();
    const userEmail = clerkUser?.emailAddresses[0]?.emailAddress;
    
    if (!userEmail) {
      throw new Error("No email found for user. Please ensure your account has a verified email.");
    }

    // Find user in db by email or clerk id
    const dbUser = await db.user.findFirst({
      where: {
        OR: [
          { email: userEmail },
          { clerkId: clerkUserId }
        ]
      }
    });
    
    let dbUserId;
    
    if (!dbUser) {
      // No user found - create a new user in our database
      try {
        const createdUser = await db.user.create({
          data: {
            id: nanoid(),
            email: userEmail,
            clerkId: clerkUserId,
            firstName: clerkUser?.firstName || undefined,
            lastName: clerkUser?.lastName || undefined,
            profileImageUrl: clerkUser?.imageUrl || undefined,
            role: "USER",
            status: "ACTIVE"
          }
        });
        dbUserId = createdUser.id;
      } catch (createError) {
        console.error("Error creating user:", createError);
        throw new Error("Failed to create user in database. Please contact support.");
      }
    } else {
      dbUserId = dbUser.id;
    }
    
    // Connect the Gmail account with the database user ID
    const result = await connectGmailAccount(dbUserId, code);
    
    // Get the email from the result
    const email = result.email || "your Gmail account";
    
    // Close the popup and redirect to settings page
    return new Response(`
      <html>
        <head>
          <title>Gmail Connected</title>
          <script>
            window.onload = function() {
              // Pass the email back to the parent window
              window.opener.postMessage({
                type: 'gmail_connected',
                email: "${email}"
              }, window.location.origin);
              window.close();
            }
          </script>
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
              display: flex;
              align-items: center;
              justify-content: center;
              height: 100vh;
              flex-direction: column;
              margin: 0;
              padding: 20px;
              text-align: center;
              color: #333;
            }
            h2 {
              margin-bottom: 12px;
            }
            .email {
              font-weight: bold;
              color: #2563eb;
            }
          </style>
        </head>
        <body>
          <h2>Gmail Connected Successfully!</h2>
          <p>Connected <span class="email">${email}</span></p>
          <p>You can close this window now.</p>
        </body>
      </html>
    `, {
      headers: {
        "Content-Type": "text/html",
      },
    });
  } catch (error) {
    console.error("Error connecting Gmail:", error);
    
    // Return an error page
    return new Response(`
      <html>
        <head>
          <title>Connection Failed</title>
          <script>
            window.onload = function() {
              window.opener.postMessage({
                type: 'gmail_connection_failed',
                error: "${error instanceof Error ? error.message.replace(/"/g, '&quot;') : 'Unknown error'}"
              }, window.location.origin);
              setTimeout(() => window.close(), 5000);
            }
          </script>
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
              display: flex;
              align-items: center;
              justify-content: center;
              height: 100vh;
              flex-direction: column;
              margin: 0;
              padding: 20px;
              text-align: center;
              color: #333;
            }
            h2 {
              margin-bottom: 12px;
              color: #e53e3e;
            }
          </style>
        </head>
        <body>
          <h2>Connection Failed</h2>
          <p>Failed to connect Gmail account: ${error instanceof Error ? error.message : 'Unknown error'}</p>
          <p>This window will close automatically in 5 seconds...</p>
        </body>
      </html>
    `, {
      headers: {
        "Content-Type": "text/html",
      },
      status: 500,
    });
  }
} 