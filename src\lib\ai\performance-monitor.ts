interface PerformanceMetrics {
  responseTime: number;
  tokenUsage: number;
  cacheHitRate: number;
  modelUsed: string;
  success: boolean;
  errorType?: string;
  timestamp: Date;
  userId: string;
  queryType: string;
}

interface PerformanceStats {
  avgResponseTime: number;
  avgTokenUsage: number;
  successRate: number;
  cacheEfficiency: number;
  modelDistribution: Record<string, number>;
  errorBreakdown: Record<string, number>;
  trends: {
    responseTime: 'improving' | 'stable' | 'degrading';
    tokenUsage: 'optimizing' | 'stable' | 'increasing';
  };
}

/**
 * Advanced Performance Monitoring for AI System Optimization
 */
export class PerformanceMonitor {
  private static metrics: PerformanceMetrics[] = [];
  private static readonly MAX_METRICS = 1000; // Keep last 1000 metrics

  /**
   * Record performance metrics for a request
   */
  static recordMetrics(metrics: Omit<PerformanceMetrics, 'timestamp'>): void {
    this.metrics.push({
      ...metrics,
      timestamp: new Date()
    });

    // Keep only recent metrics
    if (this.metrics.length > this.MAX_METRICS) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS);
    }

    // Log performance issues
    if (metrics.responseTime > 5000) {
      console.warn(`🐌 Slow response detected: ${metrics.responseTime}ms for ${metrics.queryType}`);
    }

    if (metrics.tokenUsage > 4000) {
      console.warn(`🔥 High token usage: ${metrics.tokenUsage} tokens for ${metrics.queryType}`);
    }
  }

  /**
   * Get comprehensive performance statistics
   */
  static getPerformanceStats(timeWindow: number = 24): PerformanceStats {
    const cutoff = new Date(Date.now() - timeWindow * 60 * 60 * 1000);
    const recentMetrics = this.metrics.filter(m => m.timestamp >= cutoff);

    if (recentMetrics.length === 0) {
      return this.getDefaultStats();
    }

    const avgResponseTime = recentMetrics.reduce((sum, m) => sum + m.responseTime, 0) / recentMetrics.length;
    const avgTokenUsage = recentMetrics.reduce((sum, m) => sum + m.tokenUsage, 0) / recentMetrics.length;
    const successRate = recentMetrics.filter(m => m.success).length / recentMetrics.length;
    
    // Calculate cache efficiency (assuming cache hits have lower response times)
    const fastResponses = recentMetrics.filter(m => m.responseTime < 1000).length;
    const cacheEfficiency = fastResponses / recentMetrics.length;

    // Model distribution
    const modelDistribution = recentMetrics.reduce((acc, m) => {
      acc[m.modelUsed] = (acc[m.modelUsed] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Error breakdown
    const errorBreakdown = recentMetrics
      .filter(m => !m.success && m.errorType)
      .reduce((acc, m) => {
        acc[m.errorType!] = (acc[m.errorType!] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

    // Calculate trends
    const trends = this.calculateTrends(recentMetrics);

    return {
      avgResponseTime,
      avgTokenUsage,
      successRate,
      cacheEfficiency,
      modelDistribution,
      errorBreakdown,
      trends
    };
  }

  /**
   * Get performance recommendations
   */
  static getOptimizationRecommendations(): string[] {
    const stats = this.getPerformanceStats();
    const recommendations: string[] = [];

    if (stats.avgResponseTime > 3000) {
      recommendations.push('Consider using faster models for simple queries');
    }

    if (stats.avgTokenUsage > 3000) {
      recommendations.push('Implement more aggressive message truncation');
    }

    if (stats.cacheEfficiency < 0.3) {
      recommendations.push('Increase cache TTL or improve cache key strategies');
    }

    if (stats.successRate < 0.95) {
      recommendations.push('Investigate and fix recurring errors');
    }

    if (stats.trends.responseTime === 'degrading') {
      recommendations.push('Performance is degrading - investigate system load');
    }

    return recommendations;
  }

  /**
   * Auto-optimize based on performance metrics
   */
  static getAutoOptimizationSettings(): {
    maxSteps: number;
    cacheStrategy: 'aggressive' | 'moderate' | 'conservative';
    modelSelection: 'performance' | 'balanced' | 'quality';
    messageLimit: number;
  } {
    const stats = this.getPerformanceStats();

    let maxSteps = 2;
    let cacheStrategy: 'aggressive' | 'moderate' | 'conservative' = 'moderate';
    let modelSelection: 'performance' | 'balanced' | 'quality' = 'balanced';
    let messageLimit = 3;

    // Adjust based on performance
    if (stats.avgResponseTime > 4000) {
      maxSteps = 1;
      cacheStrategy = 'aggressive';
      modelSelection = 'performance';
      messageLimit = 2;
    } else if (stats.avgResponseTime < 2000 && stats.successRate > 0.95) {
      maxSteps = 3;
      cacheStrategy = 'conservative';
      modelSelection = 'quality';
      messageLimit = 4;
    }

    return {
      maxSteps,
      cacheStrategy,
      modelSelection,
      messageLimit
    };
  }

  /**
   * Calculate performance trends
   */
  private static calculateTrends(metrics: PerformanceMetrics[]): PerformanceStats['trends'] {
    if (metrics.length < 10) {
      return { responseTime: 'stable', tokenUsage: 'stable' };
    }

    const half = Math.floor(metrics.length / 2);
    const firstHalf = metrics.slice(0, half);
    const secondHalf = metrics.slice(half);

    const firstHalfAvgResponse = firstHalf.reduce((sum, m) => sum + m.responseTime, 0) / firstHalf.length;
    const secondHalfAvgResponse = secondHalf.reduce((sum, m) => sum + m.responseTime, 0) / secondHalf.length;

    const firstHalfAvgTokens = firstHalf.reduce((sum, m) => sum + m.tokenUsage, 0) / firstHalf.length;
    const secondHalfAvgTokens = secondHalf.reduce((sum, m) => sum + m.tokenUsage, 0) / secondHalf.length;

    const responseTimeTrend = this.getTrend(firstHalfAvgResponse, secondHalfAvgResponse);
    const tokenUsageTrend = this.getTrend(firstHalfAvgTokens, secondHalfAvgTokens);

    return {
      responseTime: responseTimeTrend === 'increasing' ? 'degrading' : responseTimeTrend === 'decreasing' ? 'improving' : 'stable',
      tokenUsage: tokenUsageTrend === 'increasing' ? 'increasing' : tokenUsageTrend === 'decreasing' ? 'optimizing' : 'stable'
    };
  }

  private static getTrend(first: number, second: number): 'increasing' | 'decreasing' | 'stable' {
    const change = (second - first) / first;
    if (change > 0.1) return 'increasing';
    if (change < -0.1) return 'decreasing';
    return 'stable';
  }

  private static getDefaultStats(): PerformanceStats {
    return {
      avgResponseTime: 2000,
      avgTokenUsage: 2000,
      successRate: 0.95,
      cacheEfficiency: 0.5,
      modelDistribution: {},
      errorBreakdown: {},
      trends: { responseTime: 'stable', tokenUsage: 'stable' }
    };
  }

  /**
   * Export metrics for analysis
   */
  static exportMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }

  /**
   * Clear metrics (for testing or reset)
   */
  static clearMetrics(): void {
    this.metrics = [];
  }

  /**
   * Get real-time performance dashboard data
   */
  static getDashboardData() {
    const stats = this.getPerformanceStats(1); // Last hour
    const recommendations = this.getOptimizationRecommendations();
    const autoSettings = this.getAutoOptimizationSettings();

    return {
      currentStats: stats,
      recommendations,
      autoOptimization: autoSettings,
      systemHealth: this.getSystemHealth(stats)
    };
  }

  private static getSystemHealth(stats: PerformanceStats): 'excellent' | 'good' | 'fair' | 'poor' {
    let score = 0;

    // Response time score (0-25)
    if (stats.avgResponseTime < 2000) score += 25;
    else if (stats.avgResponseTime < 3000) score += 20;
    else if (stats.avgResponseTime < 4000) score += 15;
    else if (stats.avgResponseTime < 5000) score += 10;

    // Token usage score (0-25)
    if (stats.avgTokenUsage < 2000) score += 25;
    else if (stats.avgTokenUsage < 3000) score += 20;
    else if (stats.avgTokenUsage < 4000) score += 15;
    else if (stats.avgTokenUsage < 5000) score += 10;

    // Success rate score (0-25)
    score += Math.round(stats.successRate * 25);

    // Cache efficiency score (0-25)
    score += Math.round(stats.cacheEfficiency * 25);

    if (score >= 80) return 'excellent';
    if (score >= 60) return 'good';
    if (score >= 40) return 'fair';
    return 'poor';
  }
}

/**
 * Performance decorator for automatic monitoring
 */
export function monitored(queryType: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      let success = true;
      let errorType: string | undefined;

      try {
        const result = await method.apply(this, args);
        return result;
      } catch (error) {
        success = false;
        errorType = error instanceof Error ? error.name : 'UnknownError';
        throw error;
      } finally {
        const responseTime = Date.now() - startTime;
        
        PerformanceMonitor.recordMetrics({
          responseTime,
          tokenUsage: 0, // Would need to be calculated based on actual usage
          cacheHitRate: 0, // Would need to be tracked
          modelUsed: 'unknown', // Would need to be passed
          success,
          errorType,
          userId: 'unknown', // Would need to be passed
          queryType
        });
      }
    };

    return descriptor;
  };
}
