import db from '@/db/db';
import { Message } from 'ai';
import { CacheEngine } from './cache-engine';

interface ConversationMemory {
  userId: string;
  chatId: string;
  summary: string;
  keyTopics: string[];
  userPreferences: Record<string, any>;
  lastUpdated: Date;
  messageCount: number;
}

interface UserProfile {
  name: string;
  businessType: string;
  commonTasks: string[];
  preferredCurrency: string;
  timezone: string;
  communicationStyle: 'formal' | 'casual' | 'technical';
  expertise: 'beginner' | 'intermediate' | 'expert';
  workingHours: { start: number; end: number }; // 24-hour format
  responsePreference: 'detailed' | 'concise' | 'visual';
  industryFocus: string[];
  painPoints: string[];
  goals: string[];
}

/**
 * Advanced Memory Engine for maintaining conversation context and user preferences
 */
export class MemoryEngine {
  private static memoryCache = new Map<string, ConversationMemory>();
  private static profileCache = new Map<string, UserProfile>();
  
  /**
   * Get conversation memory for a specific chat
   */
  static async getConversationMemory(chatId: string, userId: string): Promise<ConversationMemory | null> {
    // Check cache first
    const cached = this.memoryCache.get(chatId);
    if (cached && Date.now() - cached.lastUpdated.getTime() < 300000) { // 5 minutes
      return cached;
    }

    try {
      // Get recent messages from this conversation
      const messages = await db.message.findMany({
        where: { chatId },
        orderBy: { createdAt: 'desc' },
        take: 20
      });

      if (messages.length === 0) return null;

      // Analyze conversation for key topics and patterns
      const memory = await this.analyzeConversation(messages, userId, chatId);
      
      // Cache the result
      this.memoryCache.set(chatId, memory);
      
      return memory;
    } catch (error) {
      console.error('Memory Engine Error:', error);
      return null;
    }
  }

  /**
   * Update conversation memory with new message
   */
  static async updateConversationMemory(
    chatId: string, 
    userId: string, 
    newMessage: Message
  ): Promise<void> {
    try {
      const existing = await this.getConversationMemory(chatId, userId);
      
      if (existing) {
        // Update existing memory
        existing.messageCount += 1;
        existing.lastUpdated = new Date();
        
        // Extract new topics from the message
        const newTopics = this.extractTopics(newMessage.content as string);
        existing.keyTopics = [...new Set([...existing.keyTopics, ...newTopics])].slice(0, 10);
        
        // Update cache
        this.memoryCache.set(chatId, existing);
      }
    } catch (error) {
      console.error('Failed to update conversation memory:', error);
    }
  }

  /**
   * Get user profile and preferences with advanced caching
   */
  static async getUserProfile(userId: string): Promise<UserProfile> {
    const cacheKey = CacheEngine.generateKey('user-profile', userId);

    return CacheEngine.getOrSet(
      cacheKey,
      'user-profile',
      async () => {
        try {
          // Get comprehensive user data and analyze patterns
          const [user, recentChats, invoicePatterns, recentActivity] = await Promise.all([
            db.user.findUnique({
              where: { id: userId },
              include: { aiSettings: true }
            }),

            db.chat.findMany({
              where: { userId },
              include: { messages: { take: 10 } },
              orderBy: { createdAt: 'desc' },
              take: 10
            }),

            db.invoice.findMany({
              where: { userId },
              orderBy: { createdAt: 'desc' },
              take: 50,
              select: {
                currency: true,
                vendorName: true,
                amount: true,
                createdAt: true,
                categoryId: true,
                status: true
              }
            }),

            // Get recent activity patterns
            db.message.findMany({
              where: {
                chat: { userId },
                createdAt: { gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
              },
              orderBy: { createdAt: 'desc' },
              take: 100
            })
          ]);

          const profile: UserProfile = {
            name: `${user?.firstName || ''} ${user?.lastName || ''}`.trim() || 'User',
            businessType: this.inferBusinessType(invoicePatterns),
            commonTasks: this.extractCommonTasks(recentChats),
            preferredCurrency: this.getPreferredCurrency(invoicePatterns),
            timezone: 'UTC', // Could be enhanced with user settings
            communicationStyle: this.inferCommunicationStyle(recentChats),
            expertise: this.inferExpertiseLevel(recentChats, invoicePatterns),
            workingHours: this.inferWorkingHours(recentActivity),
            responsePreference: this.inferResponsePreference(recentChats),
            industryFocus: this.inferIndustryFocus(invoicePatterns),
            painPoints: this.identifyPainPoints(recentChats, invoicePatterns),
            goals: this.inferUserGoals(recentChats, invoicePatterns)
          };

          return profile;
        } catch (error) {
          console.error('Failed to get user profile:', error);
          return this.getDefaultProfile();
        }
      }
    );
  }

  /**
   * Generate conversation summary for context
   */
  static generateConversationSummary(memory: ConversationMemory, profile: UserProfile): string {
    return `Previous conversation context:
- Topics discussed: ${memory.keyTopics.join(', ')}
- User: ${profile.name} (${profile.businessType} business)
- Communication style: ${profile.communicationStyle}
- Common tasks: ${profile.commonTasks.join(', ')}
- Messages in conversation: ${memory.messageCount}`;
  }

  /**
   * Analyze conversation for patterns and topics
   */
  private static async analyzeConversation(
    messages: any[], 
    userId: string, 
    chatId: string
  ): Promise<ConversationMemory> {
    const allContent = messages
      .map(m => typeof m.content === 'string' ? m.content : JSON.stringify(m.content))
      .join(' ');

    const keyTopics = this.extractTopics(allContent);
    const userPreferences = this.extractPreferences(allContent);

    return {
      userId,
      chatId,
      summary: this.generateSummary(allContent),
      keyTopics,
      userPreferences,
      lastUpdated: new Date(),
      messageCount: messages.length
    };
  }

  /**
   * Extract key topics from text
   */
  private static extractTopics(text: string): string[] {
    const lowerText = text.toLowerCase();
    const topics: string[] = [];

    // Financial topics
    if (lowerText.includes('invoice')) topics.push('invoices');
    if (lowerText.includes('report')) topics.push('reports');
    if (lowerText.includes('payment') || lowerText.includes('paid')) topics.push('payments');
    if (lowerText.includes('vendor') || lowerText.includes('supplier')) topics.push('vendors');
    if (lowerText.includes('expense') || lowerText.includes('cost')) topics.push('expenses');
    if (lowerText.includes('revenue') || lowerText.includes('income')) topics.push('revenue');
    if (lowerText.includes('cash flow')) topics.push('cash-flow');
    if (lowerText.includes('budget')) topics.push('budgeting');

    return [...new Set(topics)];
  }

  /**
   * Extract user preferences from conversation
   */
  private static extractPreferences(text: string): Record<string, any> {
    const preferences: Record<string, any> = {};
    
    // Currency preference
    const currencyMatch = text.match(/\$|USD|EUR|GBP|CAD/g);
    if (currencyMatch) {
      preferences.currency = currencyMatch[0] === '$' ? 'USD' : currencyMatch[0];
    }

    // Date format preference
    if (text.includes('MM/DD/YYYY')) preferences.dateFormat = 'US';
    if (text.includes('DD/MM/YYYY')) preferences.dateFormat = 'EU';

    return preferences;
  }

  /**
   * Generate conversation summary
   */
  private static generateSummary(text: string): string {
    // Simple extractive summary - could be enhanced with AI
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 20);
    return sentences.slice(0, 3).join('. ') + '.';
  }

  /**
   * Infer business type from invoice patterns
   */
  private static inferBusinessType(invoices: any[]): string {
    if (invoices.length === 0) return 'general';
    
    const vendors = invoices.map(i => i.vendorName?.toLowerCase() || '');
    
    if (vendors.some(v => v.includes('tech') || v.includes('software'))) return 'technology';
    if (vendors.some(v => v.includes('restaurant') || v.includes('food'))) return 'food-service';
    if (vendors.some(v => v.includes('retail') || v.includes('store'))) return 'retail';
    
    return 'general';
  }

  /**
   * Extract common tasks from chat history
   */
  private static extractCommonTasks(chats: any[]): string[] {
    const tasks: string[] = [];
    
    const allMessages = chats.flatMap(c => c.messages || []);
    const content = allMessages
      .map(m => typeof m.content === 'string' ? m.content : JSON.stringify(m.content))
      .join(' ')
      .toLowerCase();

    if (content.includes('create invoice')) tasks.push('creating invoices');
    if (content.includes('generate report')) tasks.push('generating reports');
    if (content.includes('track payment')) tasks.push('tracking payments');
    if (content.includes('analyze')) tasks.push('data analysis');

    return tasks.slice(0, 5);
  }

  /**
   * Get preferred currency from invoice patterns
   */
  private static getPreferredCurrency(invoices: any[]): string {
    if (invoices.length === 0) return 'USD';
    
    const currencies = invoices.map(i => i.currency).filter(Boolean);
    const currencyCount = currencies.reduce((acc, curr) => {
      acc[curr] = (acc[curr] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.keys(currencyCount).sort((a, b) => currencyCount[b] - currencyCount[a])[0] || 'USD';
  }

  /**
   * Infer communication style from chat patterns
   */
  private static inferCommunicationStyle(chats: any[]): 'formal' | 'casual' | 'technical' {
    const allMessages = chats.flatMap(c => c.messages || []);
    const content = allMessages
      .map(m => typeof m.content === 'string' ? m.content : JSON.stringify(m.content))
      .join(' ')
      .toLowerCase();

    if (content.includes('please') && content.includes('thank you')) return 'formal';
    if (content.includes('api') || content.includes('database') || content.includes('sql')) return 'technical';
    
    return 'casual';
  }

  /**
   * Infer user expertise level
   */
  private static inferExpertiseLevel(chats: any[], invoices: any[]): 'beginner' | 'intermediate' | 'expert' {
    const totalInvoices = invoices.length;
    const chatComplexity = chats.flatMap(c => c.messages || [])
      .filter(m => typeof m.content === 'string' && m.content.length > 50).length;

    if (totalInvoices > 100 && chatComplexity > 20) return 'expert';
    if (totalInvoices > 20 && chatComplexity > 5) return 'intermediate';
    return 'beginner';
  }

  /**
   * Infer working hours from activity patterns
   */
  private static inferWorkingHours(recentActivity: any[]): { start: number; end: number } {
    if (recentActivity.length === 0) return { start: 9, end: 17 };

    const hourCounts = new Array(24).fill(0);
    recentActivity.forEach(activity => {
      const hour = new Date(activity.createdAt).getHours();
      hourCounts[hour]++;
    });

    // Find the most active 8-hour window
    let maxActivity = 0;
    let bestStart = 9;

    for (let start = 0; start <= 16; start++) {
      const windowActivity = hourCounts.slice(start, start + 8).reduce((sum, count) => sum + count, 0);
      if (windowActivity > maxActivity) {
        maxActivity = windowActivity;
        bestStart = start;
      }
    }

    return { start: bestStart, end: bestStart + 8 };
  }

  /**
   * Infer response preference from chat patterns
   */
  private static inferResponsePreference(chats: any[]): 'detailed' | 'concise' | 'visual' {
    const allMessages = chats.flatMap(c => c.messages || []);
    const userMessages = allMessages.filter(m => m.role === 'user');

    if (userMessages.length === 0) return 'concise';

    const avgLength = userMessages.reduce((sum, m) => {
      const content = typeof m.content === 'string' ? m.content : JSON.stringify(m.content);
      return sum + content.length;
    }, 0) / userMessages.length;

    const hasVisualRequests = userMessages.some(m => {
      const content = typeof m.content === 'string' ? m.content : JSON.stringify(m.content);
      return content.toLowerCase().includes('chart') ||
             content.toLowerCase().includes('graph') ||
             content.toLowerCase().includes('visual');
    });

    if (hasVisualRequests) return 'visual';
    if (avgLength > 100) return 'detailed';
    return 'concise';
  }

  /**
   * Infer industry focus from vendor patterns
   */
  private static inferIndustryFocus(invoices: any[]): string[] {
    const vendorNames = invoices.map(i => i.vendorName?.toLowerCase() || '').filter(Boolean);
    const industries: string[] = [];

    const industryKeywords = {
      'technology': ['tech', 'software', 'cloud', 'saas', 'digital', 'app'],
      'healthcare': ['medical', 'health', 'clinic', 'hospital', 'pharma'],
      'retail': ['store', 'shop', 'retail', 'merchandise', 'sales'],
      'manufacturing': ['factory', 'production', 'manufacturing', 'industrial'],
      'consulting': ['consulting', 'advisory', 'services', 'professional'],
      'food-service': ['restaurant', 'food', 'catering', 'kitchen', 'dining']
    };

    Object.entries(industryKeywords).forEach(([industry, keywords]) => {
      const matches = vendorNames.filter(vendor =>
        keywords.some(keyword => vendor.includes(keyword))
      ).length;

      if (matches > 0) {
        industries.push(industry);
      }
    });

    return industries.slice(0, 3); // Top 3 industries
  }

  /**
   * Identify user pain points from conversations
   */
  private static identifyPainPoints(chats: any[], invoices: any[]): string[] {
    const painPoints: string[] = [];
    const allContent = chats.flatMap(c => c.messages || [])
      .map(m => typeof m.content === 'string' ? m.content : JSON.stringify(m.content))
      .join(' ')
      .toLowerCase();

    // Analyze for common pain points
    if (allContent.includes('slow') || allContent.includes('taking too long')) {
      painPoints.push('slow processes');
    }
    if (allContent.includes('confus') || allContent.includes('difficult')) {
      painPoints.push('complexity');
    }
    if (allContent.includes('manual') || allContent.includes('automat')) {
      painPoints.push('manual work');
    }
    if (allContent.includes('track') || allContent.includes('organiz')) {
      painPoints.push('organization');
    }

    // Analyze invoice patterns for pain points
    const overdueRate = invoices.filter(i => i.status === 'OVERDUE').length / invoices.length;
    if (overdueRate > 0.2) {
      painPoints.push('payment delays');
    }

    return painPoints.slice(0, 3);
  }

  /**
   * Infer user goals from behavior patterns
   */
  private static inferUserGoals(chats: any[], invoices: any[]): string[] {
    const goals: string[] = [];
    const allContent = chats.flatMap(c => c.messages || [])
      .map(m => typeof m.content === 'string' ? m.content : JSON.stringify(m.content))
      .join(' ')
      .toLowerCase();

    // Analyze for goal indicators
    if (allContent.includes('grow') || allContent.includes('expand')) {
      goals.push('business growth');
    }
    if (allContent.includes('save') || allContent.includes('cost') || allContent.includes('efficient')) {
      goals.push('cost optimization');
    }
    if (allContent.includes('automat') || allContent.includes('streamlin')) {
      goals.push('process automation');
    }
    if (allContent.includes('cash flow') || allContent.includes('payment')) {
      goals.push('cash flow management');
    }
    if (allContent.includes('report') || allContent.includes('analyz')) {
      goals.push('better insights');
    }

    return goals.slice(0, 3);
  }

  /**
   * Get default user profile
   */
  private static getDefaultProfile(): UserProfile {
    return {
      name: 'User',
      businessType: 'general',
      commonTasks: [],
      preferredCurrency: 'USD',
      timezone: 'UTC',
      communicationStyle: 'casual',
      expertise: 'beginner',
      workingHours: { start: 9, end: 17 },
      responsePreference: 'concise',
      industryFocus: [],
      painPoints: [],
      goals: []
    };
  }

  /**
   * Clear caches
   */
  static clearCache(userId?: string, chatId?: string) {
    if (chatId) {
      this.memoryCache.delete(chatId);
    }
    if (userId) {
      this.profileCache.delete(userId);
    }
    if (!userId && !chatId) {
      this.memoryCache.clear();
      this.profileCache.clear();
    }
  }
}
