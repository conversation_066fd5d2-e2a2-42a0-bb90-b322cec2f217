"use client";

import { ChangeEvent } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

interface VendorInfoTabProps {
  vendorInfo: {
    name: string;
    address: string;
    email: string;
    phone: string;
    taxId: string;
  };
  handleVendorInfoChange: (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
}

export function VendorInfoTab({
  vendorInfo,
  handleVendorInfoChange,
}: VendorInfoTabProps) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Vendor Information</h3>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="vendor-name">Vendor Name</Label>
          <Input
            id="vendor-name"
            name="name"
            value={vendorInfo.name}
            onChange={handleVendorInfoChange}
            placeholder="Vendor name"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="vendor-taxId">Tax ID / VAT Number</Label>
          <Input
            id="vendor-taxId"
            name="taxId"
            value={vendorInfo.taxId}
            onChange={handleVendorInfoChange}
            placeholder="Tax ID"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="vendor-address">Address</Label>
        <Textarea
          id="vendor-address"
          name="address"
          value={vendorInfo.address}
          onChange={handleVendorInfoChange}
          placeholder="Vendor address"
          rows={2}
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="vendor-email">Email</Label>
          <Input
            id="vendor-email"
            name="email"
            type="email"
            value={vendorInfo.email}
            onChange={handleVendorInfoChange}
            placeholder="<EMAIL>"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="vendor-phone">Phone</Label>
          <Input
            id="vendor-phone"
            name="phone"
            value={vendorInfo.phone}
            onChange={handleVendorInfoChange}
            placeholder="Phone number"
          />
        </div>
      </div>
    </div>
  );
}
