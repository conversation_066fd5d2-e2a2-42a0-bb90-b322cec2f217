'use client';

import React, { useState, useEffect } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { ShieldCheck, Check, Clock, RefreshCw } from 'lucide-react';
import { updateUserActiveStatus } from '@/lib/actions/team';
import TeamMembersList from './team/TeamMembersList';
import PendingInvitesList from './team/PendingInvitesList';
import RolesInfo from './team/RolesInfo';
import InviteForm from './team/InviteForm';
import { toast } from 'sonner';
import { UserRole } from '@prisma/client';
import { Button } from '@/components/ui/button';
import OrganizationSetup from './team/OrganizationSetup';
import {
  isClerkInvitationsEnabled,
  validateTeamConfig,
} from '@/lib/team-config';

// Define types for props
interface TeamMember {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string;
  profileImageUrl: string | null;
  role: UserRole;
  lastActive: Date | null;
  status: string;
  createdAt: Date;
}

interface Invite {
  id: string;
  email: string;
  role: UserRole;
  invitedBy: string;
  invitedOn: string;
  expiresAt: Date;
}

interface Statistics {
  totalMembers: number;
  activeMembers: number;
  pendingInvites: number;
  roleDistribution: Record<string, number>;
}

interface TeamProps {
  members: TeamMember[];
  invites: Invite[];
  statistics: Statistics;
  initialErrors: {
    members?: string;
    invites?: string;
    statistics?: string;
  };
}

const Team = ({
  members: initialMembers = [],
  invites: initialInvites = [],
  statistics: initialStatistics = {
    totalMembers: 0,
    activeMembers: 0,
    pendingInvites: 0,
    roleDistribution: {},
  },
  initialErrors = {},
}: TeamProps) => {
  // State
  const [members] = useState(initialMembers);
  const [invites] = useState(initialInvites);
  const [statistics] = useState(initialStatistics);
  const [activeTab, setActiveTab] = useState('members');
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Configuration: Controlled by environment variable
  // Set NEXT_PUBLIC_USE_CLERK_INVITATIONS=true in .env to enable Clerk's invitation system
  // Set NEXT_PUBLIC_USE_CLERK_INVITATIONS=false or leave unset to use custom invitations
  const USE_CLERK_INVITATIONS = isClerkInvitationsEnabled();

  // Validate configuration and show warnings if needed
  React.useEffect(() => {
    const configValidation = validateTeamConfig();
    if (!configValidation.isValid) {
      configValidation.errors.forEach((error) => {
        toast.error(`Configuration Error: ${error}`);
      });
    }
    configValidation.warnings.forEach((warning) => {
      toast.warning(`Configuration Warning: ${warning}`);
    });
  }, []);

  // Show errors as toasts
  useEffect(() => {
    if (initialErrors.members) {
      toast.error(`Error loading members: ${initialErrors.members}`);
    }
    if (initialErrors.invites) {
      toast.error(`Error loading invites: ${initialErrors.invites}`);
    }
    if (initialErrors.statistics) {
      toast.error(
        `Error loading statistics: ${initialErrors.statistics}`
      );
    }
  }, [initialErrors]);

  // Update user activity status
  useEffect(() => {
    updateUserActiveStatus().catch(() => {});
  }, []);

  // Refresh all data
  const refreshData = async () => {
    setIsRefreshing(true);

    try {
      // In a real implementation, you would fetch updated data here
      // For this example, we'll just simulate a delay
      await new Promise((resolve) => setTimeout(resolve, 500));
      
      // Trigger a re-render by updating state
      // This will cause the components to re-fetch their data
      toast.success('Data refreshed successfully');
    } catch {
      toast.error('Failed to refresh data');
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="min-h-screen flex-col bg-background dark:bg-[#0B1739] w-full space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">
              Team Management
            </h1>
            <p className="text-muted-foreground">
              Manage team members and their access
              {isRefreshing && (
                <span className="ml-2 animate-pulse">
                  Refreshing...
                </span>
              )}
            </p>
            {USE_CLERK_INVITATIONS && (
              <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-md">
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  <strong>Clerk Invitations Enabled:</strong> Using
                  Clerk&apos;s built-in invitation system. If you encounter
                  issues, you may need to create a Clerk organization
                  first.
                </p>
              </div>
            )}
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={refreshData}
              disabled={isRefreshing}
            >
              <RefreshCw
                className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`}
              />
              Refresh
            </Button>
            <div className="border border-gray-300 dark:border-gray-600 rounded-md">
              <InviteForm
                onSuccess={refreshData}
                useClerkInvitations={USE_CLERK_INVITATIONS}
              />
            </div>
          </div>
        </div>

        {/* Organization Setup Component */}
        <div className="mb-6">
          <OrganizationSetup onSetupComplete={refreshData} />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="shadow-sm">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">
                  Total Members
                </CardTitle>
                <div className="h-8 w-8 rounded-full bg-blue-50 flex items-center justify-center">
                  <ShieldCheck className="h-4 w-4 text-blue-600" />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {statistics.totalMembers}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Across all roles
              </p>
            </CardContent>
          </Card>

          <Card className="shadow-sm">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">
                  Active Members
                </CardTitle>
                <div className="h-8 w-8 rounded-full bg-green-50 flex items-center justify-center">
                  <Check className="h-4 w-4 text-green-600" />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {statistics.activeMembers}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Active in the last 24 hours
              </p>
            </CardContent>
          </Card>

          <Card className="shadow-sm">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">
                  Pending Invites
                </CardTitle>
                <div className="h-8 w-8 rounded-full bg-amber-50 flex items-center justify-center">
                  <Clock className="h-4 w-4 text-amber-600" />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {statistics.pendingInvites}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Awaiting acceptance
              </p>
            </CardContent>
          </Card>
        </div>

        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-4"
        >
          <TabsList>
            <TabsTrigger value="members">Team Members</TabsTrigger>
            <TabsTrigger value="invites">Pending Invites</TabsTrigger>
            <TabsTrigger value="roles">
              Roles & Permissions
            </TabsTrigger>
          </TabsList>

          <TabsContent value="members" className="space-y-4">
            <Card className="shadow-sm">
              <CardContent className="p-0">
                <TeamMembersList
                  members={members}
                  onRefresh={refreshData}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="invites" className="space-y-4">
            <PendingInvitesList
              invites={invites}
              onRefresh={refreshData}
            />
          </TabsContent>

          <TabsContent value="roles" className="space-y-4">
            <RolesInfo />
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default Team;
