'use client';

import { useRouter } from 'next/navigation';
import { useWindowSize } from 'usehooks-ts';
import { motion } from 'motion/react';
import { useTheme } from 'next-themes';

import { Button } from '@/components/ui/button';
import { PlusIcon } from './icons';
import { useSidebar } from '@/components/ui/sidebar';
import { memo } from 'react';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from '@/components/ui/tooltip';
import {
  VisibilityType,
  VisibilitySelector,
} from './visibility-selector';
import { SidebarToggle } from './sidebar-toggle';
import { PhoneCall, Sparkles } from 'lucide-react';
import Colors from '@/components/theme/Colors';

function PureChatHeader({
  chatId,
  selectedModelId,
  selectedVisibilityType,
  isReadonly,
}: {
  chatId: string;
  selectedModelId: string;
  selectedVisibilityType: VisibilityType;
  isReadonly: boolean;
}) {
  const router = useRouter();
  const { open } = useSidebar();
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  const { width: windowWidth } = useWindowSize();

  return (
    <TooltipProvider>
      <motion.header
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="flex sticky top-0 bg-white/95 dark:bg-[#0B1739]/95 backdrop-blur-xl py-3 items-center px-4 md:px-6 gap-3 border-b border-blue-200/50 dark:border-[#0097B1]/30 z-20 shadow-sm"
      >
        <SidebarToggle />

        <div className="flex items-center gap-2 md:gap-3">
          {(!open || windowWidth < 768) && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  className="order-2 md:order-1 md:px-3 px-3 h-9 ml-auto md:ml-0 rounded-xl bg-white/90 dark:bg-[#22304d]/90 border-blue-200/60 dark:border-[#0097B1]/40 hover:bg-white dark:hover:bg-[#22304d] hover:border-blue-500/60 dark:hover:border-[#0097B1]/80 hover:shadow-lg hover:shadow-blue-500/20 dark:hover:shadow-[#0097B1]/20 transition-all duration-300 backdrop-blur-sm"
                  onClick={() => {
                    router.push('/dashboard/chat');
                    router.refresh();
                  }}
                >
                  <PlusIcon />
                  <span className="md:inline hidden text-sm font-medium">
                    New Chat
                  </span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>New Chat</TooltipContent>
            </Tooltip>
          )}

          {!isReadonly && (
            <div className="order-1 md:order-2 bg-white/90 dark:bg-[#22304d]/90 border-blue-200/60 dark:border-[#0097B1]/40 hover:bg-white dark:hover:bg-[#22304d] hover:border-blue-500/60 dark:hover:border-[#0097B1]/80 rounded-xl backdrop-blur-sm transition-all duration-300 px-4 py-2 flex items-center gap-2">
              <Sparkles size={16} className="text-primary" />
              <span className="font-medium text-sm">Billix AI</span>
            </div>
          )}

          {!isReadonly && (
            <VisibilitySelector
              chatId={chatId}
              selectedVisibilityType={selectedVisibilityType}
              className="order-1 md:order-3 bg-white/90 dark:bg-[#22304d]/90 border-blue-200/60 dark:border-[#0097B1]/40 hover:bg-white dark:hover:bg-[#22304d] hover:border-blue-500/60 dark:hover:border-[#0097B1]/80 rounded-xl backdrop-blur-sm transition-all duration-300"
            />
          )}
        </div>

        <div className="flex items-center ml-auto gap-3">
          <div className="hidden md:flex items-center bg-gradient-to-r from-blue-50 to-purple-50 dark:from-[#0B1739]/80 dark:to-[#0B1739]/60 px-3 py-2 rounded-xl border border-blue-200/50 dark:border-[#0097B1]/30">
            <div className="flex items-center text-sm font-semibold text-transparent bg-clip-text"
                 style={{ 
                   background: `linear-gradient(135deg, ${Colors.gradients.blueToPurple[0]}, ${Colors.gradients.blueToPurple[1]})`,
                   WebkitBackgroundClip: 'text',
                   WebkitTextFillColor: 'transparent'
                 }}>
              <motion.div
                animate={{ 
                  scale: [1, 1.1, 1],
                  rotate: [0, 5, -5, 0]
                }}
                transition={{ 
                  duration: 2,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              >
                <Sparkles
                  size={16}
                  className="mr-2"
                  style={{ color: Colors.gradients.blueToPurple[0] }}
                />
              </motion.div>
              Billix AI Agent
            </div>
          </div>

          <Button 
            className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white flex md:flex py-2 px-4 h-10 order-4 rounded-xl shadow-md hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300 font-medium"
            style={{
              background: `linear-gradient(135deg, ${Colors.gradients.blueToPurple[0]}, ${Colors.gradients.blueToPurple[1]})`
            }}
          >
            <PhoneCall size={16} className="mr-2" />
            <span className="hidden sm:inline">Voice Call</span>
            <span className="sm:hidden">Call</span>
          </Button>
        </div>
      </motion.header>
    </TooltipProvider>
  );
}

export const ChatHeader = memo(
  PureChatHeader,
  (prevProps, nextProps) => {
    return prevProps.selectedModelId === nextProps.selectedModelId;
  }
);
