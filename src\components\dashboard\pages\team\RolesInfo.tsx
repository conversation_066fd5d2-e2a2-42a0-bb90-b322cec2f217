import React from 'react';
import { <PERSON>, <PERSON>Header, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { Check, ShieldX } from "lucide-react";

export default function RolesInfo() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle>Admin</CardTitle>
          <CardDescription>Full access to all features</CardDescription>
        </CardHeader>
        <CardContent className="text-sm space-y-2">
          <div className="flex items-center">
            <Check className="h-4 w-4 text-green-500 mr-2" />
            <span>Manage team members</span>
          </div>
          <div className="flex items-center">
            <Check className="h-4 w-4 text-green-500 mr-2" />
            <span>Create and edit invoices</span>
          </div>
          <div className="flex items-center">
            <Check className="h-4 w-4 text-green-500 mr-2" />
            <span>Manage subscription</span>
          </div>
          <div className="flex items-center">
            <Check className="h-4 w-4 text-green-500 mr-2" />
            <span>Access all reports</span>
          </div>
          <div className="flex items-center">
            <Check className="h-4 w-4 text-green-500 mr-2" />
            <span>System settings</span>
          </div>
        </CardContent>
      </Card>

      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle>Editor</CardTitle>
          <CardDescription>Edit and manage invoices</CardDescription>
        </CardHeader>
        <CardContent className="text-sm space-y-2">
          <div className="flex items-center">
            <Check className="h-4 w-4 text-green-500 mr-2" />
            <span>Create and edit invoices</span>
          </div>
          <div className="flex items-center">
            <Check className="h-4 w-4 text-green-500 mr-2" />
            <span>Generate reports</span>
          </div>
          <div className="flex items-center">
            <Check className="h-4 w-4 text-green-500 mr-2" />
            <span>Use AI assistant</span>
          </div>
          <div className="flex items-center opacity-50">
            <ShieldX className="h-4 w-4 text-red-500 mr-2" />
            <span>Manage team</span>
          </div>
          <div className="flex items-center opacity-50">
            <ShieldX className="h-4 w-4 text-red-500 mr-2" />
            <span>Subscription settings</span>
          </div>
        </CardContent>
      </Card>

      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle>Viewer</CardTitle>
          <CardDescription>View-only access</CardDescription>
        </CardHeader>
        <CardContent className="text-sm space-y-2">
          <div className="flex items-center">
            <Check className="h-4 w-4 text-green-500 mr-2" />
            <span>View invoices</span>
          </div>
          <div className="flex items-center">
            <Check className="h-4 w-4 text-green-500 mr-2" />
            <span>View reports</span>
          </div>
          <div className="flex items-center">
            <Check className="h-4 w-4 text-green-500 mr-2" />
            <span>Use AI assistant</span>
          </div>
          <div className="flex items-center opacity-50">
            <ShieldX className="h-4 w-4 text-red-500 mr-2" />
            <span>Edit invoices</span>
          </div>
          <div className="flex items-center opacity-50">
            <ShieldX className="h-4 w-4 text-red-500 mr-2" />
            <span>System settings</span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 