import { groq } from '@ai-sdk/groq';
import { anthropic } from '@ai-sdk/anthropic';
import { customProvider } from 'ai';

export const DEFAULT_CHAT_MODEL: string = 'billix-smart';

// Intelligent model routing based on task complexity and requirements
export const myProvider = customProvider({
  languageModels: {
    // Primary chat model - Fast and intelligent
    'billix-smart': groq('llama-3.3-70b-versatile'), // Upgraded to Llama 3.3 70B for better performance

    // Specialized models for different tasks
    'billix-fast': groq('llama-3.1-8b-instant'), // Ultra-fast for simple queries
    'billix-reasoning': groq('deepseek-r1-distill-llama-70b'), // For complex analysis
    'billix-creative': groq('llama-3.3-70b-versatile'), // For creative tasks and document generation

    // Fallback models
    'claude-smart': anthropic('claude-3-5-haiku-********'), // Anthropic fallback
    'legacy-model': groq('meta-llama/llama-4-maverick-17b-128e-instruct'), // Legacy fallback
  },
});

/**
 * Intelligent model selection based on query type and complexity
 */
export function selectOptimalModel(query: string, context?: any): string {
  const lowerQuery = query.toLowerCase();

  // Fast model for simple queries
  if (lowerQuery.length < 50 ||
      lowerQuery.includes('hello') ||
      lowerQuery.includes('hi') ||
      lowerQuery.includes('thanks')) {
    return 'billix-fast';
  }

  // Reasoning model for complex analysis
  if (lowerQuery.includes('analyze') ||
      lowerQuery.includes('compare') ||
      lowerQuery.includes('calculate') ||
      lowerQuery.includes('forecast')) {
    return 'billix-reasoning';
  }

  // Creative model for document generation
  if (lowerQuery.includes('create') ||
      lowerQuery.includes('write') ||
      lowerQuery.includes('generate') ||
      lowerQuery.includes('draft')) {
    return 'billix-creative';
  }

  // Default to smart model
  return 'billix-smart';
}

interface ChatModel {
  id: string;
  name: string;
  description: string;
}

export const chatModels: Array<ChatModel> = [
  {
    id: 'billix-chat',
    name: 'Billix AI',
    description: 'Advanced AI assistant for financial management',
  },
];
