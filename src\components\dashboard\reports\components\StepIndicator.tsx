import { Badge } from "@/components/ui/badge";
import { ChevronRight } from "lucide-react";

interface StepIndicatorProps {
  currentStep: number;
}

export function StepIndicator({ currentStep }: StepIndicatorProps) {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center">
        <Badge
          variant={currentStep >= 1 ? "default" : "outline"}
          className="h-8 w-8 rounded-full p-0 flex items-center justify-center mr-2"
        >
          1
        </Badge>
        <span className={currentStep >= 1 ? "font-medium" : "text-muted-foreground"}>
          Basic Info
        </span>
        <ChevronRight className="h-4 w-4 mx-2 text-muted-foreground" />

        <Badge
          variant={currentStep >= 2 ? "default" : "outline"}
          className="h-8 w-8 rounded-full p-0 flex items-center justify-center mr-2"
        >
          2
        </Badge>
        <span className={currentStep >= 2 ? "font-medium" : "text-muted-foreground"}>
          Data Selection
        </span>
        <ChevronRight className="h-4 w-4 mx-2 text-muted-foreground" />

        <Badge
          variant={currentStep >= 3 ? "default" : "outline"}
          className="h-8 w-8 rounded-full p-0 flex items-center justify-center mr-2"
        >
          3
        </Badge>
        <span className={currentStep >= 3 ? "font-medium" : "text-muted-foreground"}>
          Calculations
        </span>
        <ChevronRight className="h-4 w-4 mx-2 text-muted-foreground" />

        <Badge
          variant={currentStep >= 4 ? "default" : "outline"}
          className="h-8 w-8 rounded-full p-0 flex items-center justify-center mr-2"
        >
          4
        </Badge>
        <span className={currentStep >= 4 ? "font-medium" : "text-muted-foreground"}>
          Visualization
        </span>
      </div>
    </div>
  );
} 