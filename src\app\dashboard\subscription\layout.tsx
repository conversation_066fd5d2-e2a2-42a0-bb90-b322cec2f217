import type React from 'react';
import { auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import { syncUserWithDatabase } from '@/lib/actions/user';

// Add a type for the user with organizations
type UserWithOrganizations = {
  id: string;
  clerkId: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  profileImageUrl: string | null;
  createdAt: Date;
  updatedAt: Date;
  role: string;
  lastActive: Date | null;
  status: string;
  organizations: { id: string; name: string }[];
};

export default async function SubscriptionLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Check if user is authenticated
  const { userId } = await auth();

  if (!userId) {
    redirect('/sign-in');
  }

  // Ensure user exists in database
  let user: UserWithOrganizations | null = null;

  try {
    user = (await syncUserWithDatabase()) as UserWithOrganizations;
  } catch {
    // Swallow error silently for production readiness
    user = null;
  }

  // If user doesn't have an organization, redirect to onboarding
  if (
    !user ||
    !user.organizations ||
    user.organizations.length === 0
  ) {
    redirect('/onboarding');
  }

  // NOTE: We don't check for active subscription here to avoid redirect loops
  // The subscription page itself will handle the subscription status

  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1">{children}</main>
    </div>
  );
}
