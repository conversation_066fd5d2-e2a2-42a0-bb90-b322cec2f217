/* eslint-disable */
"use server";

import { ExtractedInvoiceData, InvoiceLineItem, InvoiceType } from '../types';

/**
 * Normalize extracted data to ensure type compatibility
 * Particularly handling amount conversion from string to number
 */
export async function normalizeExtractedData(data: Record<string, unknown>): Promise<ExtractedInvoiceData> {
  const normalized: ExtractedInvoiceData = {};
  
  // Process standard fields
  if (data.invoiceNumber && typeof data.invoiceNumber === 'string') {
    normalized.invoiceNumber = data.invoiceNumber;
  }
  
  if (data.vendorName && typeof data.vendorName === 'string') {
    normalized.vendorName = data.vendorName;
  }
  
  if (data.issueDate && typeof data.issueDate === 'string') {
    normalized.issueDate = data.issueDate;
  }
  
  if (data.dueDate && typeof data.dueDate === 'string') {
    normalized.dueDate = data.dueDate;
  }
  
  if (data.amount !== undefined) {
    if (typeof data.amount === 'number') {
      normalized.amount = data.amount;
    } else if (typeof data.amount === 'string') {
      const parsedAmount = parseFloat(data.amount.replace(/[^\d.-]/g, ''));
      if (!isNaN(parsedAmount)) {
        normalized.amount = parsedAmount;
      }
    }
  }
  
  if (data.currency && typeof data.currency === 'string') {
    normalized.currency = data.currency;
  }
  
  // Handle bank details if present as a nested object
  if (data.bankDetails && typeof data.bankDetails === 'object' && data.bankDetails !== null) {
    normalized.bankDetails = {};
    
    const bankDetails = data.bankDetails as Record<string, unknown>;
    
    if (bankDetails.accountNumber && typeof bankDetails.accountNumber === 'string') {
      normalized.bankDetails.accountNumber = bankDetails.accountNumber;
    }
    
    if (bankDetails.bankName && typeof bankDetails.bankName === 'string') {
      normalized.bankDetails.bankName = bankDetails.bankName;
    }
    
    if (bankDetails.swiftCode && typeof bankDetails.swiftCode === 'string') {
      normalized.bankDetails.swiftCode = bankDetails.swiftCode;
    }
    
    if (bankDetails.iban && typeof bankDetails.iban === 'string') {
      normalized.bankDetails.iban = bankDetails.iban;
    }
    
    if (bankDetails.routingNumber && typeof bankDetails.routingNumber === 'string') {
      normalized.bankDetails.routingNumber = bankDetails.routingNumber;
    }
  }
  
  // Handle payment details if present as a nested object
  if (data.paymentDetails && typeof data.paymentDetails === 'object' && data.paymentDetails !== null) {
    normalized.paymentDetails = {};
    
    const paymentDetails = data.paymentDetails as Record<string, unknown>;
    
    if (paymentDetails.method && typeof paymentDetails.method === 'string') {
      normalized.paymentDetails.method = paymentDetails.method;
    }
    
    if (paymentDetails.terms && typeof paymentDetails.terms === 'string') {
      normalized.paymentDetails.terms = paymentDetails.terms;
    }
    
    if (paymentDetails.reference && typeof paymentDetails.reference === 'string') {
      normalized.paymentDetails.reference = paymentDetails.reference;
    }
    
    if (paymentDetails.dueInDays !== undefined) {
      if (typeof paymentDetails.dueInDays === 'number') {
        normalized.paymentDetails.dueInDays = paymentDetails.dueInDays;
      } else if (typeof paymentDetails.dueInDays === 'string') {
        const parsedDays = parseInt(paymentDetails.dueInDays);
        if (!isNaN(parsedDays)) {
          normalized.paymentDetails.dueInDays = parsedDays;
        }
      }
    }
  }
  
  // Handle line items - ensure each field is properly typed
  if (data.items && Array.isArray(data.items)) {
    normalized.items = await Promise.all(data.items.map(async (item) => {
      if (typeof item === 'object' && item !== null) {
        return await normalizeLineItem(item as Record<string, unknown>);
      }
      // Default item if not an object
      return {
        description: 'Unknown item',
        quantity: 1,
        unitPrice: 0,
        totalPrice: 0
      };
    }));
  }
  
  // Handle invoice type
  if (data.invoiceType && typeof data.invoiceType === 'string') {
    // Only assign valid invoice types
    if (data.invoiceType === 'PURCHASE' || data.invoiceType === 'PAYMENT') {
      normalized.invoiceType = data.invoiceType as InvoiceType;
    }
  }
  
  // Process tax field
  if (data.tax !== undefined) {
    if (typeof data.tax === 'number') {
      normalized.tax = data.tax;
    } else if (typeof data.tax === 'string') {
      const parsedTax = parseFloat(data.tax.replace(/[^\d.-]/g, ''));
      if (!isNaN(parsedTax)) {
        normalized.tax = parsedTax;
      }
    }
  }
  
  // Process notes field
  if (data.notes && typeof data.notes === 'string') {
    normalized.notes = data.notes;
  }
  
  // Process language field
  if (data.language && typeof data.language === 'string') {
    normalized.language = data.language;
  }
  
  // Process confidence field
  if (data.confidence !== undefined) {
    if (typeof data.confidence === 'number') {
      normalized.confidence = data.confidence;
    } else if (typeof data.confidence === 'string') {
      const parsedConfidence = parseFloat(data.confidence);
      if (!isNaN(parsedConfidence)) {
        normalized.confidence = parsedConfidence;
      }
    }
  }
  
  // Handle customFields object
  if (data.customFields && typeof data.customFields === 'object' && data.customFields !== null) {
    normalized.customFields = {};
    
    // Process each custom field
    Object.entries(data.customFields as Record<string, unknown>).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        if (typeof normalized.customFields === 'object') {
          // Handle nested objects in customFields
          if (typeof value === 'object' && value !== null) {
            // @ts-ignore - Complex nested type compatibility
            normalized.customFields![key] = {};
            Object.entries(value as Record<string, unknown>).forEach(([nestedKey, nestedValue]) => {
              if (nestedValue !== null && nestedValue !== undefined) {
                // @ts-ignore - Complex nested type compatibility
                normalized.customFields![key][nestedKey] = nestedValue;
              }
            });
          } else {
            // @ts-ignore - Complex type compatibility
            normalized.customFields[key] = value;
          }
        }
      }
    });
  }
  
  Object.entries(data).forEach(([key, value]) => {
    // Skip fields we've already processed
    if ([
      'invoiceNumber', 'vendorName', 'issueDate', 'dueDate', 'amount', 
      'currency', 'items', 'invoiceType', 'tax', 'notes', 'language', 
      'confidence', 'customFields', 'bankDetails', 'paymentDetails'
    ].includes(key)) {
      return;
    }
    
    // Add the field to the normalized data
    if (value !== null && value !== undefined) {
      // If it's an object, handle it specially to preserve structure
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        // @ts-ignore - Complex nested type compatibility
        normalized[key] = {};
        Object.entries(value as Record<string, unknown>).forEach(([nestedKey, nestedValue]) => {
          if (nestedValue !== null && nestedValue !== undefined) {
            // @ts-ignore - Complex nested type compatibility
            normalized[key][nestedKey] = nestedValue;
          }
        });
      } else {
        // @ts-ignore - Complex type compatibility
        normalized[key] = value;
      }
    }
  });
  
  return normalized;
}

/**
 * Normalize line item to ensure all numeric fields are actual numbers
 */
async function normalizeLineItem(item: Record<string, unknown>): Promise<InvoiceLineItem> {
  const normalizedItem: InvoiceLineItem = {
    name: typeof item.name === 'string' ? item.name : undefined,
    description: typeof item.description === 'string' ? item.description : 'Unknown item',
    quantity: typeof item.quantity === 'number' ? item.quantity : 
              typeof item.quantity === 'string' ? parseFloat(item.quantity) || 1 : 1,
    unitPrice: typeof item.unitPrice === 'number' ? item.unitPrice : 
              typeof item.unitPrice === 'string' ? parseFloat(item.unitPrice) || 0 : 0,
    totalPrice: typeof item.totalPrice === 'number' ? item.totalPrice : 
              typeof item.totalPrice === 'string' ? parseFloat(item.totalPrice) || 0 : 0
  };
  
  // Handle optional standard fields
  if (item.taxRate !== undefined) {
    normalizedItem.taxRate = typeof item.taxRate === 'number' ? item.taxRate : 
                            typeof item.taxRate === 'string' ? parseFloat(item.taxRate) : undefined;
  }
  
  if (item.taxAmount !== undefined) {
    normalizedItem.taxAmount = typeof item.taxAmount === 'number' ? item.taxAmount : 
                              typeof item.taxAmount === 'string' ? parseFloat(item.taxAmount) : undefined;
  }
  
  if (item.discount !== undefined) {
    normalizedItem.discount = typeof item.discount === 'number' ? item.discount : 
                             typeof item.discount === 'string' ? parseFloat(item.discount) : undefined;
  }
  
  if (item.productSku !== undefined && typeof item.productSku === 'string') {
    normalizedItem.productSku = item.productSku;
  }
  
  if (item.notes !== undefined && typeof item.notes === 'string') {
    normalizedItem.notes = item.notes;
  }
  
  // Handle attributes object for dynamic line item properties
  if (item.attributes && typeof item.attributes === 'object' && item.attributes !== null) {
    normalizedItem.attributes = {};
    
    // Process each attribute
    Object.entries(item.attributes as Record<string, unknown>).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        if (typeof normalizedItem.attributes === 'object') {
          // Handle nested objects in attributes
          if (typeof value === 'object' && value !== null) {
            // @ts-ignore - Complex nested type compatibility
            normalizedItem.attributes[key] = {};
            Object.entries(value as Record<string, unknown>).forEach(([nestedKey, nestedValue]) => {
              if (nestedValue !== null && nestedValue !== undefined) {
                // @ts-ignore - Complex nested type compatibility
                normalizedItem.attributes![key][nestedKey] = nestedValue;
              }
            });
          } else {
            // @ts-ignore - Complex type compatibility
            normalizedItem.attributes[key] = value;
          }
        }
      }
    });
  }
  
  // Process any other fields not explicitly handled above
  Object.entries(item).forEach(([key, value]) => {
    // Skip fields we've already processed
    if ([
      'name', 'description', 'quantity', 'unitPrice', 'totalPrice', 'taxRate', 
      'taxAmount', 'discount', 'productSku', 'notes', 'attributes'
    ].includes(key)) {
      return;
    }
    
    // Add the field to the normalized item
    if (value !== null && value !== undefined) {
      // @ts-ignore - Complex type compatibility
      normalizedItem[key] = value;
    }
  });
  
  return normalizedItem;
}
