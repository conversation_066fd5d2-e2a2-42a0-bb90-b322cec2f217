"use client";

import { startTransition, useMemo, useOptimistic, useState } from "react";
import { motion } from "motion/react";

import { saveChatModelAsCookie } from "@/app/dashboard/chat/actions";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { chatModels } from "@/lib/ai/models";
import { cn } from "@/lib/utils";

import { CheckCircleFillIcon, ChevronDownIcon } from "./icons";
import { Sparkles } from "lucide-react";

export function ModelSelector({
  selectedModelId,
  className,
}: {
  selectedModelId: string;
} & React.ComponentProps<typeof Button>) {
  const [open, setOpen] = useState(false);
  const [optimisticModelId, setOptimisticModelId] =
    useOptimistic(selectedModelId);

  const selectedChatModel = useMemo(
    () => chatModels.find((chatModel) => chatModel.id === optimisticModelId),
    [optimisticModelId]
  );

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger
        asChild
        className={cn(
          "w-fit data-[state=open]:bg-accent/20 data-[state=open]:text-accent-foreground data-[state=open]:border-primary/50 data-[state=open]:shadow-md",
          className
        )}
      >
        <Button
          variant="outline"
          className="md:px-4 md:h-[38px] rounded-full border-border/60 bg-background/80 hover:bg-background/90 hover:border-primary/40 hover:shadow-sm transition-all duration-200 backdrop-blur-md"
        >
          <Sparkles size={16} className="mr-2 text-primary" />
          <span className="font-medium">{selectedChatModel?.name}</span>
          <ChevronDownIcon />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="start"
        className="min-w-[320px] rounded-xl p-2 border-border/60 shadow-lg bg-popover/95 backdrop-blur-md"
      >
        {chatModels.map((chatModel) => {
          const { id } = chatModel;
          const isActive = id === optimisticModelId;

          return (
            <DropdownMenuItem
              key={id}
              onSelect={() => {
                setOpen(false);

                startTransition(() => {
                  setOptimisticModelId(id);
                  saveChatModelAsCookie(id);
                });
              }}
              className="gap-4 group/item flex flex-row justify-between items-center rounded-lg p-3.5 hover:bg-accent/20 transition-all duration-200"
              data-active={isActive}
            >
              <div className="flex flex-col gap-1.5 items-start">
                <div className="font-medium flex items-center">
                  {isActive && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="mr-1.5 text-primary"
                    >
                      <Sparkles size={14} />
                    </motion.div>
                  )}
                  {chatModel.name}
                </div>
                <div className="text-xs text-muted-foreground">
                  {chatModel.description}
                </div>
              </div>

              <div className="text-primary opacity-0 group-data-[active=true]/item:opacity-100 transition-opacity duration-200">
                <motion.div
                  initial={{ scale: 0.5, opacity: 0 }}
                  animate={isActive ? { scale: 1, opacity: 1 } : { scale: 0.5, opacity: 0 }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                  <CheckCircleFillIcon />
                </motion.div>
              </div>
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
