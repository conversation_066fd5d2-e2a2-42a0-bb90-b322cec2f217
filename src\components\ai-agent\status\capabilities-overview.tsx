'use client';

import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { FileText } from 'lucide-react';

export function CapabilitiesOverview() {
  const capabilities = [
    {
      title: '🧠 Smart Conversations',
      description: 'Context-aware chat with memory and learning'
    },
    {
      title: '📄 Document Generation',
      description: 'PDF invoices, Excel reports, custom documents'
    },
    {
      title: '🎮 System Control',
      description: 'Navigate pages, execute actions, manage features'
    },
    {
      title: '🔮 Predictive Analytics',
      description: 'Business insights and proactive suggestions'
    },
    {
      title: '💾 Persistent Memory',
      description: 'Learns patterns and remembers preferences'
    },
    {
      title: '⚡ Real-time Context',
      description: 'Live business data and page awareness'
    }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          AI Capabilities
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {capabilities.map((capability, index) => (
            <div key={index} className="p-4 border rounded-lg">
              <h4 className="font-semibold mb-2">{capability.title}</h4>
              <p className="text-sm text-gray-600">{capability.description}</p>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}