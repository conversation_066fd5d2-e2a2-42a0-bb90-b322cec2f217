'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { formatCurrency } from '@/lib/utils';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

// Unified LineItem interface that combines both approaches
export interface UnifiedLineItem {
  id?: string;
  itemCode?: string;
  description?: string;
  quantity?: string | number;
  unit?: string;
  unitPrice?: string | number;
  totalPrice?: string | number;
  amount?: string | number;
  taxRate?: string | number | null;
  taxAmount?: string | number | null;
  discount?: string | number | null;
  productSku?: string | null;
  notes?: string | null;
  attributes?: Record<string, unknown>;
  [key: string]: unknown;
}

interface FinancialsData {
  subtotal?: string;
  tax?: string;
  shipping?: string;
  discount?: string;
  total?: string;
}

interface InvoiceLineItemsTableProps {
  lineItems: UnifiedLineItem[];
  financials?: FinancialsData | Record<string, string | null>;
}

// Helper function to safely convert to number for display
function toNumber(value: string | number | undefined | null): number | null {
  if (value === null || value === undefined || value === '') return null;
  const num = typeof value === 'number' ? value : parseFloat(String(value));
  return isNaN(num) ? null : num;
}

// Helper function to safely convert to string for display
function toString(value: string | number | undefined | null): string {
  if (value === null || value === undefined) return 'N/A';
  return String(value);
}

export function InvoiceLineItemsTable({ lineItems, financials }: InvoiceLineItemsTableProps) {
  // Check if we have any tax rate data to show the column
  const hasTaxRates = lineItems.some(
    (item) => item.taxRate !== null && item.taxRate !== undefined && item.taxRate !== ''
  );
  
  // Check if we have any discount data to show the column
  const hasDiscounts = lineItems.some(
    (item) => item.discount !== null && item.discount !== undefined && item.discount !== ''
  );

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">Line Items</CardTitle>
      </CardHeader>
      <CardContent>
        {lineItems.length > 0 ? (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Description</TableHead>
                  <TableHead className="text-right">Quantity</TableHead>
                  <TableHead className="text-right">Unit Price</TableHead>
                  {hasTaxRates && (
                    <TableHead className="text-right">Tax Rate</TableHead>
                  )}
                  {hasDiscounts && (
                    <TableHead className="text-right">Discount</TableHead>
                  )}
                  <TableHead className="text-right">Total</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {lineItems.map((item, index) => {
                  // Use totalPrice, amount, or calculate from quantity * unitPrice
                  const totalValue = item.totalPrice || item.amount;
                  const calculatedTotal = toNumber(item.quantity) && toNumber(item.unitPrice) 
                    ? toNumber(item.quantity)! * toNumber(item.unitPrice)! 
                    : null;
                  const displayTotal = totalValue || calculatedTotal;

                  return (
                    <TableRow key={index}>
                      <TableCell>
                        <div>
                          <span className="font-medium">
                            {item.description || toString(item.itemCode) || 'N/A'}
                          </span>
                          {item.productSku && (
                            <div className="text-xs text-muted-foreground mt-1">
                              SKU: {item.productSku}
                            </div>
                          )}
                          {item.itemCode && item.description && (
                            <div className="text-xs text-muted-foreground mt-1">
                              Code: {item.itemCode}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div>
                          {toString(item.quantity)}
                          {item.unit && (
                            <div className="text-xs text-muted-foreground">
                              {item.unit}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        {toNumber(item.unitPrice) !== null
                          ? formatCurrency(toNumber(item.unitPrice)!)
                          : toString(item.unitPrice)}
                      </TableCell>
                      {hasTaxRates && (
                        <TableCell className="text-right">
                          {item.taxRate !== null && item.taxRate !== undefined && item.taxRate !== ''
                            ? typeof item.taxRate === 'number' || !isNaN(Number(item.taxRate))
                              ? `${(Number(item.taxRate) * (Number(item.taxRate) <= 1 ? 100 : 1)).toFixed(2)}%`
                              : toString(item.taxRate)
                            : '-'}
                        </TableCell>
                      )}
                      {hasDiscounts && (
                        <TableCell className="text-right">
                          {item.discount !== null && item.discount !== undefined && item.discount !== ''
                            ? typeof item.discount === 'number' || !isNaN(Number(item.discount))
                              ? Number(item.discount) <= 1
                                ? `${(Number(item.discount) * 100).toFixed(0)}%`
                                : formatCurrency(Number(item.discount))
                              : toString(item.discount)
                            : '-'}
                        </TableCell>
                      )}
                      <TableCell className="text-right">
                        {displayTotal !== null && displayTotal !== undefined
                          ? typeof displayTotal === 'number' || !isNaN(Number(displayTotal))
                            ? formatCurrency(Number(displayTotal))
                            : toString(displayTotal)
                          : 'N/A'}
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="text-center py-4 text-muted-foreground">
            No line items available
          </div>
        )}

        {financials && (
          <div className="mt-4 flex justify-end">
            <div className="w-full max-w-xs space-y-2">
              {financials.subtotal && (
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Subtotal</span>
                  <span>{financials.subtotal}</span>
                </div>
              )}
              {financials.tax && (
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Tax</span>
                  <span>{financials.tax}</span>
                </div>
              )}
              {financials.shipping && (
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Shipping</span>
                  <span>{financials.shipping}</span>
                </div>
              )}
              {financials.discount && (
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Discount</span>
                  <span>-{financials.discount}</span>
                </div>
              )}
              <Separator />
              <div className="flex justify-between font-medium">
                <span>Total</span>
                <span>{financials.total || 'N/A'}</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}