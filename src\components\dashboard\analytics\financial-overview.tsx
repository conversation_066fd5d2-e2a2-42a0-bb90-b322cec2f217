"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils";
import {
  DollarSign,
  FileText,
  TrendingDown,
  TrendingUp,
  CreditCard,
  Receipt,
  Calculator,
  Wallet,
  Clock,
  Minus,
} from "lucide-react";
import { useEffect, useState } from "react";
import { getFinancialMetrics } from "@/lib/actions/analytics";
import type { FinancialMetrics } from "@/types/analytics";
import type { FilterValues } from "./data-filters";

interface FinancialOverviewProps {
  dateRange: {
    from: Date;
    to: Date;
  };
  filters: FilterValues;
}

export function FinancialOverview({
  dateRange,
  filters,
}: FinancialOverviewProps) {
  const [metrics, setMetrics] = useState<FinancialMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function loadMetrics() {
      setLoading(true);
      setError(null);
      try {
        const data = await getFinancialMetrics(
          dateRange.from,
          dateRange.to,
          filters
        );
        setMetrics(data);
      } catch {
        setError("Failed to load financial data");
      } finally {
        setLoading(false);
      }
    }

    loadMetrics();
  }, [dateRange, filters]);

  // Create comprehensive financial cards with real data
  const allCards = metrics
    ? [
        // Row 1 - Core Financial Metrics
        {
          title: "Total Revenue",
          value: formatCurrency(metrics.totalRevenue),
          description: (
            <div className="flex items-center pt-1">
              {metrics.monthOverMonthRevenue > 0 ? (
                <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
              ) : metrics.monthOverMonthRevenue < 0 ? (
                <TrendingDown className="mr-1 h-3 w-3 text-red-500" />
              ) : (
                <Minus className="mr-1 h-3 w-3 text-gray-500" />
              )}
              <p
                className={`text-xs ${
                  metrics.monthOverMonthRevenue > 0
                    ? "text-green-500"
                    : metrics.monthOverMonthRevenue < 0
                      ? "text-red-500"
                      : "text-gray-500"
                }`}
              >
                {Math.abs(metrics.monthOverMonthRevenue).toFixed(1)}% vs prev
                period
              </p>
            </div>
          ),
          icon: <DollarSign className="h-5 w-5 text-primary" />,
          color: "from-green-500 to-green-600",
          textColor: "text-green-600 dark:text-green-400",
        },
        {
          title: "Total Expenses",
          value: formatCurrency(metrics.totalExpenses),
          description: (
            <div className="flex items-center pt-1">
              {metrics.monthOverMonthExpenses > 0 ? (
                <TrendingUp className="mr-1 h-3 w-3 text-red-500" />
              ) : metrics.monthOverMonthExpenses < 0 ? (
                <TrendingDown className="mr-1 h-3 w-3 text-green-500" />
              ) : (
                <Minus className="mr-1 h-3 w-3 text-gray-500" />
              )}
              <p
                className={`text-xs ${
                  metrics.monthOverMonthExpenses > 0
                    ? "text-red-500"
                    : metrics.monthOverMonthExpenses < 0
                      ? "text-green-500"
                      : "text-gray-500"
                }`}
              >
                {Math.abs(metrics.monthOverMonthExpenses).toFixed(1)}% vs prev
                period
              </p>
            </div>
          ),
          icon: <DollarSign className="h-5 w-5 text-primary" />,
          color: "from-red-500 to-red-600",
          textColor: "text-red-600 dark:text-red-400",
        },
        {
          title: "Net Profit/Loss",
          value: formatCurrency(metrics.netProfitLoss),
          description: (
            <div className="flex items-center pt-1">
              {metrics.monthOverMonthProfit > 0 ? (
                <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
              ) : metrics.monthOverMonthProfit < 0 ? (
                <TrendingDown className="mr-1 h-3 w-3 text-red-500" />
              ) : (
                <Minus className="mr-1 h-3 w-3 text-gray-500" />
              )}
              <p
                className={`text-xs ${
                  metrics.monthOverMonthProfit > 0
                    ? "text-green-500"
                    : metrics.monthOverMonthProfit < 0
                      ? "text-red-500"
                      : "text-gray-500"
                }`}
              >
                {Math.abs(metrics.monthOverMonthProfit).toFixed(1)}% vs prev
                period
              </p>
            </div>
          ),
          icon: <TrendingUp className="h-5 w-5 text-primary" />,
          color:
            metrics.netProfitLoss >= 0
              ? "from-green-500 to-green-600"
              : "from-red-500 to-red-600",
          textColor:
            metrics.netProfitLoss >= 0
              ? "text-green-600 dark:text-green-400"
              : "text-red-600 dark:text-red-400",
        },
        {
          title: "Gross Margin",
          value: `${metrics.grossMargin.toFixed(1)}%`,
          description: "Profit margin on sales",
          icon: <TrendingUp className="h-5 w-5 text-primary" />,
          color: "from-blue-500 to-blue-600",
          textColor: "text-blue-600 dark:text-blue-400",
        },
        {
          title: "Total Invoices",
          value: metrics.totalInvoices.toString(),
          description: `${metrics.salesInvoiceCount} sales, ${metrics.purchaseInvoiceCount} purchases`,
          icon: <FileText className="h-5 w-5 text-primary" />,
          color: "from-purple-500 to-purple-600",
          textColor: "text-purple-600 dark:text-purple-400",
        },

        // Row 2 - Accounts & Cash Flow
        {
          title: "Accounts Receivable",
          value: formatCurrency(metrics.accountsReceivable),
          description: "Money owed to you",
          icon: <CreditCard className="h-5 w-5 text-primary" />,
          color: "from-emerald-500 to-emerald-600",
          textColor: "text-emerald-600 dark:text-emerald-400",
        },
        {
          title: "Accounts Payable",
          value: formatCurrency(metrics.accountsPayable),
          description: "Money you owe",
          icon: <Receipt className="h-5 w-5 text-primary" />,
          color: "from-orange-500 to-orange-600",
          textColor: "text-orange-600 dark:text-orange-400",
        },
        {
          title: "Net Cash Flow",
          value: formatCurrency(metrics.cashFlow),
          description: "Cash in - Cash out",
          icon: <Wallet className="h-5 w-5 text-primary" />,
          color:
            metrics.cashFlow >= 0
              ? "from-green-500 to-green-600"
              : "from-red-500 to-red-600",
          textColor:
            metrics.cashFlow >= 0
              ? "text-green-600 dark:text-green-400"
              : "text-red-600 dark:text-red-400",
        },
        {
          title: "Avg Payment Days",
          value: `${Math.round(metrics.averageDaysToPayment)} days`,
          description: "Average payment time",
          icon: <Clock className="h-5 w-5 text-primary" />,
          color: "from-indigo-500 to-indigo-600",
          textColor: "text-indigo-600 dark:text-indigo-400",
        },
        {
          title: "Tax Position",
          value: formatCurrency(metrics.netTaxPosition),
          description:
            metrics.netTaxPosition >= 0 ? "Tax payable" : "Tax claimable",
          icon: <Calculator className="h-5 w-5 text-primary" />,
          color:
            metrics.netTaxPosition >= 0
              ? "from-amber-500 to-amber-600"
              : "from-green-500 to-green-600",
          textColor:
            metrics.netTaxPosition >= 0
              ? "text-amber-600 dark:text-amber-400"
              : "text-green-600 dark:text-green-400",
        },
      ]
    : [];

  // Use only the first 5 cards for the default view
  const displayCards = allCards.slice(0, 5);

  if (error) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        {Array.from({ length: 5 }).map((_, i) => (
          <Card
            key={i}
            className="border border-destructive/20 bg-card/50 backdrop-blur-sm shadow-lg"
          >
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-destructive">
                Error
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm text-destructive/80">
                Failed to load data
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (loading || !metrics) {
    return (
      <div className="space-y-4">
        {/* First row loading state */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
          {allCards.slice(0, 5).map((card, index) => (
            <Card
              key={index}
              className="overflow-hidden border border-primary/10 bg-card/50 backdrop-blur-sm shadow-lg"
            >
              <div
                className={`absolute inset-0 bg-gradient-to-br ${card.color} opacity-5 rounded-lg`}
              ></div>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {card.title}
                </CardTitle>
                <div className="rounded-full bg-primary/10 p-2">
                  {card.icon}
                </div>
              </CardHeader>
              <CardContent>
                <div className="h-6 w-20 animate-pulse rounded bg-primary/20"></div>
                <div className="text-xs text-muted-foreground mt-2">
                  {typeof card.description === "string"
                    ? card.description
                    : "Loading..."}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Second row loading state */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
          {allCards.slice(5).map((card, index) => (
            <Card
              key={index + 5}
              className="overflow-hidden border border-primary/10 bg-card/50 backdrop-blur-sm shadow-lg"
            >
              <div
                className={`absolute inset-0 bg-gradient-to-br ${card.color} opacity-5 rounded-lg`}
              ></div>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {card.title}
                </CardTitle>
                <div className="rounded-full bg-primary/10 p-2">
                  {card.icon}
                </div>
              </CardHeader>
              <CardContent>
                <div className="h-6 w-20 animate-pulse rounded bg-primary/20"></div>
                <div className="text-xs text-muted-foreground mt-2">
                  {typeof card.description === "string"
                    ? card.description
                    : "Loading..."}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* First row - Core Financial Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        {displayCards.map((card, index) => (
          <Card
            key={index}
            className="overflow-hidden border border-primary/10 bg-card/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <div
              className={`absolute inset-0 bg-gradient-to-br ${card.color} opacity-5 rounded-lg`}
            ></div>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {card.title}
              </CardTitle>
              <div className="rounded-full bg-primary/10 p-2">{card.icon}</div>
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${card.textColor}`}>
                {card.value}
              </div>
              <div className="text-xs text-muted-foreground">
                {card.description}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Second row - Accounts & Cash Flow Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        {allCards.slice(5).map((card, index) => (
          <Card
            key={index + 5}
            className="overflow-hidden border border-primary/10 bg-card/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <div
              className={`absolute inset-0 bg-gradient-to-br ${card.color} opacity-5 rounded-lg`}
            ></div>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {card.title}
              </CardTitle>
              <div className="rounded-full bg-primary/10 p-2">{card.icon}</div>
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${card.textColor}`}>
                {card.value}
              </div>
              <div className="text-xs text-muted-foreground">
                {card.description}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}