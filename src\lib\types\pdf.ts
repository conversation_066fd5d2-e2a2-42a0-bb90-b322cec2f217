// PDF-related types and interfaces
import type { Prisma } from "@prisma/client"

export type JsonValue = Prisma.JsonValue

export interface ExtractedData {
  notes?: string
  vendor?: {
    address?: string
    email?: string
    phone?: string
  }
  customer?: {
    name?: string
    address?: string
  }
  financials?: {
    subtotal?: string
    tax?: string
    shipping?: string
    discount?: string
    total?: string
  }
  termsAndConditions?: string
  meta?: {
    language?: string
    languageName?: string
    confidence?: {
      overall?: number
    }
  }
  [key: string]: unknown
}

export interface LineItem {
  description?: string
  quantity?: number
  unitPrice?: number
  totalPrice?: number
  taxRate?: number | null
  taxAmount?: number | null
  discount?: number | null
  productSku?: string | null
  notes?: string | null
  attributes?: Record<string, unknown>
  [key: string]: unknown
}

export interface InvoiceWithExtras {
  id: string
  invoiceNumber?: string | null
  status?: string
  amount?: number | null
  currency?: string | null
  issueDate?: Date | null
  dueDate?: Date | null
  vendorName?: string | null
  notes?: string | null
  createdAt: Date
  updatedAt: Date
  category?: { name: string; color?: string } | null
  categoryId?: string | null
  extractedData?: JsonValue
  lineItems?: LineItem[]
}

export interface RenderTextOptions {
  align?: "left" | "right" | "center"
  bold?: boolean
} 