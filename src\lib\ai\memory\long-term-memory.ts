import db from '@/db/db';
import { CacheEngine } from '../cache-engine';

// Enhanced memory interfaces with system control
interface FinancialBehavior {
  spendingPatterns: SpendingPattern[];
  paymentBehavior: PaymentPattern[];
  budgetingHabits: BudgetingPattern[];
  investmentTrends: InvestmentPattern[];
  riskProfile: 'conservative' | 'moderate' | 'aggressive';
}

interface SpendingPattern {
  category: string;
  averageAmount: number;
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  trend: 'increasing' | 'stable' | 'decreasing';
  seasonality?: string[];
}

interface PaymentPattern {
  method: string;
  preference: number; // 0-1 preference score
  averageDelay: number; // days
  onTimeRate: number; // percentage
}

interface BudgetingPattern {
  category: string;
  budgetLimit: number;
  actualSpending: number;
  variance: number;
  adherenceRate: number;
}

interface InvestmentPattern {
  type: string;
  amount: number;
  returnRate: number;
  riskLevel: 'low' | 'medium' | 'high';
}

interface CommunicationProfile {
  style: 'formal' | 'casual' | 'technical';
  preferredLength: 'brief' | 'detailed' | 'comprehensive';
  responseTime: 'immediate' | 'considered' | 'delayed';
  informationDensity: 'high' | 'medium' | 'low';
  visualPreference: 'charts' | 'tables' | 'text' | 'mixed';
  languageComplexity: 'simple' | 'business' | 'technical';
}

interface UserPreferences {
  currency: string;
  dateFormat: string;
  timezone: string;
  language: string;
  theme: 'light' | 'dark' | 'auto';
  notifications: NotificationPreferences;
  privacy: PrivacyPreferences;
  accessibility: AccessibilityPreferences;
}

interface NotificationPreferences {
  email: boolean;
  push: boolean;
  sms: boolean;
  frequency: 'immediate' | 'daily' | 'weekly';
  types: string[];
}

interface PrivacyPreferences {
  dataSharing: boolean;
  analytics: boolean;
  marketing: boolean;
  thirdParty: boolean;
}

interface AccessibilityPreferences {
  screenReader: boolean;
  highContrast: boolean;
  largeText: boolean;
  reducedMotion: boolean;
}

interface BusinessGoals {
  type: 'revenue' | 'cost-reduction' | 'efficiency' | 'growth';
  target: number;
  timeframe: string;
  priority: 'high' | 'medium' | 'low';
  progress: number; // 0-100 percentage
  milestones: Milestone[];
}

interface Milestone {
  title: string;
  target: number;
  deadline: Date;
  completed: boolean;
  completedDate?: Date;
}
interface FinancialBehavior {
  spendingPatterns: SpendingPattern[];
  paymentBehavior: PaymentPattern[];
  budgetingHabits: BudgetingPattern[];
  investmentTrends: InvestmentPattern[];
  riskProfile: 'conservative' | 'moderate' | 'aggressive';
}

interface SpendingPattern {
  category: string;
  averageAmount: number;
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  trend: 'increasing' | 'stable' | 'decreasing';
  seasonality?: string[];
}

interface PaymentPattern {
  method: string;
  preference: number; // 0-1 preference score
  averageDelay: number; // days
  onTimeRate: number; // percentage
}

interface BudgetingPattern {
  category: string;
  budgetLimit: number;
  actualSpending: number;
  variance: number;
  adherenceRate: number;
}

interface InvestmentPattern {
  type: string;
  amount: number;
  returnRate: number;
  riskLevel: 'low' | 'medium' | 'high';
}

interface CommunicationProfile {
  style: 'formal' | 'casual' | 'technical';
  preferredLength: 'brief' | 'detailed' | 'comprehensive';
  responseTime: 'immediate' | 'considered' | 'delayed';
  informationDensity: 'high' | 'medium' | 'low';
  visualPreference: 'charts' | 'tables' | 'text' | 'mixed';
  languageComplexity: 'simple' | 'business' | 'technical';
}

interface UserPreferences {
  currency: string;
  dateFormat: string;
  timezone: string;
  language: string;
  theme: 'light' | 'dark' | 'auto';
  notifications: NotificationPreferences;
  privacy: PrivacyPreferences;
  accessibility: AccessibilityPreferences;
}

interface NotificationPreferences {
  email: boolean;
  push: boolean;
  sms: boolean;
  frequency: 'immediate' | 'daily' | 'weekly';
  types: string[];
}

interface PrivacyPreferences {
  dataSharing: boolean;
  analytics: boolean;
  marketing: boolean;
  thirdParty: boolean;
}

interface AccessibilityPreferences {
  screenReader: boolean;
  highContrast: boolean;
  largeText: boolean;
  reducedMotion: boolean;
}

interface BusinessGoals {
  type: 'revenue' | 'cost-reduction' | 'efficiency' | 'growth';
  target: number;
  timeframe: string;
  priority: 'high' | 'medium' | 'low';
  progress: number; // 0-100 percentage
  milestones: Milestone[];
}

interface Milestone {
  title: string;
  target: number;
  deadline: Date;
  completed: boolean;
  completedDate?: Date;
}

interface UserHistory {
  businessPatterns: BusinessPattern[];
  financialBehavior: FinancialBehavior[];
  communicationStyle: CommunicationProfile;
  preferences: UserPreferences;
  goals: BusinessGoals[];
  pageUsage: PageUsagePattern[];
  featureUsage: FeatureUsagePattern[];
  workflowPatterns: WorkflowPattern[];
}

interface BusinessPattern {
  type: 'invoicing' | 'reporting' | 'analytics' | 'payments';
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  timing: { hour: number; dayOfWeek?: number; dayOfMonth?: number };
  context: Record<string, any>;
  confidence: number;
}

interface PageUsagePattern {
  page: string;
  visitCount: number;
  avgTimeSpent: number;
  commonActions: string[];
  exitPatterns: string[];
  preferredFeatures: string[];
  lastVisited: Date;
}

interface FeatureUsagePattern {
  feature: string;
  usageCount: number;
  successRate: number;
  commonSettings: Record<string, any>;
  userSatisfaction: number;
  lastUsed: Date;
}

interface WorkflowPattern {
  name: string;
  steps: WorkflowStep[];
  frequency: number;
  successRate: number;
  avgDuration: number;
  commonVariations: WorkflowStep[][];
}

interface WorkflowStep {
  page: string;
  action: string;
  data?: Record<string, any>;
  duration: number;
}

interface ConversationTopic {
  topic: string;
  frequency: number;
  lastMentioned: Date;
  sentiment: 'positive' | 'neutral' | 'negative';
  context: string[];
  resolution: 'resolved' | 'ongoing' | 'abandoned';
}

interface UserDecision {
  decision: string;
  context: string;
  options: string[];
  chosen: string;
  reasoning: string;
  outcome: 'successful' | 'failed' | 'pending';
  timestamp: Date;
}

interface UserFeedback {
  type: 'positive' | 'negative' | 'suggestion' | 'bug';
  content: string;
  feature: string;
  rating?: number; // 1-5 scale
  actionTaken: string;
  resolved: boolean;
  timestamp: Date;
}

interface AILearning {
  learningType: 'pattern' | 'preference' | 'correction' | 'optimization';
  description: string;
  confidence: number; // 0-1
  applicability: string[]; // contexts where this learning applies
  validatedBy: 'user-feedback' | 'outcome-success' | 'pattern-match';
  timestamp: Date;
}

interface IndustryKnowledge {
  sector: string;
  commonTerms: string[];
  typicalInvoiceFlow: string[];
  seasonalPatterns: string[];
  regulatoryRequirements: string[];
  benchmarks: Record<string, number>;
}

interface VendorRelationships {
  vendorId: string;
  vendorName: string;
  relationship: 'new' | 'regular' | 'preferred' | 'problematic';
  paymentTerms: string;
  averageAmount: number;
  paymentHistory: PaymentHistoryItem[];
  communicationPreference: string;
  reliability: number; // 0-1 score
}

interface PaymentHistoryItem {
  invoiceId: string;
  amount: number;
  dueDate: Date;
  paidDate?: Date;
  daysLate: number;
  status: 'paid' | 'overdue' | 'disputed';
}

interface FinancialPattern {
  pattern: string;
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  amount: number;
  variance: number;
  predictability: number; // 0-1 score
  lastOccurrence: Date;
  nextPredicted?: Date;
}

interface SeasonalTrend {
  trend: string;
  season: 'spring' | 'summer' | 'fall' | 'winter' | 'month-end' | 'year-end';
  impact: 'increase' | 'decrease' | 'spike' | 'dip';
  magnitude: number; // percentage change
  confidence: number; // 0-1
  historicalData: Array<{ period: string; value: number }>;
}

interface ConversationTopic {
  topic: string;
  frequency: number;
  lastMentioned: Date;
  sentiment: 'positive' | 'neutral' | 'negative';
  context: string[];
  resolution: 'resolved' | 'ongoing' | 'abandoned';
}

interface UserDecision {
  decision: string;
  context: string;
  options: string[];
  chosen: string;
  reasoning: string;
  outcome: 'successful' | 'failed' | 'pending';
  timestamp: Date;
}

interface UserFeedback {
  type: 'positive' | 'negative' | 'suggestion' | 'bug';
  content: string;
  feature: string;
  rating?: number; // 1-5 scale
  actionTaken: string;
  resolved: boolean;
  timestamp: Date;
}

interface AILearning {
  learningType: 'pattern' | 'preference' | 'correction' | 'optimization';
  description: string;
  confidence: number; // 0-1
  applicability: string[]; // contexts where this learning applies
  validatedBy: 'user-feedback' | 'outcome-success' | 'pattern-match';
  timestamp: Date;
}

interface PageContext {
  page: string;
  timestamp: Date;
  userIntent: string;
  availableActions: string[];
  pageState: Record<string, any>;
  userActions: string[];
  outcome: 'success' | 'abandoned' | 'error';
}

interface ActionRequest {
  type: 'navigation' | 'feature_control' | 'data_action' | 'ui_automation';
  target: string;
  parameters: Record<string, any>;
  timestamp: Date;
  success: boolean;
  userFeedback?: string;
}

interface BusinessKnowledge {
  industry: IndustryKnowledge;
  vendors: VendorRelationships[];
  financialPatterns: FinancialPattern[];
  seasonalTrends: SeasonalTrend[];
  systemUsage: SystemUsageKnowledge;
}

interface IndustryKnowledge {
  sector: string;
  commonTerms: string[];
  typicalInvoiceFlow: string[];
  seasonalPatterns: string[];
  regulatoryRequirements: string[];
  benchmarks: Record<string, number>;
}

interface VendorRelationships {
  vendorId: string;
  vendorName: string;
  relationship: 'new' | 'regular' | 'preferred' | 'problematic';
  paymentTerms: string;
  averageAmount: number;
  paymentHistory: PaymentHistoryItem[];
  communicationPreference: string;
  reliability: number; // 0-1 score
}

interface PaymentHistoryItem {
  invoiceId: string;
  amount: number;
  dueDate: Date;
  paidDate?: Date;
  daysLate: number;
  status: 'paid' | 'overdue' | 'disputed';
}

interface FinancialPattern {
  pattern: string;
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  amount: number;
  variance: number;
  predictability: number; // 0-1 score
  lastOccurrence: Date;
  nextPredicted?: Date;
}

interface SeasonalTrend {
  trend: string;
  season: 'spring' | 'summer' | 'fall' | 'winter' | 'month-end' | 'year-end';
  impact: 'increase' | 'decrease' | 'spike' | 'dip';
  magnitude: number; // percentage change
  confidence: number; // 0-1
  historicalData: Array<{ period: string; value: number }>;
}

interface SystemUsageKnowledge {
  mostUsedPages: string[];
  mostUsedFeatures: string[];
  commonWorkflows: string[];
  painPoints: string[];
  optimizationOpportunities: string[];
}

/**
 * Advanced Long-term Memory System
 * Stores and analyzes user behavior, business patterns, and system usage
 */
export class LongTermMemory {
  
  /**
   * Get comprehensive user history with system usage patterns
   */
  static async getUserHistory(userId: string): Promise<UserHistory> {
    const cacheKey = CacheEngine.generateKey('user-history', userId);
    
    return CacheEngine.getOrSet(
      cacheKey,
      'user-profile', // 1 hour cache
      async () => {
        const [
          businessPatterns,
          pageUsage,
          featureUsage,
          workflowPatterns,
          userProfile
        ] = await Promise.all([
          this.analyzeBusinessPatterns(userId),
          this.analyzePageUsage(userId),
          this.analyzeFeatureUsage(userId),
          this.analyzeWorkflowPatterns(userId),
          this.getUserProfile(userId)
        ]);

        return {
          businessPatterns,
          financialBehavior: await this.analyzeFinancialBehavior(userId),
          communicationStyle: userProfile.communicationStyle,
          preferences: userProfile.preferences,
          goals: userProfile.goals,
          pageUsage,
          featureUsage,
          workflowPatterns
        };
      }
    );
  }

  /**
   * Analyze business patterns from user data
   */
  private static async analyzeBusinessPatterns(userId: string): Promise<BusinessPattern[]> {
    const patterns: BusinessPattern[] = [];
    
    try {
      // Analyze invoicing patterns
      const invoicePattern = await this.analyzeInvoicingPattern(userId);
      if (invoicePattern) patterns.push(invoicePattern);

      // Analyze reporting patterns
      const reportingPattern = await this.analyzeReportingPattern(userId);
      if (reportingPattern) patterns.push(reportingPattern);

      // Analyze analytics usage patterns
      const analyticsPattern = await this.analyzeAnalyticsPattern(userId);
      if (analyticsPattern) patterns.push(analyticsPattern);

    } catch (error) {
      console.error('Business pattern analysis error:', error);
    }

    return patterns;
  }

  /**
   * Analyze page usage patterns
   */
  private static async analyzePageUsage(userId: string): Promise<PageUsagePattern[]> {
    try {
      // Get page visit data from chat history and user activity
      const chatHistory = await db.chat.findMany({
        where: { userId },
        include: { messages: true },
        orderBy: { createdAt: 'desc' },
        take: 100
      });

      const pagePatterns = new Map<string, PageUsagePattern>();

      // Analyze chat messages for page context
      chatHistory.forEach(chat => {
        chat.messages.forEach(message => {
          const content = typeof message.content === 'string' 
            ? message.content 
            : JSON.stringify(message.content);
          
          // Extract page references from messages
          const pageReferences = this.extractPageReferences(content);
          pageReferences.forEach(page => {
            if (!pagePatterns.has(page)) {
              pagePatterns.set(page, {
                page,
                visitCount: 0,
                avgTimeSpent: 0,
                commonActions: [],
                exitPatterns: [],
                preferredFeatures: [],
                lastVisited: new Date()
              });
            }
            
            const pattern = pagePatterns.get(page)!;
            pattern.visitCount++;
            pattern.lastVisited = message.createdAt;
          });
        });
      });

      return Array.from(pagePatterns.values());
    } catch (error) {
      console.error('Page usage analysis error:', error);
      return [];
    }
  }

  /**
   * Analyze feature usage patterns
   */
  private static async analyzeFeatureUsage(userId: string): Promise<FeatureUsagePattern[]> {
    try {
      const features = new Map<string, FeatureUsagePattern>();

      // Analyze invoice-related features
      const invoiceCount = await db.invoice.count({ where: { userId } });
      if (invoiceCount > 0) {
        features.set('invoice-management', {
          feature: 'invoice-management',
          usageCount: invoiceCount,
          successRate: 0.95,
          commonSettings: {},
          userSatisfaction: 0.9,
          lastUsed: new Date()
        });
      }

      // Analyze report features
      const reportCount = await db.report.count({ where: { userId } });
      if (reportCount > 0) {
        features.set('report-generation', {
          feature: 'report-generation',
          usageCount: reportCount,
          successRate: 0.9,
          commonSettings: {},
          userSatisfaction: 0.85,
          lastUsed: new Date()
        });
      }

      return Array.from(features.values());
    } catch (error) {
      console.error('Feature usage analysis error:', error);
      return [];
    }
  }

  /**
   * Analyze workflow patterns
   */
  private static async analyzeWorkflowPatterns(userId: string): Promise<WorkflowPattern[]> {
    // Simplified workflow analysis - can be enhanced with actual user tracking
    const commonWorkflows: WorkflowPattern[] = [
      {
        name: 'Monthly Reporting',
        steps: [
          { page: '/dashboard', action: 'view_overview', duration: 30 },
          { page: '/analytics', action: 'generate_report', duration: 120 },
          { page: '/reports', action: 'download_pdf', duration: 15 }
        ],
        frequency: 12, // Monthly
        successRate: 0.9,
        avgDuration: 165,
        commonVariations: []
      },
      {
        name: 'Invoice Creation',
        steps: [
          { page: '/invoices', action: 'create_new', duration: 60 },
          { page: '/invoices/new', action: 'fill_details', duration: 180 },
          { page: '/invoices', action: 'send_invoice', duration: 30 }
        ],
        frequency: 50, // Varies by user
        successRate: 0.95,
        avgDuration: 270,
        commonVariations: []
      }
    ];

    return commonWorkflows;
  }

  /**
   * Store user interaction for learning
   */
  static async recordUserInteraction(
    userId: string,
    interaction: {
      page: string;
      action: string;
      context: Record<string, any>;
      outcome: 'success' | 'error' | 'abandoned';
      duration: number;
    }
  ): Promise<void> {
    try {
      // Store in database for persistence
      await db.userInteraction.create({
        data: {
          userId,
          page: interaction.page,
          action: interaction.action,
          context: JSON.stringify(interaction.context),
          outcome: interaction.outcome,
          duration: interaction.duration,
          timestamp: new Date()
        }
      });

      // Clear cache to refresh patterns
      const cacheKey = CacheEngine.generateKey('user-history', userId);
      await CacheEngine.delete(cacheKey);
    } catch (error) {
      console.error('Failed to record user interaction:', error);
    }
  }

  /**
   * Learn from user feedback
   */
  static async recordUserFeedback(
    userId: string,
    feedback: {
      type: 'positive' | 'negative' | 'suggestion';
      context: string;
      message: string;
      page?: string;
      feature?: string;
    }
  ): Promise<void> {
    try {
      await db.userFeedback.create({
        data: {
          userId,
          type: feedback.type,
          context: feedback.context,
          message: feedback.message,
          page: feedback.page,
          feature: feedback.feature,
          timestamp: new Date()
        }
      });
    } catch (error) {
      console.error('Failed to record user feedback:', error);
    }
  }

  // Helper methods
  private static extractPageReferences(content: string): string[] {
    const pages: string[] = [];
    const pageKeywords = {
      'dashboard': ['dashboard', 'overview', 'home'],
      'invoices': ['invoice', 'bill', 'payment'],
      'reports': ['report', 'analytics', 'summary'],
      'settings': ['setting', 'config', 'preference'],
      'analytics': ['analytic', 'chart', 'graph', 'trend']
    };

    Object.entries(pageKeywords).forEach(([page, keywords]) => {
      if (keywords.some(keyword => content.toLowerCase().includes(keyword))) {
        pages.push(page);
      }
    });

    return pages;
  }

  private static async analyzeInvoicingPattern(userId: string): Promise<BusinessPattern | null> {
    const invoices = await db.invoice.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: 50
    });

    if (invoices.length < 3) return null;

    // Analyze timing patterns
    const hours = invoices.map(inv => inv.createdAt.getHours());
    const mostCommonHour = this.getMostFrequent(hours);

    return {
      type: 'invoicing',
      frequency: this.determineFrequency(invoices.map(inv => inv.createdAt)),
      timing: { hour: mostCommonHour },
      context: { avgAmount: invoices.reduce((sum, inv) => sum + (inv.amount || 0), 0) / invoices.length },
      confidence: 0.8
    };
  }

  private static async analyzeReportingPattern(userId: string): Promise<BusinessPattern | null> {
    const reports = await db.report.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: 20
    });

    if (reports.length < 2) return null;

    return {
      type: 'reporting',
      frequency: 'monthly',
      timing: { hour: 10, dayOfMonth: 1 },
      context: { reportTypes: reports.map(r => r.reportType) },
      confidence: 0.7
    };
  }

  private static async analyzeAnalyticsPattern(userId: string): Promise<BusinessPattern | null> {
    // Simplified analytics pattern - would be enhanced with actual analytics usage data
    return {
      type: 'analytics',
      frequency: 'weekly',
      timing: { hour: 9, dayOfWeek: 1 },
      context: {},
      confidence: 0.6
    };
  }

  private static async getUserProfile(userId: string): Promise<any> {
    // Get from existing memory engine
    const { MemoryEngine } = await import('../memory-engine');
    return MemoryEngine.getUserProfile(userId);
  }

  private static async analyzeFinancialBehavior(userId: string): Promise<any[]> {
    // Simplified financial behavior analysis
    return [];
  }

  private static getMostFrequent<T>(arr: T[]): T {
    const frequency = arr.reduce((acc, item) => {
      acc[item as any] = (acc[item as any] || 0) + 1;
      return acc;
    }, {} as Record<any, number>);

    return Object.keys(frequency).reduce((a, b) => 
      frequency[a] > frequency[b] ? a : b
    ) as T;
  }

  private static determineFrequency(dates: Date[]): 'daily' | 'weekly' | 'monthly' | 'quarterly' {
    if (dates.length < 2) return 'monthly';
    
    const intervals = dates.slice(1).map((date, i) => 
      Math.abs(date.getTime() - dates[i].getTime()) / (24 * 60 * 60 * 1000)
    );
    
    const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
    
    if (avgInterval <= 2) return 'daily';
    if (avgInterval <= 10) return 'weekly';
    if (avgInterval <= 40) return 'monthly';
    return 'quarterly';
  }
}
