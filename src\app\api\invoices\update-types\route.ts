import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import db from "@/db/db";

export async function POST() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get all invoices for the user
    const invoices = await db.invoice.findMany({
      where: { userId },
      select: { id: true, amount: true, invoiceType: true },
    });


    // Split invoices into two groups based on amount
    // Higher amounts are more likely to be sales/income
    const sortedInvoices = [...invoices].sort((a, b) => (b.amount || 0) - (a.amount || 0));
    
    // Top half will be SALES, bottom half will be EXPENSE
    const halfLength = Math.ceil(sortedInvoices.length / 2);
    const salesInvoices = sortedInvoices.slice(0, halfLength);
    const expenseInvoices = sortedInvoices.slice(halfLength);


    // Update sales invoices
    for (const invoice of salesInvoices) {
      await db.invoice.update({
        where: { id: invoice.id },
        data: { invoiceType: 'SALES' },
      });
    }

    // Update expense invoices
    for (const invoice of expenseInvoices) {
      await db.invoice.update({
        where: { id: invoice.id },
        data: { invoiceType: 'EXPENSE' },
      });
    }

    return NextResponse.json({
      success: true,
      message: `Updated ${invoices.length} invoices: ${salesInvoices.length} to SALES and ${expenseInvoices.length} to EXPENSE`,
    });
  } catch (error) {
    console.error('Error updating invoice types:', error);
    return NextResponse.json(
      { error: 'Failed to update invoice types' },
      { status: 500 }
    );
  }
}
