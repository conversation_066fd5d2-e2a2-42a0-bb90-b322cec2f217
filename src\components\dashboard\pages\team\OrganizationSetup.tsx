import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Building,
  Mail,
  CheckCircle,
  XCircle,
  AlertCircle,
  Settings,
  ArrowRight,
} from 'lucide-react';
import { useOrganization } from '@clerk/nextjs';
import { toast } from 'sonner';

interface OrganizationSetupProps {
  onSetupComplete?: () => void;
}

interface LocalOrganization {
  id: string;
  name: string;
}

export default function OrganizationSetup({
  onSetupComplete,
}: OrganizationSetupProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [orgName, setOrgName] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [activeStep, setActiveStep] = useState<
    'choose' | 'create' | 'custom'
  >('choose');
  const [localOrganizations, setLocalOrganizations] = useState<LocalOrganization[]>([]);

  const { organization: clerkOrganization, isLoaded: orgLoaded } = useOrganization();

  // Check for both Clerk organization and local organizations
  const hasClerkOrganization = orgLoaded && !!clerkOrganization;
  const hasLocalOrganizations = localOrganizations.length > 0;
  const hasAnyOrganization = hasClerkOrganization || hasLocalOrganizations;

  // Get organization names for display
  const getOrganizationNames = () => {
    const names: string[] = [];
    if (hasClerkOrganization && clerkOrganization?.name) {
      names.push(clerkOrganization.name);
    }
    if (hasLocalOrganizations) {
      names.push(...localOrganizations.map(org => org.name));
    }
    return names;
  };

  // Load all local organizations on component mount and after creation
  const loadLocalOrganizations = async () => {
    try {
      const response = await fetch('/api/organizations/current');
      if (response.ok) {
        const data = await response.json();
        if (data.organizations) {
          setLocalOrganizations(data.organizations);
        }
      }
    } catch (error) {
      console.error('Error loading organizations:', error);
    }
  };

  useEffect(() => {
    loadLocalOrganizations();
  }, []);

  const handleCreateClerkOrg = async () => {
    if (!orgName.trim()) {
      toast.error('Please enter an organization name');
      return;
    }

    setIsCreating(true);
    try {
      const response = await fetch('/api/organizations/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: orgName.trim() }),
      });

      if (response.ok) {
        toast.success('Organization created successfully!');
        setIsOpen(false);
        setOrgName('');
        await loadLocalOrganizations();
        onSetupComplete?.();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to create organization');
      }
    } catch (error) {
      console.error('Error creating organization:', error);
      toast.error('Failed to create organization');
    } finally {
      setIsCreating(false);
    }
  };

  const handleUseCustomSystem = () => {
    setIsOpen(false);
    onSetupComplete?.();
    toast.info('Switched to custom invitation system');
  };

  return (
    <>
      {/* Status Cards */}
      <div className="grid gap-4 md:grid-cols-2 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Organization
            </CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex flex-col space-y-2">
              {hasAnyOrganization ? (
                getOrganizationNames().map((name, idx) => (
                  <div key={idx} className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm text-green-700 dark:text-green-400">{name}</span>
                  </div>
                ))
              ) : (
                <div className="flex items-center space-x-2">
                  <XCircle className="h-4 w-4 text-red-500" />
                  <span className="text-sm text-red-700 dark:text-red-400">Not set up</span>
                </div>
              )}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Required for team management
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Invitation System
            </CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-sm text-green-700 dark:text-green-400">
                Custom system active
              </span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Always available as fallback
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Setup Dialog */}
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" className="w-full">
            <Settings className="mr-2 h-4 w-4" />
            Configure Team Invitations
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Team Invitation Setup</DialogTitle>
            <DialogDescription>
              Choose how you want to handle team member invitations
            </DialogDescription>
          </DialogHeader>

          {activeStep === 'choose' && (
            <div className="space-y-4">
              <div className="grid gap-4">
                <Card
                  className="cursor-pointer hover:bg-accent"
                  onClick={() => setActiveStep('create')}
                >
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Building className="h-5 w-5" />
                        <CardTitle className="text-base">
                          Create Organization
                        </CardTitle>
                      </div>
                      <Badge variant="secondary">Recommended</Badge>
                    </div>
                    <CardDescription>
                      Create an organization for team management
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="text-sm space-y-1 text-muted-foreground">
                      <li>• Team member management</li>
                      <li>• Role-based access control</li>
                      <li>• Invitation system</li>
                      <li>• Organization-wide settings</li>
                    </ul>
                    <div className="mt-3 flex items-center text-sm">
                      <span>Setup required</span>
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </div>
                  </CardContent>
                </Card>

                <Card
                  className="cursor-pointer hover:bg-accent"
                  onClick={() => setActiveStep('custom')}
                >
                  <CardHeader>
                    <div className="flex items-center space-x-2">
                      <Mail className="h-5 w-5" />
                      <CardTitle className="text-base">
                        Custom Email System
                      </CardTitle>
                    </div>
                    <CardDescription>
                      Use the existing custom invitation system
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="text-sm space-y-1 text-muted-foreground">
                      <li>• Full control over email templates</li>
                      <li>• Custom invitation flow</li>
                      <li>• Works with any email provider</li>
                      <li>• No additional setup needed</li>
                    </ul>
                    <div className="mt-3 flex items-center text-sm text-green-600">
                      <CheckCircle className="mr-2 h-4 w-4" />
                      <span>Ready to use</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {activeStep === 'create' && (
            <div className="space-y-4">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Creating an organization will enable team management features
                </AlertDescription>
              </Alert>

              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="orgName">Organization Name</Label>
                  <Input
                    id="orgName"
                    placeholder="My Company"
                    value={orgName}
                    onChange={(e) => setOrgName(e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground">
                    This will be visible to all team members
                  </p>
                </div>
              </div>

              <DialogFooter className="gap-2">
                <Button
                  variant="outline"
                  onClick={() => setActiveStep('choose')}
                >
                  Back
                </Button>
                <Button
                  onClick={handleCreateClerkOrg}
                  disabled={isCreating || !orgName.trim()}
                >
                  {isCreating ? 'Creating...' : 'Create Organization'}
                </Button>
              </DialogFooter>
            </div>
          )}

          {activeStep === 'custom' && (
            <div className="space-y-4">
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  The custom invitation system is ready to use. Team
                  invitations will be sent via email.
                </AlertDescription>
              </Alert>

              <div className="space-y-3">
                <h4 className="font-medium">What you get:</h4>
                <ul className="text-sm space-y-2 text-muted-foreground">
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                    Email invitations with secure tokens
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                    Role-based access control
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                    Invitation management dashboard
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                    7-day invitation expiry
                  </li>
                </ul>
              </div>

              <DialogFooter className="gap-2">
                <Button
                  variant="outline"
                  onClick={() => setActiveStep('choose')}
                >
                  Back
                </Button>
                <Button onClick={handleUseCustomSystem}>
                  Use Custom System
                </Button>
              </DialogFooter>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
