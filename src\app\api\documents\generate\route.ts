import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUserId } from '@/lib/clerk-helpers';
import { PDFGenerator } from '@/lib/documents/pdf-generator';
import { ExcelGenerator } from '@/lib/documents/excel-generator';
import { PerformanceMonitor } from '@/lib/ai/performance-monitor';
import db from '@/db/db';

interface DocumentRequest {
  type: 'invoice' | 'report' | 'excel' | 'custom';
  format: 'pdf' | 'excel' | 'both';
  data: any;
  options?: {
    template?: string;
    styling?: any;
    includeCharts?: boolean;
    filename?: string;
  };
}

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  let success = false;
  
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body: DocumentRequest = await request.json();
    const { type, format, data, options = {} } = body;

    let pdfBuffer: Buffer | null = null;
    let excelBuffer: Buffer | null = null;
    let filename = options.filename || `document-${Date.now()}`;

    // Generate documents based on type and format
    switch (type) {
      case 'invoice':
        if (format === 'pdf' || format === 'both') {
          pdfBuffer = await generateInvoicePDF(data, options, userId);
          filename = `invoice-${data.invoiceNumber || Date.now()}`;
        }
        break;

      case 'report':
        if (format === 'pdf' || format === 'both') {
          pdfBuffer = await generateReportPDF(data, options);
          filename = `report-${data.title?.replace(/\s+/g, '-') || Date.now()}`;
        }
        if (format === 'excel' || format === 'both') {
          excelBuffer = await generateReportExcel(data, options);
        }
        break;

      case 'excel':
        excelBuffer = await generateExcelDocument(data, options);
        filename = options.filename || `data-export-${Date.now()}`;
        break;

      case 'custom':
        if (format === 'pdf' || format === 'both') {
          pdfBuffer = await generateCustomPDF(data, options);
        }
        if (format === 'excel' || format === 'both') {
          excelBuffer = await generateCustomExcel(data, options);
        }
        break;

      default:
        return NextResponse.json({ error: 'Invalid document type' }, { status: 400 });
    }

    // Prepare response
    const response: any = {
      success: true,
      type,
      format,
      filename,
      generatedAt: new Date().toISOString()
    };

    // Handle single file response
    if (format === 'pdf' && pdfBuffer) {
      success = true;
      return new NextResponse(pdfBuffer, {
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="${filename}.pdf"`,
          'Content-Length': pdfBuffer.length.toString()
        }
      });
    }

    if (format === 'excel' && excelBuffer) {
      success = true;
      return new NextResponse(excelBuffer, {
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'Content-Disposition': `attachment; filename="${filename}.xlsx"`,
          'Content-Length': excelBuffer.length.toString()
        }
      });
    }

    // Handle both formats - return as JSON with base64 encoded files
    if (format === 'both') {
      if (pdfBuffer) {
        response.pdf = {
          data: pdfBuffer.toString('base64'),
          filename: `${filename}.pdf`,
          mimeType: 'application/pdf'
        };
      }
      if (excelBuffer) {
        response.excel = {
          data: excelBuffer.toString('base64'),
          filename: `${filename}.xlsx`,
          mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        };
      }
      success = true;
      return NextResponse.json(response);
    }

    return NextResponse.json({ error: 'No document generated' }, { status: 500 });

  } catch (error) {
    console.error('Document generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate document', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  } finally {
    // Record performance metrics
    const userId = await getCurrentUserId().catch(() => 'unknown');
    PerformanceMonitor.recordMetrics({
      responseTime: Date.now() - startTime,
      tokenUsage: 0, // Not applicable for document generation
      cacheHitRate: 0.5, // Estimate
      modelUsed: 'document-generator',
      success,
      userId: userId || 'unknown',
      queryType: 'document_generation'
    });
  }
}

// Helper functions for different document types
async function generateInvoicePDF(data: any, options: any, userId: string): Promise<Buffer> {
  // Enhance invoice data with user business info
  const user = await db.user.findUnique({
    where: { id: userId },
    select: {
      firstName: true,
      lastName: true,
      email: true
    }
  });

  const enhancedData = {
    ...data,
    businessInfo: {
      name: `${user?.firstName || ''} ${user?.lastName || ''}`.trim() || 'Business',
      email: user?.email || '',
      address: data.businessInfo?.address || 'Business Address',
      ...data.businessInfo
    }
  };

  return PDFGenerator.generateInvoicePDF(enhancedData, options.template || 'modern');
}

async function generateReportPDF(data: any, options: any): Promise<Buffer> {
  return PDFGenerator.generateReportPDF(data, options.includeCharts !== false);
}

async function generateReportExcel(data: any, options: any): Promise<Buffer> {
  // Convert report data to Excel format
  if (data.type === 'financial_dashboard') {
    return ExcelGenerator.generateFinancialDashboard(data.data);
  } else if (data.type === 'analytics') {
    return ExcelGenerator.generateAnalyticsWorkbook(data.data);
  } else {
    // Generic data export
    return ExcelGenerator.generateDataExport(data.data || [], {
      sheetName: data.title || 'Report',
      formatting: 'professional',
      filters: true
    });
  }
}

async function generateExcelDocument(data: any, options: any): Promise<Buffer> {
  if (data.type === 'dashboard') {
    return ExcelGenerator.generateFinancialDashboard(data);
  } else if (data.type === 'analytics') {
    return ExcelGenerator.generateAnalyticsWorkbook(data);
  } else if (data.type === 'export') {
    return ExcelGenerator.generateDataExport(data.records || [], {
      sheetName: options.sheetName || 'Data',
      formatting: options.formatting || 'professional',
      filters: options.filters !== false
    });
  } else {
    return ExcelGenerator.generateCustomExcel(data);
  }
}

async function generateCustomPDF(data: any, options: any): Promise<Buffer> {
  return PDFGenerator.generateCustomPDF(data, options.styling || {});
}

async function generateCustomExcel(data: any, options: any): Promise<Buffer> {
  return ExcelGenerator.generateCustomExcel(data);
}

// GET endpoint for document templates and capabilities
export async function GET(request: NextRequest) {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const capabilities = {
      supportedTypes: ['invoice', 'report', 'excel', 'custom'],
      supportedFormats: ['pdf', 'excel', 'both'],
      invoiceTemplates: ['modern', 'classic', 'minimal'],
      excelFormats: ['basic', 'professional', 'minimal'],
      features: {
        charts: true,
        formatting: true,
        multipleSheets: true,
        customStyling: true,
        businessBranding: true
      },
      limits: {
        maxFileSize: '50MB',
        maxSheets: 10,
        maxRows: 100000
      }
    };

    return NextResponse.json(capabilities);
  } catch (error) {
    console.error('Error getting document capabilities:', error);
    return NextResponse.json({ error: 'Failed to get capabilities' }, { status: 500 });
  }
}
