## Project Overview

*   **Type:** AI-Powered Invoice Management System
*   **Description:** Bilix is an AI-powered invoice management system designed to streamline invoice processing and provide financial analysis. It leverages Next.js for the frontend, Prisma for database interaction (PostgreSQL), Clerk for user authentication, and OpenAI for OCR and AI-driven features.
*   **Primary Goal:** To streamline invoice processing, automate data extraction and categorization, and provide insightful financial analysis with AI assistance.

## Project Structure

### Framework-Specific Routing

*   **Directory Rules:**

    *   `Next.js 15 (App Router)`: Use the `app/[route]/page.tsx` conventions for all routing needs.
    *   Example 1: In Next.js 15, authentication routes follow the pattern `app/auth/[...tab]/page.tsx`.

### Core Directories

*   **Versioned Structure:**

    *   `app/api`: Next.js 15 API routes using Route Handlers.
    *   `src/views`: Reserved for React.js components and views using the composition API in Next.js 15.

### Key Files

*   **Stack-Versioned Patterns:**

    *   `app/dashboard/layout.tsx`: Implements the root layouts for the app, leveraging Next.js 15 features.
    *   `pages/_app.js`: Used only if the Pages Router pattern is applicable, not in App Router.

## Tech Stack Rules

*   **Version Enforcement:**

    *   `next@15`: App Router is required for all routes; avoid using older routing patterns such as `pages/`.

## PRD Compliance

*   **Non-Negotiable:**

    *   "Invoices must be uploaded efficiently and securely using the latest tech stack features for enhanced speed and protection." — This requirement mandates the latest Next.js 15 directory and routing structures.

## App Flow Integration

*   **Stack-Aligned Flow:**

    *   Example: Next.js 15 Auth Flow → `app/auth/login/page.tsx` uses server actions for handling authentication.

## Best Practices

*   **Next.js 15**

    *   Utilize the `app/` directory for structuring all root folders and pages.
    *   Ensure server components are used as needed for performance efficiency, leveraging React Server Components.
    *   Optimize data fetching with the new data-fetching hooks and patterns recommended by Next.js 15.

*   **Prisma**

    *   Use Prisma migrations for database schema changes to maintain consistency across environments.
    *   Secure connection strings and credentials with environment variables.
    *   Regularly backup the database to prevent data loss.

*   **Clerk Authentication**

    *   Implement robust role-based access controls (RBAC) to protect sensitive features.
    *   Use Clerk's middleware to ensure secure authentication sessions.

*   **OpenAI Integration**

    *   Validate input data thoroughly to avoid unexpected behavior.
    *   Implement limitations and error handling for API calls to manage costs and optimize performance.

## Rules

*   Derive folder/file patterns **directly** from `techStackDoc` versions.
*   Ensure the use of the `app/` directory, following the Next.js 15 App Router conventions.
*   Avoid mixing routing patterns from older versions, such as the `pages/` structure.

## Rules Metrics

### Instructions:

*   Create a metrics file in the root of the project called `cursor_metrics.md`.
*   Each time a cursor rule is used as context, update `cursor_metrics.md`.
*   Use the following format for `cursor_metrics.md:` Rules Metrics (h1)

Usage (h2) The number of times rules is used as context

`* rule-name.mdc: 5 * another-rule.mdc: 2 * ...other rules`
