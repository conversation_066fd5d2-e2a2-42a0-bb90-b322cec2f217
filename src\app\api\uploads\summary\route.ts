import { NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import db from "@/db/db";

export async function GET() {
  try {
    const user = await currentUser();
    
    if (!user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const userId = user.id;
    
    // We'll use Invoice attachments as a proxy for documents since
    // there's no dedicated uploads/documents table
    const invoicesWithFiles = await db.invoice.findMany({
      where: {
        userId,
        originalFileUrl: { not: null }
      },
      select: {
        id: true,
        title: true,
        originalFileUrl: true,
        category: {
          select: {
            name: true
          }
        },
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    });
    
    // Get categories with documents
    const categories = await db.category.findMany({
      where: {
        userId,
        invoices: {
          some: {
            originalFileUrl: { not: null }
          }
        }
      },
      select: {
        name: true,
        _count: {
          select: {
            invoices: {
              where: {
                originalFileUrl: { not: null }
              }
            }
          }
        }
      }
    });
    
    // Transform the data to match our expected format
    const categoryMap = categories.reduce((map, cat) => {
      map[cat.name] = cat._count.invoices;
      return map;
    }, {} as Record<string, number>);
    
    // Format documents from invoices
    const recentDocuments = invoicesWithFiles.map(inv => ({
      id: inv.id,
      name: inv.title || `Document-${inv.id}`,
      category: inv.category?.name || "Uncategorized",
      uploadedAt: inv.createdAt
    }));
    
    // Compile the summary
    const documentSummary = {
      totalCount: recentDocuments.length,
      categories: categoryMap,
      recentDocuments
    };

    return NextResponse.json(documentSummary);
  } catch (error) {
    console.error("Error fetching document summary:", error);
    return NextResponse.json(
      { error: "Failed to fetch document summary" },
      { status: 500 }
    );
  }
}
