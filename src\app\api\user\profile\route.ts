import { NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import db from "@/db/db";

export async function GET() {
  try {
    const user = await currentUser();
    
    if (!user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const clerkId = user.id;
    
    // Get user from database using the clerkId
    const dbUser = await db.user.findUnique({
      where: { clerkId },
      include: {
        organizations: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });
    
    if (!dbUser) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }
    
    // Format the user data for the client
    const formattedUser = {
      id: dbUser.id,
      clerkId: dbUser.clerkId,
      firstName: dbUser.firstName,
      lastName: dbUser.lastName,
      email: dbUser.email,
      profileImageUrl: dbUser.profileImageUrl,
      role: dbUser.role,
      lastActive: dbUser.lastActive,
      createdAt: dbUser.createdAt,
      updatedAt: dbUser.updatedAt,
      status: dbUser.status,
      organization: dbUser.organizations.length > 0 ? dbUser.organizations[0] : null
    };

    return NextResponse.json(formattedUser);
  } catch (error) {
    console.error("Error fetching user profile:", error);
    return NextResponse.json(
      { error: "Failed to fetch user profile" },
      { status: 500 }
    );
  }
}
