import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import db from "@/db/db";
import { getReports } from "@/lib/actions/analytics";

export async function GET() {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the user from the database
    const user = await db.user.findUnique({
      where: { clerkId: userId },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Fetch all user reports
    const reports = await getReports();
    
    // Get scheduled reports
    const scheduledReports = await db.scheduledReport.findMany({
      where: {
        userId: user.id,
      },
      include: {
        report: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Get recent report activity - recent created or modified reports
    const recentActivity = await db.report.findMany({
      where: {
        userId: user.id,
      },
      orderBy: {
        updatedAt: "desc",
      },
      take: 5,
    });

    // Count reports by type
    const reportsByType = await db.report.groupBy({
      by: ["reportType"],
      where: {
        userId: user.id,
      },
      _count: true,
    });

    // Format the reports summary
    const reportsSummary = {
      totalReports: reports.length,
      scheduledReports: scheduledReports.length,
      recentActivity,
      reportsByType,
      reports: reports, // Return all reports instead of limiting to 10
    };

    return NextResponse.json(reportsSummary);
  } catch (error) {
    console.error("Error fetching reports summary:", error);
    return NextResponse.json(
      { error: "Failed to fetch reports summary" },
      { status: 500 }
    );
  }
} 