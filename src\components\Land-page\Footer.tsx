import Link from "next/link"

const socialLinks = [
  {
    name: "X/Twitter",
    href: "https://x.com/Billix_io",
    icon: (
      <svg className="size-4 sm:size-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
        <path
          fill="currentColor"
          d="M10.488 14.651L15.25 21h7l-7.858-10.478L20.93 3h-2.65l-5.117 5.886L8.75 3h-7l7.51 10.015L2.32 21h2.65zM16.25 19L5.75 5h2l10.5 14z"
        ></path>
      </svg>
    ),
  },
  {
    name: "LinkedIn",
    href: "https://www.linkedin.com/in/billix-ai-214810361/",
    icon: (
      <svg className="size-4 sm:size-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
        <path
          fill="currentColor"
          d="M19 3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2zm-.5 15.5v-5.3a3.26 3.26 0 0 0-3.26-3.26c-.85 0-1.84.52-2.32 1.3v-1.11h-2.79v8.37h2.79v-4.93c0-.77.62-1.4 1.39-1.4a1.4 1.4 0 0 1 1.4 1.4v4.93zM6.88 8.56a1.68 1.68 0 0 0 1.68-1.68c0-.93-.75-1.69-1.68-1.69a1.69 1.69 0 0 0-1.69 1.69c0 .93.76 1.68 1.69 1.68m1.39 9.94v-8.37H5.5v8.37z"
        ></path>
      </svg>
    ),
  },
  {
    name: "Facebook",
    href: "https://www.facebook.com/people/Billixio/61575636077547/",
    icon: (
      <svg className="size-4 sm:size-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
        <path
          fill="currentColor"
          d="M22 12c0-5.52-4.48-10-10-10S2 6.48 2 12c0 4.84 3.44 8.87 8 9.8V15H8v-3h2V9.5C10 7.57 11.57 6 13.5 6H16v3h-2c-.55 0-1 .45-1 1v2h3v3h-3v6.95c5.05-.5 9-4.76 9-9.95"
        ></path>
      </svg>
    ),
  },
  {
    name: "Instagram",
    href: "https://www.instagram.com/billix.io/",
    icon: (
      <svg className="size-4 sm:size-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
        <path
          fill="currentColor"
          d="M7.8 2h8.4C19.4 2 22 4.6 22 7.8v8.4a5.8 5.8 0 0 1-5.8 5.8H7.8C4.6 22 2 19.4 2 16.2V7.8A5.8 5.8 0 0 1 7.8 2m-.2 2A3.6 3.6 0 0 0 4 7.6v8.8C4 18.39 5.61 20 7.6 20h8.8a3.6 3.6 0 0 0 3.6-3.6V7.6C20 5.61 18.39 4 16.4 4zm9.65 1.5a1.25 1.25 0 0 1 1.25 1.25A1.25 1.25 0 0 1 17.25 8A1.25 1.25 0 0 1 16 6.75a1.25 1.25 0 0 1 1.25-1.25M12 7a5 5 0 0 1 5 5a5 5 0 0 1-5 5a5 5 0 0 1-5-5a5 5 0 0 1 5-5m0 2a3 3 0 0 0-3 3a3 3 0 0 0 3 3a3 3 0 0 0 3-3a3 3 0 0 0-3-3"
        ></path>
      </svg>
    ),
  },
]

export default function FooterSection() {
  return (
    <footer className="bg-black from-background to-muted/30 overflow-hidden">
      <div className="mx-auto max-w-[1650px] px-4 sm:px-6 py-12 sm:py-16 md:py-24 lg:px-8">
        {/* Top section with logo and newsletter */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8 sm:mb-12 gap-6 sm:gap-8">
          <div className="max-w-md">
            <Link href="/" aria-label="go home" className="inline-block">
              <p className="text-3xl sm:text-4xl font-bold text-white">
                Billix
              </p>
            </Link>
            <p className="mt-3 sm:mt-4 text-sm sm:text-base text-neutral-400">
              Empowering businesses with innovative solutions that drive growth and success in the digital landscape.
            </p>
          </div>

          <div className="w-full max-w-md mt-6 lg:mt-0">
            <h3 className="text-base sm:text-lg font-semibold mb-2 sm:mb-3">Subscribe to our newsletter</h3>
            <div className="flex flex-col sm:flex-row gap-2">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex h-9 sm:h-10 w-full rounded-md border border-neutral-700 bg-neutral-900 text-white px-3 py-2 text-xs sm:text-sm placeholder:text-neutral-400 focus:outline-none focus:ring-2 focus:ring-white/40 focus:border-white/40 disabled:cursor-not-allowed disabled:opacity-50"
              />
              <button className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-xs sm:text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-white/40 focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-white text-black hover:bg-neutral-200 h-9 sm:h-10 px-4 py-2">
                Subscribe
              </button>
            </div>
          </div>
        </div>

        {/* Bottom section with social links and copyright */}
        <div className="mt-10 sm:mt-12 border-t border-border pt-6 sm:pt-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-6">
            <div className="flex flex-wrap items-center gap-2 sm:gap-3">
              {socialLinks.map((social) => (
                <Link
                  key={social.name}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label={social.name}
                  className="flex items-center justify-center rounded-full bg-neutral-900 p-2 sm:p-2.5 text-white hover:bg-white/10 hover:text-white transition-colors duration-200"
                >
                  {social.icon}
                </Link>
              ))}
            </div>

            <p className="text-xs sm:text-sm text-neutral-400 mt-4 sm:mt-0">© {new Date().getFullYear()} Billix. All rights reserved.</p>
          </div>
        </div>
      </div>
    </footer>
  )
}
