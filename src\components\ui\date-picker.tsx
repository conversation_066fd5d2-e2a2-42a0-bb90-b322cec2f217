'use client';

import { DayPicker } from 'react-day-picker';
import { Button } from '@/components/ui/button';
import { Calendar as CalendarIcon } from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface DatePickerProps {
  date?: Date;
  onDateChange?: (date: Date | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  disablePastDates?: boolean;
  disableFutureDates?: boolean;
}

export function DatePicker({
  date,
  onDateChange,
  placeholder = 'Pick a date',
  disabled = false,
  className,
  disablePastDates = false,
  disableFutureDates = false,
}: DatePickerProps) {
  const getDisabledDates = () => {
    const conditions = [];
    
    if (disablePastDates) {
      conditions.push({ before: new Date() });
    }
    
    if (disableFutureDates) {
      conditions.push({ after: new Date() });
    }
    
    return conditions.length > 0 ? conditions : undefined;
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          disabled={disabled}
          className={cn(
            'w-full justify-start text-left font-normal',
            !date && 'text-muted-foreground',
            className
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date ? format(date, 'PPP') : <span>{placeholder}</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <DayPicker
          mode="single"
          selected={date}
          onSelect={onDateChange}
          disabled={getDisabledDates()}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  );
}