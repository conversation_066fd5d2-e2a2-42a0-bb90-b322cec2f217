import React from 'react';
import {
  <PERSON>,
  But<PERSON>,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import Colors from '../../../components/theme/Colors';

interface TeamInviteEmailProps {
  inviterName: string;
  organizationName: string;
  role: string;
  inviteUrl: string;
  expiresAt: Date;
}

export const TeamInviteEmailTemplate = ({
  inviterName,
  organizationName,
  role,
  inviteUrl,
  expiresAt,
}: TeamInviteEmailProps) => {
  const expiryDate = new Intl.DateTimeFormat('en-US', {
    dateStyle: 'long',
  }).format(expiresAt);

  return (
    <Html>
      <Head />
      <Preview>
        {inviterName} has invited you to join {organizationName} as a{' '}
        {role}
      </Preview>
      <Body style={styles.body}>
        <Container style={styles.container}>
          <Heading style={styles.heading}>
            You&apos;ve been invited to join {organizationName}
          </Heading>

          <Section style={styles.section}>
            <Text style={styles.text}>Hello,</Text>
            <Text style={styles.text}>
              {inviterName} has invited you to join{' '}
              <strong>{organizationName}</strong> as a{' '}
              <strong>{role}</strong>.
            </Text>
            <Text style={styles.text}>
              As a {role}, you&apos;ll have access to collaborate with
              the team and utilize the platform&apos;s features
              according to your role permissions.
            </Text>

            <Button href={inviteUrl} style={styles.button}>
              Accept Invitation
            </Button>

            <Text style={styles.note}>
              This invitation will expire on {expiryDate}.
            </Text>

            <Text style={styles.text}>
              If you have any questions, you can reply to this email
              to contact the team.
            </Text>
          </Section>

          <Hr style={styles.hr} />

          <Text style={styles.footer}>
            If you weren&apos;t expecting this invitation, you can
            ignore this email.
          </Text>
        </Container>
      </Body>
    </Html>
  );
};

const styles = {
  body: {
    backgroundColor: Colors.background,
    fontFamily:
      '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  },
  container: {
    margin: '0 auto',
    padding: '20px 0',
    maxWidth: '600px',
  },
  heading: {
    fontSize: '24px',
    fontWeight: 'bold',
    marginTop: '32px',
    color: Colors.text,
  },
  section: {
    backgroundColor: Colors.textLight,
    padding: '32px',
    borderRadius: '8px',
    boxShadow:
      '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    margin: '20px 0',
  },
  text: {
    fontSize: '16px',
    color: Colors.text,
    lineHeight: '24px',
    marginBottom: '14px',
  },
  button: {
    backgroundColor: Colors.primaryDark,
    color: Colors.textLight,
    borderRadius: '6px',
    fontSize: '16px',
    padding: '12px 24px',
    textDecoration: 'none',
    textAlign: 'center' as const,
    display: 'block',
    marginTop: '26px',
    marginBottom: '26px',
    fontWeight: 'bold',
  },
  note: {
    fontSize: '14px',
    color: Colors.info,
    fontStyle: 'italic',
    marginTop: '14px',
    marginBottom: '24px',
  },
  hr: {
    borderColor: Colors.border,
    margin: '26px 0',
  },
  footer: {
    fontSize: '14px',
    color: Colors.info,
    textAlign: 'center' as const,
    marginTop: '20px',
  },
};
