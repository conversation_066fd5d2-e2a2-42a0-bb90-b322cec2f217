'use client';

import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import {
  Zap,
  MessageSquare,
  FileText,
  ArrowUp,
  Clock,
  CheckCircle,
} from 'lucide-react';
import { useRouter } from 'next/navigation';

interface LimitExceededModalProps {
  isOpen: boolean;
  onClose: () => void;
  limitType: 'chat' | 'invoice';
  currentUsage: number;
  limit: number;
  resetDate: Date;
}

export function LimitExceededModal({
  isOpen,
  onClose,
  limitType,
  currentUsage,
  limit,
  resetDate,
}: LimitExceededModalProps) {
  const [isUpgrading, setIsUpgrading] = useState(false);
  const router = useRouter();

  const handleUpgrade = async () => {
    setIsUpgrading(true);
    router.push('/pricing');
  };

  const getIcon = () => {
    return limitType === 'chat' ? (
      <MessageSquare className="h-6 w-6" />
    ) : (
      <FileText className="h-6 w-6" />
    );
  };

  const getTitle = () => {
    return limitType === 'chat'
      ? 'Chat Limit Reached'
      : 'Invoice Upload Limit Reached';
  };

  const getDescription = () => {
    return limitType === 'chat'
      ? 'You have reached your monthly chat message limit'
      : 'You have reached your monthly invoice upload limit';
  };

  const getFeatureText = () => {
    return limitType === 'chat' ? 'chat messages' : 'invoice uploads';
  };

  const daysUntilReset = Math.ceil(
    (resetDate.getTime() - new Date().getTime()) /
      (1000 * 60 * 60 * 24)
  );

  const planBenefits = [
    {
      name: 'Business Plan',
      price: '$79/month',
      chatLimit: 50,
      invoiceLimit: 100,
      popular: true,
    },
    {
      name: 'Enterprise Plan',
      price: '$199/month',
      chatLimit: 500,
      invoiceLimit: 1000,
      popular: false,
    },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 bg-red-100 rounded-full">
              <Zap className="h-6 w-6 text-red-600" />
            </div>
            <div>
              <DialogTitle className="text-xl font-semibold">
                {getTitle()}
              </DialogTitle>
              <DialogDescription className="text-base">
                {getDescription()}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-4">
          {/* Current Usage Status */}
          <Card className="bg-red-50 border-red-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-red-100 rounded-full">
                    {getIcon()}
                  </div>
                  <div>
                    <p className="font-medium text-red-900">
                      {currentUsage.toLocaleString()} of{' '}
                      {limit.toLocaleString()} {getFeatureText()} used
                    </p>
                    <p className="text-sm text-red-700">
                      100% of your monthly limit reached
                    </p>
                  </div>
                </div>
                <Badge variant="destructive">Limit Reached</Badge>
              </div>
            </CardContent>
          </Card>

          {/* Reset Information */}
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Clock className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium text-blue-900">
                    Usage resets in {daysUntilReset} day
                    {daysUntilReset !== 1 ? 's' : ''}
                  </p>
                  <p className="text-sm text-blue-700">
                    Your usage will reset on{' '}
                    {resetDate.toLocaleDateString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Upgrade Options */}
          <div className="space-y-3">
            <h4 className="font-medium">
              Upgrade to continue using Billix
            </h4>
            <div className="grid gap-3">
              {planBenefits.map((plan) => (
                <Card
                  key={plan.name}
                  className={`relative cursor-pointer transition-all hover:shadow-md ${
                    plan.popular
                      ? 'ring-2 ring-blue-500 bg-blue-50'
                      : ''
                  }`}
                  onClick={handleUpgrade}
                >
                  {plan.popular && (
                    <div className="absolute -top-2 left-4">
                      <Badge className="bg-blue-600 hover:bg-blue-700">
                        Most Popular
                      </Badge>
                    </div>
                  )}
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="flex items-center gap-2">
                          <h5 className="font-medium">{plan.name}</h5>
                          <span className="text-lg font-bold text-blue-600">
                            {plan.price}
                          </span>
                        </div>
                        <div className="flex items-center gap-4 mt-1 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <MessageSquare className="h-3 w-3" />
                            {plan.chatLimit} chats/month
                          </div>
                          <div className="flex items-center gap-1">
                            <FileText className="h-3 w-3" />
                            {plan.invoiceLimit} invoices/month
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="text-sm text-green-600 font-medium">
                          Instant Access
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            className="w-full sm:w-auto"
          >
            Maybe Later
          </Button>
          <Button
            onClick={handleUpgrade}
            disabled={isUpgrading}
            className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700"
          >
            {isUpgrading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Upgrading...
              </>
            ) : (
              <>
                <ArrowUp className="h-4 w-4 mr-2" />
                Upgrade Now
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
