'use client';

import { useState } from 'react';
import { motion } from 'motion/react';
import {
  FolderIcon,
  MoreHorizontalIcon,
  PlusIcon,
  SearchIcon,
  TrashIcon,
  EditIcon
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from '@/components/ui/alert-dialog';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle
} from '@/components/ui/dialog';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from '@/components/ui/pagination';

interface Folder {
  id: string;
  name: string;
  invoiceCount: number;
  invoiceIds: string[];
  createdAt: string;
}

interface FolderListProps {
  folders: Folder[];
  total: number;
  page?: number;
  limit?: number;
  totalPages?: number;
  onPageChange?: (page: number) => void;
  onSearch?: (searchTerm: string) => void;
  onCreateFolder?: () => void;
  onDeleteFolder?: (folderId: string) => void;
  onRenameFolder?: (folderId: string, name: string) => void;
  onViewFolder?: (folderId: string) => void;
}

export function FolderList({
  folders,
  total,
  page = 1,
  totalPages = 1,
  onPageChange,
  onSearch,
  onCreateFolder,
  onDeleteFolder,
  onRenameFolder,
  onViewFolder
}: FolderListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [folderToDelete, setFolderToDelete] = useState<string | null>(null);
  const [folderToRename, setFolderToRename] = useState<{id: string, name: string} | null>(null);
  const [newFolderName, setNewFolderName] = useState('');

  const handleSearch = () => {
    onSearch?.(searchTerm);
  };

  const handleRenameSubmit = () => {
    if (folderToRename && newFolderName.trim()) {
      onRenameFolder?.(folderToRename.id, newFolderName);
      setFolderToRename(null);
      setNewFolderName('');
    }
  };

  if (folders.length === 0) {
    return (
      <div className="p-6 text-center bg-card/50 border border-border/40 rounded-xl shadow-sm">
        <p className="text-muted-foreground">No folders found.</p>
        {onCreateFolder && (
          <Button
            variant="outline"
            className="mt-4"
            onClick={onCreateFolder}
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Create Folder
          </Button>
        )}
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="space-y-4"
    >
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-4">
        <h3 className="text-lg font-medium">Folders</h3>

        <div className="flex flex-col md:flex-row gap-2 w-full md:w-auto">
          {onSearch && (
            <div className="relative">
              <Input
                placeholder="Search folders..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                className="pl-8 w-full md:w-[200px]"
              />
              <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            </div>
          )}

          {onCreateFolder && (
            <Button
              variant="outline"
              size="sm"
              onClick={onCreateFolder}
              className="flex items-center gap-2"
            >
              <PlusIcon className="h-4 w-4" />
              <span>New Folder</span>
            </Button>
          )}
        </div>

        <span className="text-sm text-muted-foreground whitespace-nowrap">
          Showing {folders.length} of {total} folders
        </span>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {folders.map((folder) => (
          <motion.div
            key={folder.id}
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            <Card
              className="h-full border border-border/40 shadow-md hover:shadow-lg transition-shadow duration-200 hover:border-primary/30"
              onClick={() => onViewFolder?.(folder.id)}
            >
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg font-semibold flex items-center">
                    <FolderIcon className="h-5 w-5 mr-2 text-primary" />
                    {folder.name}
                  </CardTitle>

                  {(onDeleteFolder || onRenameFolder) && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreHorizontalIcon className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>

                        {onRenameFolder && (
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              setFolderToRename({ id: folder.id, name: folder.name });
                              setNewFolderName(folder.name);
                            }}
                            className="flex items-center gap-2"
                          >
                            <EditIcon className="h-4 w-4" />
                            <span>Rename</span>
                          </DropdownMenuItem>
                        )}

                        {onDeleteFolder && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation();
                                setFolderToDelete(folder.id);
                              }}
                              className="flex items-center gap-2 text-red-600"
                            >
                              <TrashIcon className="h-4 w-4" />
                              <span>Delete</span>
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
                <CardDescription>
                  Created on {formatDate(folder.createdAt)}
                </CardDescription>
              </CardHeader>

              <CardContent className="pb-2">
                <Badge className="bg-primary/20 text-primary border-primary/30">
                  {folder.invoiceCount} {folder.invoiceCount === 1 ? 'invoice' : 'invoices'}
                </Badge>
              </CardContent>

              <CardFooter className="pt-2">
                <div className="text-sm text-muted-foreground">
                  ID: {folder.id}
                </div>

                {onViewFolder && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="ml-auto text-primary"
                    onClick={(e) => {
                      e.stopPropagation();
                      onViewFolder(folder.id);
                    }}
                  >
                    View
                  </Button>
                )}
              </CardFooter>
            </Card>
          </motion.div>
        ))}
      </div>

      {totalPages > 1 && onPageChange && (
        <Pagination className="mt-8">
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={() => page > 1 && onPageChange(page - 1)}
                className={page <= 1 ? 'pointer-events-none opacity-50' : ''}
              />
            </PaginationItem>

            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              // Show first page, last page, current page, and pages around current
              let pageNum: number | null = null;

              if (i === 0) {
                pageNum = 1;
              } else if (i === 4) {
                pageNum = totalPages;
              } else if (totalPages <= 5) {
                pageNum = i + 1;
              } else if (page <= 3) {
                pageNum = i + 1;
              } else if (page >= totalPages - 2) {
                pageNum = totalPages - 4 + i;
              } else {
                pageNum = page - 1 + i;
              }

              // Show ellipsis for gaps
              if (i === 1 && page > 3 && totalPages > 5) {
                return (
                  <PaginationItem key="ellipsis-start">
                    <PaginationEllipsis />
                  </PaginationItem>
                );
              }

              if (i === 3 && page < totalPages - 2 && totalPages > 5) {
                return (
                  <PaginationItem key="ellipsis-end">
                    <PaginationEllipsis />
                  </PaginationItem>
                );
              }

              if (pageNum !== null) {
                return (
                  <PaginationItem key={pageNum}>
                    <PaginationLink
                      isActive={page === pageNum}
                      onClick={() => onPageChange(pageNum!)}
                    >
                      {pageNum}
                    </PaginationLink>
                  </PaginationItem>
                );
              }

              return null;
            })}

            <PaginationItem>
              <PaginationNext
                onClick={() => page < totalPages && onPageChange(page + 1)}
                className={page >= totalPages ? 'pointer-events-none opacity-50' : ''}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={folderToDelete !== null} onOpenChange={(open) => !open && setFolderToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will delete the folder and remove all invoice associations. The invoices themselves will not be deleted.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (folderToDelete) {
                  onDeleteFolder?.(folderToDelete);
                  setFolderToDelete(null);
                }
              }}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Rename Folder Dialog */}
      <Dialog open={folderToRename !== null} onOpenChange={(open) => !open && setFolderToRename(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Rename Folder</DialogTitle>
            <DialogDescription>
              Enter a new name for the folder.
            </DialogDescription>
          </DialogHeader>

          <Input
            value={newFolderName}
            onChange={(e) => setNewFolderName(e.target.value)}
            placeholder="Folder name"
            className="mt-4"
            onKeyDown={(e) => e.key === 'Enter' && handleRenameSubmit()}
          />

          <DialogFooter className="mt-4">
            <Button variant="outline" onClick={() => setFolderToRename(null)}>
              Cancel
            </Button>
            <Button onClick={handleRenameSubmit}>
              Save
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </motion.div>
  );
}
