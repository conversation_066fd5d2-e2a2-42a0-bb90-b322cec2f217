'use client';

import React, { createContext, useContext, useState } from 'react';
import { DateRange } from 'react-day-picker';

interface DateFilterContextType {
  dateRange: DateRange | undefined;
  setDateRange: (range: DateRange | undefined) => void;
  getDateRangeForAPI: () => { from: string; to: string } | null;
}

const DateFilterContext = createContext<
  DateFilterContextType | undefined
>(undefined);

export function DateFilterProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(2023, 9, 1), // Oct 1, 2023 (same as original default)
    to: new Date(), // Today
  });

  const getDateRangeForAPI = () => {
    if (!dateRange?.from || !dateRange?.to) {
      return null;
    }
    return {
      from: dateRange.from.toISOString(),
      to: dateRange.to.toISOString(),
    };
  };

  return (
    <DateFilterContext.Provider
      value={{
        dateRange,
        setDateRange,
        getDateRangeForAPI,
      }}
    >
      {children}
    </DateFilterContext.Provider>
  );
}

export function useDateFilter() {
  const context = useContext(DateFilterContext);
  if (context === undefined) {
    throw new Error(
      'useDateFilter must be used within a DateFilterProvider'
    );
  }
  return context;
}
