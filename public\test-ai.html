<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Generation Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">🧠 BILLIX AI Agent - Document Generation Test</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Test Document Generation -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">📄 Document Generation</h2>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium mb-2">Document Type:</label>
                        <select id="documentType" class="w-full p-2 border rounded">
                            <option value="invoice">Invoice</option>
                            <option value="report">Financial Report</option>
                            <option value="dashboard">Dashboard</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium mb-2">Format:</label>
                        <select id="documentFormat" class="w-full p-2 border rounded">
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                            <option value="image">Image</option>
                            <option value="all">All Formats</option>
                        </select>
                    </div>
                    
                    <button onclick="generateDocument()" id="generateBtn" 
                            class="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 disabled:opacity-50">
                        Generate Document
                    </button>
                </div>
                
                <div id="documentResults" class="mt-4 space-y-2"></div>
            </div>
            
            <!-- Test AI Chat -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">💬 AI Chat Test</h2>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium mb-2">Message:</label>
                        <input type="text" id="chatMessage" 
                               placeholder="e.g., 'Create an invoice PDF'"
                               class="w-full p-2 border rounded">
                    </div>
                    
                    <button onclick="testChat()" id="chatBtn" 
                            class="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700 disabled:opacity-50">
                        Send Message
                    </button>
                </div>
                
                <div id="chatResults" class="mt-4 p-4 bg-gray-50 rounded min-h-[100px]"></div>
            </div>
        </div>
        
        <!-- Results Section -->
        <div id="allResults" class="mt-8"></div>
    </div>

    <script>
        async function generateDocument() {
            const btn = document.getElementById('generateBtn');
            const results = document.getElementById('documentResults');
            const type = document.getElementById('documentType').value;
            const format = document.getElementById('documentFormat').value;
            
            btn.disabled = true;
            btn.textContent = 'Generating...';
            results.innerHTML = '<div class="text-blue-600">⏳ Generating document...</div>';
            
            try {
                const response = await fetch('/api/test-document', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ type, format }),
                });

                const data = await response.json();
                
                if (data.success) {
                    results.innerHTML = '<div class="text-green-600 font-semibold">✅ Success!</div>';
                    
                    data.documents.forEach(doc => {
                        const docDiv = document.createElement('div');
                        docDiv.className = 'mt-2 p-3 bg-green-50 border border-green-200 rounded';
                        docDiv.innerHTML = `
                            <div class="flex justify-between items-center">
                                <div>
                                    <div class="font-medium">${doc.metadata.title}</div>
                                    <div class="text-sm text-gray-600">${doc.format.toUpperCase()} • ${(doc.metadata.size / 1024).toFixed(1)} KB</div>
                                </div>
                                <a href="${doc.downloadUrl}" target="_blank" 
                                   class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">
                                    Download
                                </a>
                            </div>
                        `;
                        results.appendChild(docDiv);
                    });
                } else {
                    results.innerHTML = `<div class="text-red-600">❌ Error: ${data.error}</div>`;
                }
            } catch (error) {
                results.innerHTML = `<div class="text-red-600">❌ Error: ${error.message}</div>`;
                console.error('Error:', error);
            } finally {
                btn.disabled = false;
                btn.textContent = 'Generate Document';
            }
        }
        
        async function testChat() {
            const btn = document.getElementById('chatBtn');
            const results = document.getElementById('chatResults');
            const message = document.getElementById('chatMessage').value;
            
            if (!message.trim()) {
                alert('Please enter a message');
                return;
            }
            
            btn.disabled = true;
            btn.textContent = 'Sending...';
            results.innerHTML = '<div class="text-blue-600">⏳ Processing message...</div>';
            
            try {
                const response = await fetch('/api/chat/smart', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        id: 'test-chat-' + Date.now(),
                        messages: [
                            {
                                role: 'user',
                                content: message
                            }
                        ]
                    }),
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                // Handle streaming response
                const reader = response.body.getReader();
                results.innerHTML = '<div class="text-green-600 font-semibold">✅ AI Response:</div>';
                
                let buffer = '';
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    buffer += new TextDecoder().decode(value);
                    const lines = buffer.split('\\n');
                    buffer = lines.pop() || '';
                    
                    for (const line of lines) {
                        if (line.startsWith('2:')) {
                            try {
                                const data = JSON.parse(line.substring(2));
                                if (data.type === 'document-generated') {
                                    const docDiv = document.createElement('div');
                                    docDiv.className = 'mt-2 p-3 bg-blue-50 border border-blue-200 rounded';
                                    docDiv.innerHTML = `
                                        <div class="font-medium text-blue-800">📄 Documents Generated!</div>
                                        ${data.documents.map(doc => `
                                            <div class="mt-2 flex justify-between items-center">
                                                <span>${doc.title} (${doc.format.toUpperCase()})</span>
                                                <a href="${doc.downloadUrl}" target="_blank" 
                                                   class="bg-blue-600 text-white px-2 py-1 rounded text-xs">Download</a>
                                            </div>
                                        `).join('')}
                                    `;
                                    results.appendChild(docDiv);
                                }
                            } catch (e) {
                                // Ignore parsing errors for non-JSON content
                            }
                        }
                    }
                }
                
            } catch (error) {
                results.innerHTML = `<div class="text-red-600">❌ Error: ${error.message}</div>`;
                console.error('Error:', error);
            } finally {
                btn.disabled = false;
                btn.textContent = 'Send Message';
            }
        }
    </script>
</body>
</html>
