import type { InvoiceData } from '@/types/invoice';

export interface ProcessingFile {
  file: File;
  progress: number;
  status: 'pending' | 'processing' | 'completed' | 'error';
  error?: string;
  data?: InvoiceData;
  previewUrl?: string;
  source?: 'upload' | 'email';
  emailMetadata?: {
    messageId?: string;
    subject?: string;
    from?: string;
    date?: string;
    messageUrl?: string;
  };
}
