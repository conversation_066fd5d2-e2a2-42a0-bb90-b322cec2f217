import * as ExcelJS from "exceljs"
import { formatDate, formatNotes } from "../utils/pdf"
import type { InvoiceWithExtras, ExtractedData } from "../types/pdf"

// Function to format currency
function formatCurrency(amount: number, currency = "USD"): string {
    return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: currency,
    }).format(amount)
}

// Function to safely extract nested data
function extractNestedData(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
        return current && current[key] !== undefined ? current[key] : null
    }, obj)
}

// Function to flatten nested objects for Excel display
function flattenObject(obj: any, prefix = '', maxDepth = 3, currentDepth = 0): Record<string, any> {
    const flattened: Record<string, any> = {}
    
    if (currentDepth >= maxDepth) {
        return { [prefix]: JSON.stringify(obj) }
    }
    
    if (obj && typeof obj === 'object' && !Array.isArray(obj)) {
        Object.keys(obj).forEach(key => {
            const value = obj[key]
            const newKey = prefix ? `${prefix}.${key}` : key
            
            if (value && typeof value === 'object' && !Array.isArray(value)) {
                Object.assign(flattened, flattenObject(value, newKey, maxDepth, currentDepth + 1))
            } else if (Array.isArray(value)) {
                flattened[newKey] = value.map(item => 
                    typeof item === 'object' ? JSON.stringify(item) : String(item)
                ).join('; ')
            } else {
                flattened[newKey] = value
            }
        })
    } else {
        flattened[prefix] = obj
    }
    
    return flattened
}

export async function generateExcel(invoices: InvoiceWithExtras[], fields: string[]): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook()
    workbook.creator = "Invoice Management System"
    workbook.created = new Date()

    // Main invoice summary sheet
    const summarySheet = workbook.addWorksheet("Invoice Summary", {
        properties: { tabColor: { argb: "4F81BD" } },
        views: [{ state: "frozen", ySplit: 1 }]
    })

    // Define comprehensive columns for summary
    const summaryColumns: Partial<ExcelJS.Column>[] = [
        { header: "Invoice Number", key: "invoiceNumber", width: 20 },
        { header: "Vendor Name", key: "vendorName", width: 25 },
        { header: "Issue Date", key: "issueDate", width: 15 },
        { header: "Due Date", key: "dueDate", width: 15 },
        { header: "Amount", key: "amount", width: 15 },
        { header: "Currency", key: "currency", width: 10 },
        { header: "Status", key: "status", width: 12 },
        { header: "Category", key: "category", width: 15 },
    ]

    summarySheet.columns = summaryColumns

    // Add invoice data to summary
    invoices.forEach((invoice, index) => {
        const row = summarySheet.addRow({
            invoiceNumber: invoice.invoiceNumber || `INV-${index + 1}`,
            vendorName: invoice.vendorName || "Unknown",
            issueDate: invoice.issueDate ? formatDate(invoice.issueDate) : "N/A",
            dueDate: invoice.dueDate ? formatDate(invoice.dueDate) : "N/A",
            amount: invoice.amount || 0,
            currency: invoice.currency || "USD",
            status: invoice.status || "Unknown",
            category: invoice.category?.name || "Uncategorized",
        })

        // Format amount as currency
        const amountCell = row.getCell('amount')
        amountCell.numFmt = '"$"#,##0.00'
        
        // Color code by status
        const statusCell = row.getCell('status')
        switch (invoice.status?.toLowerCase()) {
            case 'paid':
                statusCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'C8E6C9' } }
                break
            case 'pending':
                statusCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF3E0' } }
                break
            case 'overdue':
                statusCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFCDD2' } }
                break
        }
    })

    // Style the header
    const headerRow = summarySheet.getRow(1)
    headerRow.font = { bold: true, color: { argb: "FFFFFF" } }
    headerRow.fill = { type: "pattern", pattern: "solid", fgColor: { argb: "4F81BD" } }
    headerRow.alignment = { vertical: "middle", horizontal: "center" }

    // Add borders
    summarySheet.eachRow({ includeEmpty: false }, (row) => {
        row.eachCell({ includeEmpty: false }, (cell) => {
            cell.border = {
                top: { style: "thin" },
                left: { style: "thin" },
                bottom: { style: "thin" },
                right: { style: "thin" },
            }
        })
    })

    // Line Items Sheet - This is the main sheet the user wants to see properly
    const lineItemsSheet = workbook.addWorksheet("Line Items", {
        properties: { tabColor: { argb: "9932CC" } },
    })

    const lineItemColumns = [
        { header: "Invoice Number", key: "invoiceNumber", width: 20 },
        { header: "Vendor", key: "vendorName", width: 25 },
        { header: "Description", key: "description", width: 40 },
        { header: "Quantity", key: "quantity", width: 10 },
        { header: "Unit Price", key: "unitPrice", width: 15 },
        { header: "Total Price", key: "totalPrice", width: 15 },
        { header: "Tax Rate", key: "taxRate", width: 12 },
        { header: "Tax Amount", key: "taxAmount", width: 15 },
        { header: "Discount", key: "discount", width: 12 },
        { header: "Product SKU", key: "productSku", width: 20 },
        { header: "Notes", key: "notes", width: 30 },
    ]

    lineItemsSheet.columns = lineItemColumns

    // Add line item data - THIS IS THE KEY PART
    invoices.forEach(invoice => {
        // First, try to get line items from the actual database
        if (invoice.lineItems && Array.isArray(invoice.lineItems) && invoice.lineItems.length > 0) {
            invoice.lineItems.forEach(item => {
                const row = lineItemsSheet.addRow({
                    invoiceNumber: invoice.invoiceNumber || invoice.id,
                    vendorName: invoice.vendorName || "Unknown",
                    description: item.description || "N/A",
                    quantity: item.quantity || 0,
                    unitPrice: item.unitPrice || 0,
                    totalPrice: item.totalPrice || 0,
                    taxRate: item.taxRate ? `${(item.taxRate * 100).toFixed(2)}%` : "N/A",
                    taxAmount: item.taxAmount || "N/A",
                    discount: item.discount || "N/A",
                    productSku: item.productSku || "N/A",
                    notes: item.notes || "N/A",
                })

                // Format currency cells
                const currencyCells = ['unitPrice', 'totalPrice', 'taxAmount']
                currencyCells.forEach((key: string) => {
                    const cell = row.getCell(key)
                    if (typeof cell.value === 'number') {
                        cell.numFmt = '"$"#,##0.00'
                    }
                })
            })
        } else {
            // If no database line items, try to extract from extractedData
            const extractedData = invoice.extractedData && typeof invoice.extractedData === "object"
                ? (invoice.extractedData as unknown as ExtractedData)
                : undefined

            if (extractedData?.lineItems && Array.isArray(extractedData.lineItems)) {
                extractedData.lineItems.forEach((item: any) => {
                    const row = lineItemsSheet.addRow({
                        invoiceNumber: invoice.invoiceNumber || invoice.id,
                        vendorName: invoice.vendorName || "Unknown",
                        description: item.description || item.name || "N/A",
                        quantity: parseFloat(item.quantity) || 1,
                        unitPrice: parseFloat(item.unitPrice) || 0,
                        totalPrice: parseFloat(item.amount || item.totalPrice || item.total) || 0,
                        taxRate: item.taxRate ? `${parseFloat(item.taxRate)}%` : "N/A",
                        taxAmount: parseFloat(item.taxAmount) || "N/A",
                        discount: parseFloat(item.discount) || "N/A",
                        productSku: item.sku || item.productSku || "N/A",
                        notes: item.notes || "N/A",
                    })

                    // Format currency cells
                    const currencyCells = ['unitPrice', 'totalPrice', 'taxAmount']
                    currencyCells.forEach((key: string) => {
                        const cell = row.getCell(key)
                        if (typeof cell.value === 'number') {
                            cell.numFmt = '"$"#,##0.00'
                        }
                    })
                })
            } else {
                // Add a placeholder row showing no line items
                lineItemsSheet.addRow({
                    invoiceNumber: invoice.invoiceNumber || invoice.id,
                    vendorName: invoice.vendorName || "Unknown",
                    description: "No line items found",
                    quantity: "N/A",
                    unitPrice: "N/A",
                    totalPrice: "N/A",
                    taxRate: "N/A",
                    taxAmount: "N/A",
                    discount: "N/A",
                    productSku: "N/A",
                    notes: "Check extracted data or original document",
                })
            }
        }
    })

    // Style line items sheet
    const lineItemsHeaderRow = lineItemsSheet.getRow(1)
    lineItemsHeaderRow.font = { bold: true, color: { argb: "FFFFFF" } }
    lineItemsHeaderRow.fill = { type: "pattern", pattern: "solid", fgColor: { argb: "9932CC" } }
    lineItemsHeaderRow.alignment = { vertical: "middle", horizontal: "center" }

    lineItemsSheet.eachRow({ includeEmpty: false }, (row) => {
        row.eachCell({ includeEmpty: false }, (cell) => {
            cell.border = {
                top: { style: "thin" },
                left: { style: "thin" },
                bottom: { style: "thin" },
                right: { style: "thin" },
            }
        })
    })

    // Only add additional sheets if specifically requested
    if (fields.includes("vendor") || fields.includes("customer") || fields.includes("financials")) {
        // Additional data sheet
        const detailsSheet = workbook.addWorksheet("Additional Details", {
            properties: { tabColor: { argb: "92D050" } },
        })

        const detailColumns = [
            { header: "Invoice Number", key: "invoiceNumber", width: 20 },
            { header: "Vendor Name", key: "vendorName", width: 25 },
            { header: "Vendor Email", key: "vendorEmail", width: 25 },
            { header: "Vendor Phone", key: "vendorPhone", width: 20 },
            { header: "Vendor Address", key: "vendorAddress", width: 40 },
            { header: "Subtotal", key: "subtotal", width: 15 },
            { header: "Tax Amount", key: "taxAmount", width: 15 },
            { header: "Total Amount", key: "totalAmount", width: 15 },
            { header: "Notes", key: "notes", width: 40 },
        ]

        detailsSheet.columns = detailColumns

        invoices.forEach(invoice => {
            const extractedData = invoice.extractedData && typeof invoice.extractedData === "object"
                ? (invoice.extractedData as unknown as ExtractedData)
                : undefined

            const vendorData = extractedData?.vendor || {} as any
            const financials = extractedData?.financials || {} as any
            
            const row = detailsSheet.addRow({
                invoiceNumber: invoice.invoiceNumber || invoice.id,
                vendorName: invoice.vendorName || vendorData.name || "Unknown",
                vendorEmail: vendorData.email || "N/A",
                vendorPhone: vendorData.phone || "N/A", 
                vendorAddress: vendorData.address || "N/A",
                subtotal: financials.subtotal || "N/A",
                taxAmount: financials.tax || "N/A",
                totalAmount: invoice.amount || financials.total || "N/A",
                notes: invoice.notes || extractedData?.notes || "N/A",
            })

            // Format currency cells
            const currencyFields = ['subtotal', 'taxAmount', 'totalAmount']
            currencyFields.forEach((key: string) => {
                const cell = row.getCell(key)
                if (typeof cell.value === 'number') {
                    cell.numFmt = '"$"#,##0.00'
                }
            })
        })

        // Style details sheet
        const detailsHeaderRow = detailsSheet.getRow(1)
        detailsHeaderRow.font = { bold: true, color: { argb: "FFFFFF" } }
        detailsHeaderRow.fill = { type: "pattern", pattern: "solid", fgColor: { argb: "92D050" } }
        detailsHeaderRow.alignment = { vertical: "middle", horizontal: "center" }

        detailsSheet.eachRow({ includeEmpty: false }, (row) => {
            row.eachCell({ includeEmpty: false }, (cell) => {
                cell.border = {
                    top: { style: "thin" },
                    left: { style: "thin" },
                    bottom: { style: "thin" },
                    right: { style: "thin" },
                }
            })
        })
    }

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer()
    return Buffer.from(buffer)
}
