'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  MessageSquare,
  FileText,
  Calendar,
  Zap,
  ArrowUp,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

interface UsageStats {
  chatUsage: number;
  invoiceUsage: number;
  chatLimit: number;
  invoiceLimit: number;
  resetDate: Date;
  daysUntilReset: number;
}

interface UsageCardProps {
  title: string;
  icon: React.ReactNode;
  current: number;
  limit: number;
  type: 'chat' | 'invoice';
  daysUntilReset: number;
}

function UsageCard({
  title,
  icon,
  current,
  limit,
  type,
  daysUntilReset,
}: UsageCardProps) {
  const percentage = limit > 0 ? (current / limit) * 100 : 0;
  const remaining = Math.max(0, limit - current);
  const isNearLimit = percentage >= 80;
  const isAtLimit = percentage >= 100;
  const getStatusBadge = () => {
    if (isAtLimit) {
      return (
        <Badge variant="destructive" className="text-xs">
          Limit Reached
        </Badge>
      );
    }
    if (isNearLimit) {
      return (
        <Badge
          variant="secondary"
          className="text-xs bg-yellow-100 text-yellow-800"
        >
          Near Limit
        </Badge>
      );
    }
    return (
      <Badge
        variant="secondary"
        className="text-xs bg-green-100 text-green-800"
      >
        Available
      </Badge>
    );
  };

  return (
    <Card className="h-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium flex items-center gap-2">
          {icon}
          {title}
        </CardTitle>
        {getStatusBadge()}
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex justify-between items-end">
            <div>
              <div className="text-2xl font-bold">
                {current.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                of {limit.toLocaleString()} used
              </p>
            </div>
            <div className="text-right">
              <div className="text-sm font-medium text-muted-foreground">
                {remaining.toLocaleString()} remaining
              </div>
              <p className="text-xs text-muted-foreground">
                Resets in {daysUntilReset} day
                {daysUntilReset !== 1 ? 's' : ''}
              </p>
            </div>
          </div>

          <div className="space-y-1">
            <Progress value={percentage} className="h-2" />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{Math.round(percentage)}% used</span>
              <span>{Math.round(100 - percentage)}% available</span>
            </div>
          </div>

          {isAtLimit && type === 'chat' && (
            <div className="mt-3 rounded-lg overflow-hidden border-none bg-gradient-to-br from-red-100/80 to-rose-100/80 dark:from-red-900/40 dark:to-rose-900/30 shadow flex items-center gap-3 p-4">
              <div className="rounded-full bg-red-200/60 dark:bg-red-900/60 p-2">
                <MessageSquare className="h-5 w-5 text-red-600 dark:text-red-400" />
              </div>
              <div>
                <p className="text-sm font-semibold text-red-900 dark:text-red-200">Chat Limit Reached</p>
                <p className="text-xs text-red-800 dark:text-red-300 mt-1">You&apos;ve reached your monthly chat limit. Upgrade your plan to continue.</p>
              </div>
            </div>
          )}
          {isAtLimit && type === 'invoice' && (
            <div className="mt-3 rounded-lg overflow-hidden border-none bg-gradient-to-br from-red-100/80 to-rose-100/80 dark:from-red-900/40 dark:to-rose-900/30 shadow flex items-center gap-3 p-4">
              <div className="rounded-full bg-red-200/60 dark:bg-red-900/60 p-2">
                <FileText className="h-5 w-5 text-red-600 dark:text-red-400" />
              </div>
              <div>
                <p className="text-sm font-semibold text-red-900 dark:text-red-200">Invoice Limit Reached</p>
                <p className="text-xs text-red-800 dark:text-red-300 mt-1">You&apos;ve reached your monthly invoice limit. Upgrade your plan to continue.</p>
              </div>
            </div>
          )}
          {isAtLimit && type !== 'chat' && type !== 'invoice' && (
            <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded-md">
              <p className="text-xs text-red-800">
                You&apos;ve reached your monthly {type} limit. Upgrade your plan to continue.
              </p>
            </div>
          )}

          {isNearLimit && !isAtLimit && (
            <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-xs text-yellow-800">
                You&apos;re approaching your monthly {type} limit. Consider
                upgrading your plan.
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export function UsageDashboard() {
  const [usageStats, setUsageStats] = useState<UsageStats | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    fetchUsageStats();
  }, []);

  const fetchUsageStats = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/usage/stats');

      if (!response.ok) {
        throw new Error('Failed to fetch usage stats');
      }

      const data = await response.json();

      if (data.success) {
        setUsageStats({
          ...data.stats,
          resetDate: new Date(data.stats.resetDate),
        });
      } else {
        throw new Error(data.error || 'Failed to load usage stats');
      }
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : 'Failed to load usage stats'
      );
      toast.error('Failed to load usage statistics');
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpgrade = () => {
    router.push('/dashboard/subscription');
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Usage Overview</h3>
        </div>
        <div className="grid gap-4 md:grid-cols-2">
          {[1, 2].map((i) => (
            <Card key={i} className="h-48">
              <CardContent className="p-6">
                <div className="animate-pulse space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/3"></div>
                  <div className="h-2 bg-gray-200 rounded"></div>
                  <div className="space-y-2">
                    <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error || !usageStats) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Usage Overview</h3>
        </div>
        <Card>
          <CardContent className="p-6 text-center">
            <div className="text-muted-foreground">
              {error || 'Unable to load usage statistics'}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={fetchUsageStats}
              className="mt-3"
            >
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const isAnyLimitReached =
    usageStats.chatUsage >= usageStats.chatLimit ||
    usageStats.invoiceUsage >= usageStats.invoiceLimit;

  const isAnyNearLimit =
    usageStats.chatUsage / usageStats.chatLimit >= 0.8 ||
    usageStats.invoiceUsage / usageStats.invoiceLimit >= 0.8;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Usage Overview</h3>
          <p className="text-sm text-muted-foreground">
            Track your monthly usage and limits
          </p>
        </div>

        {(isAnyLimitReached || isAnyNearLimit) && (
          <Button
            onClick={handleUpgrade}
            className="flex items-center gap-2"
          >
            <ArrowUp className="h-4 w-4" />
            Upgrade Plan
          </Button>
        )}
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <UsageCard
          title="Chat Messages"
          icon={<MessageSquare className="h-4 w-4" />}
          current={usageStats.chatUsage}
          limit={usageStats.chatLimit}
          type="chat"
          daysUntilReset={usageStats.daysUntilReset}
        />

        <UsageCard
          title="Invoice Uploads"
          icon={<FileText className="h-4 w-4" />}
          current={usageStats.invoiceUsage}
          limit={usageStats.invoiceLimit}
          type="invoice"
          daysUntilReset={usageStats.daysUntilReset}
        />
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-base">
            <Calendar className="h-4 w-4" />
            Billing Cycle
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">
                Current billing period ends in
              </p>
              <p className="font-medium">
                {usageStats.daysUntilReset} day
                {usageStats.daysUntilReset !== 1 ? 's' : ''}
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-muted-foreground">
                Usage resets on
              </p>
              <p className="font-medium">
                {usageStats.resetDate.toLocaleDateString()}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {isAnyLimitReached && (
        <Card className="overflow-hidden border-none bg-gradient-to-br from-red-100/80 to-rose-100/80 dark:from-red-900/40 dark:to-rose-900/30 shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              <div className="rounded-full bg-red-200/60 dark:bg-red-900/60 p-3 mt-1">
                <Zap className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-lg text-red-900 dark:text-red-200 flex items-center gap-2">
                  Usage Limit Reached
                </h4>
                <p className="text-sm text-red-800 dark:text-red-300 mt-1">
                  You&apos;ve reached your monthly usage limit. Upgrade your plan to continue using all features.
                </p>
                <Button
                  onClick={handleUpgrade}
                  size="sm"
                  className="mt-4 bg-gradient-to-r from-red-500 to-rose-500 text-white shadow-md hover:from-red-600 hover:to-rose-600 dark:from-red-700 dark:to-rose-700 dark:hover:from-red-800 dark:hover:to-rose-800 rounded-full px-6"
                >
                  <ArrowUp className="h-4 w-4 mr-2" />
                  Upgrade Now
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
UsageDashboard.displayName = 'UsageDashboard';
