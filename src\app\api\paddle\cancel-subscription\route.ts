import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { cancelPaddleSubscription } from '@/actions/paddle-actions';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    let body;
    try {
      body = await request.json();
    } catch (err) {
      console.error('Failed to parse JSON body:', err);
      return NextResponse.json(
        { success: false, error: 'Invalid JSON body' },
        { status: 400 }
      );
    }
    console.log('Cancel subscription request body:', body);
    const { subscriptionId } = body;

    if (!subscriptionId) {
      console.error('Missing subscriptionId in request body:', body);
      return NextResponse.json(
        { success: false, error: 'Subscription ID is required in body', body },
        { status: 400 }
      );
    }

    // Log userId and subscriptionId for debugging
    console.log('UserId:', userId, 'SubscriptionId:', subscriptionId);

    const result = await cancelPaddleSubscription(subscriptionId);

    // If cancellation failed, return more details
    if (!result.success) {
      console.error('Cancel subscription failed:', result.error);
      return NextResponse.json(
        { success: false, error: result.error, debug: result },
        { status: 400 }
      );
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Cancel subscription error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error', 
        ...(error instanceof Error && error.stack ? { stack: error.stack } : {})
      },
      { status: 500 }
    );
  }
}
