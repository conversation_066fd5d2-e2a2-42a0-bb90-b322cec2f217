// Form validation utilities for invoice forms

export interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// Basic validation for required fields
export function validateRequired(value: unknown, fieldName: string, displayName?: string): ValidationError | null {
  const label = displayName || fieldName;
  
  if (value === undefined || value === null || value === '') {
    return {
      field: fieldName,
      message: `${label} is required`
    };
  }
  return null;
}

// Validate numeric values
export function validateNumeric(value: unknown, fieldName: string, displayName?: string): ValidationError | null {
  const label = displayName || fieldName;
  
  if (value === undefined || value === null || value === '') {
    return null; // Skip validation if empty (use validateRequired for required fields)
  }
  
  const numValue = parseFloat(value as string);
  if (isNaN(numValue)) {
    return {
      field: fieldName,
      message: `${label} must be a valid number`
    };
  }
  return null;
}

// Validate positive numbers
export function validatePositive(value: unknown, fieldName: string, displayName?: string): ValidationError | null {
  const label = displayName || fieldName;
  
  if (value === undefined || value === null || value === '') {
    return null; // Skip validation if empty
  }
  
  const numValue = parseFloat(value as string);
  if (isNaN(numValue)) {
    return {
      field: fieldName,
      message: `${label} must be a valid number`
    };
  }
  
  if (numValue <= 0) {
    return {
      field: fieldName,
      message: `${label} must be greater than zero`
    };
  }
  
  return null;
}

// Validate email format
export function validateEmail(value: unknown, fieldName: string, displayName?: string): ValidationError | null {
  const label = displayName || fieldName;
  
  if (value === undefined || value === null || value === '') {
    return null; // Skip validation if empty
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(value as string)) {
    return {
      field: fieldName,
      message: `${label} must be a valid email address`
    };
  }
  
  return null;
}

// Validate date
export function validateDate(value: unknown, fieldName: string, displayName?: string): ValidationError | null {
  const label = displayName || fieldName;
  
  if (value === undefined || value === null || value === '') {
    return null; // Skip validation if empty
  }
  
  if (!(value instanceof Date) || isNaN(value.getTime())) {
    return {
      field: fieldName,
      message: `${label} must be a valid date`
    };
  }
  
  return null;
}

// Validate invoice basic data
export function validateBasicData(data: unknown): ValidationResult {
  const errors: ValidationError[] = [];
  const dataObj = data as Record<string, unknown>;
  
  // Required fields
  const requiredFields = [
    { field: 'invoiceNumber', label: 'Invoice number' },
    { field: 'status', label: 'Status' }
  ];
  
  requiredFields.forEach(({ field, label }) => {
    const error = validateRequired(dataObj[field], field, label);
    if (error) errors.push(error);
  });
  
  // Numeric fields
  const numericFields = [
    { field: 'amount', label: 'Amount' }
  ];
  
  numericFields.forEach(({ field, label }) => {
    const error = validateNumeric(dataObj[field], field, label);
    if (error) errors.push(error);
  });
  
  // Date fields
  const dateFields = [
    { field: 'issueDate', label: 'Issue date' },
    { field: 'dueDate', label: 'Due date' }
  ];
  
  dateFields.forEach(({ field, label }) => {
    const error = validateDate(dataObj[field], field, label);
    if (error) errors.push(error);
  });
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// Validate line items
export function validateLineItems(lineItems: unknown[]): ValidationResult {
  const errors: ValidationError[] = [];
  
  if (!lineItems || lineItems.length === 0) {
    errors.push({
      field: 'lineItems',
      message: 'At least one line item is required'
    });
    return { isValid: false, errors };
  }
  
  lineItems.forEach((item, index) => {
    const itemObj = item as Record<string, unknown>;
    
    // Required fields for each line item
    const requiredFields = [
      { field: 'description', label: `Item ${index + 1} description` }
    ];
    
    requiredFields.forEach(({ field, label }) => {
      const error = validateRequired(itemObj[field], `lineItems[${index}].${field}`, label);
      if (error) errors.push(error);
    });
    
    // Numeric fields for each line item
    const numericFields = [
      { field: 'quantity', label: `Item ${index + 1} quantity` },
      { field: 'unitPrice', label: `Item ${index + 1} unit price` },
      { field: 'totalPrice', label: `Item ${index + 1} total price` }
    ];
    
    numericFields.forEach(({ field, label }) => {
      const error = validateNumeric(itemObj[field], `lineItems[${index}].${field}`, label);
      if (error) errors.push(error);
    });
    
    // Positive fields for each line item
    const positiveFields = [
      { field: 'quantity', label: `Item ${index + 1} quantity` },
      { field: 'unitPrice', label: `Item ${index + 1} unit price` }
    ];
    
    positiveFields.forEach(({ field, label }) => {
      const error = validatePositive(itemObj[field], `lineItems[${index}].${field}`, label);
      if (error) errors.push(error);
    });
  });
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// Validate vendor info
export function validateVendorInfo(vendorInfo: unknown): ValidationResult {
  const errors: ValidationError[] = [];
  const vendorObj = vendorInfo as Record<string, unknown>;
  
  // Required fields
  const requiredFields = [
    { field: 'name', label: 'Vendor name' }
  ];
  
  requiredFields.forEach(({ field, label }) => {
    const error = validateRequired(vendorObj[field], `vendorInfo.${field}`, label);
    if (error) errors.push(error);
  });
  
  // Email validation
  const emailError = validateEmail(vendorObj.email, 'vendorInfo.email', 'Vendor email');
  if (emailError) errors.push(emailError);
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// Validate customer info
export function validateCustomerInfo(customerInfo: unknown): ValidationResult {
  const errors: ValidationError[] = [];
  const customerObj = customerInfo as Record<string, unknown>;
  
  // Email validation
  const emailError = validateEmail(customerObj.email, 'customerInfo.email', 'Customer email');
  if (emailError) errors.push(emailError);
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// Validate financial info
export function validateFinancialInfo(financialInfo: unknown): ValidationResult {
  const errors: ValidationError[] = [];
  const financeObj = financialInfo as Record<string, unknown>;
  
  // Numeric fields
  const numericFields = [
    { field: 'subtotal', label: 'Subtotal' },
    { field: 'tax', label: 'Tax' },
    { field: 'shipping', label: 'Shipping' },
    { field: 'discount', label: 'Discount' },
    { field: 'total', label: 'Total' }
  ];
  
  numericFields.forEach(({ field, label }) => {
    if (financeObj[field]) {
      const error = validateNumeric(financeObj[field], `financialInfo.${field}`, label);
      if (error) errors.push(error);
    }
  });
  
  return {
    isValid: errors.length === 0,
    errors
  };
}
