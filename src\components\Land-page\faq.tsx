"use client"

import * as React from "react"
import { motion, AnimatePresence } from "motion/react"
import { ChevronDown } from "lucide-react"
import { cn } from "@/lib/utils"

// Define types for the components
interface FaqItem {
  question: string;
  answer: string;
}

interface FaqSectionProps {
  title: string;
  description?: string;
  items: FaqItem[];
  className?: string;
}

interface FaqItemProps {
  question: string;
  answer: string;
  index: number;
}

// FaqSection Component
const FaqSection = React.forwardRef<HTMLElement, FaqSectionProps>(
  ({ className, title, description, items, ...props }, ref) => {
    return (
      <section
        ref={ref}
        className={cn("py-8 sm:py-12 md:py-16 w-full bg-black text-white overflow-hidden", className)}
        {...props}
      >
        <div className="container max-w-7xl mx-auto px-4 sm:px-5 md:px-6">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="mb-8 sm:mb-10 md:mb-16"
          >
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-semibold mb-3 sm:mb-4 text-white">
              {title}
            </h2>
            {description && <p className="text-sm sm:text-base text-gray-300 max-w-2xl">{description}</p>}
          </motion.div>

          {/* FAQ Items */}
          <div className="max-w-7xl mx-auto space-y-3 sm:space-y-4">
            {items.map((item, index) => (
              <FaqItem 
                key={index} 
                question={item.question} 
                answer={item.answer} 
                index={index} 
              />
            ))}
          </div>
        </div>
      </section>
    )
  }
)
FaqSection.displayName = "FaqSection"

// Internal FaqItem component
const FaqItem = React.forwardRef<HTMLDivElement, FaqItemProps>(
  ({ question, answer, index }, ref) => {
    const [isOpen, setIsOpen] = React.useState(false)

    return (
      <motion.div
        ref={ref}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.2, delay: index * 0.1 }}
        className={cn(
          "group rounded-lg sm:rounded-xl overflow-hidden",
          "transition-all duration-200 ease-in-out",
          "border border-neutral-600",
          isOpen ? "bg-neutral-900" : "hover:bg-neutral-900/50",
        )}
      >
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="w-full px-3 sm:px-4 md:px-6 py-4 sm:py-5 md:py-6 h-auto justify-between bg-black text-white flex items-center transition-colors duration-200 hover:bg-neutral-900/80 focus:outline-none focus:ring-2 focus:ring-primary rounded-lg"
        >
          <h3
            className={cn(
              "text-base sm:text-lg font-medium transition-colors duration-200 text-left break-words mr-2",
              "text-gray-300",
              isOpen && "text-white",
            )} 
          >
            {question}
          </h3>
          <motion.div
            animate={{
              rotate: isOpen ? 180 : 0,
              scale: isOpen ? 1.1 : 1,
            }}
            transition={{ duration: 0.2 }}
            className={cn(
              "p-0.5 rounded-full flex-shrink-0 ml-2 sm:ml-3",
              "transition-colors duration-200",
              isOpen ? "text-primary" : "text-muted-foreground",
            )}
          >
            <ChevronDown className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
          </motion.div>
        </button>
        <AnimatePresence initial={false}>
          {isOpen && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{
                height: "auto",
                opacity: 1,
                transition: { duration: 0.2, ease: "easeOut" },
              }}
              exit={{
                height: 0,
                opacity: 0,
                transition: { duration: 0.2, ease: "easeIn" },
              }}
            >
              <div className="px-3 sm:px-4 md:px-6 pb-4 sm:pb-5 md:pb-6 pt-0 sm:pt-1">
                <motion.p
                  initial={{ y: -10, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  exit={{ y: -10, opacity: 0 }}
                  className="text-sm sm:text-base text-gray-300 leading-relaxed break-words"
                >
                  {answer}
                </motion.p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    )
  }
)
FaqItem.displayName = "FaqItem"

// Main FAQ Component with example data
function FAQ() {
  // Example FAQ items
  const faqItems: FaqItem[] = [
    {
      question: "How does your AI invoice processing work?",
      answer:
        "Our AI-powered platform automatically reads, extracts, and organizes key data from invoices, eliminating the need for manual entry. Simply upload your invoice, and our AI processes it in real-time, providing you with structured data ready for analysis or export.",
    },
    {
      question: "Can I export invoice data to multiple file formats?",
      answer:
        "Yes! Our tool supports various export options, including Excel, PDF, and CSV formats, allowing you to seamlessly integrate the extracted data into your accounting systems or share it with your team.",
    },
    {
      question: "What types of invoices can your AI handle?",
      answer:
        "Our AI is designed to handle a wide range of invoice types, whether structured or unstructured. Whether it's a standard purchase invoice, utility bill, or custom format, our AI adapts to various invoice layouts and ensures accurate data extraction.",
    },
    {
      question: "How does the AI chatbot help with financial calculations?",
      answer:
        "Our intelligent AI chatbot is built to assist with a variety of financial tasks. From performing complex calculations to generating custom financial reports, it helps you make data-driven decisions quickly. Simply ask the chatbot for specific calculations, and it delivers results instantly.",
    },
    {
      question: "Is your platform easy to integrate with my existing systems?",
      answer:
        "Absolutely! Our platform is designed for seamless integration with popular accounting software and ERP systems. With our flexible export options and API capabilities, you can quickly sync the extracted data with your current tools, ensuring a smooth workflow with minimal disruption.",
    },
  ]

  return (
    <main className="py-6 sm:py-8 md:py-12 bg-black overflow-hidden">
      <FaqSection
        title="Frequently Asked Questions"
        description="Find answers to commonly asked questions about our platform and services."
        items={faqItems}
      />
    </main>
  )
}

// Export both components
export { FaqSection }
export default FAQ
