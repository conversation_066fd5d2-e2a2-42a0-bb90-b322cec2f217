'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FilePreview } from '@/components/dashboard/pages/upload/file-preview';
import { FileText, FileImage } from 'lucide-react';

interface InvoiceDocumentPreviewProps {
  invoice: {
    thumbnailUrl?: string | null;
    originalFileUrl?: string | null;
  };
  onPreviewClick?: () => void;
}

export function InvoiceDocumentPreview({ 
  invoice, 
  onPreviewClick 
}: InvoiceDocumentPreviewProps) {
  // Determine if we have an image to preview
  const hasPreviewImage = invoice.thumbnailUrl || invoice.originalFileUrl;

  // Get file type for preview
  const getFileType = (): string => {
    if (!invoice.originalFileUrl) return 'application/pdf';
    const url = invoice.originalFileUrl.toLowerCase();
    if (url.endsWith('.pdf')) return 'application/pdf';
    if (url.endsWith('.jpg') || url.endsWith('.jpeg')) return 'image/jpeg';
    if (url.endsWith('.png')) return 'image/png';
    return 'application/octet-stream';
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">Document Preview</CardTitle>
      </CardHeader>
      <CardContent>
        {hasPreviewImage ? (
          <div className="aspect-[3/4] relative border rounded-md overflow-hidden bg-muted/50">
            <FilePreview
              src={invoice.thumbnailUrl ?? invoice.originalFileUrl ?? ''}
              type={getFileType()}
              large={false}
            />
            {onPreviewClick && (
              <Button
                variant="secondary"
                size="sm"
                className="absolute bottom-2 right-2"
                onClick={onPreviewClick}
              >
                <FileImage className="mr-2 h-4 w-4" />
                View Full Size
              </Button>
            )}
          </div>
        ) : (
          <div className="aspect-[3/4] flex items-center justify-center border rounded-md bg-muted/50">
            <div className="text-center p-4">
              <FileText className="h-10 w-10 mx-auto text-muted-foreground" />
              <p className="mt-2 text-sm text-muted-foreground">
                No preview available
              </p>
            </div>
          </div>
        )}

        {invoice.originalFileUrl && (
          <Button
            variant="outline"
            className="w-full mt-4"
            onClick={() =>
              window.open(invoice.originalFileUrl || '', '_blank')
            }
          >
            <FileText className="mr-2 h-4 w-4" />
            Open Original Document
          </Button>
        )}
      </CardContent>
    </Card>
  );
}