'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MessageSquare, Zap, Calendar } from 'lucide-react';

interface ChatUsageLimitModalProps {
  isOpen: boolean;
  onClose: () => void;
  feature: 'chat' | 'upload';
  message: string;
  stats?: {
    chatUsage: number;
    invoiceUsage: number;
    chatLimit: number;
    invoiceLimit: number;
    daysUntilReset: number;
  };
}

export function ChatUsageLimitModal({
  isOpen,
  onClose,
  feature,
  message,
  stats,
}: ChatUsageLimitModalProps) {
  const [isUpgrading, setIsUpgrading] = useState(false);
  const router = useRouter();

  const handleUpgrade = () => {
    setIsUpgrading(true);
    // Redirect to subscription page with context
    const encodedMessage = encodeURIComponent(message);
    router.push(
      `/dashboard/subscription?limit_exceeded=${feature}&message=${encodedMessage}`
    );
  };

  const FeatureIcon = feature === 'chat' ? MessageSquare : Zap;
  const featureText =
    feature === 'chat' ? 'Chat Messages' : 'Invoice Uploads';
  const currentUsage =
    feature === 'chat' ? stats?.chatUsage : stats?.invoiceUsage;
  const limit =
    feature === 'chat' ? stats?.chatLimit : stats?.invoiceLimit;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-2 mb-2">
            <div className="p-2 rounded-full bg-destructive/10">
              <FeatureIcon className="h-5 w-5 text-destructive" />
            </div>
            <DialogTitle className="text-lg">
              {featureText} Limit Reached
            </DialogTitle>
          </div>
          <DialogDescription className="text-left space-y-3">
            <p>{message}</p>

            {stats && (
              <div className="space-y-2">
                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <span className="text-sm font-medium">
                    Current Usage
                  </span>
                  <Badge variant="secondary">
                    {currentUsage} / {limit}
                  </Badge>
                </div>

                {stats.daysUntilReset > 0 && (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Calendar className="h-4 w-4" />
                    <span>Resets in {stats.daysUntilReset} days</span>
                  </div>
                )}
              </div>
            )}
          </DialogDescription>
        </DialogHeader>

        <DialogFooter className="flex gap-2 sm:gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isUpgrading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleUpgrade}
            disabled={isUpgrading}
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
          >
            {isUpgrading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Redirecting...
              </>
            ) : (
              <>
                <Zap className="w-4 h-4 mr-2" />
                Upgrade Plan
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
