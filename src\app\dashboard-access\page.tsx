import DashboardRedirect from '@/components/dashboard/DashboardRedirect';

interface DashboardAccessPageProps {
  searchParams?: Promise<{
    return_url?: string;
  }>;
}

export default async function DashboardAccessPage({
  searchParams,
}: DashboardAccessPageProps) {
  const params = await searchParams;
  const returnUrl = params?.return_url || '/dashboard';

  return <DashboardRedirect returnUrl={returnUrl} />;
}
