"use client";

import { useState, useEffect } from "react";
import { Filter } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";


import { FinancialOverview } from "./financial-overview";
import { ExpenseTrends } from "./expense-trends";
import { VendorAnalysis } from "./vendor-analysis";
import { CategoryBreakdown } from "./category-breakdown";
import { InvoiceStatusAnalysis } from "./invoice-status-analysis";
import { DataFilters, type FilterValues } from "./data-filters";
import { AIInsights } from "./ai-insights";
import { RevenueAnalysisComponent } from "./revenue-analysis";
import { CashFlowAnalysisComponent } from "./cash-flow-analysis";
import { AccountsAnalysisComponent } from "./accounts-analysis";
import { use<PERSON>ear<PERSON><PERSON><PERSON><PERSON>, useRouter, usePathname } from "next/navigation";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { DateRangePicker } from "@/components/dashboard/dashboard-comp/date-range-picker";
import { DateRange } from "@/components/ui/date-range-picker";

// Define our own date range type with required fields
interface AnalyticsDateRange {
  from: Date;
  to: Date;
}

// Default date range constant
const defaultDateRange: AnalyticsDateRange = {
  from: new Date(2020, 0, 1), // Start from 2020
  to: new Date(2026, 11, 31), // End at 2026
};

export function AnalyticsContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  // Helper to convert DateRange to AnalyticsDateRange
  const getAnalyticsDateRange = (dateRange: DateRange): AnalyticsDateRange => ({
    from: dateRange.from || defaultDateRange.from,
    to: dateRange.to || defaultDateRange.to,
  });

  const [date, setDate] = useState<DateRange>({
    from: defaultDateRange.from,
    to: defaultDateRange.to
  });

  // Initialize filters from URL params
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<FilterValues>(() => {
    return {
      vendor: searchParams.get("vendor") || "all",
      category: searchParams.get("category") || "all",
      status: searchParams.get("status") || "all",
      search: searchParams.get("search") || "",
      amountRange: [
        0, // Always start from 0
        1000000, // Set a very high maximum to include all invoices
      ],
    };
  });

  // Initialize active tab from URL params
  const [activeTab, setActiveTab] = useState(
    searchParams.get("tab") || "overview"
  );

  // Update URL when filters or date change
  useEffect(() => {
    const params = new URLSearchParams();

    // Add date params
    params.set("from", (date.from || defaultDateRange.from).toISOString());
    params.set("to", (date.to || defaultDateRange.to).toISOString());

    // Add filter params
    if (filters.vendor !== "all") params.set("vendor", filters.vendor);
    if (filters.category !== "all") params.set("category", filters.category);
    if (filters.status !== "all") params.set("status", filters.status);
    if (filters.search) params.set("search", filters.search);
    params.set("minAmount", filters.amountRange[0].toString());
    params.set("maxAmount", filters.amountRange[1].toString());

    // Add active tab
    params.set("tab", activeTab);

    // Update URL
    router.replace(`${pathname}?${params.toString()}`);
  }, [date, filters, activeTab, pathname, router]);

  const handleFilterChange = (newFilters: FilterValues) => {
    setFilters(newFilters);
  };

  return (
    <div className="flex flex-col">
      <div className="flex flex-col space-y-4 p-8 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">Analytics</h2>
          <div className="flex items-center space-x-2">
            <DateRangePicker
              dateRange={date}
              onDateRangeChange={(range) => {
                if (range) {
                  setDate(range);
                } else {
                  setDate({
                    from: defaultDateRange.from,
                    to: defaultDateRange.to
                  });
                }
              }}
              placeholder="Select date range"
              className="w-[300px]"
            />
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="mr-2 h-4 w-4" />
              Filters
            </Button>
          </div>
        </div>

        {showFilters && (
          <DataFilters
            onFilterChange={handleFilterChange}
            initialFilters={filters}
          />
        )}

        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-4"
        >
          <TabsList className="grid w-full grid-cols-8">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="revenue">Revenue</TabsTrigger>
            <TabsTrigger value="expenses">Expenses</TabsTrigger>
            <TabsTrigger value="cashflow">Cash Flow</TabsTrigger>
            <TabsTrigger value="accounts">Accounts</TabsTrigger>
            <TabsTrigger value="vendors">Vendors</TabsTrigger>
            <TabsTrigger value="status">Status</TabsTrigger>
            <TabsTrigger value="insights">AI Insights</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <FinancialOverview dateRange={getAnalyticsDateRange(date)} filters={filters} />
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
              <Card className="col-span-4">
                <CardHeader>
                  <CardTitle>Expense Trends</CardTitle>
                  <CardDescription>
                    Monthly expense patterns over time
                  </CardDescription>
                </CardHeader>
                <CardContent className="h-[400px] p-0 pl-2 pr-4 pb-4">
                  <ExpenseTrends dateRange={getAnalyticsDateRange(date)} filters={filters} />
                </CardContent>
              </Card>
              <Card className="col-span-3">
                <CardHeader>
                  <CardTitle>Category Breakdown</CardTitle>
                  <CardDescription>
                    Distribution of expenses by category
                  </CardDescription>
                </CardHeader>
                <CardContent className="h-[400px] p-4 pb-0">
                  <CategoryBreakdown dateRange={getAnalyticsDateRange(date)} filters={filters} />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="revenue" className="space-y-4">
            <RevenueAnalysisComponent dateRange={getAnalyticsDateRange(date)} filters={filters} />
          </TabsContent>

          <TabsContent value="expenses" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Expense Trends</CardTitle>
                <CardDescription>
                  Monthly expense patterns over time
                </CardDescription>
              </CardHeader>
              <CardContent className="h-[500px] p-0 pl-2 pr-4 pb-4">
                <ExpenseTrends
                  dateRange={getAnalyticsDateRange(date)}
                  height={500}
                  filters={filters}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="cashflow" className="space-y-4">
            <CashFlowAnalysisComponent dateRange={getAnalyticsDateRange(date)} filters={filters} />
          </TabsContent>

          <TabsContent value="accounts" className="space-y-4">
            <AccountsAnalysisComponent dateRange={getAnalyticsDateRange(date)} filters={filters} />
          </TabsContent>

          <TabsContent value="vendors" className="space-y-4">
            <VendorAnalysis dateRange={getAnalyticsDateRange(date)} filters={filters} />
          </TabsContent>

          <TabsContent value="status" className="space-y-4">
            <InvoiceStatusAnalysis dateRange={getAnalyticsDateRange(date)} filters={filters} />
          </TabsContent>

          <TabsContent value="insights" className="space-y-4">
            <AIInsights dateRange={getAnalyticsDateRange(date)} filters={filters} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

export function AnalyticsDashboard() {
  return (
    <DashboardLayout>
      <AnalyticsContent />
    </DashboardLayout>
  );
}