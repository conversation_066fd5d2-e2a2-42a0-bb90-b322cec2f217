"use client";

import { useState } from "react";
import { InvoiceData } from "@/types/invoice";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import {
  Ta<PERSON>,
  TabsContent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger
} from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  AlertTriangle,
  AlertCircle,
  CheckCircle,
  Calendar,
  DollarSign,
  FileText,
  Link as LinkIcon,
  ShieldCheck,
  ShieldAlert,
  ShieldX,
  Clock,
  TrendingUp,
  FileBarChart,
  Banknote,
  Percent,
  BarChart3,
  Zap
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { formatCurrency } from "@/lib/utils";
import { cn } from "@/lib/utils";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface InvoiceAuditInsightsProps {
  invoice: Partial<InvoiceData>;
}

export function InvoiceAuditInsights({ invoice }: InvoiceAuditInsightsProps) {
  const [activeTab, setActiveTab] = useState("audit");

  // Extract enhanced data from meta field
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const enhancedData = (invoice.meta?.enhancedData || {}) as any;
  const auditData = invoice.meta?.audit;

  // Determine audit status badge
  const getAuditBadge = () => {
    if (!auditData?.status) return null;

    switch (auditData.status) {
      case "PASS":
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Badge className="bg-green-500 hover:bg-green-600 shadow-sm transition-all duration-200 transform hover:scale-105">
                  <ShieldCheck className="w-4 h-4 mr-1" /> Pass
                </Badge>
              </TooltipTrigger>
              <TooltipContent>
                <p>No significant issues detected</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      case "WARNING":
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Badge className="bg-yellow-500 hover:bg-yellow-600 shadow-sm transition-all duration-200 transform hover:scale-105">
                  <ShieldAlert className="w-4 h-4 mr-1" /> Warning
                </Badge>
              </TooltipTrigger>
              <TooltipContent>
                <p>Potential issues detected</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      case "FAIL":
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Badge className="bg-red-500 hover:bg-red-600 shadow-sm transition-all duration-200 transform hover:scale-105">
                  <ShieldX className="w-4 h-4 mr-1" /> Failed
                </Badge>
              </TooltipTrigger>
              <TooltipContent>
                <p>Critical issues detected</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      default:
        return null;
    }
  };

  return (
    <Card className="w-full overflow-hidden border-slate-200 dark:border-slate-700 shadow-md transition-all duration-300 hover:shadow-lg">
      <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/40 dark:to-indigo-950/40 border-b">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-xl flex items-center gap-2">
              <Zap className="h-5 w-5 text-blue-500" />
              AI-Powered Invoice Analysis
            </CardTitle>
            <CardDescription className="mt-1">
              Advanced insights and verification powered by artificial intelligence
            </CardDescription>
          </div>
          {getAuditBadge()}
        </div>
      </CardHeader>
      <CardContent className="p-5">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-3 mb-6 tabs-container p-1 rounded-lg">
            <TabsTrigger
              value="audit"
              className="tab-trigger flex items-center gap-1.5"
            >
              <ShieldAlert className="w-4 h-4" />
              Audit & Fraud
            </TabsTrigger>
            <TabsTrigger
              value="related"
              className="tab-trigger flex items-center gap-1.5"
            >
              <FileBarChart className="w-4 h-4" />
              Related Documents
            </TabsTrigger>
            <TabsTrigger
              value="payment"
              className="tab-trigger flex items-center gap-1.5"
            >
              <Banknote className="w-4 h-4" />
              Payment Prediction
            </TabsTrigger>
          </TabsList>

          <TabsContent value="audit" className="space-y-6 animate-in fade-in-50 duration-300">
            {auditData ? (
              <>
                <div className="bg-white dark:bg-slate-800 rounded-xl p-5 shadow-sm border border-slate-100 dark:border-slate-700">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-base font-semibold flex items-center gap-2">
                      <BarChart3 className="h-5 w-5 text-blue-500" />
                      Fraud Risk Score
                    </h3>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Badge
                            className={cn(
                              "text-white font-medium px-3 py-1 transition-all duration-200",
                              (auditData.fraudIndicators?.score || 0) > 70
                                ? "bg-red-500 hover:bg-red-600"
                                : (auditData.fraudIndicators?.score || 0) > 30
                                  ? "bg-yellow-500 hover:bg-yellow-600"
                                  : "bg-green-500 hover:bg-green-600"
                            )}
                          >
                            {auditData.fraudIndicators?.score || 0}/100
                          </Badge>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>
                            {(auditData.fraudIndicators?.score || 0) > 70
                              ? "High risk - Immediate attention required"
                              : (auditData.fraudIndicators?.score || 0) > 30
                                ? "Medium risk - Review recommended"
                                : "Low risk - No immediate action needed"}
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>

                  <div className="relative pt-1">
                    <div className="flex mb-2 items-center justify-between">
                      <div>
                        <span className="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400">
                          Low
                        </span>
                      </div>
                      <div className="text-right">
                        <span className="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400">
                          High
                        </span>
                      </div>
                    </div>
                    <div className="h-3 w-full overflow-hidden rounded-full bg-slate-100 dark:bg-slate-700 shadow-inner">
                      <div
                        className={cn(
                          "h-full transition-all duration-500 ease-out",
                          (auditData.fraudIndicators?.score || 0) > 70
                            ? "bg-gradient-to-r from-yellow-500 to-red-500"
                            : (auditData.fraudIndicators?.score || 0) > 30
                              ? "bg-gradient-to-r from-green-500 to-yellow-500"
                              : "bg-gradient-to-r from-green-400 to-green-500"
                        )}
                        style={{ width: `${auditData.fraudIndicators?.score || 0}%` }}
                      />
                    </div>
                  </div>

                  <div className="text-sm text-slate-500 dark:text-slate-400 mt-3">
                    {(auditData.fraudIndicators?.score || 0) > 70
                      ? "This invoice has a high risk score and requires immediate verification."
                      : (auditData.fraudIndicators?.score || 0) > 30
                        ? "This invoice has some potential issues that should be reviewed."
                        : "This invoice appears to be legitimate with no significant issues detected."}
                  </div>
                </div>

                <div className="bg-white dark:bg-slate-800 rounded-xl p-5 shadow-sm border border-slate-100 dark:border-slate-700">
                  <h3 className="text-base font-semibold flex items-center gap-2 mb-4">
                    <AlertCircle className="h-5 w-5 text-blue-500" />
                    Issues Detected
                  </h3>

                  {auditData.issues && auditData.issues.length > 0 ? (
                    <div className="space-y-3">
                      {auditData.issues.map((issue, index) => (
                        <div
                          key={index}
                          className={cn(
                            "p-4 rounded-lg text-sm border transition-all duration-200 hover:shadow-md",
                            issue.severity === "HIGH"
                              ? "bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800"
                              : issue.severity === "MEDIUM"
                                ? "bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800"
                                : "bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800"
                          )}
                        >
                          <div className="flex items-center gap-2 mb-1">
                            {issue.severity === "HIGH" ? (
                              <AlertCircle className="h-4 w-4 text-red-500" />
                            ) : issue.severity === "MEDIUM" ? (
                              <AlertTriangle className="h-4 w-4 text-yellow-500" />
                            ) : (
                              <AlertCircle className="h-4 w-4 text-blue-500" />
                            )}
                            <div className="font-medium">
                              {issue.type.replace(/_/g, " ")}
                            </div>
                            <Badge
                              className={cn(
                                "ml-auto",
                                issue.severity === "HIGH"
                                  ? "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 hover:bg-red-200"
                                  : issue.severity === "MEDIUM"
                                    ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400 hover:bg-yellow-200"
                                    : "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 hover:bg-blue-200"
                              )}
                            >
                              {issue.severity}
                            </Badge>
                          </div>
                          <p className="text-slate-600 dark:text-slate-300">{issue.description}</p>
                          {issue.affectedFields && issue.affectedFields.length > 0 && (
                            <div className="mt-2 text-xs text-slate-500 dark:text-slate-400">
                              <span className="font-medium">Affected fields:</span>{" "}
                              {issue.affectedFields.join(", ")}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-100 dark:border-green-800">
                      <CheckCircle className="h-12 w-12 mx-auto text-green-500 mb-2" />
                      <p className="text-green-800 dark:text-green-400 font-medium">No issues detected</p>
                      <p className="text-sm text-green-600 dark:text-green-500 mt-1">This invoice passed all verification checks</p>
                    </div>
                  )}
                </div>

                {auditData.taxCompliance && (
                  <div className="bg-white dark:bg-slate-800 rounded-xl p-5 shadow-sm border border-slate-100 dark:border-slate-700">
                    <h3 className="text-base font-semibold flex items-center gap-2 mb-3">
                      <Percent className="h-5 w-5 text-blue-500" />
                      Tax Compliance
                    </h3>
                    <Badge
                      className={cn(
                        "mb-3",
                        auditData.taxCompliance.status === "COMPLIANT"
                          ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                          : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                      )}
                    >
                      {auditData.taxCompliance.status}
                    </Badge>
                    <p className="text-sm text-slate-600 dark:text-slate-300">
                      {auditData.taxCompliance.details}
                    </p>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-12 bg-slate-50 dark:bg-slate-800/50 rounded-xl border border-slate-200 dark:border-slate-700">
                <FileBarChart className="h-16 w-16 mx-auto text-slate-400 mb-4" />
                <h3 className="text-lg font-medium text-slate-700 dark:text-slate-300">No audit data available</h3>
                <p className="text-sm text-slate-500 dark:text-slate-400 mt-2 max-w-md mx-auto">
                  This invoice hasn&apos;t been analyzed yet. Try uploading a new invoice to use advanced fraud detection features.
                </p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="related" className="animate-in fade-in-50 duration-300">
            {enhancedData.relatedDocuments && Array.isArray(enhancedData.relatedDocuments) && enhancedData.relatedDocuments.length > 0 ? (
              <div className="space-y-4">
                <div className="bg-white dark:bg-slate-800 rounded-xl p-5 shadow-sm border border-slate-100 dark:border-slate-700">
                  <h3 className="text-base font-semibold flex items-center gap-2 mb-4">
                    <FileText className="h-5 w-5 text-blue-500" />
                    Related Documents
                  </h3>

                  <div className="space-y-3">
                    {enhancedData.relatedDocuments.map((doc: {
                      id: string;
                      type: string;
                      documentNumber: string;
                      date?: string;
                      amount?: number;
                      currency?: string;
                      fileUrl?: string;
                    }, index: number) => (
                      <div
                        key={index}
                        className="bg-slate-50 dark:bg-slate-900 rounded-lg border border-slate-200 dark:border-slate-700 overflow-hidden transition-all duration-200 hover:shadow-md"
                      >
                        <div className="p-4 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
                          <div className="flex items-center">
                            {doc.type === 'PURCHASE_ORDER' ? (
                              <FileBarChart className="w-10 h-10 mr-3 text-indigo-500 bg-indigo-100 dark:bg-indigo-900/30 p-2 rounded-full" />
                            ) : doc.type === 'PAYMENT_RECEIPT' ? (
                              <DollarSign className="w-10 h-10 mr-3 text-green-500 bg-green-100 dark:bg-green-900/30 p-2 rounded-full" />
                            ) : doc.type === 'SHIPPING_DOCUMENT' ? (
                              <TrendingUp className="w-10 h-10 mr-3 text-blue-500 bg-blue-100 dark:bg-blue-900/30 p-2 rounded-full" />
                            ) : (
                              <FileText className="w-10 h-10 mr-3 text-blue-500 bg-blue-100 dark:bg-blue-900/30 p-2 rounded-full" />
                            )}
                            <div>
                              <div className="font-medium text-slate-900 dark:text-slate-100">{doc.type.replace(/_/g, " ")}</div>
                              <div className="text-sm text-slate-500 dark:text-slate-400 flex items-center gap-1 mt-1">
                                <span className="font-mono">{doc.documentNumber}</span>
                                {doc.date && (
                                  <>
                                    <span className="text-slate-300 dark:text-slate-600">•</span>
                                    <span>{new Date(doc.date).toLocaleDateString()}</span>
                                  </>
                                )}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-3 ml-auto">
                            {doc.amount !== undefined && doc.currency && (
                              <div className="text-right font-medium text-slate-700 dark:text-slate-300">
                                {formatCurrency(doc.amount, doc.currency)}
                              </div>
                            )}
                            {doc.fileUrl && (
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="rounded-full h-9 w-9 p-0 transition-all duration-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-900/30 dark:hover:text-blue-400"
                                      asChild
                                    >
                                      <a href={doc.fileUrl} target="_blank" rel="noopener noreferrer">
                                        <LinkIcon className="w-4 h-4" />
                                      </a>
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>View document</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-white dark:bg-slate-800 rounded-xl p-5 shadow-sm border border-slate-100 dark:border-slate-700">
                  <h3 className="text-base font-semibold flex items-center gap-2 mb-3">
                    <Clock className="h-5 w-5 text-blue-500" />
                    Document Timeline
                  </h3>

                  <div className="relative pl-6 border-l-2 border-slate-200 dark:border-slate-700 space-y-6 py-2">
                    {enhancedData.relatedDocuments
                      .filter((doc: {
                        id: string;
                        type: string;
                        documentNumber: string;
                        date?: string;
                        amount?: number;
                        currency?: string;
                        fileUrl?: string;
                      }) => doc.date)
                      .sort((a: { date?: string }, b: { date?: string }) =>
                        new Date(a.date!).getTime() - new Date(b.date!).getTime()
                      )
                      .map((doc: {
                        id: string;
                        type: string;
                        documentNumber: string;
                        date?: string;
                        amount?: number;
                        currency?: string;
                        fileUrl?: string;
                      }, index: number) => (
                        <div key={index} className="relative">
                          <div className="absolute -left-[25px] h-4 w-4 rounded-full bg-blue-500"></div>
                          <div className="text-xs text-slate-500 dark:text-slate-400 mb-1">
                            {new Date(doc.date!).toLocaleDateString()}
                          </div>
                          <div className="font-medium text-slate-800 dark:text-slate-200">
                            {doc.type.replace(/_/g, " ")}
                          </div>
                          <div className="text-sm text-slate-600 dark:text-slate-400">
                            {doc.documentNumber}
                            {doc.amount !== undefined && doc.currency && (
                              <span className="ml-2">({formatCurrency(doc.amount, doc.currency)})</span>
                            )}
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-12 bg-slate-50 dark:bg-slate-800/50 rounded-xl border border-slate-200 dark:border-slate-700">
                <FileText className="h-16 w-16 mx-auto text-slate-400 mb-4" />
                <h3 className="text-lg font-medium text-slate-700 dark:text-slate-300">No related documents found</h3>
                <p className="text-sm text-slate-500 dark:text-slate-400 mt-2 max-w-md mx-auto">
                  No related documents were found for this invoice. Related documents might include purchase orders, receipts, or shipping documents.
                </p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="payment" className="animate-in fade-in-50 duration-300">
            {enhancedData.paymentPrediction ? (
              <div className="space-y-4">
                <div className="bg-white dark:bg-slate-800 rounded-xl p-5 shadow-sm border border-slate-100 dark:border-slate-700">
                  <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-6">
                    <div className="flex items-start gap-4">
                      <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-full">
                        <Calendar className="w-8 h-8 text-blue-500" />
                      </div>
                      <div>
                        <h3 className="text-base font-semibold text-slate-900 dark:text-slate-100">Predicted Payment Date</h3>
                        <div className="text-xl font-bold text-blue-600 dark:text-blue-400 mt-1">
                          {new Date(enhancedData.paymentPrediction.predictedPayDate).toLocaleDateString(undefined, {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          })}
                        </div>
                        {enhancedData.paymentPrediction.historicalAvgDays && (
                          <div className="text-sm text-slate-500 dark:text-slate-400 mt-1">
                            Based on historical average of {enhancedData.paymentPrediction.historicalAvgDays} days
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex items-start gap-4">
                      <div className={cn(
                        "p-3 rounded-full",
                        enhancedData.paymentPrediction.riskLevel === "HIGH"
                          ? "bg-red-100 dark:bg-red-900/30"
                          : enhancedData.paymentPrediction.riskLevel === "MEDIUM"
                            ? "bg-yellow-100 dark:bg-yellow-900/30"
                            : "bg-green-100 dark:bg-green-900/30"
                      )}>
                        <ShieldAlert className={cn(
                          "w-8 h-8",
                          enhancedData.paymentPrediction.riskLevel === "HIGH"
                            ? "text-red-500"
                            : enhancedData.paymentPrediction.riskLevel === "MEDIUM"
                              ? "text-yellow-500"
                              : "text-green-500"
                        )} />
                      </div>
                      <div>
                        <h3 className="text-base font-semibold text-slate-900 dark:text-slate-100">Payment Risk</h3>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge className={cn(
                            "text-white font-medium px-3 py-1",
                            enhancedData.paymentPrediction.riskLevel === "HIGH"
                              ? "bg-red-500"
                              : enhancedData.paymentPrediction.riskLevel === "MEDIUM"
                                ? "bg-yellow-500"
                                : "bg-green-500"
                          )}>
                            {enhancedData.paymentPrediction.riskLevel}
                          </Badge>
                          <span className="text-sm text-slate-500 dark:text-slate-400">
                            {enhancedData.paymentPrediction.riskLevel === "HIGH"
                              ? "High risk of late payment"
                              : enhancedData.paymentPrediction.riskLevel === "MEDIUM"
                                ? "Moderate risk of delay"
                                : "Likely to be paid on time"}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-white dark:bg-slate-800 rounded-xl p-5 shadow-sm border border-slate-100 dark:border-slate-700">
                    <h3 className="text-base font-semibold flex items-center gap-2 mb-4">
                      <TrendingUp className="h-5 w-5 text-blue-500" />
                      Prediction Factors
                    </h3>

                    <ul className="space-y-3">
                      {enhancedData.paymentPrediction.factors.map((factor: string, index: number) => (
                        <li key={index} className="flex items-start gap-2">
                          <div className="bg-blue-100 dark:bg-blue-900/30 p-1 rounded-full mt-0.5">
                            <CheckCircle className="w-3 h-3 text-blue-600 dark:text-blue-400" />
                          </div>
                          <span className="text-slate-700 dark:text-slate-300">{factor}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="bg-white dark:bg-slate-800 rounded-xl p-5 shadow-sm border border-slate-100 dark:border-slate-700">
                    <h3 className="text-base font-semibold flex items-center gap-2 mb-4">
                      <Percent className="h-5 w-5 text-blue-500" />
                      Prediction Confidence
                    </h3>

                    <div className="flex items-center justify-between mb-2">
                      <div className="text-sm font-medium text-slate-600 dark:text-slate-400">Confidence Level</div>
                      <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                        {Math.round(enhancedData.paymentPrediction.confidence * 100)}%
                      </div>
                    </div>

                    <div className="h-4 w-full overflow-hidden rounded-full bg-slate-100 dark:bg-slate-700 shadow-inner">
                      <div
                        className="h-full bg-gradient-to-r from-blue-400 to-blue-600 transition-all duration-500 ease-out"
                        style={{ width: `${enhancedData.paymentPrediction.confidence * 100}%` }}
                      />
                    </div>

                    <div className="mt-4 text-sm text-slate-500 dark:text-slate-400">
                      {enhancedData.paymentPrediction.confidence > 0.8
                        ? "High confidence prediction based on strong historical patterns and reliable data points."
                        : enhancedData.paymentPrediction.confidence > 0.5
                          ? "Moderate confidence prediction with some uncertainty factors."
                          : "Low confidence prediction due to limited historical data or unusual patterns."}
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-12 bg-slate-50 dark:bg-slate-800/50 rounded-xl border border-slate-200 dark:border-slate-700">
                <DollarSign className="h-16 w-16 mx-auto text-slate-400 mb-4" />
                <h3 className="text-lg font-medium text-slate-700 dark:text-slate-300">No payment prediction available</h3>
                <p className="text-sm text-slate-500 dark:text-slate-400 mt-2 max-w-md mx-auto">
                  Payment prediction data is not available for this invoice. This feature uses AI to predict when an invoice is likely to be paid based on historical patterns.
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
