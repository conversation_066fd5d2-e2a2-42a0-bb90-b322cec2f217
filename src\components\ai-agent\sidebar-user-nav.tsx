"use client";
import { ChevronUp } from "lucide-react";
import Image from "next/image";
import { useClerk, useUser } from "@clerk/nextjs";
import { useTheme } from "next-themes";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";

export function SidebarUserNav() {
  const { setTheme, theme } = useTheme();
  const { signOut } = useClerk();
  const { user } = useUser();

  if (!user) return null;

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton className="data-[state=open]:bg-sidebar-accent bg-background/80 backdrop-blur-sm data-[state=open]:text-sidebar-accent-foreground h-12 rounded-xl border border-border/40 hover:border-border/80 transition-colors">
              <Image
                src={
                  user.imageUrl ||
                  `https://avatar.vercel.sh/${user.emailAddresses[0]?.emailAddress}`
                }
                alt={user.emailAddresses[0]?.emailAddress ?? "User Avatar"}
                width={28}
                height={28}
                className="rounded-full ring-2 ring-indigo-500/20 mr-2"
              />
              <span className="truncate font-medium">
                {user.emailAddresses[0]?.emailAddress}
              </span>
              <ChevronUp className="ml-auto text-muted-foreground" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            side="top"
            className="w-[--radix-popper-anchor-width] rounded-xl p-1 border-border/60 shadow-lg"
          >
            <DropdownMenuItem
              className="cursor-pointer rounded-lg p-2.5 hover:bg-accent/50"
              onSelect={() => setTheme(theme === "dark" ? "light" : "dark")}
            >
              {`Toggle ${theme === "light" ? "dark" : "light"} mode`}
            </DropdownMenuItem>
            <DropdownMenuSeparator className="my-1" />
            <DropdownMenuItem
              asChild
              className="rounded-lg p-2.5 hover:bg-accent/50 text-red-500 hover:text-red-600 dark:text-red-400 dark:hover:text-red-300"
            >
              <button
                type="button"
                className="w-full cursor-pointer text-left"
                onClick={() => {
                  signOut();
                }}
              >
                Sign out
              </button>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
