import { Webhook } from "svix";
import { NextRequest, NextResponse } from "next/server";
import { WebhookEvent } from "@clerk/nextjs/server";
import db from "@/db/db";
import { getUserIdFromClerkId } from "@/lib/actions/user";
import { Prisma } from "@prisma/client";

// Define interface for user data in webhook
interface ClerkWebhookUserData {
  id: string;
  first_name?: string | null;
  last_name?: string | null;
  image_url?: string | null;
  email_addresses?: {
    id: string;
    email_address: string;
  }[];
  primary_email_address_id?: string | null;
}

export async function POST(req: NextRequest) {
  // Get the headers from the request
  const svix_id = req.headers.get("svix-id") || "";
  const svix_timestamp = req.headers.get("svix-timestamp") || "";
  const svix_signature = req.headers.get("svix-signature") || "";

  // If there are no headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    return new Response("Error: Missing svix headers", {
      status: 400,
    });
  }

  // Get the body
  const payload = await req.json();
  const body = JSON.stringify(payload);

  // Create a new Svix instance with your webhook secret
  const wh = new Webhook(process.env.CLERK_WEBHOOK_SECRET || "");

  let evt: WebhookEvent;

  // Verify the webhook
  try {
    evt = wh.verify(body, {
      "svix-id": svix_id,
      "svix-timestamp": svix_timestamp,
      "svix-signature": svix_signature,
    }) as WebhookEvent;
  } catch {
    return new Response("Error verifying webhook", {
      status: 400,
    });
  }

  // Get the ID and type
  const eventType = evt.type;

  // Process the webhook based on the event type
  try {
    if (eventType === "user.created") {
      // New user created in Clerk, pass the user data from the webhook
      await syncUserFromWebhook(evt.data as ClerkWebhookUserData);
    } else if (eventType === "user.updated") {
      // User updated in Clerk, pass the user data from the webhook
      await syncUserFromWebhook(evt.data as ClerkWebhookUserData);
    } else if (eventType === "user.deleted") {
      // User deleted in Clerk, handle in database
      const clerkUserId = evt.data.id as string;
      const dbUserId = await getUserIdFromClerkId(clerkUserId);

      if (dbUserId) {
        // Soft delete or anonymize user data
        await db.user.update({
          where: { id: dbUserId },
          data: {
            status: "INACTIVE",
            email: `deleted-${dbUserId}@example.com`, // Anonymize email
            firstName: "Deleted",
            lastName: "User",
            profileImageUrl: null,
          },
        });
      }
    } else if (eventType === "organizationMembership.created") {
      // Use the auth-independent function to sync user data
      await syncUserFromWebhook(evt.data as ClerkWebhookUserData);
    }

    return NextResponse.json({ success: true });
  } catch {
    return NextResponse.json(
      { success: false, error: "Error processing webhook" },
      { status: 500 }
    );
  }
}

// Helper function to sync user from webhook data
async function syncUserFromWebhook(userData: ClerkWebhookUserData) {
  if (!userData || !userData.id) {
    throw new Error("Invalid user data in webhook");
  }
  
  try {
    // Extract email from webhook data
    const email = extractEmailFromUserData(userData);
    
    if (!email) {
      throw new Error("User has no email address in webhook data");
    }

    // First, check if the user already exists by clerkId
    let existingUser = await db.user.findUnique({
      where: { clerkId: userData.id },
    });

    // If not found by clerkId, check by email
    if (!existingUser) {
      existingUser = await db.user.findUnique({
        where: { email: email }
      });
    }

    if (existingUser) {
      // Update user data - ensure clerkId is set if found by email
      const updatedUser = await db.user.update({
        where: {
          id: existingUser.id,
        },
        data: {
          clerkId: userData.id, // Always update clerkId to ensure it's properly linked
          firstName: userData.first_name || existingUser.firstName,
          lastName: userData.last_name || existingUser.lastName,
          email: email || existingUser.email,
          profileImageUrl: userData.image_url || existingUser.profileImageUrl,
          lastActive: new Date(),
          status: "ACTIVE",
        },
      });
      
      return updatedUser;
    }

    // Create user if not exists
    const newUser = await db.user.create({
      data: {
        clerkId: userData.id,
        email: email,
        firstName: userData.first_name || "",
        lastName: userData.last_name || "",
        profileImageUrl: userData.image_url || "",
        role: "USER",
        lastActive: new Date(),
      },
    });
    
    return newUser;
  } catch (error) {
    // Handle specific errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      // P2002 is the code for unique constraint violation
      if (error.code === 'P2002') {
        try {
          const userEmail = extractEmailFromUserData(userData);
          // Find the user by email and update
          const existingUser = await db.user.findUnique({
            where: { email: userEmail }
          });
          
          if (existingUser) {
            const updatedUser = await db.user.update({
              where: { id: existingUser.id },
              data: {
                clerkId: userData.id,
                firstName: userData.first_name || existingUser.firstName,
                lastName: userData.last_name || existingUser.lastName,
                profileImageUrl: userData.image_url || existingUser.profileImageUrl,
                lastActive: new Date(),
                status: "ACTIVE",
              }
            });
            
            return updatedUser;
          }
        } catch {
          // Handle retry error
        }
      }
    }
    
    throw error;
  }
}

// Helper function to extract email from user data
function extractEmailFromUserData(userData: ClerkWebhookUserData): string {
  if (!userData.email_addresses || userData.email_addresses.length === 0) {
    return "";
  }
  
  const primaryEmailObj = userData.email_addresses.find(
    (email) => email.id === userData.primary_email_address_id
  );
  
  return primaryEmailObj?.email_address || 
         userData.email_addresses[0]?.email_address || 
         "";
}
