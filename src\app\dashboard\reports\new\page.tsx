"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  ArrowLeft,
  Save,
  BarChart3,
  Line<PERSON>hart,
  PieChart,
  Loader2,
} from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { createReport, generateReportData } from "@/lib/actions/reports";
import { toast } from "sonner";

// Define the form schema
// Define all report types as string literals to ensure consistency
const ReportTypes = {
  EXPENSES: 'EXPENSES',
  VENDOR_ANALYSIS: 'VENDOR_ANALYSIS',
  CATEGORY_ANALYSIS: 'CATEGORY_ANALYSIS',
  CASH_FLOW: 'CASH_FLOW',
  SALES: 'SALES',
  TAX: 'TAX',
  CUSTOM: 'CUSTOM',
  INVOICE_SUMMARY: 'INVOICE_SUMMARY',
  PROFIT_LOSS: 'PROFIT_LOSS',
  CATEGORY_BREAKDOWN: 'CATEGORY_BREAKDOWN',
  BALANCE_SHEET: 'BALANCE_SHEET',
};

const formSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters"),
  description: z.string().optional(),
  type: z.enum([
    ReportTypes.EXPENSES,
    ReportTypes.VENDOR_ANALYSIS,
    ReportTypes.CATEGORY_ANALYSIS,
    ReportTypes.CASH_FLOW,
    ReportTypes.SALES,
    ReportTypes.TAX,
    ReportTypes.CUSTOM,
    ReportTypes.INVOICE_SUMMARY,
    ReportTypes.PROFIT_LOSS,
    ReportTypes.CATEGORY_BREAKDOWN,
    ReportTypes.BALANCE_SHEET,
  ]),
  startDate: z.date(),
  endDate: z.date(),
  visualizationType: z.string(),
});

export default function NewReportPage() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  // Initialize the form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      type: ReportTypes.EXPENSES,
      startDate: new Date(new Date().setDate(1)), // First day of current month
      endDate: new Date(), // Today
      visualizationType: "bar",
    },
  });

  // Handle form submission
  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsSubmitting(true);

    try {
      // Create the report
      const dateRange = {
        startDate: values.startDate.toISOString(),
        endDate: values.endDate.toISOString(),
      };

      const report = await createReport({
        name: values.name,
        description: values.description,
        type: values.type,
        dateRange,
        visualizationType: values.visualizationType,
      });

      // Generate the report data
      await generateReportData(report.id);

      toast.success("Report created successfully");
      router.push(`/dashboard/reports/view/${report.id}`);
    } catch {
      toast.error("Failed to create report");
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <DashboardLayout>
      <div className="flex min-h-screen w-full flex-col">
        <header className="sticky top-0 z-10 border-b bg-background">
          <div className="flex h-16 items-center justify-between px-6">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => router.back()}
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <h1 className="text-xl font-semibold">Create New Report</h1>
            </div>

            <Button
              type="submit"
              form="report-form"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Create Report
                </>
              )}
            </Button>
          </div>
        </header>

        <main className="flex-1 space-y-6 p-6 md:p-8">
          <Form {...form}>
            <form
              id="report-form"
              onSubmit={form.handleSubmit(onSubmit)}
              className="space-y-8"
            >
              <Card>
                <CardHeader>
                  <CardTitle>Report Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid gap-6 sm:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Report Name</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter report name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="type"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Report Type</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select report type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem key="expenses" value={ReportTypes.EXPENSES}>
                                Expenses
                              </SelectItem>
                              <SelectItem key="vendor-analysis" value={ReportTypes.VENDOR_ANALYSIS}>
                                Vendor Analysis
                              </SelectItem>
                              <SelectItem key="category-analysis" value={ReportTypes.CATEGORY_ANALYSIS}>
                                Category Analysis
                              </SelectItem>
                              <SelectItem key="cash-flow" value={ReportTypes.CASH_FLOW}>
                                Cash Flow
                              </SelectItem>
                              <SelectItem key="sales" value={ReportTypes.SALES}>
                                Sales
                              </SelectItem>
                              <SelectItem key="tax" value={ReportTypes.TAX}>
                                Tax
                              </SelectItem>
                              <SelectItem key="custom" value={ReportTypes.CUSTOM}>
                                Custom
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter a description of this report"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Separator />

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Date Range</h3>
                    <div className="space-y-4">
                      <FormItem className="flex flex-col">
                        <FormLabel>Date Range</FormLabel>
                        <DateRangePicker
                          dateRange={{
                            from: form.watch("startDate"),
                            to: form.watch("endDate")
                          }}
                          onDateRangeChange={(range) => {
                            if (range?.from) form.setValue("startDate", range.from)
                            if (range?.to) form.setValue("endDate", range.to)
                          }}
                          placeholder="Select date range for report"
                          className="w-full"
                        />
                        {(form.formState.errors.startDate || form.formState.errors.endDate) && (
                          <FormMessage>
                            {form.formState.errors.startDate?.message || form.formState.errors.endDate?.message}
                          </FormMessage>
                        )}
                      </FormItem>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Visualization</h3>
                    <FormField
                      control={form.control}
                      name="visualizationType"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Tabs
                              value={field.value}
                              onValueChange={field.onChange}
                              className="w-full"
                            >
                              <TabsList className="grid w-full grid-cols-3">
                                <TabsTrigger value="bar" className="flex items-center gap-2">
                                  <BarChart3 className="h-4 w-4" />
                                  Bar Chart
                                </TabsTrigger>
                                <TabsTrigger value="line" className="flex items-center gap-2">
                                  <LineChart className="h-4 w-4" />
                                  Line Chart
                                </TabsTrigger>
                                <TabsTrigger value="pie" className="flex items-center gap-2">
                                  <PieChart className="h-4 w-4" />
                                  Pie Chart
                                </TabsTrigger>
                              </TabsList>
                            </Tabs>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>
            </form>
          </Form>
        </main>
      </div>
    </DashboardLayout>
  );
}
