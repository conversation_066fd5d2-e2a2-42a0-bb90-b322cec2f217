import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import db from '@/db/db';

export async function GET() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user from database
    const user = await db.user.findUnique({
      where: { clerkId: userId },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Get user's subscriptions with transaction history
    const subscriptions = await db.subscription.findMany({
      where: {
        userId: user.id,
      },
      include: {
        plan: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Transform subscription data into billing history format
    const billingHistory = subscriptions.map((subscription) => {
      const amount = subscription.plan.price
        ? (parseInt(subscription.plan.price) / 100).toFixed(2)
        : '0.00';

      return {
        id: subscription.id,
        date: subscription.createdAt,
        description: `${subscription.plan.name} - ${subscription.plan.interval || 'month'}ly subscription`,
        amount: `$${amount}`,
        status: subscription.status === 'active' ? 'Paid' : 'Pending',
        transactionId: subscription.paddleTransactionId,
        subscriptionId: subscription.paddleSubscriptionId,
        planName: subscription.plan.name,
        billingPeriod: subscription.plan.interval || 'month',
      };
    });

    return NextResponse.json({
      success: true,
      data: billingHistory,
    });
  } catch (error) {
    console.error('Error fetching billing history:', error);
    return NextResponse.json(
      { error: 'Failed to fetch billing history' },
      { status: 500 }
    );
  }
}
