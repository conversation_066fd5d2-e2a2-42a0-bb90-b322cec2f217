/**
 * Performance monitoring utilities for the Invoice Dashboard
 */

import React from 'react';

interface PerformanceMetrics {
  renderCount: number;
  lastRender: number;
  apiCalls: { timestamp: number; url: string; duration?: number }[];
  memoryUsage?: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics = {
    renderCount: 0,
    lastRender: Date.now(),
    apiCalls: [],
  };

  private startTime = Date.now();
  private renderThreshold = 100; // Alert if more than 100 renders in 10 seconds
  private apiCallThreshold = 20; // Alert if more than 20 API calls in 10 seconds

  /**
   * Track a component render
   */
  trackRender() {
    this.metrics.renderCount++;
    this.metrics.lastRender = Date.now();

    // Check for excessive rendering
    if (this.metrics.renderCount > this.renderThreshold) {
      // Removed console.warn for production
    }

    if (process.env.NODE_ENV === 'development') {
      // Removed console.log for production
    }
  }

  /**
   * Track an API call
   */
  trackApiCall(url: string, startTime?: number) {
    const timestamp = Date.now();
    const duration = startTime ? timestamp - startTime : undefined;

    this.metrics.apiCalls.push({ timestamp, url, duration });

    // Remove old API calls (older than 10 seconds)
    const tenSecondsAgo = timestamp - 10000;
    this.metrics.apiCalls = this.metrics.apiCalls.filter(
      call => call.timestamp > tenSecondsAgo
    );

    // Check for excessive API calls
    if (this.metrics.apiCalls.length > this.apiCallThreshold) {
      // Removed console.error for production
    }

    if (duration && duration > 3000) {
      // Removed console.warn for production
    }
  }

  /**
   * Track memory usage (if available)
   */
  trackMemoryUsage() {
    if ('memory' in performance) {
      const memory = (performance as unknown as { memory: { usedJSHeapSize: number } }).memory;
      this.metrics.memoryUsage = memory.usedJSHeapSize;

      // Alert if memory usage is high (> 100MB)
      if (memory.usedJSHeapSize > 100 * 1024 * 1024) {
        // Removed console.warn for production
      }
    }
  }

  /**
   * Get current metrics
   */
  getMetrics(): PerformanceMetrics & { uptime: number } {
    return {
      ...this.metrics,
      uptime: Date.now() - this.startTime,
    };
  }

  /**
   * Reset metrics
   */
  reset() {
    this.metrics = {
      renderCount: 0,
      lastRender: Date.now(),
      apiCalls: [],
    };
    this.startTime = Date.now();
  }

  /**
   * Start performance monitoring for a component
   */
  startMonitoring() {
    if (process.env.NODE_ENV === 'development') {
      // Removed console.log for production
    }

    // Monitor memory every 5 seconds
    const memoryInterval = setInterval(() => {
      this.trackMemoryUsage();
    }, 5000);

    // Return cleanup function
    return () => {
      clearInterval(memoryInterval);
      if (process.env.NODE_ENV === 'development') {
        // Removed console.log for production
      }
    };
  }
}

// Export singleton instance
export const performanceMonitor = new PerformanceMonitor();

/**
 * React hook for performance monitoring
 */
export function usePerformanceMonitor() {
  const [metrics, setMetrics] = React.useState(performanceMonitor.getMetrics());

  React.useEffect(() => {
    // Track initial render
    performanceMonitor.trackRender();

    // Start monitoring
    const cleanup = performanceMonitor.startMonitoring();

    // Update metrics every second
    const interval = setInterval(() => {
      setMetrics(performanceMonitor.getMetrics());
    }, 1000);

    return () => {
      cleanup();
      clearInterval(interval);
    };
  }, []);

  // Track every render
  React.useEffect(() => {
    performanceMonitor.trackRender();
  });

  return metrics;
}

/**
 * Higher-order component for performance monitoring
 */
export function withPerformanceMonitoring<P extends object>(
  Component: React.ComponentType<P>,
) {
  const WrappedComponent = (props: P) => {
    usePerformanceMonitor();
    return <Component {...props} />;
  };

  WrappedComponent.displayName = `withPerformanceMonitoring(${Component.displayName || Component.name})`;

  return WrappedComponent;
}

/**
 * Utility to wrap fetch calls with performance tracking
 */
export function monitoredFetch(url: string, options?: RequestInit) {
  const startTime = Date.now();

  performanceMonitor.trackApiCall(url, startTime);

  return fetch(url, options)
    .then(response => {
      performanceMonitor.trackApiCall(url, startTime);
      return response;
    })
    .catch(error => {
      performanceMonitor.trackApiCall(url, startTime);
      throw error;
    });
}

export default performanceMonitor;