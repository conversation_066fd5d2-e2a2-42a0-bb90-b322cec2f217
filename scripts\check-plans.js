const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkPlans() {
  try {
    const allPlans = await prisma.plan.findMany({
      select: {
        id: true,
        productName: true,
        name: true,
        chatLimit: true,
        invoiceLimit: true,
        price: true,
        paddlePriceId: true,
      },
    });

    return { success: true, plans: allPlans };
  } catch (error) {
    return { success: false, error: error.message };
  } finally {
    await prisma.$disconnect();
  }
}

// Run the check
checkPlans()
  .then((result) => {
    if (result.success) {
      process.exit(0);
    } else {
      process.exit(1);
    }
  })
  .catch((error) => {
    process.exit(1);
  });
