"use client";

import PricingPageComponent from "@/components/pricing/PricingPage";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

interface SubscriptionPlan {
  name: string;
  price: string;
  currency: string;
  billingInterval: string;
}

interface SubscriptionData {
  id?: string;
  status?: string;
  plan?: SubscriptionPlan;
  startDate?: string | null;
  endDate?: string | null;
  renewalDate?: string | null;
  isActive?: boolean;
  error?: string;
}

export default function LandingPricingSection() {
  const [subscription, setSubscription] = useState<SubscriptionData | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    fetch("/api/subscriptions/active")
      .then((res) => res.json())
      .then((data: SubscriptionData) => {
        setSubscription(data);
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
        setSubscription(null);
      });
  }, []);

  function handleGetStarted() {
    if (loading) return;
    if (!subscription || !subscription.isActive) {
      router.push("/pricing");
      return;
    }                                               
    toast(
      <div>
        <div className="font-semibold mb-1">You already have an active plan</div>
        <div className="text-sm">
          <div>Plan: <span className="font-medium">{subscription.plan?.name || "N/A"}</span></div>
          <div>Status: <span className="font-medium">{subscription.status || "N/A"}</span></div>
          <div>Renews: <span className="font-medium">{subscription.renewalDate ? new Date(subscription.renewalDate).toLocaleDateString() : 'N/A'}</span></div>
        </div>
      </div>,
      { duration: 6000 }
    );
  }

  return (
    <PricingPageComponent onGetStarted={handleGetStarted} />
  );
} 