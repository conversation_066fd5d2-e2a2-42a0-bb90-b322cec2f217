import { NextRequest, NextResponse } from 'next/server';

export async function GET() {
  return NextResponse.json({
    success: true,
    message: 'Paddle webhook endpoint is accessible',
    timestamp: new Date().toISOString(),
  });
}

export async function POST(req: NextRequest) {
  const body = await req.text();
  
  return NextResponse.json({
    success: true,
    message: 'Paddle webhook test endpoint received POST request',
    bodyLength: body.length,
    timestamp: new Date().toISOString(),
  });
} 