import { currentUser } from '@clerk/nextjs/server';
import { ArtifactKind } from '@/components/ai-agent/artifact';
import db from '@/db/db';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  if (!id || id.trim() === '') {
    return new Response('Missing or empty id', { status: 400 });
  }

  const user = await currentUser();

  if (!user) {
    return new Response('Unauthorized', { status: 401 });
  }

  // Get user from database by email
  const dbUser = await db.user.findUnique({
    where: { email: user.emailAddresses[0].emailAddress }
  });

  if (!dbUser) {
    return new Response('User not found', { status: 404 });
  }

  const documents = await db.document.findMany({
    where: { id }
  });

  const [document] = documents;

  if (!document) {
    return new Response('Not Found', { status: 404 });
  }

  if (document.userId !== dbUser.id) {
    return new Response('Unauthorized', { status: 401 });
  }

  return Response.json(documents, { status: 200 });
}

export async function POST(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  if (!id || id.trim() === '') {
    return new Response('Missing or empty id', { status: 400 });
  }

  const user = await currentUser();

  if (!user) {
    return new Response('Unauthorized', { status: 401 });
  }

  // Get user from database by email
  const dbUser = await db.user.findUnique({
    where: { email: user.emailAddresses[0].emailAddress }
  });

  if (!dbUser) {
    return new Response('User not found', { status: 404 });
  }

  const {
    content,
    title,
    kind,
  }: { content: string; title: string; kind: ArtifactKind } =
    await request.json();

  try {
    const document = await db.document.create({
      data: {
        id,
        content,
        title,
        kind,
        userId: dbUser.id,
        createdAt: new Date(),
      }
    });

    return Response.json(document, { status: 200 });
  } catch (error) {
    console.error('Failed to save document:', error);
    return new Response('Failed to save document', { status: 500 });
  }
}

export async function PATCH(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  const { timestamp }: { timestamp: string } = await request.json();

  if (!id || id.trim() === '') {
    return new Response('Missing or empty id', { status: 400 });
  }

  const user = await currentUser();

  if (!user) {
    return new Response('Unauthorized', { status: 401 });
  }

  // Get user from database by email
  const dbUser = await db.user.findUnique({
    where: { email: user.emailAddresses[0].emailAddress }
  });

  if (!dbUser) {
    return new Response('User not found', { status: 404 });
  }

  const documents = await db.document.findMany({
    where: { id }
  });

  const [document] = documents;

  if (!document) {
    return new Response('Document not found', { status: 404 });
  }

  if (document.userId !== dbUser.id) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    // Delete documents with the same ID created after the timestamp
    await db.document.deleteMany({
      where: {
        id,
        createdAt: {
          gt: new Date(timestamp)
        }
      }
    });

    return new Response('Deleted', { status: 200 });
  } catch (error) {
    console.error('Failed to delete documents:', error);
    return new Response('Failed to delete documents', { status: 500 });
  }
}
