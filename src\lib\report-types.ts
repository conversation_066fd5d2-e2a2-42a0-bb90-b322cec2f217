// Define report types (client-safe version)
export enum ReportType {
  EXPENSES = 'EXPENSES',
  VENDOR_ANALYSIS = 'VENDOR_ANALYSIS',
  CATEGORY_ANALYSIS = 'CATEGORY_ANALYSIS',
  CASH_FLOW = 'CASH_FLOW',
  SALES = 'SALES',
  TAX = 'TAX',
  CUSTOM = 'CUSTOM',
  // Keep old types for backward compatibility
  INVOICE_SUMMARY = 'INVOICE_SUMMARY',
  PROFIT_LOSS = 'PROFIT_LOSS',
  CATEGORY_BREAKDOWN = 'CATEGORY_BREAKDOWN',
  BALANCE_SHEET = 'BALANCE_SHEET',
}

// Define chart types (client-safe version)
export enum ChartType {
  BAR = 'BAR',
  LINE = 'LINE',
  PIE = 'PIE',
  STACKED_BAR = 'STACKED_BAR',
  AREA = 'AREA',
}

// Define schedule frequencies (client-safe version)
export enum ScheduleFrequency {
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY',
  QUARTERLY = 'QUARTERLY',
}

/**
 * Get a human-readable name for a report type
 */
export function getReportTypeName(reportType: ReportType | string): string {
  switch (reportType) {
    // New report types
    case ReportType.EXPENSES:
      return 'Expenses';
    case ReportType.VENDOR_ANALYSIS:
      return 'Vendor Analysis';
    case ReportType.CATEGORY_ANALYSIS:
      return 'Category Analysis';
    case ReportType.CASH_FLOW:
      return 'Cash Flow';
    case ReportType.SALES:
      return 'Sales';
    case ReportType.TAX:
      return 'Tax';
    case ReportType.CUSTOM:
      return 'Custom';

    // Old report types for backward compatibility
    case ReportType.INVOICE_SUMMARY:
      return 'Invoice Summary';
    case ReportType.PROFIT_LOSS:
      return 'Profit & Loss';
    case ReportType.CATEGORY_BREAKDOWN:
      return 'Category Breakdown';
    case ReportType.BALANCE_SHEET:
      return 'Balance Sheet';
    default:
      return 'Unknown Report Type';
  }
}

/**
 * Get a description for a report type
 */
export function getReportTypeDescription(reportType: ReportType | string): string {
  switch (reportType) {
    // New report types
    case ReportType.EXPENSES:
      return 'Overview of all expenses and spending patterns.';
    case ReportType.VENDOR_ANALYSIS:
      return 'Detailed analysis of spending by vendor.';
    case ReportType.CATEGORY_ANALYSIS:
      return 'Analysis of expenses broken down by category.';
    case ReportType.CASH_FLOW:
      return 'Analysis of cash inflows and outflows over time.';
    case ReportType.SALES:
      return 'Overview of sales performance and trends.';
    case ReportType.TAX:
      return 'Summary of tax-related expenses and liabilities.';
    case ReportType.CUSTOM:
      return 'Custom report with user-defined parameters.';

    // Old report types for backward compatibility
    case ReportType.INVOICE_SUMMARY:
      return 'A summary of all invoices, including totals, status, and trends.';
    case ReportType.PROFIT_LOSS:
      return 'Overview of revenue, expenses, and net profit/loss.';
    case ReportType.CATEGORY_BREAKDOWN:
      return 'Breakdown of expenses by category.';
    case ReportType.BALANCE_SHEET:
      return 'Summary of assets, liabilities, and equity.';
    default:
      return 'No description available.';
  }
}

/**
 * Get all available report types
 */
export function getAllReportTypes(): { value: ReportType; label: string; description: string }[] {
  // Only include the new report types
  const reportTypes = [
    ReportType.EXPENSES,
    ReportType.VENDOR_ANALYSIS,
    ReportType.CATEGORY_ANALYSIS,
    ReportType.CASH_FLOW,
    ReportType.SALES,
    ReportType.TAX,
    ReportType.CUSTOM,
  ];

  return reportTypes.map((type) => ({
    value: type,
    label: getReportTypeName(type),
    description: getReportTypeDescription(type),
  }));
}
