import { DocumentGenerationEngine } from './document-generation-engine';
import { ContextEngine } from './context-engine';
import { MemoryEngine } from './memory-engine';
import { LongTermMemory } from './memory/long-term-memory';
import { PredictionEngine } from './prediction-engine';
import { AnalyticsEngine } from './analytics-engine';
import { ActionEngine } from './action-engine';
import { CacheEngine } from './cache-engine';
import { PerformanceMonitor } from './performance-monitor';
import { selectOptimalModel } from './models';
import db from '@/db/db';

interface ChatRequest {
  userId: string;
  message: string;
  chatId: string;
  context?: {
    page?: string;
    previousMessages?: any[];
    userIntent?: string;
  };
}

interface ChatResponse {
  message: string;
  actions?: ActionResult[];
  documents?: GeneratedDocument[];
  suggestions?: string[];
  insights?: PredictiveInsight[];
  analytics?: any;
  metadata: {
    responseTime: number;
    confidence: number;
    model: string;
    tokensUsed: number;
  };
}

interface ActionResult {
  type: string;
  success: boolean;
  data?: any;
  message: string;
}

interface GeneratedDocument {
  id: string;
  type: string;
  format: string;
  buffer: Buffer;
  downloadUrl?: string;
  metadata: any;
}

interface PredictiveInsight {
  type: string;
  title: string;
  description: string;
  confidence: number;
  actionable: boolean;
  priority: string;
}

/**
 * Advanced BILLIX AI Agent v3.0
 * Intelligent chat interface with document generation, memory, and proactive insights
 */
export class BillixAIAgent {
  private static initialized = false;

  /**
   * Initialize the AI Agent system
   */
  static async initialize(): Promise<void> {
    if (this.initialized) return;

    console.log('🚀 Initializing BILLIX AI Agent v3.0...');
    
    // Initialize all subsystems
    await Promise.all([
      this.loadSystemPrompts(),
      this.initializePerformanceMonitoring()
    ]);

    this.initialized = true;
    console.log('✅ BILLIX AI Agent v3.0 initialized successfully');
  }

  /**
   * Process chat message with intelligent context and actions
   */
  static async processChat(request: ChatRequest): Promise<ChatResponse> {
    const startTime = Date.now();
    
    try {
      // Get comprehensive user context
      const userContext = await ContextEngine.getUserContext(
        request.userId,
        request.context?.page
      );

      // Analyze message for intent and actions
      const intent = this.analyzeIntent(request.message, userContext);
      
      // Select optimal model based on complexity
      const model = selectOptimalModel(request.message, userContext);

      // Process with intelligent routing
      const response = await this.processWithIntelligence(
        request,
        userContext,
        intent,
        model
      );

      // Record performance metrics
      const responseTime = Date.now() - startTime;
      PerformanceMonitor.recordMetrics({
        responseTime,
        tokenUsage: response.metadata.tokensUsed,
        cacheHitRate: 0.8, // Would be calculated
        modelUsed: model,
        success: true,
        userId: request.userId,
        queryType: intent.primary
      });

      return response;

    } catch (error) {
      console.error('Chat processing error:', error);
      
      // Return fallback response
      return {
        message: "I apologize, but I encountered an issue. Let me help you with your request in a different way.",
        metadata: {
          responseTime: Date.now() - startTime,
          confidence: 0.5,
          model: 'fallback',
          tokensUsed: 0
        }
      };
    }
  }

  /**
   * Generate documents based on user request
   */
  static async generateDocument(
    userId: string,
    request: {
      type: 'invoice' | 'report' | 'contract' | 'analysis' | 'dashboard';
      format: 'pdf' | 'excel' | 'image' | 'all';
      data?: any;
      customizations?: any;
    }
  ): Promise<GeneratedDocument[]> {
    return DocumentGenerationEngine.generateDocument(userId, {
      type: request.type,
      format: request.format,
      data: request.data || await this.getRelevantData(userId, request.type),
      options: {
        includeCharts: true,
        includeImages: true,
        includeBranding: true,
        quality: 'high',
        language: 'en',
        rtl: false,
        ...request.customizations
      }
    });
  }

  /**
   * Get proactive insights and suggestions
   */
  static async getInsights(userId: string): Promise<PredictiveInsight[]> {
    return PredictionEngine.generateInsights(userId);
  }

  /**
   * Get comprehensive analytics
   */
  static async getAnalytics(userId: string, period?: string): Promise<any> {
    return AnalyticsEngine.generateAdvancedAnalytics(userId);
  }

  /**
   * Process with intelligent routing and context
   */
  private static async processWithIntelligence(
    request: ChatRequest,
    userContext: any,
    intent: any,
    model: string
  ): Promise<ChatResponse> {
    let message = '';
    let actions: ActionResult[] = [];
    let documents: GeneratedDocument[] = [];
    let suggestions: string[] = [];
    let insights: PredictiveInsight[] = [];
    let analytics: any = null;

    // Handle different types of requests
    switch (intent.primary) {
      case 'document_generation':
        documents = await this.handleDocumentGeneration(request, intent);
        message = this.generateDocumentResponse(documents, intent);
        break;

      case 'data_query':
        const queryResult = await this.handleDataQuery(request, userContext, intent);
        message = queryResult.message;
        actions = queryResult.actions || [];
        break;

      case 'analytics_request':
        analytics = await this.handleAnalyticsRequest(request, userContext, intent);
        message = this.generateAnalyticsResponse(analytics, intent);
        break;

      case 'financial_help':
        const helpResult = await this.handleFinancialHelp(request, userContext, intent);
        message = helpResult.message;
        suggestions = helpResult.suggestions || [];
        insights = helpResult.insights || [];
        break;

      case 'invoice_inquiry':
        const invoiceResult = await this.handleInvoiceInquiry(request, userContext, intent);
        message = invoiceResult.message;
        suggestions = invoiceResult.suggestions || [];
        break;

      case 'financial_inquiry':
        const financialResult = await this.handleFinancialInquiry(request, userContext, intent);
        message = financialResult.message;
        suggestions = financialResult.suggestions || [];
        break;

      case 'general_conversation':
        message = await this.handleGeneralConversation(request, userContext, intent);
        break;

      default:
        message = await this.handleGenericRequest(request, userContext, intent);
    }

    // Get contextual suggestions if not already provided
    if (suggestions.length === 0) {
      suggestions = await this.generateContextualSuggestions(userContext, intent);
    }

    // Get proactive insights periodically
    if (this.shouldProvideInsights(userContext, intent)) {
      insights = await PredictionEngine.generateInsights(request.userId);
    }

    // Record conversation for learning
    await this.recordConversation(request, {
      message,
      actions,
      documents,
      suggestions,
      insights
    });

    return {
      message,
      actions,
      documents,
      suggestions,
      insights,
      analytics,
      metadata: {
        responseTime: 0, // Will be set by caller
        confidence: intent.confidence,
        model,
        tokensUsed: this.estimateTokenUsage(message)
      }
    };
  }

  /**
   * Analyze user intent from message
   */
  private static analyzeIntent(message: string, context: any): any {
    const lower = message.toLowerCase();
    
    // Document generation patterns
    if (lower.includes('generate') || lower.includes('create') || lower.includes('make')) {
      if (lower.includes('invoice')) {
        return { primary: 'document_generation', secondary: 'invoice', confidence: 0.9 };
      }
      if (lower.includes('report')) {
        return { primary: 'document_generation', secondary: 'report', confidence: 0.9 };
      }
      if (lower.includes('pdf') || lower.includes('excel') || lower.includes('image')) {
        return { primary: 'document_generation', secondary: 'generic', confidence: 0.8 };
      }
    }

    // Data query patterns
    if (lower.includes('show') || lower.includes('list') || lower.includes('find') || lower.includes('how many')) {
      if (lower.includes('invoice')) {
        return { primary: 'data_query', secondary: 'invoices', confidence: 0.9 };
      }
      return { primary: 'data_query', secondary: 'search', confidence: 0.8 };
    }

    // Analytics patterns
    if (lower.includes('analyze') || lower.includes('analytics') || lower.includes('trends') || lower.includes('summary')) {
      return { primary: 'analytics_request', secondary: 'analysis', confidence: 0.9 };
    }

    // Financial help patterns
    if (lower.includes('help') || lower.includes('advice') || lower.includes('suggest') || lower.includes('recommend')) {
      return { primary: 'financial_help', secondary: 'guidance', confidence: 0.7 };
    }

    // Invoice-specific questions
    if (lower.includes('invoice') && (lower.includes('what') || lower.includes('when') || lower.includes('who') || lower.includes('how'))) {
      return { primary: 'invoice_inquiry', secondary: 'details', confidence: 0.8 };
    }

    // Money/financial questions
    if (lower.includes('money') || lower.includes('revenue') || lower.includes('total') || lower.includes('financial')) {
      return { primary: 'financial_inquiry', secondary: 'status', confidence: 0.8 };
    }

    return { primary: 'general_conversation', secondary: 'chat', confidence: 0.6 };
  }

  /**
   * Handle document generation requests
   */
  private static async handleDocumentGeneration(
    request: ChatRequest,
    intent: any
  ): Promise<GeneratedDocument[]> {
    let documentType = intent.secondary;
    let format = 'pdf';
    let data: any = null;

    // Extract format from message
    if (request.message.toLowerCase().includes('excel')) format = 'excel';
    if (request.message.toLowerCase().includes('image')) format = 'image';
    if (request.message.toLowerCase().includes('all')) format = 'all';

    // Get relevant data based on document type
    switch (documentType) {
      case 'invoice':
        data = await this.getLatestInvoiceData(request.userId);
        break;
      case 'report':
        data = await this.getReportData(request.userId);
        break;
      default:
        data = await this.getGeneralFinancialData(request.userId);
    }

    return DocumentGenerationEngine.generateDocument(request.userId, {
      type: documentType,
      format: format as any,
      data,
      options: {
        includeCharts: true,
        includeImages: true,
        includeBranding: true,
        quality: 'high',
        language: 'en',
        rtl: false
      }
    });
  }

  /**
   * Handle data query requests
   */
  private static async handleDataQuery(
    request: ChatRequest,
    context: any,
    intent: any
  ): Promise<{ message: string; actions?: ActionResult[] }> {
    const actions = await ActionEngine.executeAction(
      intent.secondary || 'general',
      { query: request.message },
      request.userId
    );

    let message = '';
    if (actions.success) {
      message = this.formatDataResults([actions]);
    } else {
      message = "I couldn't find the specific data you requested. Could you please be more specific?";
    }

    return { message, actions: [actions] };
  }

  /**
   * Handle analytics requests
   */
  private static async handleAnalyticsRequest(
    request: ChatRequest,
    context: any,
    intent: any
  ): Promise<any> {
    return AnalyticsEngine.generateAdvancedAnalytics(request.userId);
  }

  /**
   * Handle financial help requests
   */
  private static async handleFinancialHelp(
    request: ChatRequest,
    context: any,
    intent: any
  ): Promise<{ message: string; suggestions?: string[]; insights?: PredictiveInsight[] }> {
    const insights = await PredictionEngine.generateInsights(request.userId);
    const analytics = await AnalyticsEngine.generateAdvancedAnalytics(request.userId);

    const message = this.generateFinancialAdvice(request.message, context, analytics, insights);
    const suggestions = this.generateActionableSuggestions(analytics, insights);

    return { message, suggestions, insights };
  }

  /**
   * Handle invoice-specific inquiries
   */
  private static async handleInvoiceInquiry(
    request: ChatRequest,
    context: any,
    intent: any
  ): Promise<{ message: string; suggestions?: string[] }> {
    const businessInfo = context.business;
    let message = '';
    const suggestions = [];

    if (request.message.toLowerCase().includes('how many')) {
      message = `You have ${businessInfo.totalInvoices} invoices in total. `;
      if (businessInfo.paidInvoices > 0) message += `${businessInfo.paidInvoices} are paid, `;
      if (businessInfo.pendingInvoices > 0) message += `${businessInfo.pendingInvoices} are pending, `;
      if (businessInfo.overdueInvoices > 0) message += `${businessInfo.overdueInvoices} are overdue.`;
      
      suggestions.push('Show me my recent invoices', 'Generate a financial report', 'Create a new invoice');
    } else if (request.message.toLowerCase().includes('latest') || request.message.toLowerCase().includes('recent')) {
      message = `Your most recent invoices include those from the past month. You have ${businessInfo.recentInvoices || 5} invoices created recently.`;
      suggestions.push('List all invoices', 'Generate invoice report', 'Show overdue invoices');
    } else if (request.message.toLowerCase().includes('overdue')) {
      message = businessInfo.overdueInvoices > 0 
        ? `You have ${businessInfo.overdueInvoices} overdue invoices that need attention. I recommend following up on these to improve cash flow.`
        : `Great news! You don't have any overdue invoices at the moment.`;
      suggestions.push('Show overdue invoices', 'Send payment reminders', 'Generate aging report');
    } else {
      message = `I can help you with any invoice-related questions. You currently have ${businessInfo.totalInvoices} invoices worth $${businessInfo.totalRevenue.toLocaleString()}.`;
      suggestions.push('List my invoices', 'Create new invoice', 'Generate invoice report');
    }

    return { message, suggestions };
  }

  /**
   * Handle financial status inquiries
   */
  private static async handleFinancialInquiry(
    request: ChatRequest,
    context: any,
    intent: any
  ): Promise<{ message: string; suggestions?: string[] }> {
    const businessInfo = context.business;
    let message = '';
    const suggestions = [];

    if (request.message.toLowerCase().includes('total') || request.message.toLowerCase().includes('revenue')) {
      message = `Your total revenue from invoices is $${businessInfo.totalRevenue.toLocaleString()}. `;
      message += `This comes from ${businessInfo.totalInvoices} invoices. `;
      
      if (businessInfo.monthlyTrend) {
        const trend = businessInfo.monthlyTrend > 0 ? 'up' : 'down';
        message += `Your monthly trend is ${trend} ${Math.abs(businessInfo.monthlyTrend)}%.`;
      }

      suggestions.push('Generate financial report', 'Show monthly analytics', 'Compare with last year');
    } else if (request.message.toLowerCase().includes('cash flow') || request.message.toLowerCase().includes('money')) {
      const paidAmount = businessInfo.paidAmount || 0;
      const pendingAmount = businessInfo.totalRevenue - paidAmount;
      
      message = `Your cash flow status: $${paidAmount.toLocaleString()} received, $${pendingAmount.toLocaleString()} pending. `;
      
      if (businessInfo.overdueInvoices > 0) {
        message += `${businessInfo.overdueInvoices} invoices are overdue and need attention.`;
        suggestions.push('Follow up on overdue invoices', 'Send payment reminders');
      }

      suggestions.push('Generate cash flow report', 'View payment trends', 'Set up automated reminders');
    } else {
      message = `Your financial overview: ${businessInfo.totalInvoices} invoices worth $${businessInfo.totalRevenue.toLocaleString()}. `;
      message += `${businessInfo.paidInvoices} are paid, ${businessInfo.pendingInvoices} pending, ${businessInfo.overdueInvoices} overdue.`;
      
      suggestions.push('Detailed financial report', 'Monthly summary', 'Trend analysis');
    }

    return { message, suggestions };
  }

  /**
   * Handle general conversation
   */
  private static async handleGeneralConversation(
    request: ChatRequest,
    context: any,
    intent: any
  ): Promise<string> {
    // Use the user's business context to provide personalized responses
    const businessInfo = context.business;
    const greeting = this.getPersonalizedGreeting(context.user);

    if (request.message.toLowerCase().includes('hello') || request.message.toLowerCase().includes('hi')) {
      const insights = [];
      if (businessInfo.totalInvoices > 0) {
        insights.push(`you have ${businessInfo.totalInvoices} invoices totaling $${businessInfo.totalRevenue.toLocaleString()}`);
      }
      if (businessInfo.pendingInvoices > 0) {
        insights.push(`${businessInfo.pendingInvoices} are still pending`);
      }
      if (businessInfo.overdueInvoices > 0) {
        insights.push(`${businessInfo.overdueInvoices} are overdue`);
      }

      const contextInfo = insights.length > 0 ? `. I can see ${insights.join(', ')}` : '';
      return `${greeting}! I'm your AI financial assistant${contextInfo}. How can I help you today?`;
    }

    // Handle document creation requests
    if (request.message.toLowerCase().includes('create') || request.message.toLowerCase().includes('generate')) {
      if (request.message.toLowerCase().includes('invoice')) {
        return `I can help you create an invoice! Would you like me to generate a PDF, Excel, or image version? I can use your latest invoice data or you can provide specific details.`;
      }
      if (request.message.toLowerCase().includes('report')) {
        return `I'd be happy to generate a financial report for you. I can create PDF reports with charts, Excel dashboards with pivot tables, or visual summaries. What type of report would you like?`;
      }
    }

    // Handle questions about invoices
    if (request.message.toLowerCase().includes('invoice')) {
      if (businessInfo.totalInvoices === 0) {
        return `You don't have any invoices yet. Would you like me to help you create your first invoice? I can generate it in PDF, Excel, or image format.`;
      }
      return `You have ${businessInfo.totalInvoices} invoices with a total value of $${businessInfo.totalRevenue.toLocaleString()}. ${businessInfo.paidInvoices} are paid, ${businessInfo.pendingInvoices} are pending, and ${businessInfo.overdueInvoices} are overdue. What specific information would you like about your invoices?`;
    }

    // Handle financial questions
    if (request.message.toLowerCase().includes('money') || request.message.toLowerCase().includes('financial')) {
      const advice = [];
      if (businessInfo.overdueInvoices > 0) {
        advice.push(`You have ${businessInfo.overdueInvoices} overdue invoices - I recommend following up on these`);
      }
      if (businessInfo.monthlyTrend && businessInfo.monthlyTrend < 0) {
        advice.push(`Your revenue trend is down ${Math.abs(businessInfo.monthlyTrend)}% this month`);
      }
      
      return advice.length > 0 
        ? `Here's your financial overview: ${advice.join('. ')}. Would you like me to generate a detailed report or help with any specific actions?`
        : `Your finances look good! You have $${businessInfo.totalRevenue.toLocaleString()} in total invoices. I can help you generate reports, create new invoices, or analyze trends. What would you like to do?`;
    }

    return `I'm here to help with your financial management. Based on your account, I can assist with invoices, reports, analytics, and document generation. What would you like to work on?`;
  }

  /**
   * Handle generic requests
   */
  private static async handleGenericRequest(
    request: ChatRequest,
    context: any,
    intent: any
  ): Promise<string> {
    return `I understand you're asking about "${request.message}". I can help you with financial data, invoice management, report generation, and business analytics. Could you provide more details about what you need?`;
  }

  // Helper methods for response generation
  private static generateDocumentResponse(documents: GeneratedDocument[], intent: any): string {
    if (documents.length === 0) {
      return "I wasn't able to generate the document you requested. Please check your data and try again, or let me know if you need help with the format or content.";
    }

    const docTypes = documents.map(d => d.type).join(', ');
    const formats = documents.map(d => d.format).join(', ');

    let response = `Great! I've generated your ${docTypes} document(s) in ${formats} format(s). `;
    
    if (documents.length === 1) {
      const doc = documents[0];
      response += `The ${doc.format.toUpperCase()} file is ready for download`;
      if (doc.metadata.pages) {
        response += ` (${doc.metadata.pages} pages)`;
      }
      if (doc.metadata.sheets) {
        response += ` (${doc.metadata.sheets} sheets)`;
      }
      response += '.';
    } else {
      response += `All ${documents.length} files are ready for download.`;
    }

    // Add helpful suggestions based on document type
    if (intent.secondary === 'invoice') {
      response += ' You can now send this invoice to your client, save it for your records, or generate additional formats if needed.';
    } else if (intent.secondary === 'report') {
      response += ' This report includes your latest financial data with charts and summaries for easy analysis.';
    }

    return response;
  }

  private static generateAnalyticsResponse(analytics: any, intent: any): string {
    if (!analytics) return "I couldn't generate analytics at this time.";

    const insights = [];
    if (analytics.financialHealth) {
      insights.push(`Your financial health score is ${Math.round(analytics.financialHealth.score * 100)}/100 (${analytics.financialHealth.status})`);
    }
    if (analytics.businessInsights?.length > 0) {
      insights.push(`Key insight: ${analytics.businessInsights[0].description}`);
    }

    return insights.length > 0 
      ? `Here's your analytics summary:\n\n${insights.join('\n')}`
      : "I've generated your analytics report with comprehensive insights.";
  }

  private static generateFinancialAdvice(
    message: string,
    context: any,
    analytics: any,
    insights: PredictiveInsight[]
  ): string {
    const advice = [];
    
    if (analytics?.financialHealth?.status === 'poor') {
      advice.push("I notice some areas where your financial health could improve.");
    }
    
    if (insights.length > 0) {
      const highPriorityInsights = insights.filter(i => i.priority === 'high');
      if (highPriorityInsights.length > 0) {
        advice.push(`Important: ${highPriorityInsights[0].description}`);
      }
    }

    return advice.length > 0 
      ? advice.join(' ') 
      : "Based on your financial data, you're doing well. I can provide specific advice if you have particular questions.";
  }

  private static generateActionableSuggestions(analytics: any, insights: PredictiveInsight[]): string[] {
    const suggestions = [];
    
    if (insights.filter(i => i.actionable).length > 0) {
      suggestions.push("Review overdue invoices for potential cash flow improvement");
      suggestions.push("Generate monthly financial report");
      suggestions.push("Set up automated payment reminders");
    }

    return suggestions;
  }

  private static generateContextualSuggestions(context: any, intent: any): Promise<string[]> {
    const suggestions = [];
    
    // Based on current page
    if (context.system.currentPage === '/dashboard') {
      suggestions.push("Generate monthly report", "View analytics", "Check overdue invoices");
    } else if (context.system.currentPage === '/invoices') {
      suggestions.push("Create new invoice", "Export to Excel", "Analyze payment patterns");
    }

    return Promise.resolve(suggestions);
  }

  private static formatDataResults(actions: ActionResult[]): string {
    const results = actions.map(action => {
      if (action.type === 'invoice' && action.data) {
        return `Found ${action.data.length || 0} invoices`;
      }
      return action.message;
    });

    return results.join('. ');
  }

  private static getPersonalizedGreeting(user: any): string {
    const hour = new Date().getHours();
    const timeGreeting = hour < 12 ? 'Good morning' : hour < 17 ? 'Good afternoon' : 'Good evening';
    const name = user.name ? `, ${user.name.split(' ')[0]}` : '';
    return `${timeGreeting}${name}`;
  }

  private static shouldProvideInsights(context: any, intent: any): boolean {
    // Provide insights for financial help or if user hasn't seen them recently
    return intent.primary === 'financial_help' || Math.random() < 0.3;
  }

  private static estimateTokenUsage(message: string): number {
    // Rough estimation: ~1 token per 4 characters
    return Math.ceil(message.length / 4);
  }

  // Data retrieval methods
  private static async getRelevantData(userId: string, type: string): Promise<any> {
    switch (type) {
      case 'invoice':
        return this.getLatestInvoiceData(userId);
      case 'report':
        return this.getReportData(userId);
      case 'dashboard':
        return this.getDashboardData(userId);
      default:
        return this.getGeneralFinancialData(userId);
    }
  }

  private static async getLatestInvoiceData(userId: string): Promise<any> {
    const invoice = await db.invoice.findFirst({
      where: { userId },
      orderBy: { createdAt: 'desc' }
    });

    if (invoice) {
      return {
        invoiceNumber: invoice.invoiceNumber || `INV-${Date.now()}`,
        vendorName: invoice.vendorName || 'Sample Vendor',
        amount: invoice.amount || 1000,
        issueDate: invoice.issueDate || new Date(),
        dueDate: invoice.dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        title: invoice.title || 'Professional services',
        status: invoice.status,
        lineItems: [
          {
            description: invoice.title || 'Professional services',
            quantity: 1,
            unitPrice: invoice.amount || 1000,
            totalPrice: invoice.amount || 1000
          }
        ]
      };
    }

    return {
      invoiceNumber: 'SAMPLE-001',
      vendorName: 'Sample Vendor',
      amount: 1000,
      issueDate: new Date(),
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      title: 'Sample Invoice',
      lineItems: [
        {
          description: 'Sample service',
          quantity: 1,
          unitPrice: 1000,
          totalPrice: 1000
        }
      ]
    };
  }

  private static async getReportData(userId: string): Promise<any> {
    const invoices = await db.invoice.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: 50
    });

    return {
      title: 'Monthly Financial Report',
      period: new Date().toISOString().substring(0, 7),
      summary: {
        totalInvoices: invoices.length,
        totalAmount: invoices.reduce((sum, inv) => sum + (inv.amount || 0), 0),
        paidCount: invoices.filter(inv => inv.status === 'PAID').length,
        pendingCount: invoices.filter(inv => inv.status === 'PENDING').length
      },
      invoices
    };
  }

  private static async getDashboardData(userId: string): Promise<any> {
    const invoices = await db.invoice.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' }
    });

    const thisMonth = new Date();
    thisMonth.setDate(1);
    thisMonth.setHours(0, 0, 0, 0);

    const thisMonthInvoices = invoices.filter(inv => inv.createdAt >= thisMonth);
    const lastMonthInvoices = invoices.filter(inv => {
      const lastMonth = new Date(thisMonth);
      lastMonth.setMonth(lastMonth.getMonth() - 1);
      return inv.createdAt >= lastMonth && inv.createdAt < thisMonth;
    });

    return {
      title: 'Financial Dashboard',
      period: new Date().toISOString().substring(0, 7),
      overview: {
        totalInvoices: invoices.length,
        totalRevenue: invoices.reduce((sum, inv) => sum + (inv.amount || 0), 0),
        thisMonth: {
          count: thisMonthInvoices.length,
          revenue: thisMonthInvoices.reduce((sum, inv) => sum + (inv.amount || 0), 0)
        },
        lastMonth: {
          count: lastMonthInvoices.length,
          revenue: lastMonthInvoices.reduce((sum, inv) => sum + (inv.amount || 0), 0)
        }
      },
      statusBreakdown: {
        paid: invoices.filter(inv => inv.status === 'PAID').length,
        pending: invoices.filter(inv => inv.status === 'PENDING').length,
        overdue: invoices.filter(inv => inv.status === 'OVERDUE').length
      },
      recentInvoices: invoices.slice(0, 10)
    };
  }

  private static async getGeneralFinancialData(userId: string): Promise<any> {
    const invoices = await db.invoice.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' }
    });

    return {
      totalRevenue: invoices.reduce((sum, inv) => sum + (inv.amount || 0), 0),
      invoiceCount: invoices.length,
      recentInvoices: invoices.slice(0, 10)
    };
  }

  private static async recordConversation(
    request: ChatRequest,
    response: any
  ): Promise<void> {
    try {
      await LongTermMemory.recordUserInteraction(request.userId, {
        page: request.context?.page || 'chat',
        action: 'chat_message',
        context: {
          message: request.message,
          intent: response.intent,
          actions: response.actions?.length || 0,
          documents: response.documents?.length || 0
        },
        outcome: 'success',
        duration: 0
      });
    } catch (error) {
      console.error('Failed to record conversation:', error);
    }
  }

  private static async loadSystemPrompts(): Promise<void> {
    // Load and cache system prompts
  }

  private static async initializePerformanceMonitoring(): Promise<void> {
    // Initialize performance monitoring
  }
}
