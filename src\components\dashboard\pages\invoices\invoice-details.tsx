'use client';

/* eslint-disable @typescript-eslint/no-explicit-any */

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { InvoiceStatusBadge } from '@/components/dashboard/pages/invoices/invoice-status-badge';
// formatCurrency and formatDate are now used in the smaller components
import {
  ArrowLeft,
  Download,
  Edit,
  FolderPlus,
  Sparkles,
} from 'lucide-react';
import { InvoiceAuditInsights } from '@/components/invoice/invoice-audit-insights';

// Import new smaller components
import { InvoiceSummaryCard } from '@/components/dashboard/pages/invoices/invoice-summary-card';
import { InvoiceLineItemsTable, UnifiedLineItem } from '@/components/dashboard/pages/invoices/invoice-line-items-table';
import { InvoiceDocumentPreview } from '@/components/dashboard/pages/invoices/invoice-document-preview';
import { InvoiceActionsCard } from '@/components/dashboard/pages/invoices/invoice-actions-card';
import { InvoiceMetadataCard } from '@/components/dashboard/pages/invoices/invoice-metadata-card';
import { InvoiceNotesCard } from '@/components/dashboard/pages/invoices/invoice-notes-card';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { FilePreview } from '@/components/dashboard/pages/upload/file-preview';
import {
  updateInvoiceStatus,
  deleteInvoice,
} from '@/actions/invoice-actions';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { DownloadOptions } from '@/components/dashboard/pages/invoices/download-options';
import { ExportDialog } from '@/components/dashboard/pages/invoices/export-dialog';
import { InvoiceEditForm } from '@/components/dashboard/pages/invoices/invoice-edit-form';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { InvoiceStatus } from '@/lib/types';
import { generateEnhancedData } from '@/lib/generate-enhanced-data';

// Import the unified LineItem from types
import { LineItem } from '@/types/invoice';

// Add back needed icons for tabs
import { Building, CreditCard, FileText } from 'lucide-react';

// Get file type for preview
const getFileType = (originalFileUrl?: string | null): string => {
  if (!originalFileUrl) return 'application/pdf';
  const url = originalFileUrl.toLowerCase();
  if (url.endsWith('.pdf')) return 'application/pdf';
  if (url.endsWith('.jpg') || url.endsWith('.jpeg')) return 'image/jpeg';
  if (url.endsWith('.png')) return 'image/png';
  return 'application/octet-stream';
};

// Utility function to render nested object fields robustly
function renderObjectFields(
  obj: Record<string, any>,
  parentKey?: string
) {
  return Object.entries(obj).map(([key, value]) => {
    // Compose a label, optionally with parent key for clarity
    const label =
      (parentKey ? parentKey + ' ' : '') +
      key.replace(/([A-Z])/g, ' $1').trim();

    if (value && typeof value === 'object' && !Array.isArray(value)) {
      // Recursively render nested objects, but skip empty objects
      const nested = renderObjectFields(value, label);
      if (!nested || nested.length === 0) return null;
      return (
        <div key={key} className="space-y-1 ml-4">
          <p className="text-xs text-muted-foreground capitalize">
            {label}:
          </p>
          {nested}
        </div>
      );
    }
    if (
      value !== null &&
      value !== undefined &&
      value !== '' &&
      typeof value !== 'object'
    ) {
      return (
        <div key={key} className="space-y-1">
          <p className="text-sm text-muted-foreground capitalize">
            {label}
          </p>
          <p className="font-medium">{String(value)}</p>
        </div>
      );
    }
    return null;
  });
}

interface ExtractedData {
  vendor?: Record<string, string | null>;
  customer?: Record<string, string | null>;
  financials?: Record<string, string | null>;
  payment?: Record<string, string | null>;
  notes?: string;
  termsAndConditions?: string;
  meta?: {
    language?: string;
    languageName?: string;
    confidence?: {
      overall: number;
    };
    audit?: {
      status: 'PASS' | 'WARNING' | 'FAIL';
      issues?: Array<{
        type: string;
        severity: 'LOW' | 'MEDIUM' | 'HIGH';
        description: string;
        affectedFields?: string[];
      }>;
      fraudIndicators?: {
        score: number;
        flags?: string[];
      };
      taxCompliance?: {
        status: 'COMPLIANT' | 'NON_COMPLIANT' | 'UNKNOWN';
        details?: string;
      };
    };
    enhancedData?: {
      auditStatus?: string;
      fraudScore?: number;
      relatedDocuments?: Array<{
        id: string;
        type: string;
        documentNumber: string;
        fileUrl?: string;
        date?: Date;
        amount?: number;
        currency?: string;
      }>;
      paymentPrediction?: {
        predictedPayDate: Date;
        confidence: number;
        factors: string[];
        historicalAvgDays?: number;
        riskLevel?: 'LOW' | 'MEDIUM' | 'HIGH';
      };
      exchangeRates?: Record<string, number>;
    };
  };
  [key: string]: unknown;
}

interface InvoiceForDisplay {
  id: string;
  invoiceNumber?: string | null;
  status: InvoiceStatus;
  issueDate?: Date | null;
  dueDate?: Date | null;
  amount?: number | null;
  currency?: string | null;
  category?: { id: string; name: string; color?: string } | null;
  vendorName?: string | null;
  thumbnailUrl?: string | null;
  originalFileUrl?: string | null;
  createdAt: Date;
  updatedAt: Date;
  notes?: string | null;
  lineItems?: LineItem[];
  extractedData?: ExtractedData;
  [key: string]: unknown;
}

interface InvoiceDetailsProps {
  invoice: InvoiceForDisplay;
}

export function InvoiceDetails({ invoice }: InvoiceDetailsProps) {
  const router = useRouter();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isImagePreviewOpen, setIsImagePreviewOpen] = useState(false);
  const [isDownloadOptionsOpen, setIsDownloadOptionsOpen] =
    useState(false);
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);
  const [isEditFormOpen, setIsEditFormOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [activeTab, setActiveTab] = useState('details');

  // Extract line items from the invoice data and convert to UnifiedLineItem
  const lineItems: UnifiedLineItem[] = (invoice.lineItems || []).map((item) => ({
    ...item,
    // Ensure backwards compatibility by mapping amount to totalPrice if needed
    totalPrice: item.totalPrice || item.amount,
  }));

  // Determine if we have an image to preview
  const hasPreviewImage =
    invoice.thumbnailUrl || invoice.originalFileUrl;

  // Prepare a complete invoice object for the edit form
  const invoiceDataForForm = {
    id: invoice.id,
    invoiceNumber:
      invoice.invoiceNumber === null
        ? undefined
        : invoice.invoiceNumber,
    status: invoice.status,
    issueDate: invoice.issueDate ?? undefined,
    dueDate: invoice.dueDate ?? undefined,
    amount: invoice.amount ?? undefined,
    currency: invoice.currency ?? undefined,
    vendorName: invoice.vendorName ?? undefined,
    notes: invoice.notes === null ? undefined : invoice.notes,
    category: invoice.category,
    lineItems: invoice.lineItems || [],
    extractedData: invoice.extractedData,
    createdAt: invoice.createdAt,
    updatedAt: invoice.updatedAt,
    // Include all other necessary fields
    originalFileUrl: invoice.originalFileUrl,
    thumbnailUrl: invoice.thumbnailUrl,
  };

  // Convert ExtractedData to a format compatible with InvoiceAuditInsights
  const convertToAuditInsightsData = () => {
    // Start with the basic invoice data
    const baseData = {
      vendor: invoice.extractedData?.vendor || {},
      customer: invoice.extractedData?.customer || {},
      financials: invoice.extractedData?.financials || {},
      payment: invoice.extractedData?.payment || {},
      invoiceNumber: invoice.invoiceNumber || undefined,
      date: invoice.issueDate
        ? invoice.issueDate.toISOString()
        : undefined,
      dueDate: invoice.dueDate
        ? invoice.dueDate.toISOString()
        : undefined,
    };

    // Generate enhanced data if it doesn't exist
    if (
      !invoice.extractedData?.meta?.enhancedData &&
      !invoice.extractedData?.meta?.audit
    ) {
      // Generate sample enhanced data
      const enhancedData = generateEnhancedData(
        invoice as unknown as Parameters<
          typeof generateEnhancedData
        >[0]
      );

      // Merge with base data
      return {
        ...baseData,
        meta: enhancedData.meta,
      };
    }

    // Use existing enhanced data if available
    return {
      ...baseData,
      meta: invoice.extractedData?.meta,
    };
  };

  // Handle status update
  const handleStatusUpdate = async (status: InvoiceStatus) => {
    try {
      setIsUpdating(true);
      await updateInvoiceStatus(invoice.id, status);
      toast.success(
        `Invoice has been marked as ${status.toLowerCase()}.`
      );
      router.refresh();
    } catch {
      toast.error('Failed to update invoice status.');
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle invoice deletion
  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      await deleteInvoice(invoice.id);
      toast.success('The invoice has been permanently deleted.');
      router.push('/dashboard/invoices');
    } catch {
      toast.error('Failed to delete the invoice.');
    } finally {
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
    }
  };

  // Render extracted data fields
  const renderExtractedData = () => {
    if (!invoice.extractedData) return null;

    // Filter out sections we already display elsewhere
    const excludedSections = [
      'vendor',
      'customer',
      'financials',
      'lineItems',
      'meta',
    ];

    return Object.entries(invoice.extractedData)
      .filter(
        ([key]) =>
          !excludedSections.includes(key) &&
          typeof invoice.extractedData?.[key] === 'object'
      )
      .map(([sectionKey, sectionData]) => (
        <Card key={sectionKey} className="mb-4">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg capitalize">
              {sectionKey.replace(/([A-Z])/g, ' $1').trim()}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {renderObjectFields(
                sectionData as Record<string, unknown>
              )}
            </div>
          </CardContent>
        </Card>
      ));
  };

  // Render dynamic line item fields
  const renderDynamicLineItemFields = () => {
    if (!lineItems.length) return null;

    // Get all unique keys from all line items
    const allKeys = new Set<string>();
    lineItems.forEach((item: LineItem) => {
      if (item.attributes) {
        Object.keys(item.attributes).forEach((key) =>
          allKeys.add(key)
        );
      }
    });

    // Filter out standard keys that we already display
    const standardKeys = [
      'description',
      'quantity',
      'unitPrice',
      'totalPrice',
      'taxRate',
      'taxAmount',
      'discount',
      'productSku',
    ];
    const dynamicKeys = Array.from(allKeys).filter(
      (key) => !standardKeys.includes(key)
    );

    if (dynamicKeys.length === 0) return null;

    return (
      <Card className="mb-4">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">
            Additional Line Item Details
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Item</TableHead>
                {dynamicKeys.map((key) => (
                  <TableHead key={key} className="capitalize">
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {lineItems.map((item: LineItem, index: number) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">
                    {item.description || `Item ${index + 1}`}
                  </TableCell>
                  {dynamicKeys.map((key) => (
                    <TableCell key={key}>
                      {item.attributes && item.attributes[key]
                        ? String(item.attributes[key])
                        : '-'}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" asChild>
            <Link href="/dashboard/invoices">
              <ArrowLeft className="h-4 w-4" />
              <span className="sr-only">Back to invoices</span>
            </Link>
          </Button>
          <h1 className="text-2xl font-bold tracking-tight">
            Invoice {invoice.invoiceNumber || invoice.id}
          </h1>
          <InvoiceStatusBadge status={invoice.status} />
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsDownloadOptionsOpen(true)}
          >
            <Download className="mr-2 h-4 w-4" />
            Download
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsExportDialogOpen(true)}
          >
            <FolderPlus className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button size="sm" onClick={() => setIsEditFormOpen(true)}>
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="details">Invoice Details</TabsTrigger>
          <TabsTrigger value="document">Document</TabsTrigger>
          <TabsTrigger value="extracted">Extracted Data</TabsTrigger>
          <TabsTrigger value="insights" className="flex items-center">
            <Sparkles className="mr-2 h-4 w-4" />
            AI Insights
          </TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-6 mt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2 space-y-6">
              {/* Invoice Summary Card */}
              <InvoiceSummaryCard invoice={invoice} />

              {/* Line Items */}
              <InvoiceLineItemsTable 
                lineItems={lineItems} 
                financials={invoice.extractedData?.financials}
              />

              {/* Notes and Additional Information */}
              <InvoiceNotesCard invoice={invoice} />

              {/* Dynamic Line Item Fields */}
              {renderDynamicLineItemFields()}
            </div>

            <div className="space-y-6">
              {/* Document Preview */}
              <InvoiceDocumentPreview
                invoice={invoice}
                onPreviewClick={() => setIsImagePreviewOpen(true)}
              />

              {/* Actions Card */}
              <InvoiceActionsCard
                invoice={invoice}
                isUpdating={isUpdating}
                onStatusUpdate={handleStatusUpdate}
                onShare={() => {
                  // TODO: Implement share functionality
                  console.log('Share invoice');
                }}
                onDelete={() => setIsDeleteDialogOpen(true)}
              />

              {/* Metadata Card */}
              <InvoiceMetadataCard invoice={invoice} />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="document" className="mt-6">
          <Card>
            <CardContent className="p-6">
              {hasPreviewImage ? (
                <div className="flex flex-col items-center">
                  <div className="w-full max-w-3xl border rounded-md overflow-hidden bg-muted/50">
                    <FilePreview
                      src={
                        invoice.originalFileUrl ??
                        invoice.thumbnailUrl ??
                        ''
                      }
                      type={getFileType(invoice.originalFileUrl)}
                      large={true}
                    />
                  </div>
                  <div className="flex gap-2 mt-4">
                    {invoice.originalFileUrl && (
                      <Button
                        variant="outline"
                        onClick={() =>
                          window.open(
                            invoice.originalFileUrl || '',
                            '_blank'
                          )
                        }
                      >
                        <FileText className="mr-2 h-4 w-4" />
                        Open Original
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      onClick={() => setIsDownloadOptionsOpen(true)}
                    >
                      <Download className="mr-2 h-4 w-4" />
                      Download
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-12">
                  <FileText className="h-16 w-16 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">
                    No document available
                  </h3>
                  <p className="text-muted-foreground mt-2">
                    This invoice doesn&apos;t have an associated
                    document.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="extracted" className="mt-6">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">
                Extracted Data
              </CardTitle>
            </CardHeader>
            <CardContent>
              {invoice.extractedData ? (
                <ScrollArea className="h-[600px] pr-4">
                  <div className="space-y-6">
                    {/* Vendor Information */}
                    {invoice.extractedData?.vendor && (
                      <div className="space-y-3">
                        <h3 className="text-md font-semibold flex items-center">
                          <Building className="mr-2 h-4 w-4" />
                          Vendor Information
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 bg-muted/30 p-4 rounded-md">
                          {renderObjectFields(
                            invoice.extractedData.vendor
                          )}
                        </div>
                      </div>
                    )}

                    {/* Customer Information */}
                    {invoice.extractedData?.customer && (
                      <div className="space-y-3">
                        <h3 className="text-md font-semibold flex items-center">
                          <Building className="mr-2 h-4 w-4" />
                          Customer Information
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 bg-muted/30 p-4 rounded-md">
                          {renderObjectFields(
                            invoice.extractedData.customer
                          )}
                        </div>
                      </div>
                    )}

                    {/* Financial Information */}
                    {invoice.extractedData?.financials && (
                      <div className="space-y-3">
                        <h3 className="text-md font-semibold flex items-center">
                          <CreditCard className="mr-2 h-4 w-4" />
                          Financial Information
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 bg-muted/30 p-4 rounded-md">
                          {renderObjectFields(
                            invoice.extractedData.financials
                          )}
                        </div>
                      </div>
                    )}

                    {/* Payment Information */}
                    {invoice.extractedData?.payment && (
                      <div className="space-y-3">
                        <h3 className="text-md font-semibold flex items-center">
                          <CreditCard className="mr-2 h-4 w-4" />
                          Payment Information
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 bg-muted/30 p-4 rounded-md">
                          {Object.entries(
                            invoice.extractedData.payment
                          ).map(([key, value]) =>
                            value &&
                            typeof value === 'object' &&
                            !Array.isArray(value) ? (
                              <div key={key} className="mb-2">
                                <p className="text-sm text-muted-foreground capitalize">
                                  {key
                                    .replace(/([A-Z])/g, ' $1')
                                    .trim()}
                                </p>
                                {renderObjectFields(value)}
                              </div>
                            ) : value && value !== '' ? (
                              <div key={key} className="mb-2">
                                <p className="text-sm text-muted-foreground capitalize">
                                  {key
                                    .replace(/([A-Z])/g, ' $1')
                                    .trim()}
                                </p>
                                <p className="font-medium">
                                  {String(value)}
                                </p>
                              </div>
                            ) : null
                          )}
                        </div>
                      </div>
                    )}

                    {/* Other Sections */}
                    {renderExtractedData()}
                  </div>
                </ScrollArea>
              ) : (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 mx-auto text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-medium">
                    No extracted data available
                  </h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    This invoice doesn&apos;t have any extracted data.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* AI Insights & Audit Analysis */}
            <InvoiceAuditInsights
              invoice={
                convertToAuditInsightsData() as Parameters<
                  typeof InvoiceAuditInsights
                >[0]['invoice']
              }
            />

            {/* Currency conversion functionality removed for performance optimization */}
          </div>

          {/* If no AI insights are available */}
          {!invoice.extractedData?.meta?.enhancedData &&
            !invoice.extractedData?.meta?.audit && (
              <div className="mt-6 text-center py-8 bg-muted/30 rounded-lg">
                <Sparkles className="h-12 w-12 mx-auto text-muted-foreground" />
                <h3 className="mt-4 text-lg font-medium">
                  AI insights not available
                </h3>
                <p className="mt-2 text-sm text-muted-foreground max-w-md mx-auto">
                  This invoice was processed before AI analysis
                  features were enabled. Try uploading a
                  new invoice to use advanced fraud detection, payment
                  prediction, and more.
                </p>
                <Button className="mt-4" asChild>
                  <Link href="/dashboard/upload">
                    Upload New Invoice
                  </Link>
                </Button>
              </div>
            )}
        </TabsContent>
      </Tabs>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              Are you sure you want to delete this invoice?
            </DialogTitle>
            <DialogDescription>
              This action cannot be undone. This will permanently
              delete the invoice and all associated data.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isDeleting}
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Image Preview Dialog */}
      <Dialog
        open={isImagePreviewOpen}
        onOpenChange={setIsImagePreviewOpen}
      >
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Invoice Document</DialogTitle>
          </DialogHeader>
          <div className="overflow-auto max-h-[80vh]">
            <FilePreview
              src={
                invoice.originalFileUrl ?? invoice.thumbnailUrl ?? ''
              }
              type={getFileType(invoice.originalFileUrl)}
              large={true}
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* Download Options Dialog */}
      <DownloadOptions
        isOpen={isDownloadOptionsOpen}
        onOpenChange={setIsDownloadOptionsOpen}
        invoice={invoice}
      />

      {/* Export Dialog */}
      <ExportDialog
        isOpen={isExportDialogOpen}
        onOpenChange={setIsExportDialogOpen}
        invoices={[invoice]}
      />

      {/* Edit Form */}
      <InvoiceEditForm
        invoice={
          invoiceDataForForm as Parameters<
            typeof InvoiceEditForm
          >[0]['invoice']
        }
        isOpen={isEditFormOpen}
        onClose={() => setIsEditFormOpen(false)}
      />
    </div>
  );
}
