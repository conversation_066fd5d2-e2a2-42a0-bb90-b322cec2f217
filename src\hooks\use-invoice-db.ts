"use client";

import { useState } from "react";
import { type InvoiceData } from "@/types/invoice";
import { saveInvoiceToDatabase } from "@/actions/save-invoice-to-db";

interface SaveResult {
  success: boolean;
  error?: string;
  data?: {
    id: string;
    invoiceId?: string;
    [key: string]: unknown;
  };
  isDuplicate?: boolean;
  isPossibleDuplicate?: boolean;
}

export function useInvoiceDatabase() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [results, setResults] = useState<SaveResult[]>([]);
  
  /**
   * Save a single invoice to the database
   */
  const saveInvoice = async (invoice: InvoiceData): Promise<SaveResult> => {
    try {
      setIsLoading(true);
      const result = await saveInvoiceToDatabase(invoice);
      
      if (!result.success) {
        return { 
          success: false, 
          error: result.error || "Failed to save invoice" 
        };
      }
      
      return { 
        success: true, 
        data: { id: result.data?.id || '', invoiceId: result.data?.id }
      };
    } catch (err) {
      return { 
        success: false, 
        error: err instanceof Error ? err.message : "Unknown error occurred" 
      };
    } finally {
      setIsLoading(false);
    }
  };
  
  /**
   * Save multiple invoices to the database
   */
  const saveMultipleInvoices = async (invoices: InvoiceData[]): Promise<SaveResult[]> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const results = [];
      
      for (const invoice of invoices) {
        const result = await saveInvoice(invoice);
        results.push(result);
      }
      
      setResults(results);
      
      // Check if any failed
      const failedCount = results.filter(r => !r.success).length;
      if (failedCount > 0) {
        setError(`Failed to save ${failedCount} of ${invoices.length} invoices`);
      }
      
      return results;
    } catch (err) {
      setError(err instanceof Error ? err.message : "Unknown error occurred");
      return [];
    } finally {
      setIsLoading(false);
    }
  };
  
  return {
    isLoading,
    error,
    results,
    saveInvoice,
    saveMultipleInvoices,
    clearError: () => setError(null),
    clearResults: () => setResults([])
  };
} 