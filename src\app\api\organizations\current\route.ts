import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import db from '@/db/db';

export async function GET() {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the current user with organizations
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      include: { organizations: true },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      );
    }

    // Return all organizations for the user
    const organizations = user.organizations.map(org => ({
      id: org.id,
      name: org.name,
    }));

    return NextResponse.json({
      success: true,
      organizations,
    });
  } catch (error) {
    console.error('Error getting organizations:', error);
    return NextResponse.json(
      { error: 'Failed to get organizations' },
      { status: 500 }
    );
  }
} 