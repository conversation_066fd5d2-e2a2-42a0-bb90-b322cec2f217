import { NextResponse } from "next/server";
import { clerkClient } from "@clerk/nextjs/server";
import db from "@/db/db";

/**
 * This endpoint performs a full sync between Clerk and your database
 * It should be called manually or on a schedule to ensure data consistency
 */
export async function POST(req: Request) {
  try {
    // Check for API key or other authorization
    const authHeader = req.headers.get("authorization");
    if (!authHeader || authHeader !== `Bearer ${process.env.API_SECRET_KEY}`) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get all users from Clerk
    const clerk = await clerkClient();
    const clerkUsers = await clerk.users.getUserList({
      limit: 100,
    });

    // Get all users from database
    const dbUsers = await db.user.findMany({
      select: {
        id: true,
        clerkId: true,
        email: true,
      },
    });

    // Create a map of Clerk IDs to database users
    const dbUsersByClerkId = new Map(
      dbUsers.map((user) => [user.clerkId, user])
    );

    // Create a map of emails to database users
    const dbUsersByEmail = new Map(
      dbUsers.map((user) => [user.email.toLowerCase(), user])
    );

    // Track stats
    const stats = {
      created: 0,
      updated: 0,
      deleted: 0,
      skipped: 0,
    };

    // Process each Clerk user
    for (const clerkUser of clerkUsers.data) {
      // Get primary email
      const primaryEmail =
        clerkUser.emailAddresses.find(
          (email: { id: string; emailAddress: string }) => email.id === clerkUser.primaryEmailAddressId
        )?.emailAddress || clerkUser.emailAddresses[0]?.emailAddress;

      if (!primaryEmail) {
        console.log(`Skipping user ${clerkUser.id} - no email address`);
        stats.skipped++;
        continue;
      }

      // Check if user exists in database by Clerk ID
      let dbUser = dbUsersByClerkId.get(clerkUser.id);

      // If not found by Clerk ID, try by email
      if (!dbUser && primaryEmail) {
        dbUser = dbUsersByEmail.get(primaryEmail.toLowerCase());
      }

      if (dbUser) {
        // Update existing user
        await db.user.update({
          where: { id: dbUser.id },
          data: {
            clerkId: clerkUser.id,
            email: primaryEmail,
            firstName: clerkUser.firstName || "",
            lastName: clerkUser.lastName || "",
            profileImageUrl: clerkUser.imageUrl || "",
            status: "ACTIVE",
          },
        });
        stats.updated++;
      } else {
        // Create new user
        await db.user.create({
          data: {
            clerkId: clerkUser.id,
            email: primaryEmail,
            firstName: clerkUser.firstName || "",
            lastName: clerkUser.lastName || "",
            profileImageUrl: clerkUser.imageUrl || "",
            role: "USER",
            status: "ACTIVE",
          },
        });
        stats.created++;
      }
    }

    // Handle deleted users (in Clerk but not in database)
    const clerkUserIds = new Set(clerkUsers.data.map((user: { id: string }) => user.id));

    for (const dbUser of dbUsers) {
      if (dbUser.clerkId && !clerkUserIds.has(dbUser.clerkId)) {
        // User exists in database but not in Clerk
        await db.user.update({
          where: { id: dbUser.id },
          data: {
            status: "INACTIVE",
          },
        });
        stats.deleted++;
      }
    }

    return NextResponse.json({
      success: true,
      message: "User sync completed",
      stats,
    });
  } catch (error) {
    console.error("Error syncing users:", error);
    return NextResponse.json(
      { error: "Failed to sync users" },
      { status: 500 }
    );
  }
}
