import { redirect } from 'next/navigation';
import { checkSubscriptionStatus, clearSubscriptionCache } from './subscription-cache';

/**
 * Server-side subscription guard for pages and layouts
 * Redirects to pricing if no active subscription found
 * No loading states - instant redirect or continue
 */
export async function requireActiveSubscription(currentPath?: string): Promise<void> {
  const subscriptionStatus = await checkSubscriptionStatus();
  
  if (!subscriptionStatus.hasActiveSubscription) {
    // Clear cache on failed check to ensure fresh data on next attempt
    clearSubscriptionCache();
    
    // Encode current path for return URL
    const returnUrl = currentPath ? encodeURIComponent(currentPath) : '';
    const redirectPath = returnUrl ? `/pricing?return_url=${returnUrl}` : '/pricing';
    
    redirect(redirectPath);
  }
}

/**
 * Check if user has active subscription without redirecting
 * Useful for conditional rendering or logic
 */
export async function hasActiveSubscription(): Promise<boolean> {
  const subscriptionStatus = await checkSubscriptionStatus();
  return subscriptionStatus.hasActiveSubscription;
}

/**
 * Get full subscription details for server components
 */
export async function getSubscriptionDetails() {
  return await checkSubscriptionStatus();
}

/**
 * Force refresh subscription cache
 * Useful after payment success or subscription changes
 */
export async function refreshSubscriptionStatus(): Promise<void> {
  await checkSubscriptionStatus(true);
}

/**
 * Pages that should bypass subscription checks
 * These are subscription-related pages that users need access to
 */
export const SUBSCRIPTION_BYPASS_PATHS = [
  '/dashboard/subscription',
  '/confirmation',
  '/pricing',
  '/checkout',
  '/success',
];

/**
 * Check if current path should bypass subscription requirements
 */
export function shouldBypassSubscriptionCheck(pathname: string): boolean {
  return SUBSCRIPTION_BYPASS_PATHS.some(path => pathname.startsWith(path));
}

/**
 * Server-side subscription check with path-aware bypass
 * Use this in pages and layouts for automatic handling
 */
export async function checkSubscriptionWithBypass(pathname: string): Promise<void> {
  // Skip subscription check for bypass paths
  if (shouldBypassSubscriptionCheck(pathname)) {
    return;
  }
  
  // Require active subscription for all other paths
  await requireActiveSubscription(pathname);
}