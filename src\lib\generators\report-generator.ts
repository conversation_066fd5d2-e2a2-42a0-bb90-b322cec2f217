import { jsPDF } from "jspdf";
import autoTable from "jspdf-autotable";
import * as ExcelJS from "exceljs";
import { loadAmiriFont } from "../fonts/amiri-loader";
import { formatNotes, containsArabic, formatDate } from "../utils/pdf";

// Unified interface for report data
interface ReportData {
  id: string;
  title: string;
  description?: string;
  reportType: string;
  startDate?: Date | null;
  endDate?: Date | null;
  createdAt: Date;
  data: Array<{
    dataPoint: string;
    value: number;
    label: string;
    category: string;
    invoiceId?: string | null;
  }>;
}

interface GenerationOptions {
  includeCharts?: boolean;
  includeRawData?: boolean;
  includeSummary?: boolean;
  includeAnalytics?: boolean;
  template?: 'professional' | 'modern' | 'executive';
  pageLayout?: 'portrait' | 'landscape';
}

interface RenderTextOptions {
  bold?: boolean;
  align?: 'left' | 'center' | 'right';
}

/**
 * Unified Report Generator for both PDF and Excel
 */
export class ReportGenerator {
  /**
   * Generate PDF report
   */
  static async generatePDF(report: ReportData, options: GenerationOptions = {}): Promise<Buffer> {
    try {
      // Validate report data
      if (!report || !report.data || !Array.isArray(report.data)) {
        throw new Error("Invalid report data structure");
      }

      const doc = new jsPDF({
        orientation: options.pageLayout || 'portrait',
        unit: "mm",
        format: "a4",
        compress: true,
      });

      await this.setupFont(doc);
      
      let currentY = 20;
      const pageHeight = doc.internal.pageSize.height;
      const pageWidth = doc.internal.pageSize.width;
      const margins = { top: 20, bottom: 20, left: 15, right: 15 };

      // Add header with company branding
      currentY = this.addReportHeader(doc, report, currentY, pageWidth, margins);

      // Add executive summary if enabled
      if (options.includeSummary !== false) {
        currentY = this.addExecutiveSummary(doc, report, currentY, margins, pageHeight, pageWidth);
      }

      // Add data analysis based on report type
      currentY = this.addDataAnalysis(doc, report, currentY, margins, pageHeight, pageWidth);

      // Add summary statistics
      currentY = this.addSummaryStatistics(doc, report, currentY, margins, pageHeight, pageWidth);

      // Add raw data table if enabled
      if (options.includeRawData !== false) {
        currentY = this.addRawDataTable(doc, report, currentY, margins, pageHeight, pageWidth);
      }

      // Add footer
      this.addFooter(doc, pageHeight, margins);

      return Buffer.from(doc.output("arraybuffer"));
    } catch (error) {
      console.error("PDF generation error:", error);
      throw new Error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate Excel report
   */
  static async generateExcel(report: ReportData, options: GenerationOptions = {}): Promise<Buffer> {
    try {
      // Validate report data
      if (!report || !report.data || !Array.isArray(report.data)) {
        throw new Error("Invalid report data structure");
      }

      const workbook = new ExcelJS.Workbook();
      workbook.creator = "Billix Reporting System";
      workbook.created = new Date();

      // Colors for styling
      const colors = {
        primary: 'FF2E7D32',
        secondary: 'FF1976D2',
        headerBg: 'FF1976D2',
        headerText: 'FFFFFFFF',
        alternateRow: 'FFF5F5F5',
        success: 'FF4CAF50',
        warning: 'FFFF9800',
        danger: 'FFF44336',
      };

      // Executive Summary sheet
      this.createExecutiveSummarySheet(workbook, report, colors);
      
      // Data Analysis sheet
      this.createDataAnalysisSheet(workbook, report, colors);
      
      // Raw data sheet
      if (options.includeRawData !== false) {
        this.createRawDataSheet(workbook, report, colors);
      }
      
      // Analytics sheet
      if (options.includeAnalytics !== false) {
        this.createAnalyticsSheet(workbook, report, colors);
      }
      
      // Trends sheet
      this.createTrendsSheet(workbook, report, colors);

      return Buffer.from(await workbook.xlsx.writeBuffer());
    } catch (error) {
      console.error("Excel generation error:", error);
      throw new Error(`Failed to generate Excel: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // PDF Helper Methods
  private static async setupFont(doc: jsPDF): Promise<void> {
    try {
      await loadAmiriFont(doc);
      doc.setFont("Amiri", "normal");
    } catch (error) {
      console.warn("Error loading Amiri font:", error);
      doc.setFont("helvetica", "normal");
    }
  }

  private static renderText(doc: jsPDF, text: string, x: number, y: number, options: RenderTextOptions = {}): void {
    if (options.bold) {
      doc.setFont(containsArabic(text) ? "Amiri" : "helvetica", "bold");
    } else {
      doc.setFont(containsArabic(text) ? "Amiri" : "helvetica", "normal");
    }

    if (containsArabic(text)) {
      doc.setR2L(true);
    } else {
      doc.setR2L(false);
    }

    const alignOptions: any = {};
    if (options.align) {
      alignOptions.align = options.align;
    }

    doc.text(text, x, y, alignOptions);
  }

  private static addReportHeader(
    doc: jsPDF,
    report: ReportData,
    currentY: number,
    pageWidth: number,
    margins: { top: number; bottom: number; left: number; right: number }
  ): number {
    // Company branding bar
    doc.setFillColor(46, 125, 50);
    doc.rect(0, 0, pageWidth, 15, 'F');
    
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(12);
    this.renderText(doc, "BILLIX REPORTS", margins.left, 10, { bold: true });
    
    // Current date
    const now = new Date();
    this.renderText(doc, formatDate(now), pageWidth - margins.right - 40, 10, { align: "right" });

    currentY = 25;

    // Report title
    doc.setFontSize(24);
    doc.setTextColor(46, 125, 50);
    this.renderText(doc, report.title, margins.left, currentY, { bold: true });
    currentY += 15;

    // Report type badge
    this.addReportTypeBadge(doc, report.reportType, pageWidth - margins.right - 60, currentY - 12);

    // Description
    if (report.description) {
      doc.setFontSize(11);
      doc.setTextColor(100, 100, 100);
      const descLines = doc.splitTextToSize(report.description, pageWidth - margins.left - margins.right);
      descLines.forEach((line: string) => {
        this.renderText(doc, line, margins.left, currentY);
        currentY += 6;
      });
    }

    // Date range
    if (report.startDate && report.endDate) {
      currentY += 5;
      doc.setFontSize(10);
      doc.setTextColor(120, 120, 120);
      const dateRange = `Period: ${formatDate(report.startDate)} to ${formatDate(report.endDate)}`;
      this.renderText(doc, dateRange, margins.left, currentY);
      currentY += 8;
    }

    // Decorative line
    doc.setLineWidth(1);
    doc.setDrawColor(46, 125, 50);
    doc.line(margins.left, currentY, pageWidth - margins.right, currentY);
    currentY += 15;

    return currentY;
  }

  private static addReportTypeBadge(doc: jsPDF, reportType: string, x: number, y: number): void {
    const typeColors: Record<string, [number, number, number]> = {
      EXPENSES: [244, 67, 54],
      VENDOR_ANALYSIS: [156, 39, 176],
      CATEGORY_ANALYSIS: [255, 152, 0],
      CASH_FLOW: [76, 175, 80],
      CUSTOM: [96, 125, 139],
    };

    const color = typeColors[reportType] || [100, 100, 100];
    const displayText = this.formatReportType(reportType);
    
    doc.setFillColor(color[0], color[1], color[2]);
    doc.roundedRect(x, y, 55, 8, 2, 2, 'F');
    
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(8);
    this.renderText(doc, displayText, x + 27.5, y + 5.5, { align: "center", bold: true });
  }

  private static addExecutiveSummary(
    doc: jsPDF,
    report: ReportData,
    currentY: number,
    margins: { top: number; bottom: number; left: number; right: number },
    pageHeight: number,
    pageWidth: number
  ): number {
    // Check space
    if (currentY > pageHeight - 100) {
      doc.addPage();
      currentY = margins.top;
    }

    doc.setFontSize(16);
    doc.setTextColor(46, 125, 50);
    this.renderText(doc, "Executive Summary", margins.left, currentY, { bold: true });
    currentY += 15;

    // Calculate key metrics
    const totalValue = report.data.reduce((sum, item) => sum + item.value, 0);
    const dataCount = report.data.length;
    const avgValue = dataCount > 0 ? totalValue / dataCount : 0;
    const maxValue = Math.max(...report.data.map(d => d.value));

    // Summary box
    doc.setFillColor(248, 249, 250);
    doc.roundedRect(margins.left, currentY, pageWidth - margins.left - margins.right, 35, 3, 3, 'F');
    
    doc.setDrawColor(230, 230, 230);
    doc.setLineWidth(0.5);
    doc.roundedRect(margins.left, currentY, pageWidth - margins.left - margins.right, 35, 3, 3, 'S');

    const summaryY = currentY + 8;
    doc.setFontSize(12);
    doc.setTextColor(46, 125, 50);
    this.renderText(doc, "KEY METRICS", margins.left + 5, summaryY, { bold: true });

    doc.setFontSize(10);
    doc.setTextColor(60, 60, 60);
    
    const col1X = margins.left + 5;
    const col2X = margins.left + 70;
    const col3X = margins.left + 135;
    const metricsY = summaryY + 8;
    
    this.renderText(doc, `Total Value: ${this.formatCurrency(totalValue)}`, col1X, metricsY);
    this.renderText(doc, `Data Points: ${dataCount}`, col2X, metricsY);
    this.renderText(doc, `Max Value: ${this.formatCurrency(maxValue)}`, col3X, metricsY);
    
    const metricsY2 = metricsY + 8;
    this.renderText(doc, `Average Value: ${this.formatCurrency(avgValue)}`, col1X, metricsY2);
    
    // Add trend analysis
    const trendText = this.calculateTrend(report.data);
    this.renderText(doc, `Trend: ${trendText}`, col2X, metricsY2);

    return currentY + 45;
  }

  private static addDataAnalysis(
    doc: jsPDF,
    report: ReportData,
    currentY: number,
    margins: { top: number; bottom: number; left: number; right: number },
    pageHeight: number,
    pageWidth: number
  ): number {
    // Check space
    if (currentY > pageHeight - 80) {
      doc.addPage();
      currentY = margins.top;
    }

    doc.setFontSize(14);
    doc.setTextColor(46, 125, 50);
    this.renderText(doc, "Data Analysis", margins.left, currentY, { bold: true });
    currentY += 12;

    // Group data by category for analysis
    const categoryGroups = this.groupDataByCategory(report.data);
    
    // Filter out the totalAll property and ensure we have valid data
    const validCategories = Object.entries(categoryGroups).filter(([key, data]) => 
      key !== 'totalAll' && data && typeof data === 'object' && typeof data.total === 'number'
    );
    
    const sortedCategories = validCategories
      .sort(([,a], [,b]) => (b.total || 0) - (a.total || 0))
      .slice(0, 10); // Top 10 categories

    if (sortedCategories.length > 0) {
      // Category analysis table
      const headers = ['Category', 'Total Value', 'Count', 'Percentage'];
      const tableData = sortedCategories.map(([category, data]) => [
        category,
        this.formatCurrency(data.total || 0),
        (data.count || 0).toString(),
        `${(((data.total || 0) / (categoryGroups.totalAll || 1)) * 100).toFixed(1)}%`
      ]);

      autoTable(doc, {
        startY: currentY,
        head: [headers],
        body: tableData,
        theme: 'grid',
        headStyles: {
          fillColor: [46, 125, 50],
          textColor: [255, 255, 255],
          fontStyle: 'bold',
          fontSize: 9,
          halign: 'center',
        },
        styles: {
          fontSize: 8,
          cellPadding: 2,
          halign: 'center',
        },
        columnStyles: {
          0: { halign: 'left' },
          1: { halign: 'right' },
          2: { halign: 'center' },
          3: { halign: 'right' },
        },
        margin: { left: margins.left, right: margins.right },
      });

      currentY = (doc as any).lastAutoTable.finalY + 15;
    } else {
      // No data available message
      doc.setFontSize(10);
      doc.setTextColor(128, 128, 128);
      this.renderText(doc, "No data available for analysis", margins.left, currentY);
      currentY += 15;
    }

    return currentY;
  }

  private static addSummaryStatistics(
    doc: jsPDF,
    report: ReportData,
    currentY: number,
    margins: { top: number; bottom: number; left: number; right: number },
    pageHeight: number,
    pageWidth: number
  ): number {
    // Check space
    if (currentY > pageHeight - 60) {
      doc.addPage();
      currentY = margins.top;
    }

    doc.setFontSize(14);
    doc.setTextColor(46, 125, 50);
    this.renderText(doc, "Statistical Summary", margins.left, currentY, { bold: true });
    currentY += 12;

    // Calculate statistics
    const values = report.data.map(d => d.value);
    const stats = this.calculateStatistics(values);

    // Statistics in two columns
    const leftCol = margins.left;
    const rightCol = pageWidth / 2 + 10;
    let leftY = currentY;
    let rightY = currentY;

    doc.setFontSize(10);
    doc.setTextColor(60, 60, 60);

    // Left column
    leftY = this.addLabelValue(doc, "Mean", this.formatCurrency(stats.mean), leftCol, leftY);
    leftY = this.addLabelValue(doc, "Median", this.formatCurrency(stats.median), leftCol, leftY);
    leftY = this.addLabelValue(doc, "Standard Deviation", this.formatCurrency(stats.stdDev), leftCol, leftY);

    // Right column
    rightY = this.addLabelValue(doc, "Minimum", this.formatCurrency(stats.min), rightCol, rightY);
    rightY = this.addLabelValue(doc, "Maximum", this.formatCurrency(stats.max), rightCol, rightY);
    rightY = this.addLabelValue(doc, "Range", this.formatCurrency(stats.range), rightCol, rightY);

    return Math.max(leftY, rightY) + 15;
  }

  private static addRawDataTable(
    doc: jsPDF,
    report: ReportData,
    currentY: number,
    margins: { top: number; bottom: number; left: number; right: number },
    pageHeight: number,
    pageWidth: number
  ): number {
    // Check space
    if (currentY > pageHeight - 80) {
      doc.addPage();
      currentY = margins.top;
    }

    doc.setFontSize(14);
    doc.setTextColor(46, 125, 50);
    this.renderText(doc, "Detailed Data", margins.left, currentY, { bold: true });
    currentY += 12;

    // Limit to first 50 rows for PDF readability
    const limitedData = report.data.slice(0, 50);
    
    const headers = ['Data Point', 'Label', 'Category', 'Value'];
    const tableData = limitedData.map(item => [
      item.dataPoint,
      item.label || 'N/A',
      item.category || 'N/A',
      this.formatCurrency(item.value)
    ]);

    autoTable(doc, {
      startY: currentY,
      head: [headers],
      body: tableData,
      theme: 'striped',
      headStyles: {
        fillColor: [46, 125, 50],
        textColor: [255, 255, 255],
        fontStyle: 'bold',
        fontSize: 8,
        halign: 'center',
      },
      styles: {
        fontSize: 7,
        cellPadding: 1.5,
      },
      columnStyles: {
        0: { halign: 'left', cellWidth: 30 },
        1: { halign: 'left', cellWidth: 'auto' },
        2: { halign: 'left', cellWidth: 25 },
        3: { halign: 'right', cellWidth: 25 },
      },
      margin: { left: margins.left, right: margins.right },
    });

    let finalY = (doc as any).lastAutoTable.finalY + 10;

    if (report.data.length > 50) {
      doc.setFontSize(8);
      doc.setTextColor(120, 120, 120);
      this.renderText(doc, `Note: Showing first 50 of ${report.data.length} data points. See Excel export for complete data.`, 
        margins.left, finalY);
      finalY += 8;
    }

    return finalY;
  }

  private static addFooter(
    doc: jsPDF,
    pageHeight: number,
    margins: { top: number; bottom: number; left: number; right: number }
  ): void {
    const footerY = pageHeight - margins.bottom;
    
    doc.setFontSize(8);
    doc.setTextColor(150, 150, 150);
    this.renderText(doc, "Generated by Billix Reporting System", margins.left, footerY);
    this.renderText(doc, `Page 1`, doc.internal.pageSize.width - margins.right - 20, footerY);
  }

  private static addLabelValue(doc: jsPDF, label: string, value: string, x: number, y: number): number {
    doc.setTextColor(100, 100, 100);
    this.renderText(doc, label + ":", x, y, { bold: true });
    
    doc.setTextColor(60, 60, 60);
    this.renderText(doc, value, x + 45, y);
    
    return y + 7;
  }

  // Excel Helper Methods
  private static createExecutiveSummarySheet(workbook: ExcelJS.Workbook, report: ReportData, colors: any): void {
    const sheet = workbook.addWorksheet("Executive Summary", {
      properties: { tabColor: { argb: colors.primary } },
      views: [{ state: "frozen", ySplit: 1 }]
    });

    // Add company branding
    this.addExcelBranding(sheet, colors);

    // Report header
    let row = 3;
    sheet.mergeCells(`A${row}:F${row}`);
    const titleCell = sheet.getCell(`A${row}`);
    titleCell.value = report.title;
    titleCell.font = { size: 18, bold: true };
    titleCell.alignment = { horizontal: 'center' };
    row += 2;

    // Report details
    sheet.getCell(`A${row}`).value = "Report Type:";
    sheet.getCell(`B${row}`).value = this.formatReportType(report.reportType);
    sheet.getCell(`D${row}`).value = "Generated:";
    sheet.getCell(`E${row}`).value = new Date();
    row++;

    if (report.description) {
      sheet.getCell(`A${row}`).value = "Description:";
      sheet.getCell(`B${row}`).value = report.description;
      sheet.mergeCells(`B${row}:F${row}`);
      row++;
    }

    if (report.startDate && report.endDate) {
      sheet.getCell(`A${row}`).value = "Period:";
      sheet.getCell(`B${row}`).value = `${formatDate(report.startDate)} to ${formatDate(report.endDate)}`;
      sheet.mergeCells(`B${row}:F${row}`);
      row++;
    }

    row += 2;

    // Key metrics
    const totalValue = report.data.reduce((sum, item) => sum + item.value, 0);
    const dataCount = report.data.length;
    const avgValue = dataCount > 0 ? totalValue / dataCount : 0;
    const maxValue = Math.max(...report.data.map(d => d.value));

    sheet.getCell(`A${row}`).value = "KEY METRICS";
    sheet.getCell(`A${row}`).font = { bold: true, size: 14, color: { argb: colors.primary } };
    row++;

    const metrics = [
      ["Total Value", totalValue],
      ["Data Points", dataCount],
      ["Average Value", avgValue],
      ["Maximum Value", maxValue],
    ];

    metrics.forEach(([label, value]) => {
      sheet.getCell(`A${row}`).value = label;
      sheet.getCell(`A${row}`).font = { bold: true };
      
      const valueCell = sheet.getCell(`B${row}`);
      valueCell.value = typeof value === 'number' && label.toString().toLowerCase().includes('value') ? value : value;
      
      if (typeof value === 'number' && label.toString().toLowerCase().includes('value')) {
        valueCell.numFmt = '"$"#,##0.00';
      }
      
      row++;
    });

    this.styleWorksheet(sheet, colors);
  }

  private static createDataAnalysisSheet(workbook: ExcelJS.Workbook, report: ReportData, colors: any): void {
    const sheet = workbook.addWorksheet("Data Analysis", {
      properties: { tabColor: { argb: colors.secondary } }
    });

    // Group data by category
    const categoryGroups = this.groupDataByCategory(report.data);
    const sortedCategories = Object.entries(categoryGroups)
      .sort(([,a], [,b]) => b.total - a.total);

    const columns: Partial<ExcelJS.Column>[] = [
      { header: "Category", key: "category", width: 25 },
      { header: "Total Value", key: "totalValue", width: 15 },
      { header: "Data Points", key: "count", width: 15 },
      { header: "Average", key: "average", width: 15 },
      { header: "Percentage", key: "percentage", width: 15 },
    ];

    sheet.columns = columns;

    sortedCategories.forEach(([category, data]) => {
      const row = sheet.addRow({
        category: category,
        totalValue: data.total,
        count: data.count,
        average: data.average,
        percentage: (data.total / categoryGroups.totalAll) * 100,
      });

      // Format currency and percentage cells
      row.getCell('totalValue').numFmt = '"$"#,##0.00';
      row.getCell('average').numFmt = '"$"#,##0.00';
      row.getCell('percentage').numFmt = '0.00%';
    });

    this.styleWorksheet(sheet, colors);
  }

  private static createRawDataSheet(workbook: ExcelJS.Workbook, report: ReportData, colors: any): void {
    const sheet = workbook.addWorksheet("Raw Data", {
      properties: { tabColor: { argb: colors.warning } }
    });

    const columns: Partial<ExcelJS.Column>[] = [
      { header: "Data Point", key: "dataPoint", width: 20 },
      { header: "Label", key: "label", width: 30 },
      { header: "Category", key: "category", width: 20 },
      { header: "Value", key: "value", width: 15 },
      { header: "Invoice ID", key: "invoiceId", width: 20 },
    ];

    sheet.columns = columns;

    report.data.forEach(item => {
      const row = sheet.addRow({
        dataPoint: item.dataPoint,
        label: item.label || "N/A",
        category: item.category || "N/A",
        value: item.value,
        invoiceId: item.invoiceId || "N/A",
      });

      // Format currency cell
      row.getCell('value').numFmt = '"$"#,##0.00';
    });

    this.styleWorksheet(sheet, colors);
  }

  private static createAnalyticsSheet(workbook: ExcelJS.Workbook, report: ReportData, colors: any): void {
    const sheet = workbook.addWorksheet("Analytics", {
      properties: { tabColor: { argb: colors.success } }
    });

    // Add statistical analysis
    const values = report.data.map(d => d.value);
    const stats = this.calculateStatistics(values);

    sheet.getCell('A1').value = "STATISTICAL ANALYSIS";
    sheet.getCell('A1').font = { bold: true, size: 14, color: { argb: colors.primary } };
    
    let row = 3;
    const statEntries = Object.entries(stats);
    statEntries.forEach(([key, value]) => {
      sheet.getCell(`A${row}`).value = this.formatFieldName(key);
      sheet.getCell(`A${row}`).font = { bold: true };
      
      const valueCell = sheet.getCell(`B${row}`);
      valueCell.value = value;
      valueCell.numFmt = '"$"#,##0.00';
      
      row++;
    });

    this.styleWorksheet(sheet, colors);
  }

  private static createTrendsSheet(workbook: ExcelJS.Workbook, report: ReportData, colors: any): void {
    const sheet = workbook.addWorksheet("Trends", {
      properties: { tabColor: { argb: colors.danger } }
    });

    // Group data by label (often dates) for trend analysis
    const labelGroups: Record<string, { total: number; count: number }> = {};
    
    report.data.forEach(item => {
      const label = item.label || 'Unknown';
      if (!labelGroups[label]) {
        labelGroups[label] = { total: 0, count: 0 };
      }
      labelGroups[label].total += item.value;
      labelGroups[label].count += 1;
    });

    const columns: Partial<ExcelJS.Column>[] = [
      { header: "Period/Label", key: "label", width: 25 },
      { header: "Total Value", key: "total", width: 15 },
      { header: "Data Points", key: "count", width: 15 },
      { header: "Average", key: "average", width: 15 },
    ];

    sheet.columns = columns;

    Object.entries(labelGroups).forEach(([label, data]) => {
      const row = sheet.addRow({
        label: label,
        total: data.total,
        count: data.count,
        average: data.total / data.count,
      });

      // Format currency cells
      row.getCell('total').numFmt = '"$"#,##0.00';
      row.getCell('average').numFmt = '"$"#,##0.00';
    });

    this.styleWorksheet(sheet, colors);
  }

  // Utility methods
  private static formatCurrency(amount: number): string {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  }

  private static formatReportType(type: string): string {
    switch (type) {
      case "EXPENSES":
        return "Expenses";
      case "VENDOR_ANALYSIS":
        return "Vendor Analysis";
      case "CATEGORY_ANALYSIS":
        return "Category Analysis";
      case "CASH_FLOW":
        return "Cash Flow";
      case "CUSTOM":
        return "Custom Report";
      default:
        return "Report";
    }
  }

  private static formatFieldName(key: string): string {
    return key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
  }

  private static groupDataByCategory(data: Array<{ category: string; value: number }>) {
    const groups: Record<string, { total: number; count: number; average: number }> = {};
    let totalAll = 0;

    // Handle empty or invalid data
    if (!data || !Array.isArray(data) || data.length === 0) {
      return { totalAll: 0 };
    }

    data.forEach(item => {
      if (!item || typeof item !== 'object') return;
      
      const category = item.category || 'Unknown';
      const value = typeof item.value === 'number' ? item.value : 0;
      
      if (!groups[category]) {
        groups[category] = { total: 0, count: 0, average: 0 };
      }
      groups[category].total += value;
      groups[category].count += 1;
      totalAll += value;
    });

    // Calculate averages
    Object.values(groups).forEach(group => {
      group.average = group.count > 0 ? group.total / group.count : 0;
    });

    return { ...groups, totalAll };
  }

  private static calculateStatistics(values: number[]) {
    if (!values || !Array.isArray(values) || values.length === 0) {
      return { mean: 0, median: 0, stdDev: 0, min: 0, max: 0, range: 0 };
    }

    // Filter out invalid values
    const validValues = values.filter(val => typeof val === 'number' && !isNaN(val) && isFinite(val));
    
    if (validValues.length === 0) {
      return { mean: 0, median: 0, stdDev: 0, min: 0, max: 0, range: 0 };
    }

    const sorted = [...validValues].sort((a, b) => a - b);
    const mean = validValues.reduce((sum, val) => sum + val, 0) / validValues.length;
    const median = sorted.length % 2 === 0 
      ? (sorted[sorted.length / 2 - 1] + sorted[sorted.length / 2]) / 2
      : sorted[Math.floor(sorted.length / 2)];
    
    const variance = validValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / validValues.length;
    const stdDev = Math.sqrt(variance);
    
    const min = Math.min(...validValues);
    const max = Math.max(...validValues);
    const range = max - min;

    return { mean, median, stdDev, min, max, range };
  }

  private static calculateTrend(data: Array<{ value: number; label: string }>): string {
    if (data.length < 2) return "Insufficient data";
    
    const sortedData = data.sort((a, b) => a.label.localeCompare(b.label));
    const firstHalf = sortedData.slice(0, Math.floor(sortedData.length / 2));
    const secondHalf = sortedData.slice(Math.floor(sortedData.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, item) => sum + item.value, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, item) => sum + item.value, 0) / secondHalf.length;
    
    const change = ((secondAvg - firstAvg) / firstAvg) * 100;
    
    if (Math.abs(change) < 1) return "Stable";
    return change > 0 ? `Increasing (${change.toFixed(1)}%)` : `Decreasing (${Math.abs(change).toFixed(1)}%)`;
  }

  private static addExcelBranding(sheet: ExcelJS.Worksheet, colors: any): void {
    sheet.mergeCells('A1:F1');
    const logoCell = sheet.getCell('A1');
    logoCell.value = 'BILLIX REPORTING SYSTEM';
    logoCell.font = { size: 14, bold: true, color: { argb: colors.headerText } };
    logoCell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: colors.primary }
    };
    logoCell.alignment = { horizontal: 'center', vertical: 'middle' };
  }

  private static styleWorksheet(sheet: ExcelJS.Worksheet, colors: any): void {
    // Style header row
    const headerRow = sheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: colors.headerText } };
    headerRow.fill = { 
      type: "pattern", 
      pattern: "solid", 
      fgColor: { argb: colors.headerBg } 
    };
    headerRow.alignment = { vertical: "middle", horizontal: "center" };
    headerRow.height = 25;

    // Add borders and alternating row colors
    sheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
      if (rowNumber > 1 && rowNumber % 2 === 0) {
        row.fill = { 
          type: "pattern", 
          pattern: "solid", 
          fgColor: { argb: colors.alternateRow } 
        };
      }

      row.eachCell({ includeEmpty: false }, (cell) => {
        cell.border = {
          top: { style: "thin" },
          left: { style: "thin" },
          bottom: { style: "thin" },
          right: { style: "thin" },
        };
      });
    });
  }
}

// Main export functions
export async function generateReportPDF(report: ReportData, options: GenerationOptions = {}): Promise<Buffer> {
  return ReportGenerator.generatePDF(report, options);
}

export async function generateReportExcel(report: ReportData, options: GenerationOptions = {}): Promise<Buffer> {
  return ReportGenerator.generateExcel(report, options);
}