import { prisma } from '@/lib/prisma';
import { InvoiceData } from '@/types/invoice';

/**
 * Analyze payment patterns for a vendor
 */
export async function analyzeVendorPaymentPatterns(
  vendorName: string,
  userId: string
): Promise<{
  averageDays: number;
  paymentTrend: 'improving' | 'declining' | 'stable';
  totalInvoices: number;
  onTimePercentage: number;
}> {
  try {
    // Get all paid invoices for this vendor
    const paidInvoices = await prisma.invoice.findMany({
      where: {
        vendorName,
        user: {
          clerkId: userId,
        },
        status: 'PAID',
      },
      select: {
        issueDate: true,
        dueDate: true,
        createdAt: true,
      },
    });

    if (paidInvoices.length === 0) {
      return {
        averageDays: 30, // Default assumption
        paymentTrend: 'stable',
        totalInvoices: 0,
        onTimePercentage: 0,
      };
    }

    // Calculate payment days for each invoice
    const paymentDays = paidInvoices
      .filter((invoice) => invoice.issueDate && invoice.dueDate)
      .map((invoice) => {
        const issueDate = new Date(invoice.issueDate!);
        const dueDate = new Date(invoice.dueDate!);
        const daysDiff = Math.ceil(
          (dueDate.getTime() - issueDate.getTime()) /
            (1000 * 60 * 60 * 24)
        );
        return daysDiff;
      });

    const averageDays =
      paymentDays.reduce((sum, days) => sum + days, 0) /
      paymentDays.length;

    // Calculate on-time payment percentage (within due date)
    const onTimePayments = paymentDays.filter(
      (days) => days <= 0
    ).length;
    const onTimePercentage =
      (onTimePayments / paymentDays.length) * 100;

    // Determine trend (simplified - could be more sophisticated)
    let paymentTrend: 'improving' | 'declining' | 'stable' = 'stable';
    if (paymentDays.length >= 3) {
      const recentAvg =
        paymentDays.slice(-3).reduce((sum, days) => sum + days, 0) /
        3;
      const olderAvg =
        paymentDays
          .slice(0, -3)
          .reduce((sum, days) => sum + days, 0) /
        (paymentDays.length - 3);

      if (recentAvg < olderAvg - 2) {
        paymentTrend = 'improving';
      } else if (recentAvg > olderAvg + 2) {
        paymentTrend = 'declining';
      }
    }

    return {
      averageDays: Math.round(averageDays),
      paymentTrend,
      totalInvoices: paidInvoices.length,
      onTimePercentage: Math.round(onTimePercentage),
    };
  } catch {
    return {
      averageDays: 30,
      paymentTrend: 'stable',
      totalInvoices: 0,
      onTimePercentage: 0,
    };
  }
}

/**
 * Predict when an invoice is likely to be paid based on historical data
 */
export async function predictPaymentTiming(
  invoiceData: InvoiceData,
  userId: string
): Promise<{
  predictedPayDate: string;
  confidence: number;
  factors: string[];
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  historicalAvgDays?: number;
}> {
  try {
    const vendorName = invoiceData.vendor?.name;

    if (!vendorName) {
      // Default prediction without vendor data
      const dueDate = new Date(invoiceData.dueDate || new Date());
      dueDate.setDate(dueDate.getDate() + 30); // Default 30 days

      return {
        predictedPayDate: dueDate.toISOString(),
        confidence: 0.3,
        factors: ['No historical vendor data available'],
        riskLevel: 'MEDIUM',
      };
    }

    // Analyze vendor payment patterns
    const vendorPatterns = await analyzeVendorPaymentPatterns(
      vendorName,
      userId
    );

    // Get invoice amount for risk assessment
    const amount = invoiceData.financials?.total
      ? parseFloat(
          invoiceData.financials.total.replace(/[^0-9.-]+/g, '')
        )
      : 0;

    // Calculate predicted payment date
    const issueDate = new Date(invoiceData.date || new Date());
    const predictedDate = new Date(issueDate);
    predictedDate.setDate(
      predictedDate.getDate() + vendorPatterns.averageDays
    );

    // Determine confidence based on historical data
    let confidence = 0.5; // Base confidence
    if (vendorPatterns.totalInvoices >= 5) {
      confidence += 0.3; // More historical data increases confidence
    }
    if (vendorPatterns.onTimePercentage > 80) {
      confidence += 0.2; // Good payment history increases confidence
    }

    // Determine risk level
    let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' = 'MEDIUM';
    if (
      vendorPatterns.onTimePercentage > 90 &&
      vendorPatterns.paymentTrend !== 'declining'
    ) {
      riskLevel = 'LOW';
    } else if (
      vendorPatterns.onTimePercentage < 60 ||
      vendorPatterns.paymentTrend === 'declining'
    ) {
      riskLevel = 'HIGH';
    }

    // Generate factors that influenced the prediction
    const factors = [
      `Based on ${vendorPatterns.totalInvoices} historical invoices`,
      `Vendor pays on time ${vendorPatterns.onTimePercentage}% of the time`,
      `Payment trend is ${vendorPatterns.paymentTrend}`,
      amount > 10000
        ? 'High invoice amount may delay payment'
        : 'Standard invoice amount',
    ];

    return {
      predictedPayDate: predictedDate.toISOString(),
      confidence: Math.min(confidence, 1.0),
      factors,
      riskLevel,
      historicalAvgDays: vendorPatterns.averageDays,
    };
  } catch {
    // Fallback prediction
    const dueDate = new Date(invoiceData.dueDate || new Date());
    dueDate.setDate(dueDate.getDate() + 30);

    return {
      predictedPayDate: dueDate.toISOString(),
      confidence: 0.2,
      factors: ['Error occurred during prediction'],
      riskLevel: 'MEDIUM',
    };
  }
}

// Note: Currency exchange functions have been moved to the backend API
// Use the functions from @/actions/currency-conversion.ts instead
