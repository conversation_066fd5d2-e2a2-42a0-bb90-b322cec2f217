import { NextResponse } from "next/server";
import db from "@/db/db";
import { fetchInvoiceEmails } from "@/lib/services/gmail-service";
import { convertEmailAttachmentsToFiles } from "@/lib/services/attachment-converter";
import { extractInvoiceData } from "@/actions/extract-invoice";
import { saveInvoiceToDatabase } from "@/actions/save-invoice-to-db";
import { nanoid } from "nanoid";

// Define an interface for File with email metadata
export interface FileWithEmailMetadata extends File {
  emailMetadata?: {
    messageId?: string;
    subject?: string;
    from?: string;
    date?: string;
    messageUrl?: string;
  };
}

// Make this a GET request to be callable from cron jobs
export async function GET(request: Request) {
  const authHeader = request.headers.get('authorization');
  const isVercelCron = authHeader === `Bearer ${process.env.CRON_SECRET}`;
  
  // Only allow this to be called from Vercel Cron or local development
  if (!isVercelCron && process.env.NODE_ENV !== 'development') {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  
  try {
    // Get all active email sync jobs
    const syncJobs = await db.emailSyncJob.findMany({
      where: {
        isActive: true
      },
      select: {
        id: true,
        userId: true,
        provider: true,
        frequency: true,
        lastRun: true,
        autoProcess: true
      }
    });
    
    
    const results = [];
    
    // Process each job
    for (const job of syncJobs) {
      try {
        // Determine if this job should run now based on frequency
        const shouldRun = shouldRunBasedOnFrequency(job.frequency, job.lastRun);
        
        if (shouldRun) {
          // Run sync for this user
          
          // Get user from database
          const user = await db.user.findFirst({
            where: { id: job.userId }
          });
          
          if (!user || !user.clerkId) {
            console.error(`User ${job.userId} not found or has no clerkId`);
            continue;
          }
          
          // Get start date based on last run or default to 30 days ago
          const startDate = job.lastRun || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
          
          // Fetch emails
          const emailResult = await fetchInvoiceEmails(job.userId, {
            startDate,
            endDate: new Date()
          });
          
          // Update last run time
          await db.emailSyncJob.update({
            where: { id: job.id },
            data: {
              lastRun: new Date()
            }
          });
          
          // Check if auto-processing is enabled
          if (job.autoProcess && emailResult.success && emailResult.attachments.length > 0) {
            // Convert attachments to files
            const files = await convertEmailAttachmentsToFiles(emailResult.attachments);
            
            let processed = 0;
            let duplicates = 0;
            
            // Process and save each file
            for (const file of files) {
              try {
                // Extract form data
                const formData = new FormData();
                formData.append("file", file);
                
                // Add email metadata if available
                const emailMetadata = (file as FileWithEmailMetadata).emailMetadata;
                if (emailMetadata) {
                  formData.append("emailMetadata", JSON.stringify(emailMetadata));
                }
                
                // Extract invoice data
                const extractionResult = await extractInvoiceData(formData);
                
                // Handle both single and batch extraction results
                const hasError = 'error' in extractionResult ? extractionResult.error : ('errors' in extractionResult && extractionResult.errors && extractionResult.errors.length > 0);
                if (hasError) {
                  const errorMsg = 'error' in extractionResult ? extractionResult.error : ('errors' in extractionResult ? extractionResult.errors?.[0] : 'Unknown error');
                  console.error(`Error extracting data from ${file.name}:`, errorMsg);
                  continue;
                }
                
                if (!extractionResult.data) {
                  console.error(`No data extracted from ${file.name}`);
                  continue;
                }
                
                // Handle single invoice data (for single file upload)
                const invoiceData = Array.isArray(extractionResult.data) ? extractionResult.data[0] : extractionResult.data;
                
                if (!invoiceData) {
                  console.error(`No valid invoice data found in ${file.name}`);
                  continue;
                }
                
                // Check for duplicates by invoice number if available
                if (invoiceData.invoiceNumber) {
                  const existingInvoice = await db.invoice.findFirst({
                    where: {
                      invoiceNumber: invoiceData.invoiceNumber,
                      user: {
                        clerkId: user.clerkId
                      }
                    }
                  });
                  
                  if (existingInvoice) {
                    duplicates++;
                    continue;
                  }
                }
                
                // Save to database
                const saveResult = await saveInvoiceToDatabase(invoiceData);
                
                if (saveResult.success) {
                  processed++;
                } else {
                  console.error(`Error saving ${file.name}:`, saveResult.error);
                }
              } catch (fileError) {
                console.error(`Error processing file ${file.name}:`, fileError);
              }
            }
            
            // Create a record in email sync history
            await db.emailSyncHistory.create({
              data: {
                id: nanoid(),
                userId: job.userId,
                provider: job.provider,
                processedCount: processed,
                status: processed > 0 ? "success" : "error",
                notes: `Synced ${emailResult.attachments.length} attachments. Processed ${processed} invoices. Skipped ${duplicates} duplicates.`
              }
            });
            
            results.push({
              userId: job.userId,
              provider: job.provider,
              attachments: emailResult.attachments.length,
              processed,
              duplicates,
              status: "success"
            });
          } else {
            // Create a record in email sync history without processing
            await db.emailSyncHistory.create({
              data: {
                id: nanoid(),
                userId: job.userId,
                provider: job.provider,
                processedCount: 0,
                status: "success",
                notes: `Synced ${emailResult.attachments?.length || 0} attachments. Auto-processing is ${job.autoProcess ? 'enabled' : 'disabled'}.`
              }
            });
            
            results.push({
              userId: job.userId,
              provider: job.provider,
              attachments: emailResult.attachments?.length || 0,
              processed: 0,
              status: "success",
              autoProcess: job.autoProcess
            });
          }
        }
      } catch (error) {
        console.error(`Error processing sync job for user ${job.userId}:`, error);
        results.push({
          userId: job.userId,
          provider: job.provider,
          error: error instanceof Error ? error.message : "Unknown error",
          status: "error"
        });
      }
    }
    
    return NextResponse.json({
      success: true,
      processed: results.length,
      results
    });
  } catch (error) {
    console.error("Error running email sync jobs:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}

function shouldRunBasedOnFrequency(frequency: string, lastRun: Date | null): boolean {
  if (!lastRun) return true;
  
  const now = new Date();
  const hoursSinceLastRun = (now.getTime() - lastRun.getTime()) / (1000 * 60 * 60);
  
  switch (frequency) {
    case "hourly":
      return hoursSinceLastRun >= 1;
    case "daily":
      return hoursSinceLastRun >= 24;
    case "weekly":
      return hoursSinceLastRun >= 168;
    case "monthly":
      return hoursSinceLastRun >= 720; // 30 days * 24 hours
    default:
      return hoursSinceLastRun >= 24; // default to daily
  }
} 