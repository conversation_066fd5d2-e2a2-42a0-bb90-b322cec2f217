import { currentUser } from '@clerk/nextjs/server';
import db from '@/db/db';

interface SubscriptionStatus {
  hasActiveSubscription: boolean;
  subscription?: {
    id: number;
    status: string;
  } | null;
  cachedAt: number;
}

// In-memory cache with TTL (5 minutes)
const subscriptionCache = new Map<string, SubscriptionStatus>();
const CACHE_TTL = 5 * 60 * 1000;

export async function checkSubscriptionStatus(forceRefresh = false): Promise<SubscriptionStatus> {
  const user = await currentUser();
  
  if (!user?.id) {
    return {
      hasActiveSubscription: false,
      subscription: null,
      cachedAt: Date.now()
    };
  }

  return checkSubscriptionStatusWithUserId(user.id, forceRefresh);
}

export async function checkSubscriptionStatusWithUserId(clerkUserId: string, forceRefresh = false): Promise<SubscriptionStatus> {
  const cacheKey = `subscription_${clerkUserId}`;
  const now = Date.now();
  
  if (!forceRefresh) {
    const cached = subscriptionCache.get(cacheKey);
    if (cached && (now - cached.cachedAt) < CACHE_TTL) {
      return cached;
    }
  }

  try {
    const dbUser = await db.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    if (!dbUser) {
      const result: SubscriptionStatus = {
        hasActiveSubscription: false,
        subscription: null,
        cachedAt: now
      };
      subscriptionCache.set(cacheKey, result);
      return result;
    }

    const subscription = await db.subscription.findFirst({
      where: {
        userId: dbUser.id,
        status: { in: ['active', 'trialing', 'past_due'] },
      },
      orderBy: { id: 'desc' },
    });

    const result: SubscriptionStatus = {
      hasActiveSubscription: !!subscription,
      subscription: subscription ? {
        id: subscription.id,
        status: subscription.status,
      } : null,
      cachedAt: now
    };

    subscriptionCache.set(cacheKey, result);
    return result;

  } catch (error) {
    console.error('Error checking subscription status:', error);
    return {
      hasActiveSubscription: false,
      subscription: null,
      cachedAt: now
    };
  }
}

export function clearSubscriptionCache(): void {
  subscriptionCache.clear();
}