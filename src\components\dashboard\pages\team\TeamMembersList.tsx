import React, { useState } from 'react';
import { UserRole } from '@prisma/client';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Mail, MoreHorizontal, Check, Clock, ShieldCheck, ShieldX } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { updateMemberRole, removeMember } from '@/lib/actions/team';
import { toast } from 'sonner';
import { formatTimeElapsed } from '@/lib/utils';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Define types for team member
interface TeamMember {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string;
  profileImageUrl: string | null;
  role: UserRole;
  lastActive: Date | null;
  status: string;
  createdAt: Date;
}

interface TeamMembersListProps {
  members: TeamMember[];
  onRefresh: () => void;
}

export default function TeamMembersList({ members, onRefresh }: TeamMembersListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMember, setSelectedMember] = useState<TeamMember | null>(null);
  const [isRoleDialogOpen, setIsRoleDialogOpen] = useState(false);
  const [isRemoveDialogOpen, setIsRemoveDialogOpen] = useState(false);
  const [newRole, setNewRole] = useState<UserRole>(UserRole.VIEWER);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Filter members based on search term
  const filteredMembers = members.filter(member => {
    const fullName = `${member.firstName || ''} ${member.lastName || ''}`.trim().toLowerCase();
    const email = member.email.toLowerCase();
    const search = searchTerm.toLowerCase();
    
    return fullName.includes(search) || email.includes(search);
  });

  // Get role badge
  const getRoleBadge = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return <Badge className="bg-blue-600">Admin</Badge>;
      case UserRole.EDITOR:
        return (
          <Badge
            variant="outline"
            className="text-green-600 border-green-200 bg-green-50"
          >
            Editor
          </Badge>
        );
      case UserRole.VIEWER:
        return (
          <Badge
            variant="outline"
            className="text-amber-600 border-amber-200 bg-amber-50"
          >
            Viewer
          </Badge>
        );
      default:
        return <Badge variant="outline">{role}</Badge>;
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return <Check className="h-4 w-4 text-green-500" />;
      case "INACTIVE":
        return <Clock className="h-4 w-4 text-amber-500" />;
      case "SUSPENDED":
        return <ShieldX className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  // Handle role change
  const handleRoleChange = async () => {
    if (!selectedMember || !newRole) return;
    
    setIsSubmitting(true);
    try {
      const result = await updateMemberRole(selectedMember.id, newRole);
      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success(`Role updated to ${newRole}`);
        onRefresh();
      }
    } finally {
      setIsSubmitting(false);
      setIsRoleDialogOpen(false);
    }
  };

  // Handle member removal
  const handleRemoveMember = async () => {
    if (!selectedMember) return;
    
    setIsSubmitting(true);
    try {
      const result = await removeMember(selectedMember.id);
      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success('Member removed from organization');
        onRefresh();
      }
    } finally {
      setIsSubmitting(false);
      setIsRemoveDialogOpen(false);
    }
  };

  return (
    <>
      <div className="flex flex-col sm:flex-row gap-3 justify-between">
        <div className="relative w-full sm:w-72">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search members..."
            className="pl-8 w-full"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b">
              <th className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Name
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Role
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Status
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Last Active
              </th>
              <th className="px-4 py-3 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y">
            {filteredMembers.map((member) => (
              <tr key={member.id} className="hover:bg-muted/50">
                <td className="px-4 py-3 whitespace-nowrap">
                  <div className="flex items-center">
                    <Avatar className="h-8 w-8 mr-3">
                      <AvatarImage
                        src={member.profileImageUrl || `https://api.dicebear.com/7.x/avataaars/svg?seed=${member.email}`}
                        alt={`${member.firstName || ''} ${member.lastName || ''}`.trim() || member.email}
                      />
                      <AvatarFallback>
                        {member.firstName?.[0] || ''}
                        {member.lastName?.[0] || ''}
                        {!member.firstName && !member.lastName ? member.email[0].toUpperCase() : ''}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">
                        {`${member.firstName || ''} ${member.lastName || ''}`.trim() || 'Unnamed User'}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {member.email}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-4 py-3 whitespace-nowrap">
                  {getRoleBadge(member.role)}
                </td>
                <td className="px-4 py-3 whitespace-nowrap">
                  <div className="flex items-center">
                    {getStatusIcon(member.status)}
                    <span className="ml-1.5 text-sm">
                      {member.status.charAt(0) + member.status.slice(1).toLowerCase()}
                    </span>
                  </div>
                </td>
                <td className="px-4 py-3 whitespace-nowrap text-sm text-muted-foreground">
                  {member.lastActive ? formatTimeElapsed(member.lastActive) : 'Never'}
                </td>
                <td className="px-4 py-3 whitespace-nowrap text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8"
                      >
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">Actions</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        onClick={() => window.location.href = `mailto:${member.email}`}
                      >
                        <Mail className="mr-2 h-4 w-4" />
                        Email
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => {
                          setSelectedMember(member);
                          setNewRole(member.role);
                          setIsRoleDialogOpen(true);
                        }}
                      >
                        <ShieldCheck className="mr-2 h-4 w-4" />
                        Change Role
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        className="text-red-600"
                        onClick={() => {
                          setSelectedMember(member);
                          setIsRemoveDialogOpen(true);
                        }}
                      >
                        <ShieldX className="mr-2 h-4 w-4" />
                        Remove
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </td>
              </tr>
            ))}
            {filteredMembers.length === 0 && (
              <tr>
                <td colSpan={5} className="px-4 py-8 text-center text-muted-foreground">
                  {searchTerm 
                    ? 'No members found matching your search.' 
                    : 'No team members found. Invite some members to get started.'}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Change Role Dialog */}
      <AlertDialog open={isRoleDialogOpen} onOpenChange={setIsRoleDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Change Team Member Role</AlertDialogTitle>
            <AlertDialogDescription>
              This will change the role and permissions for {selectedMember?.firstName || selectedMember?.email}.
            </AlertDialogDescription>
          </AlertDialogHeader>
          
          <div className="py-4">
            <Select 
              value={newRole} 
              onValueChange={(value) => setNewRole(value as UserRole)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={UserRole.ADMIN}>Admin</SelectItem>
                <SelectItem value={UserRole.EDITOR}>Editor</SelectItem>
                <SelectItem value={UserRole.VIEWER}>Viewer</SelectItem>
              </SelectContent>
            </Select>
            
            <div className="mt-4 text-sm text-muted-foreground">
              {newRole === UserRole.ADMIN && (
                <p>Admins can manage team members, subscription, and have full access to all features.</p>
              )}
              {newRole === UserRole.EDITOR && (
                <p>Editors can create and edit content but cannot manage team members or subscription settings.</p>
              )}
              {newRole === UserRole.VIEWER && (
                <p>Viewers have read-only access to content but cannot make any changes.</p>
              )}
            </div>
          </div>
          
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isSubmitting}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleRoleChange}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Updating...' : 'Update Role'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Remove Member Dialog */}
      <AlertDialog open={isRemoveDialogOpen} onOpenChange={setIsRemoveDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remove Team Member</AlertDialogTitle>
            <AlertDialogDescription>
              This will remove {selectedMember?.firstName || selectedMember?.email} from your organization. 
              They will lose access to all resources and data immediately.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isSubmitting}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleRemoveMember}
              disabled={isSubmitting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isSubmitting ? 'Removing...' : 'Remove Member'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
} 