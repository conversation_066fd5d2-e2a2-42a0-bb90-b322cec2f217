import db from '@/db/db';
import { currentUser } from '@clerk/nextjs/server';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const chatId = searchParams.get('chatId');

  if (!chatId) {
    return new Response('chatId is required', { status: 400 });
  }

  const user = await currentUser();

  if (!user) {
    return new Response('Unauthorized', { status: 401 });
  }

  const votes = await db.vote.findMany({
    where: { chatId }
  });

  return Response.json(votes, { status: 200 });
}

export async function PATCH(request: Request) {
  const {
    chatId,
    messageId,
    type,
  }: { chatId: string; messageId: string; type: 'up' | 'down' } =
    await request.json();

  if (!chatId || !messageId || !type) {
    return new Response('messageId and type are required', { status: 400 });
  }

  const user = await currentUser();

  if (!user) {
    return new Response('Unauthorized', { status: 401 });
  }

  // Check if vote already exists
  const existingVote = await db.vote.findUnique({
    where: {
      chatId_messageId: {
        chatId,
        messageId
      }
    }
  });

  if (existingVote) {
    // Update existing vote
    await db.vote.update({
      where: {
        chatId_messageId: {
          chatId,
          messageId
        }
      },
      data: {
        isUpvoted: type === 'up'
      }
    });
  } else {
    // Create new vote
    await db.vote.create({
      data: {
        chatId,
        messageId,
        isUpvoted: type === 'up'
      }
    });
  }

  return new Response('Message voted', { status: 200 });
}
