import db from '@/db/db';
import { auth } from '@clerk/nextjs/server';

export interface UsageStats {
  chatUsage: number;
  invoiceUsage: number;
  chatLimit: number;
  invoiceLimit: number;
  resetDate: Date;
  daysUntilReset: number;
}

export interface UsageCheckResult {
  allowed: boolean;
  currentUsage: number;
  limit: number;
  remaining: number;
  resetDate: Date;
  message?: string;
}

/**
 * Get the current month's usage for a user
 */
export async function getCurrentUsage(
  userId: string
): Promise<UsageStats | null> {
  try {
    // Convert Clerk user ID to database user ID if needed
    let dbUserId = userId;

    // Check if this looks like a Clerk ID (starts with "user_")
    if (userId.startsWith('user_')) {
      const user = await db.user.findUnique({
        where: { clerkId: userId },
        select: { id: true },
      });

      if (!user) {
        return null;
      }

      dbUserId = user.id;
    }

    // Get user's active subscription with plan
    const subscription = await db.subscription.findFirst({
      where: {
        userId: dbUserId, // Use the database user ID
        status: 'active',
      },
      include: {
        plan: true,
      },
    });

    if (!subscription?.plan) {
      return null;
    }

    const chatLimit = subscription.plan.chatLimit || 0;
    const invoiceLimit = subscription.plan.invoiceLimit || 0;

    // Get or create user usage record for current month
    const now = new Date();
    const currentMonthStart = new Date(
      now.getFullYear(),
      now.getMonth(),
      1
    );
    const nextMonthStart = new Date(
      now.getFullYear(),
      now.getMonth() + 1,
      1
    );

    let userUsage = await db.userUsage.findUnique({
      where: { userId: dbUserId }, // Use the database user ID
    });

    // Create usage record if it doesn't exist or if it's a new month
    if (!userUsage || userUsage.resetDate <= currentMonthStart) {
      userUsage = await db.userUsage.upsert({
        where: { userId: dbUserId }, // Use the database user ID
        update: {
          chatUsage: 0,
          invoiceUsage: 0,
          resetDate: nextMonthStart,
        },
        create: {
          userId: dbUserId, // Use the database user ID
          chatUsage: 0,
          invoiceUsage: 0,
          resetDate: nextMonthStart,
        },
      });
    }

    // Calculate days until reset
    const daysUntilReset = Math.ceil(
      (userUsage.resetDate.getTime() - now.getTime()) /
        (1000 * 60 * 60 * 24)
    );

    const stats: UsageStats = {
      chatUsage: userUsage.chatUsage,
      invoiceUsage: userUsage.invoiceUsage,
      chatLimit,
      invoiceLimit,
      resetDate: userUsage.resetDate,
      daysUntilReset: Math.max(0, daysUntilReset),
    };

    return stats;
  } catch {
    return null;
  }
}

/**
 * Check if user can perform a chat action
 */
export async function checkChatUsage(
  userId: string
): Promise<UsageCheckResult> {
  try {
    const usage = await getCurrentUsage(userId);

    if (!usage) {
      return {
        allowed: false,
        currentUsage: 0,
        limit: 0,
        remaining: 0,
        resetDate: new Date(),
        message:
          "You don't have sufficient credits to continue. Please subscribe or upgrade your plan.",
      };
    }

    const allowed = usage.chatUsage < usage.chatLimit;
    const remaining = Math.max(0, usage.chatLimit - usage.chatUsage);

    return {
      allowed,
      currentUsage: usage.chatUsage,
      limit: usage.chatLimit,
      remaining,
      resetDate: usage.resetDate,
      message: allowed
        ? undefined
        : `You've reached your monthly chat limit of ${usage.chatLimit}. ${usage.chatLimit === 0 ? 'Subscribe to get more chats' : 'Upgrade your plan'} or wait ${usage.daysUntilReset} days for reset.`,
    };
  } catch {
    return {
      allowed: false,
      currentUsage: 0,
      limit: 0,
      remaining: 0,
      resetDate: new Date(),
      message: 'Error checking usage limits. Please try again.',
    };
  }
}

/**
 * Check if user can perform an invoice upload action
 */
export async function checkInvoiceUsage(
  userId: string
): Promise<UsageCheckResult> {
  try {
    const usage = await getCurrentUsage(userId);

    if (!usage) {
      return {
        allowed: false,
        currentUsage: 0,
        limit: 0,
        remaining: 0,
        resetDate: new Date(),
        message:
          "You don't have sufficient credits to continue. Please subscribe or upgrade your plan.",
      };
    }

    const allowed = usage.invoiceUsage < usage.invoiceLimit;
    const remaining = Math.max(
      0,
      usage.invoiceLimit - usage.invoiceUsage
    );

    return {
      allowed,
      currentUsage: usage.invoiceUsage,
      limit: usage.invoiceLimit,
      remaining,
      resetDate: usage.resetDate,
      message: allowed
        ? undefined
        : `You've reached your monthly invoice limit of ${usage.invoiceLimit}. ${usage.invoiceLimit === 0 ? 'Subscribe to get more uploads' : 'Upgrade your plan'} or wait ${usage.daysUntilReset} days for reset.`,
    };
  } catch {
    return {
      allowed: false,
      currentUsage: 0,
      limit: 0,
      remaining: 0,
      resetDate: new Date(),
      message: 'Error checking usage limits. Please try again.',
    };
  }
}

/**
 * Increment chat usage for a user
 */
export async function incrementChatUsage(
  userId: string
): Promise<boolean> {
  try {
    // Convert Clerk user ID to database user ID if needed
    let dbUserId = userId;
    if (userId.startsWith('user_')) {
      const user = await db.user.findUnique({
        where: { clerkId: userId },
        select: { id: true },
      });
      if (!user) {
        return false;
      }
      dbUserId = user.id;
    }

    // First check if the action is allowed
    const checkResult = await checkChatUsage(userId);

    if (!checkResult.allowed) {
      return false;
    }

    // Increment the usage
    await db.userUsage.update({
      where: { userId: dbUserId },
      data: {
        chatUsage: {
          increment: 1,
        },
      },
    });

    return true;
  } catch {
    return false;
  }
}

/**
 * Increment invoice usage for a user
 */
export async function incrementInvoiceUsage(
  userId: string
): Promise<boolean> {
  try {
    // Convert Clerk user ID to database user ID if needed
    let dbUserId = userId;
    if (userId.startsWith('user_')) {
      const user = await db.user.findUnique({
        where: { clerkId: userId },
        select: { id: true },
      });
      if (!user) {
        return false;
      }
      dbUserId = user.id;
    }

    // First check if the action is allowed
    const checkResult = await checkInvoiceUsage(userId);

    if (!checkResult.allowed) {
      return false;
    }

    // Increment the usage
    await db.userUsage.update({
      where: { userId: dbUserId },
      data: {
        invoiceUsage: {
          increment: 1,
        },
      },
    });

    return true;
  } catch {
    return false;
  }
}

/**
 * Get usage stats for dashboard display
 */
export async function getUserUsageStats(
  userId: string
): Promise<UsageStats | null> {
  return getCurrentUsage(userId);
}

/**
 * Reset usage for all users (typically called monthly by a cron job)
 */
export async function resetAllUsage(): Promise<{
  success: boolean;
  resetCount: number;
}> {
  try {
    const now = new Date();
    const nextMonthStart = new Date(
      now.getFullYear(),
      now.getMonth() + 1,
      1
    );

    const result = await db.userUsage.updateMany({
      data: {
        chatUsage: 0,
        invoiceUsage: 0,
        resetDate: nextMonthStart,
      },
    });

    return { success: true, resetCount: result.count };
  } catch {
    return { success: false, resetCount: 0 };
  }
}

/**
 * Helper function to get current user from auth and check usage
 */
export async function checkCurrentUserChatUsage(): Promise<UsageCheckResult> {
  const { userId } = await auth();

  if (!userId) {
    return {
      allowed: false,
      currentUsage: 0,
      limit: 0,
      remaining: 0,
      resetDate: new Date(),
      message: 'User not authenticated',
    };
  }

  return checkChatUsage(userId);
}

/**
 * Helper function to get current user from auth and check invoice usage
 */
export async function checkCurrentUserInvoiceUsage(): Promise<UsageCheckResult> {
  const { userId } = await auth();

  if (!userId) {
    return {
      allowed: false,
      currentUsage: 0,
      limit: 0,
      remaining: 0,
      resetDate: new Date(),
      message: 'User not authenticated',
    };
  }

  return checkInvoiceUsage(userId);
}

/**
 * Helper function to increment current user's chat usage
 */
export async function incrementCurrentUserChatUsage(): Promise<boolean> {
  const { userId } = await auth();

  if (!userId) {
    return false;
  }

  return incrementChatUsage(userId);
}

/**
 * Helper function to increment current user's invoice usage
 */
export async function incrementCurrentUserInvoiceUsage(): Promise<boolean> {
  const { userId } = await auth();

  if (!userId) {
    return false;
  }

  return incrementInvoiceUsage(userId);
}
