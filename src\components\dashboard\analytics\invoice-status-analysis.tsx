"use client";

import { useEffect, useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ChartContainer, ChartTooltip } from "@/components/ui/chart";
import {
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Pie,
  PieChart,
  ResponsiveContainer,
  XAxis,
  YAxis,
} from "recharts";
import { formatCurrency } from "@/lib/utils";
import { getInvoiceStatusAnalysis } from "@/lib/actions/analytics";
import type { FilterValues } from "./data-filters";
import { FileCheck, FileWarning, FileClock } from "lucide-react";
import Colors from '@/components/theme/Colors';
import { toast } from "sonner";

interface InvoiceStatusAnalysisProps {
  dateRange: {
    from: Date;
    to: Date;
  };
  filters: FilterValues;
}

interface StatusData {
  name: string;
  value: number;
  count: number;
  percentage: number;
  color: string;
}

interface AgingData {
  name: string;
  value: number;
  count: number;
  color: string;
}

export function InvoiceStatusAnalysis({
  dateRange,
  filters,
}: InvoiceStatusAnalysisProps) {
  const [statusData, setStatusData] = useState<StatusData[]>([]);
  const [agingData, setAgingData] = useState<AgingData[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeStatusIndex, setActiveStatusIndex] = useState<
    number | undefined
  >(undefined);

  useEffect(() => {
    async function loadStatusData() {
      setLoading(true);
      try {
        const { statusAnalysis, agingData } = await getInvoiceStatusAnalysis(
          dateRange.from,
          dateRange.to,
          filters
        );
        setStatusData(statusAnalysis);
        setAgingData(agingData);
      } catch {
        toast.error("Failed to load invoice status data");
      } finally {
        setLoading(false);
      }
    }

    loadStatusData();
  }, [dateRange, filters]);

  const statusChartConfig = {
    status: {
      label: "Status",
    },
  };

  const agingChartConfig = {
    aging: {
      label: "Aging",
    },
  };

  const onPieEnter = (_: unknown, index: number) => {
    setActiveStatusIndex(index);
  };

  const onPieLeave = () => {
    setActiveStatusIndex(undefined);
  };

  const RADIAN = Math.PI / 180;
  const renderCustomizedLabel = ({
    cx,
    cy,
    midAngle,
    innerRadius,
    outerRadius,
    percent,
  }: {
    cx: number;
    cy: number;
    midAngle: number;
    innerRadius: number;
    outerRadius: number;
    percent: number;
  }) => {
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill={Colors.textLight}
        textAnchor={x > cx ? "start" : "end"}
        dominantBaseline="central"
        className="text-xs font-medium"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  const getStatusIcon = (status: string) => {
    switch (status.toUpperCase()) {
      case "PAID":
        return <FileCheck className={`h-5 w-5 text-[${Colors.success}]`} />;
      case "PENDING":
        return <FileClock className={`h-5 w-5 text-[${Colors.warning}]`} />;
      case "OVERDUE":
        return <FileWarning className={`h-5 w-5 text-[${Colors.error}]`} />;
      default:
        return <FileCheck className={`h-5 w-5 text-[${Colors.text}]`} />;
    }
  };

  return (
    <div className="grid gap-6 md:grid-cols-2">
      <Card className={`border border-[${Colors.primary}]/10 bg-[${Colors.background}]/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300`}>
        <CardHeader className="flex flex-row items-start justify-between">
          <div>
            <CardTitle className="text-xl font-bold">Payment Status</CardTitle>
            <CardDescription>Paid vs. Pending vs. Overdue</CardDescription>
          </div>
          <div className="flex space-x-2">
            {statusData.slice(0, 3).map((status) => (
              <div key={status.name} className="mt-1">
                {getStatusIcon(status.name)}
              </div>
            ))}
          </div>
        </CardHeader>
        <CardContent>
          <ChartContainer config={statusChartConfig} className="h-[400px]">
            {loading ? (
              <div className="flex h-full items-center justify-center">
                <div className="animate-pulse flex flex-col items-center gap-2">
                  <div className={`h-8 w-8 rounded-full bg-[${Colors.primary}]/20`}></div>
                  <p className="text-muted-foreground">Loading chart data...</p>
                </div>
              </div>
            ) : statusData.length === 0 ? (
              <div className="flex h-full items-center justify-center">
                <p className="text-muted-foreground">
                  No status data available for the selected filters
                </p>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <defs>
                    {statusData.map((entry, index) => (
                      <filter
                        key={`shadow-${index}`}
                        id={`status-shadow-${index}`}
                        x="-20%"
                        y="-20%"
                        width="140%"
                        height="140%"
                      >
                        <feDropShadow
                          dx="0"
                          dy="0"
                          stdDeviation="6"
                          floodOpacity="0.3"
                        />
                      </filter>
                    ))}
                  </defs>
                  <Pie
                    data={statusData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={renderCustomizedLabel}
                    outerRadius={140}
                    fill="#8884d8"
                    dataKey="value"
                    animationDuration={1500}
                    animationBegin={300}
                    onMouseEnter={onPieEnter}
                    onMouseLeave={onPieLeave}
                  >
                    {statusData.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={entry.color}
                        filter={
                          activeStatusIndex === index
                            ? `url(#status-shadow-${index})`
                            : undefined
                        }
                        stroke={activeStatusIndex === index ? "white" : "none"}
                        strokeWidth={activeStatusIndex === index ? 2 : 0}
                        className="transition-all duration-300"
                        style={{
                          transform:
                            activeStatusIndex === index
                              ? "scale(1.05)"
                              : "scale(1)",
                          transformOrigin: "center",
                        }}
                      />
                    ))}
                  </Pie>
                  <ChartTooltip
                    content={({ active, payload }) => {
                      if (active && payload && payload.length) {
                        const data = payload[0].payload;
                        return (
                          <div className="rounded-lg border bg-background p-3 shadow-md">
                            <div className="grid grid-cols-2 gap-2">
                              <div className="flex flex-col">
                                <span className="text-[0.70rem] uppercase text-muted-foreground">
                                  Status
                                </span>
                                <div className="font-bold text-foreground flex items-center gap-1">
                                  {getStatusIcon(data.name)}
                                  <span>{data.name}</span>
                                </div>
                              </div>
                              <div className="flex flex-col">
                                <span className="text-[0.70rem] uppercase text-muted-foreground">
                                  Amount
                                </span>
                                <span className="font-bold">
                                  {formatCurrency(data.value)}
                                </span>
                              </div>
                              <div className="flex flex-col">
                                <span className="text-[0.70rem] uppercase text-muted-foreground">
                                  Count
                                </span>
                                <span className="font-bold">
                                  {data.count} invoices
                                </span>
                              </div>
                              <div className="flex flex-col">
                                <span className="text-[0.70rem] uppercase text-muted-foreground">
                                  Percentage
                                </span>
                                <span className="font-bold">
                                  {data.percentage}%
                                </span>
                              </div>
                            </div>
                          </div>
                        );
                      }
                      return null;
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            )}
          </ChartContainer>
        </CardContent>
      </Card>
      <Card className={`border border-[${Colors.primary}]/10 bg-[${Colors.background}]/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300`}>
        <CardHeader>
          <CardTitle className="text-xl font-bold">Aging Analysis</CardTitle>
          <CardDescription>Group invoices by age</CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer config={agingChartConfig} className="h-[400px]">
            {loading ? (
              <div className="flex h-full items-center justify-center">
                <div className="animate-pulse flex flex-col items-center gap-2">
                  <div className={`h-8 w-8 rounded-full bg-[${Colors.primary}]/20`}></div>
                  <p className="text-muted-foreground">Loading chart data...</p>
                </div>
              </div>
            ) : agingData.length === 0 ? (
              <div className="flex h-full items-center justify-center">
                <p className="text-muted-foreground">
                  No aging data available for the selected filters
                </p>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={agingData}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <defs>
                    {agingData.map((entry, index) => (
                      <linearGradient
                        key={`gradient-${index}`}
                        id={`barGradient-${index}`}
                        x1="0"
                        y1="0"
                        x2="0"
                        y2="1"
                      >
                        <stop
                          offset="0%"
                          stopColor={entry.color}
                          stopOpacity={0.8}
                        />
                        <stop
                          offset="100%"
                          stopColor={entry.color}
                          stopOpacity={0.3}
                        />
                      </linearGradient>
                    ))}
                  </defs>
                  <CartesianGrid
                    strokeDasharray="3 3"
                    vertical={false}
                    stroke="hsl(var(--border))"
                    opacity={0.3}
                  />
                  <XAxis
                    dataKey="name"
                    tickLine={false}
                    axisLine={false}
                    stroke="hsl(var(--foreground))"
                    fontSize={12}
                  />
                  <YAxis
                    tickFormatter={(value) => `$${value / 1000}k`}
                    tickLine={false}
                    axisLine={false}
                    stroke="hsl(var(--foreground))"
                    fontSize={12}
                  />
                  <ChartTooltip
                    content={({ active, payload }) => {
                      if (active && payload && payload.length) {
                        const data = payload[0].payload;
                        return (
                          <div className="rounded-lg border bg-background p-3 shadow-md">
                            <div className="grid grid-cols-2 gap-2">
                              <div className="flex flex-col">
                                <span className="text-[0.70rem] uppercase text-muted-foreground">
                                  Age
                                </span>
                                <span className="font-bold text-foreground">
                                  {data.name}
                                </span>
                              </div>
                              <div className="flex flex-col">
                                <span className="text-[0.70rem] uppercase text-muted-foreground">
                                  Amount
                                </span>
                                <span className="font-bold">
                                  {formatCurrency(data.value)}
                                </span>
                              </div>
                              <div className="flex flex-col">
                                <span className="text-[0.70rem] uppercase text-muted-foreground">
                                  Count
                                </span>
                                <span className="font-bold">
                                  {data.count} invoices
                                </span>
                              </div>
                            </div>
                          </div>
                        );
                      }
                      return null;
                    }}
                  />
                  <Bar
                    dataKey="value"
                    radius={[8, 8, 0, 0]}
                    animationDuration={1500}
                    animationBegin={300}
                  >
                    {agingData.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={`url(#barGradient-${index})`}
                        stroke={entry.color}
                        strokeWidth={1}
                        className="hover:opacity-90 transition-opacity duration-300"
                      />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            )}
          </ChartContainer>
        </CardContent>
      </Card>
    </div>
  );
}
