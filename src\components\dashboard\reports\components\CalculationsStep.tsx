import { ArrowR<PERSON> } from "lucide-react";
import { 
  <PERSON>, Card<PERSON><PERSON>er, CardTitle, CardDescription, CardContent, CardFooter 
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { 
  Select, SelectContent, SelectItem, SelectTrigger, SelectValue 
} from "@/components/ui/select";
import { CalculationCard } from "./CalculationCard";
import { FormData } from "../custom-report-builder";

interface CalculationsStepProps {
  formData: FormData;
  setFormData: React.Dispatch<React.SetStateAction<FormData>>;
  handleSelectChange: (id: string, value: string) => void;
  onBack: () => void;
  onNext: () => void;
}

export function CalculationsStep({
  formData,
  setFormData,
  handleSelectChange,
  onBack,
  onNext
}: CalculationsStepProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Calculations & Aggregations</CardTitle>
        <CardDescription>
          Define calculations and aggregations for your report
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium">Calculated Fields</h3>
          </div>

          <div className="space-y-3">
            {formData.selectedDataSource === "invoices" && (
              <>
                <CalculationCard
                  name="Total Amount"
                  formula="SUM(amount)"
                  type="Currency"
                  onSelect={() => {
                    setFormData(prev => ({
                      ...prev,
                      calculations: [...prev.calculations, "SUM(amount)"]
                    }));
                  }}
                  selected={formData.calculations.includes("SUM(amount)")}
                />
                <CalculationCard
                  name="Average Invoice Value"
                  formula="AVG(amount)"
                  type="Currency"
                  onSelect={() => {
                    setFormData(prev => ({
                      ...prev,
                      calculations: [...prev.calculations, "AVG(amount)"]
                    }));
                  }}
                  selected={formData.calculations.includes("AVG(amount)")}
                />
                <CalculationCard
                  name="Invoice Count"
                  formula="COUNT(invoiceNumber)"
                  type="Number"
                  onSelect={() => {
                    setFormData(prev => ({
                      ...prev,
                      calculations: [...prev.calculations, "COUNT(invoiceNumber)"]
                    }));
                  }}
                  selected={formData.calculations.includes("COUNT(invoiceNumber)")}
                />
              </>
            )}
            
            {formData.selectedDataSource === "vendors" && (
              <>
                <CalculationCard
                  name="Vendor Count"
                  formula="COUNT(name)"
                  type="Number"
                  onSelect={() => {
                    setFormData(prev => ({
                      ...prev,
                      calculations: [...prev.calculations, "COUNT(name)"]
                    }));
                  }}
                  selected={formData.calculations.includes("COUNT(name)")}
                />
              </>
            )}
            
            {formData.selectedDataSource === "categories" && (
              <>
                <CalculationCard
                  name="Category Count"
                  formula="COUNT(name)"
                  type="Number"
                  onSelect={() => {
                    setFormData(prev => ({
                      ...prev,
                      calculations: [...prev.calculations, "COUNT(name)"]
                    }));
                  }}
                  selected={formData.calculations.includes("COUNT(name)")}
                />
              </>
            )}
          </div>
        </div>

        <Separator />

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium">Grouping & Aggregations</h3>
          </div>

          <div className="grid gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="group-by">Group By</Label>
              <Select 
                value={formData.groupBy} 
                onValueChange={(value) => handleSelectChange('groupBy', value)}
              >
                <SelectTrigger id="group-by">
                  <SelectValue placeholder="Select field" />
                </SelectTrigger>
                <SelectContent>
                  {formData.selectedDataSource === "invoices" && (
                    <>
                      <SelectItem value="month">Month</SelectItem>
                      <SelectItem value="vendorName">Vendor</SelectItem>
                      <SelectItem value="status">Status</SelectItem>
                      <SelectItem value="category">Category</SelectItem>
                    </>
                  )}
                  {formData.selectedDataSource === "vendors" && (
                    <>
                      <SelectItem value="name">Name</SelectItem>
                    </>
                  )}
                  {formData.selectedDataSource === "categories" && (
                    <>
                      <SelectItem value="name">Category Name</SelectItem>
                    </>
                  )}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="sort-by">Sort By</Label>
              <Select
                value={formData.sortBy} 
                onValueChange={(value) => handleSelectChange('sortBy', value)}
              >
                <SelectTrigger id="sort-by">
                  <SelectValue placeholder="Select field" />
                </SelectTrigger>
                <SelectContent>
                  {formData.selectedDataSource === "invoices" && (
                    <>
                      <SelectItem value="issueDate">Issue Date</SelectItem>
                      <SelectItem value="amount">Amount</SelectItem>
                      <SelectItem value="vendorName">Vendor Name</SelectItem>
                    </>
                  )}
                  {formData.selectedDataSource === "vendors" && (
                    <>
                      <SelectItem value="name">Name</SelectItem>
                    </>
                  )}
                  {formData.selectedDataSource === "categories" && (
                    <>
                      <SelectItem value="name">Category Name</SelectItem>
                    </>
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={onBack}>
          Back
        </Button>
        <Button onClick={onNext}>
          Next
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  );
} 