"use client"

import React from "react"
import {
  <PERSON>,
  LineChart as RechartsLineChart,
  CartesianGrid,
  XAxis,
  YAxis,
  Area,
  ResponsiveContainer,
  ReferenceLine
} from "recharts"

import { cn } from "@/lib/utils"
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent
} from "./chart"
import Colors from "../theme/Colors"

export interface LineChartProps {
  data: Record<string, unknown>[]
  x: string
  y: string | string[]
  className?: string
  colors?: string[]
  showLegend?: boolean
  showGrid?: boolean
  showDots?: boolean
  curveType?: "linear" | "monotone" | "natural" | "step"
  strokeWidth?: number
  fillArea?: boolean
  height?: number | string
  xLabel?: string
  yLabel?: string
  animationDuration?: number
  showReferenceLine?: boolean
  referenceLineValue?: number
  referenceLineLabel?: string
  dotSize?: number
  activeDotSize?: number
  gradientFill?: boolean
  dashArray?: string
}

// Enhanced color palette with gradients
const defaultColors = [
  { main: Colors.chartColors[0], gradient: ["rgba(67, 97, 238, 0.6)", "rgba(67, 97, 238, 0.1)"] },
  { main: Colors.chartColors[1], gradient: ["rgba(76, 201, 240, 0.6)", "rgba(76, 201, 240, 0.1)"] },
  { main: Colors.chartColors[2], gradient: ["rgba(247, 37, 133, 0.6)", "rgba(247, 37, 133, 0.1)"] },
  { main: Colors.chartColors[3], gradient: ["rgba(77, 144, 142, 0.6)", "rgba(77, 144, 142, 0.1)"] },
  { main: Colors.chartColors[4], gradient: ["rgba(248, 150, 30, 0.6)", "rgba(248, 150, 30, 0.1)"] },
  { main: Colors.chartColors[5], gradient: ["rgba(144, 190, 109, 0.6)", "rgba(144, 190, 109, 0.1)"] },
];

export function LineChart({
  data = [],
  x,
  y,
  className,
  colors,
  showLegend = true,
  showGrid = true,
  showDots = true,
  curveType = "monotone",
  strokeWidth = 2,
  fillArea = false,
  xLabel,
  yLabel,
  animationDuration = 1500,
  showReferenceLine = false,
  referenceLineValue = 0,
  referenceLineLabel = "Average",
  dotSize = 4,
  activeDotSize = 6,
  gradientFill = true,
  dashArray = "3 3"
}: LineChartProps) {
  const yFields = Array.isArray(y) ? y : [y]
  const colorPalette = colors ? colors.map(color => ({ main: color, gradient: [color, color] })) : defaultColors;

  // Create chart config from the y fields
  const chartConfig = yFields.reduce((acc, field, index) => {
    acc[field] = {
      label: field.replace(/_/g, ' ').replace(/^\w/, c => c.toUpperCase()),
      color: colorPalette[index % colorPalette.length].main
    }
    return acc
  }, {} as Record<string, { label: string, color: string }>)

  // Create unique gradient IDs for each line
  const gradientIds = yFields.map((field, index) =>
    `line-gradient-${field.replace(/[^a-zA-Z0-9]/g, '')}-${index}`
  );

  return (
    <ChartContainer className={cn("rounded-lg", className)} config={chartConfig}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsLineChart
          data={data}
          margin={{ top: 20, right: 20, bottom: 25, left: 25 }}
        >
          {/* Define gradients for area fills */}
          <defs>
            {yFields.map((field, index) => (
              <linearGradient
                key={gradientIds[index]}
                id={gradientIds[index]}
                x1="0" y1="0" x2="0" y2="1"
              >
                <stop
                  offset="0%"
                  stopColor={colorPalette[index % colorPalette.length].main}
                  stopOpacity={0.6}
                />
                <stop
                  offset="100%"
                  stopColor={colorPalette[index % colorPalette.length].main}
                  stopOpacity={0.05}
                />
              </linearGradient>
            ))}
          </defs>

          {showGrid && (
            <CartesianGrid
              strokeDasharray={dashArray}
              stroke={Colors.border}
              strokeOpacity={0.6}
              vertical={true}
              horizontal={true}
            />
          )}

          <XAxis
            dataKey={x}
            axisLine={false}
            tickLine={false}
            tick={{ fill: Colors.info, fontSize: 12 }}
            label={xLabel ? {
              value: xLabel,
              position: "bottom",
              offset: 15,
              style: { fill: Colors.text, fontSize: 14, fontWeight: 500 }
            } : undefined}
          />
          <YAxis
            axisLine={false}
            tickLine={false}
            tick={{ fill: Colors.info, fontSize: 12 }}
            label={yLabel ? {
              value: yLabel,
              angle: -90,
              position: "left",
              offset: -10,
              style: { fill: Colors.text, fontSize: 14, fontWeight: 500 }
            } : undefined}
          />

          {showReferenceLine && (
            <ReferenceLine
              y={referenceLineValue}
              stroke={Colors.border}
              strokeDasharray="3 3"
              label={{
                value: referenceLineLabel,
                position: 'right',
                fill: Colors.textLight,
                fontSize: 12
              }}
            />
          )}

          <ChartTooltip
            content={<ChartTooltipContent />}
            cursor={{ stroke: Colors.border, strokeWidth: 1, strokeDasharray: '4 4' }}
            wrapperStyle={{
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              border: `1px solid ${Colors.border}`,
              borderRadius: '6px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
            }}
          />

          {showLegend && (
            <ChartLegend
              content={<ChartLegendContent />}
              verticalAlign="top"
              align="center"
              wrapperStyle={{ paddingBottom: '10px' }}
            />
          )}

          {fillArea && yFields.map((field, index) => (
            <Area
              key={`area-${field}`}
              type={curveType}
              dataKey={field}
              stroke="none"
              fill={gradientFill ? `url(#${gradientIds[index]})` : colorPalette[index % colorPalette.length].main}
              fillOpacity={1}
            />
          ))}

          {yFields.map((field, index) => (
            <Line
              key={field}
              type={curveType}
              dataKey={field}
              stroke={colorPalette[index % colorPalette.length].main}
              strokeWidth={strokeWidth}
              dot={showDots ? {
                fill: colorPalette[index % colorPalette.length].main,
                r: dotSize,
                strokeWidth: 1,
                stroke: Colors.border
              } : false}
              activeDot={{
                r: activeDotSize,
                fill: colorPalette[index % colorPalette.length].main,
                stroke: Colors.border,
                strokeWidth: 2
              }}
              connectNulls={true}
              animationDuration={animationDuration}
              animationEasing="ease-in-out"
            />
          ))}
        </RechartsLineChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}
