import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { ScheduledReportEmailTemplate } from '@/lib/email/templates/scheduled-report-email';

export async function POST(request: Request) {
  try {
    // Initialize Resend with API key inside the function
    const resendApiKey = process.env.RESEND_API_KEY;
    if (!resendApiKey) {
      return NextResponse.json(
        { error: 'Email service not configured' },
        { status: 500 }
      );
    }

    const resend = new Resend(resendApiKey);

    const {
      recipients,
      reportName,
      pdfUrl,
      excelUrl,
      userName = 'there',
      customSubject,
      customMessage,
      includeCompanyLogo = true,
    } = await request.json();

    // Validate required fields
    if (
      !recipients ||
      !Array.isArray(recipients) ||
      recipients.length === 0
    ) {
      return NextResponse.json(
        { error: 'Recipients are required' },
        { status: 400 }
      );
    }

    if (!reportName) {
      return NextResponse.json(
        { error: 'Report name is required' },
        { status: 400 }
      );
    }

    if (!pdfUrl && !excelUrl) {
      return NextResponse.json(
        { error: 'At least one report URL is required' },
        { status: 400 }
      );
    }

    const fromEmail = process.env.EMAIL_FROM || '<EMAIL>';
    const appUrl =
      process.env.NEXT_PUBLIC_APP_URL || 'https://billix.io';

    // Send email using Resend
    const { data, error } = await resend.emails.send({
      from: `Billix Reports <${fromEmail}>`,
      to: recipients,
      subject: customSubject || `Your Report: ${reportName} is Ready`,
      react: ScheduledReportEmailTemplate({
        reportName,
        pdfUrl,
        excelUrl,
        userName,
        customMessage,
        includeCompanyLogo,
        templateStyle: 'professional',
        primaryColor: '#3C5A99',
        includeMetadata: true,
        generatedDate: new Date(),
        appUrl,
      }),
    });

    if (error) {
      console.error('Error sending email with Resend:', error);
      return NextResponse.json(
        { error: `Failed to send email: ${error.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true, messageId: data?.id });
  } catch (error) {
    console.error('Error in send-report API route:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
