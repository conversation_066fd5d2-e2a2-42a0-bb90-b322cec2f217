'use client';

import { useState } from 'react';
import { FilePreview } from '@/components/dashboard/pages/upload/file-preview';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { InvoiceDataDisplay } from '@/components/dashboard/pages/upload/invoice-data-display';
import type { InvoiceData } from '@/types/invoice';
import { safeToString } from '@/utils/object-to-string';
import {
  CheckCircle,
  XCircle,
  Clock,
  Globe,
  AlertTriangle,
  ChevronDown,
  ChevronUp,
  Briefcase,
  Building,
  ShoppingBag,
  Save,
} from 'lucide-react';
import { saveInvoiceToDatabase } from '@/actions/save-invoice-to-db';
import { updateInvoiceMetadata } from '@/actions/invoice-actions';
import { toast } from 'sonner';
import { ScannerAnimation } from './scanner-animation';

interface ProcessingFileCardProps {
  file: {
    file: File;
    previewUrl?: string;
    progress: number;
    status: 'pending' | 'processing' | 'completed' | 'error';
    error?: string;
    data?: InvoiceData;
  };
  index: number;
  onUpdateData: (index: number, data: InvoiceData) => void;
}

export function ProcessingFileCard({
  file,
  index,
  onUpdateData,
}: ProcessingFileCardProps) {
  // Auto-expand when processing is completed
  const [isDetailsOpen, setIsDetailsOpen] = useState(file.status === 'completed');
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [saveSuccess, setSaveSuccess] = useState(false);

  const toggleDetails = () => {
    setIsDetailsOpen(!isDetailsOpen);
  };

  // Function to format processing time
  const formatProcessingTime = (ms: number) => {
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    const minutes = Math.floor(ms / 60000);
    const seconds = ((ms % 60000) / 1000).toFixed(1);
    return `${minutes}m ${seconds}s`;
  };

  // Get category icon
  const getCategoryIcon = (category?: string) => {
    if (!category) return <Briefcase className="h-4 w-4" />;

    switch (category.toLowerCase()) {
      case 'technology':
      case 'it services':
      case 'telecommunications':
        return (
          <svg
            className="h-4 w-4"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <rect width="20" height="14" x="2" y="3" rx="2" />
            <line x1="8" x2="16" y1="21" y2="21" />
            <line x1="12" x2="12" y1="17" y2="21" />
          </svg>
        );
      case 'retail':
      case 'wholesale':
        return <ShoppingBag className="h-4 w-4" />;
      case 'food & beverage':
      case 'hospitality':
        return (
          <svg
            className="h-4 w-4"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M17 12h.01" />
            <path d="M19.5 9.5L21 8l-1.5-1.5a5.5 5.5 0 0 0-8 0l9.5 9.5a5.5 5.5 0 0 0 0-8L19.5 9.5Z" />
            <path d="M8 14.5 5 12l3-2.5m6 0-2.5-2.5L9 9.5" />
            <path d="M7 9.5c-.5-1-1.5-2-3-2" />
            <path d="M11 3v2" />
            <path d="m13.5 5.5-2 2" />
            <path d="M7 14.5v-2" />
            <path d="M3 7H1" />
            <path d="M5 3h2" />
          </svg>
        );
      case 'manufacturing':
      case 'construction':
        return <Building className="h-4 w-4" />;
      default:
        return <Briefcase className="h-4 w-4" />;
    }
  };

  // Handle saving invoice with user selections
  const handleSaveInvoice = async () => {
    setIsSaving(true);
    setSaveError(null);
    setSaveSuccess(false);

    try {
      if (file.data) {
        if (file.data.id) {
          // Update metadata only if the invoice already has an ID
          const result = await updateInvoiceMetadata(file.data.id, {
            selectedCategory:
              file.data.meta?.suggestions?.selectedCategory,
            selectedVendorType:
              file.data.meta?.suggestions?.selectedVendorType,
          });
          if (result.success) {
            setSaveSuccess(true);
            toast.success('Invoice updated with your selections.');
            if (result.data) {
              // Convert database result to InvoiceData format (null -> undefined, Date -> string)
              const convertedData: Partial<InvoiceData> = {
                id: result.data.id,
                invoiceNumber: result.data.invoiceNumber ?? undefined,
                date:
                  result.data.issueDate?.toISOString() ?? undefined,
                dueDate:
                  result.data.dueDate?.toISOString() ?? undefined,
                notes: result.data.notes ?? undefined,
                fileUrl: result.data.originalFileUrl ?? undefined,
                thumbnailUrl: result.data.thumbnailUrl ?? undefined,
                // Only include properties that exist in InvoiceData interface
              };

              onUpdateData(index, {
                ...file.data,
                ...convertedData,
              });
            }
            setTimeout(() => setSaveSuccess(false), 3000);
          } else {
            setSaveError(result.error || 'Failed to update invoice');
            toast.error(result.error || 'Failed to update invoice');
          }
        } else {
          // Save new invoice to database
          const result = await saveInvoiceToDatabase(file.data);
          if (result.success) {
            setSaveSuccess(true);
            toast.success('Invoice saved with your selections.');
            if (result.data?.id) {
              const updatedData = {
                ...file.data,
                id: result.data.id,
              };
              onUpdateData(index, updatedData);
            }
            setTimeout(() => setSaveSuccess(false), 3000);
          } else {
            // Handle save error
            setSaveError(result.error || 'Failed to save invoice');
            toast.error(result.error || 'Failed to save invoice');
          }
        }
      } else {
        // If the file hasn't been processed successfully yet
        setSaveError(
          'Please wait for the invoice to be fully processed before saving.'
        );
        toast.error(
          'Please wait for the invoice to be fully processed before saving.'
        );
      }
    } catch (error) {
      setSaveError(
        error instanceof Error ? error.message : 'Unknown error'
      );
      toast.error(
        error instanceof Error ? error.message : 'Unknown error'
      );
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div
      className={`border rounded-lg overflow-hidden shadow-sm transition-shadow hover:shadow-md ${
        file.status === 'completed'
          ? 'border-green-200 dark:border-green-900/50'
          : file.status === 'error'
            ? 'border-red-200 dark:border-red-900/50'
            : file.status === 'processing'
              ? 'border-blue-200 dark:border-blue-900/50'
              : ''
      }`}
    >
      <div className="flex items-center p-4 bg-muted/30 dark:bg-muted/10">
        <div className="h-16 w-16 overflow-hidden rounded-md mr-4 bg-white dark:bg-gray-900 border dark:border-gray-800">
          <FilePreview
            src={file.previewUrl || ''}
            type={file.file.type}
            previewMode="icon"
          />
        </div>
        <div className="flex-1">
          <h3 className="font-medium truncate">{file.file.name}</h3>
          <div className="flex flex-wrap items-center text-sm text-muted-foreground">
            <span className="mr-3">
              {(file.file.size / 1024 / 1024).toFixed(2)}MB
            </span>

            {/* Display language and country when available */}
            {file.data?.meta?.language && (
              <span className="flex items-center ml-2 mr-3">
                <Globe className="h-3 w-3 mr-1" />
                {file.data.meta.languageName ||
                  file.data.meta.language}
              </span>
            )}

            {/* Display suggested category */}
            {file.data?.meta?.suggestions?.category && (
              <span className="flex items-center ml-2 mr-3">
                {getCategoryIcon(file.data.meta.suggestions.category)}
                <span className="ml-1">
                  {file.data.meta.suggestions.category}
                </span>
              </span>
            )}
          </div>
        </div>

        <div className="flex items-center ml-4 gap-2">
          {file.status === 'pending' && (
            <Badge variant="outline" className="font-normal">
              Pending
            </Badge>
          )}
          {file.status === 'processing' && (
            <div className="flex items-center">
              <Badge
                variant="secondary"
                className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200 font-normal flex items-center"
              >
                <Clock className="h-3 w-3 mr-1 animate-pulse" />
                Processing
              </Badge>
            </div>
          )}
          {file.status === 'completed' && (
            <div className="flex items-center">
              <Badge
                variant="secondary"
                className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200 font-normal flex items-center"
              >
                <CheckCircle className="h-3 w-3 mr-1" />
                Completed
              </Badge>
              {file.data?.meta?.processingTime && (
                <span className="text-xs text-muted-foreground ml-2 flex items-center">
                  <Clock className="h-3 w-3 mr-1" />
                  {formatProcessingTime(
                    file.data.meta.processingTime
                  )}
                </span>
              )}
            </div>
          )}
          {file.status === 'error' && (
            <Badge
              variant="destructive"
              className="font-normal flex items-center dark:bg-red-900/70"
            >
              <XCircle className="h-3 w-3 mr-1" />
              Error
            </Badge>
          )}



          {/* Toggle details button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleDetails}
            className="p-1 h-auto"
          >
            {isDetailsOpen ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>

      {/* Progress bar */}
      <Progress
        value={file.progress}
        className="h-1.5 rounded-none"
        style={{
          backgroundColor:
            file.status === 'error'
              ? 'rgb(239, 68, 68, 0.2)' // red-500 with opacity
              : file.status === 'completed'
                ? 'rgb(34, 197, 94, 0.2)' // green-500 with opacity
                : 'rgb(236, 236, 236)',
        }}
        color={
          file.status === 'error'
            ? 'rgb(239, 68, 68)' // red-500
            : file.status === 'completed'
              ? 'rgb(34, 197, 94)' // green-500
              : undefined
        }
      />

      {/* Scanner Animation for Processing State */}
      {file.status === 'processing' && (
        <div className="p-4 border-t bg-gradient-to-br from-indigo-50/50 to-purple-50/50 dark:from-indigo-950/20 dark:to-purple-950/20">
          <ScannerAnimation
            imageUrl={file.previewUrl}
            title="Processing Invoice"
            subtitle="Extracting and analyzing invoice data..."
          />
        </div>
      )}

      {file.error && (
        <div className="p-4 text-sm border-t bg-red-50 dark:bg-red-950/30 text-red-800 dark:text-red-200 border-red-100 dark:border-red-900/50">
          <div className="flex items-start">
            <AlertTriangle className="h-4 w-4 text-red-500 dark:text-red-400 mr-2 flex-shrink-0 mt-0.5" />
            <div>
              <p className="font-medium">Error encountered:</p>
              <p>{file.error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Details section */}
      {isDetailsOpen && file.data && (
        <div className="p-4 border-t bg-muted/20 dark:bg-muted/5">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-medium flex items-center">
              Extracted Data
            </h3>
            <Button
              onClick={handleSaveInvoice}
              disabled={isSaving || saveSuccess}
              size="sm"
              className="ml-2"
            >
              {isSaving ? (
                <span className="flex items-center">
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Saving...
                </span>
              ) : saveSuccess ? (
                'Saved!'
              ) : (
                <>
                  <Save className="h-4 w-4 mr-1" />
                  Save
                </>
              )}
            </Button>
          </div>

          {/* Quick preview of key data */}
          {!isDetailsOpen && file.data && (
            <div className="mt-3 grid grid-cols-2 md:grid-cols-4 gap-3">
              {file.data.vendor?.name && (
                <div className="bg-muted/30 dark:bg-muted/10 p-2 rounded text-sm">
                  <span className="block text-xs text-muted-foreground">
                    Vendor
                  </span>
                  <span className="font-medium truncate block">
                    {file.data.vendor.name}
                  </span>
                </div>
              )}

              {file.data.date && (
                <div className="bg-muted/30 dark:bg-muted/10 p-2 rounded text-sm">
                  <span className="block text-xs text-muted-foreground">
                    Date
                  </span>
                  <span className="font-medium">
                    {safeToString(file.data.date)}
                  </span>
                </div>
              )}

              {file.data.financials?.total && (
                <div className="bg-muted/30 dark:bg-muted/10 p-2 rounded text-sm">
                  <span className="block text-xs text-muted-foreground">
                    Total
                  </span>
                  <span className="font-medium">
                    {safeToString(file.data.financials.total)}
                  </span>
                </div>
              )}

              {file.data.lineItems?.length > 0 && (
                <div className="bg-muted/30 dark:bg-muted/10 p-2 rounded text-sm">
                  <span className="block text-xs text-muted-foreground">
                    Line Items
                  </span>
                  <span className="font-medium">
                    {file.data.lineItems.length}
                  </span>
                </div>
              )}

              {/* Show category if available */}
              {file.data.meta?.suggestions?.category && (
                <div className="bg-muted/30 dark:bg-muted/10 p-2 rounded text-sm col-span-2">
                  <span className="block text-xs text-muted-foreground">
                    Suggested Category
                  </span>
                  <span className="font-medium flex items-center">
                    {getCategoryIcon(
                      file.data.meta.suggestions.category
                    )}
                    <span className="ml-1">
                      {safeToString(
                        file.data.meta.suggestions.category
                      )}
                    </span>
                  </span>
                </div>
              )}

              {/* Show vendor type if available */}
              {file.data.meta?.suggestions?.vendorType && (
                <div className="bg-muted/30 dark:bg-muted/10 p-2 rounded text-sm col-span-2">
                  <span className="block text-xs text-muted-foreground">
                    Vendor Type
                  </span>
                  <span className="font-medium">
                    {safeToString(
                      file.data.meta.suggestions.vendorType
                    )}
                  </span>
                </div>
              )}
            </div>
          )}

          {/* Full invoice data display */}
          <InvoiceDataDisplay
            data={file.data}
            onUpdate={(updatedData) =>
              onUpdateData(index, updatedData)
            }
          />

          {/* Save error display */}
          {saveError && (
            <div className="mt-4 p-3 bg-red-50 dark:bg-red-950/30 text-red-800 dark:text-red-200 rounded-lg">
              <p className="text-sm font-medium">Save Error:</p>
              <p className="text-sm">{saveError}</p>
            </div>
          )}

          {/* Save success display */}
          {saveSuccess && (
            <div className="mt-4 p-3 bg-green-50 dark:bg-green-950/30 text-green-800 dark:text-green-200 rounded-lg">
              <p className="text-sm font-medium">
                Invoice saved successfully!
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
