'use client';

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatDate } from '@/lib/utils';

interface InvoiceMetadataCardProps {
  invoice: {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    extractedData?: {
      meta?: {
        language?: string;
        languageName?: string;
        confidence?: {
          overall: number;
        };
      };
    };
  };
}

export function InvoiceMetadataCard({ invoice }: InvoiceMetadataCardProps) {
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">Metadata</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-muted-foreground">Created</span>
            <span>{formatDate(invoice.createdAt)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Last Updated</span>
            <span>{formatDate(invoice.updatedAt)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Invoice ID</span>
            <span className="font-mono">{invoice.id.substring(0, 8)}</span>
          </div>
          {invoice.extractedData?.meta?.language && (
            <div className="flex justify-between">
              <span className="text-muted-foreground">Language</span>
              <span>
                {invoice.extractedData.meta.languageName ||
                  invoice.extractedData.meta.language}
              </span>
            </div>
          )}
          {invoice.extractedData?.meta?.confidence?.overall !== undefined && (
            <div className="flex justify-between">
              <span className="text-muted-foreground">
                Extraction Confidence
              </span>
              <Badge variant="outline">
                {invoice.extractedData.meta.confidence.overall}%
              </Badge>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}