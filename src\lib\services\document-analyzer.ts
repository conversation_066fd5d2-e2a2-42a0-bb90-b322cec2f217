"use server";

import { DocumentType } from '../types';

interface DocumentStructure {
  header?: {
    bounds: [number, number, number, number];
    elements: DocumentElement[];
  };
  body?: {
    bounds: [number, number, number, number];
    elements: DocumentElement[];
  };
  footer?: {
    bounds: [number, number, number, number];
    elements: DocumentElement[];
  };
  tables?: {
    bounds: [number, number, number, number];
    rowCount: number;
    columnCount: number;
    headerRow?: boolean;
  }[];
  signatures?: {
    bounds: [number, number, number, number];
    name?: string;
  }[];
}

interface DocumentElement {
  type: 'text' | 'image' | 'table' | 'checkbox' | 'signature' | 'stamp' | 'barcode' | 'form_field';
  bounds: [number, number, number, number];
  content?: string;
  confidence?: number;
}

interface DocumentAnalysisResult {
  documentType: DocumentType;
  confidence: number;
  language: string;
  pageCount: number;
  structure?: DocumentStructure;
  orientation: 'portrait' | 'landscape';
  quality: 'high' | 'medium' | 'low';
  metadata: Record<string, unknown>;
}

/**
 * Analyze document to determine type, structure, and other features
 */
export async function analyzeDocument(fileUrl: string): Promise<DocumentAnalysisResult> {
  // Extract file type from URL
  const fileType = getFileTypeFromUrl(fileUrl);
  
  // In a production system, this would use computer vision and ML to analyze the document
  // For now, we'll create a simulated analysis based on file type
  
  // Default result
  const result: DocumentAnalysisResult = {
    documentType: 'INVOICE',  // Default to invoice
    confidence: 0.8,
    language: 'en',
    pageCount: 1,
    orientation: 'portrait',
    quality: 'high',
    metadata: {}
  };
  
  // In a real system, here we would call a document analysis API or ML model
  
  // Simulate different document types based on file extension for testing
  if (fileType === 'pdf') {
    // PDFs are more likely to be formal documents like contracts
    if (Math.random() > 0.7) {
      result.documentType = 'CONTRACT';
      result.structure = createContractStructure();
    } else {
      result.documentType = 'INVOICE';
      result.structure = createInvoiceStructure();
    }
    result.pageCount = Math.floor(Math.random() * 5) + 1;
  } else if (fileType === 'image') {
    // Images are more likely to be certificates or receipts
    if (Math.random() > 0.6) {
      result.documentType = 'CERTIFICATE';
      result.structure = createCertificateStructure();
    } else {
      result.documentType = 'INVOICE';
      result.structure = createInvoiceStructure();
      // Images of invoices often have lower quality
      result.quality = Math.random() > 0.5 ? 'medium' : 'high';
    }
  } else if (fileType === 'spreadsheet') {
    // Spreadsheets are typically invoices or financial data
    result.documentType = 'INVOICE';
    result.structure = createSpreadsheetInvoiceStructure();
  } else {
    // For unknown types, lower the confidence
    result.confidence = 0.5;
  }
  
  return result;
}

/**
 * Identify file type from URL or extension
 */
function getFileTypeFromUrl(fileUrl: string): string {
  const extension = fileUrl.split('.').pop()?.toLowerCase();
  
  if (['pdf'].includes(extension || '')) {
    return 'pdf';
  } else if (['jpg', 'jpeg', 'png', 'tiff', 'tif', 'webp'].includes(extension || '')) {
    return 'image';
  } else if (['xls', 'xlsx', 'csv'].includes(extension || '')) {
    return 'spreadsheet';
  }
  
  return 'unknown';
}

/**
 * Create a sample invoice structure
 */
function createInvoiceStructure(): DocumentStructure {
  return {
    header: {
      bounds: [0, 100, 20, 0],
      elements: [
        {
          type: 'text',
          bounds: [2, 95, 15, 5],
          content: 'INVOICE',
          confidence: 0.9
        },
        {
          type: 'image',
          bounds: [2, 30, 15, 5],
          content: 'company_logo',
          confidence: 0.8
        }
      ]
    },
    body: {
      bounds: [20, 100, 80, 0],
      elements: [
        {
          type: 'text',
          bounds: [22, 95, 25, 5],
          content: 'Invoice details',
          confidence: 0.9
        }
      ]
    },
    tables: [
      {
        bounds: [30, 95, 70, 5],
        rowCount: 5,
        columnCount: 4,
        headerRow: true
      }
    ],
    footer: {
      bounds: [80, 100, 100, 0],
      elements: [
        {
          type: 'text',
          bounds: [82, 95, 95, 5],
          content: 'Payment information',
          confidence: 0.85
        }
      ]
    }
  };
}

/**
 * Create a sample contract structure
 */
function createContractStructure(): DocumentStructure {
  return {
    header: {
      bounds: [0, 100, 15, 0],
      elements: [
        {
          type: 'text',
          bounds: [2, 95, 12, 5],
          content: 'CONTRACT AGREEMENT',
          confidence: 0.95
        }
      ]
    },
    body: {
      bounds: [15, 100, 90, 0],
      elements: [
        {
          type: 'text',
          bounds: [16, 95, 89, 16],
          content: 'Contract terms...',
          confidence: 0.9
        }
      ]
    },
    signatures: [
      {
        bounds: [70, 90, 80, 85],
        name: 'Party A'
      },
      {
        bounds: [70, 90, 90, 85],
        name: 'Party B'
      }
    ],
    footer: {
      bounds: [90, 100, 100, 0],
      elements: [
        {
          type: 'text',
          bounds: [91, 99, 98, 91],
          content: 'Page 1 of 10',
          confidence: 0.8
        }
      ]
    }
  };
}

/**
 * Create a sample certificate structure
 */
function createCertificateStructure(): DocumentStructure {
  return {
    header: {
      bounds: [0, 100, 30, 0],
      elements: [
        {
          type: 'text',
          bounds: [10, 90, 25, 10],
          content: 'CERTIFICATE',
          confidence: 0.95
        },
        {
          type: 'image',
          bounds: [40, 70, 60, 40],
          content: 'seal',
          confidence: 0.8
        }
      ]
    },
    body: {
      bounds: [30, 100, 70, 0],
      elements: [
        {
          type: 'text',
          bounds: [35, 85, 65, 35],
          content: 'This certifies that...',
          confidence: 0.9
        }
      ]
    },
    signatures: [
      {
        bounds: [70, 90, 80, 85],
        name: 'Authority signature'
      }
    ],
    footer: {
      bounds: [70, 100, 100, 0],
      elements: [
        {
          type: 'text',
          bounds: [75, 95, 90, 75],
          content: 'Official certification',
          confidence: 0.85
        }
      ]
    }
  };
}

/**
 * Create a sample spreadsheet invoice structure
 */
function createSpreadsheetInvoiceStructure(): DocumentStructure {
  return {
    header: {
      bounds: [0, 100, 10, 0],
      elements: [
        {
          type: 'text',
          bounds: [0, 100, 10, 0],
          content: 'INVOICE',
          confidence: 0.9
        }
      ]
    },
    body: {
      bounds: [10, 100, 100, 0],
      elements: []
    },
    tables: [
      {
        bounds: [10, 100, 100, 10],
        rowCount: 20,
        columnCount: 8,
        headerRow: true
      }
    ]
  };
}

/**
 * Identify the document type from content and structure
 * In a real implementation, this would use ML to analyze the content
 */
export async function detectDocumentType(
  text: string,
  structure?: DocumentStructure
): Promise<{ documentType: DocumentType; confidence: number }> {
  // Convert to lowercase for easier matching
  const lowerText = text.toLowerCase();
  
  // Invoice keywords
  const invoiceKeywords = [
    'invoice', 'bill', 'receipt', 'payment', 'order', 'purchase', 
    'customer', 'quantity', 'price', 'total', 'subtotal', 'tax', 
    'amount due', 'payment terms', 'due date'
  ];
  
  // Contract keywords
  const contractKeywords = [
    'contract', 'agreement', 'terms', 'conditions', 'parties', 
    'hereby', 'obligations', 'rights', 'provisions', 'terminate', 
    'clause', 'effective date', 'execute', 'binding', 'legal'
  ];
  
  // Certificate keywords
  const certificateKeywords = [
    'certificate', 'certify', 'achievement', 'award', 'completion', 
    'qualified', 'granted', 'diploma', 'accredited', 'recognition', 
    'presented', 'authorized', 'verified', 'credential'
  ];
  
  // Count keyword occurrences
  const invoiceCount = invoiceKeywords.filter(keyword => lowerText.includes(keyword)).length;
  const contractCount = contractKeywords.filter(keyword => lowerText.includes(keyword)).length;
  const certificateCount = certificateKeywords.filter(keyword => lowerText.includes(keyword)).length;
  
  // Calculate scores (normalize by dividing by the number of keywords)
  const invoiceScore = invoiceCount / invoiceKeywords.length;
  const contractScore = contractCount / contractKeywords.length;
  const certificateScore = certificateCount / certificateKeywords.length;
  
  // Consider structure if available
  let structureScore = 0;
  if (structure) {
    // Invoices typically have tables
    if (structure.tables && structure.tables.length > 0) {
      structureScore += 0.3;
    }
    
    // Contracts typically have signatures
    if (structure.signatures && structure.signatures.length >= 2) {
      structureScore -= 0.2; // Less likely to be an invoice
    }
  }
  
  // Determine document type based on keyword scores and structure
  const maxScore = Math.max(invoiceScore, contractScore, certificateScore);
  
  if (maxScore === invoiceScore) {
    return { 
      documentType: 'INVOICE', 
      confidence: Math.min(0.5 + invoiceScore + structureScore, 0.98)
    };
  } else if (maxScore === contractScore) {
    return { 
      documentType: 'CONTRACT', 
      confidence: Math.min(0.5 + contractScore, 0.98)
    };
  } else if (maxScore === certificateScore) {
    return { 
      documentType: 'CERTIFICATE',
      confidence: Math.min(0.5 + certificateScore, 0.98)
    };
  }
  
  // Default to OTHER with low confidence if nothing matches well
  return { documentType: 'OTHER', confidence: 0.3 };
}
