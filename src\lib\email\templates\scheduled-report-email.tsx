import React from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON>, 
  Container, 
  Head, 
  <PERSON>ing, 
  Hr, 
  Html, 
  Img,
  Link,
  Preview, 
  Section, 
  Text 
} from '@react-email/components';
import Colors from "../../../components/theme/Colors";

interface ScheduledReportEmailProps {
  reportName: string;
  pdfUrl?: string;
  excelUrl?: string;
  userName?: string;
  customMessage?: string;
  includeCompanyLogo?: boolean;
  templateStyle?: 'professional' | 'standard' | 'minimal';
  primaryColor?: string;
  includeMetadata?: boolean;
  generatedDate?: Date;
  appUrl?: string;
}

export const ScheduledReportEmailTemplate = ({
  reportName,
  pdfUrl,
  excelUrl,
  userName = 'there',
  customMessage,
  includeCompanyLogo = true,
  primaryColor = Colors.primaryDark,
  includeMetadata = true,
  generatedDate = new Date(),
  appUrl = process.env.NEXT_PUBLIC_APP_URL,
}: ScheduledReportEmailProps) => {
  const formattedDate = generatedDate.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });

  return (
    <Html>
      <Head />
      <Preview>
        Your report &quot;{reportName}&quot; is ready for download
      </Preview>
      <Body style={styles.body}>
        <Container style={styles.container}>
          {includeCompanyLogo && (
            <div style={styles.logoContainer}>
              <Img
                src={'/billix_logo.jpg'}
                width="120"
                alt="Billix"
                style={styles.logo}
              />
            </div>
          )}
          
          <Heading style={styles.heading}>
            Your Report Is Ready
          </Heading>
          
          <Section style={styles.section}>
            <Text style={styles.text}>
              Hello {userName},
            </Text>
            
            {customMessage ? (
              <Text style={styles.text}>{customMessage}</Text>
            ) : (
              <Text style={styles.text}>
                Your report <strong>{reportName}</strong> has been generated and is now ready for you to download.
              </Text>
            )}
            
            <div style={styles.buttonContainer}>
              {pdfUrl && (
                <Button href={pdfUrl} style={{...styles.button, backgroundColor: primaryColor }}>
                  Download PDF
                </Button>
              )}
              
              {excelUrl && (
                <Button href={excelUrl} style={{...styles.button, backgroundColor: Colors.success }}>
                  Download Excel
                </Button>
              )}
            </div>
            
            {includeMetadata && (
              <Text style={styles.note}>
                This report was generated on {formattedDate}.
              </Text>
            )}
            
            <div style={styles.dashboardContainer}>
              <Text style={styles.dashboardText}>
                You can also view and manage all your reports directly in the dashboard:
              </Text>
              <Button href={`${appUrl}/dashboard/reports`} style={styles.dashboardButton}>
                View in Dashboard
              </Button>
            </div>
          </Section>
          
          <Hr style={styles.hr} />
          
          <Text style={styles.footer}>
            &copy; {new Date().getFullYear()} Billix. All rights reserved.
          </Text>
          
          <Text style={styles.helpText}>
            Need help? Visit the <Link href={`${appUrl}/dashboard/settings`} style={styles.helpLink}>Support and Help Center</Link> in your dashboard.
          </Text>
        </Container>
      </Body>
    </Html>
  );
};

const styles = {
  body: {
    backgroundColor: Colors.background,
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    padding: '20px 0',
  },
  container: {
    margin: '0 auto',
    maxWidth: '600px',
    backgroundColor: Colors.textLight,
    borderRadius: '12px',
    overflow: 'hidden',
    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.05)',
  },
  logoContainer: {
    textAlign: 'center' as const,
    backgroundColor: Colors.primaryDark,
    padding: '20px 0',
  },
  logo: {
    margin: '0 auto',
  },
  heading: {
    fontSize: '24px',
    fontWeight: 'bold',
    textAlign: 'center' as const,
    padding: '24px 0 12px',
    margin: '0',
    color: Colors.text,
  },
  section: {
    padding: '0 32px 32px',
  },
  text: {
    fontSize: '16px',
    color: Colors.text,
    lineHeight: '24px',
    marginBottom: '16px',
  },
  buttonContainer: {
    display: 'flex',
    flexDirection: 'row' as const,
    justifyContent: 'center' as const,
    gap: '40px',
    margin: '32px 0',
    flexWrap: 'wrap' as const,
  },
  button: {
    backgroundColor: Colors.primaryDark,
    color: Colors.textLight,
    borderRadius: '6px',
    fontSize: '16px',
    padding: '12px 24px',
    textDecoration: 'none',
    textAlign: 'center' as const,
    display: 'inline-block',
    fontWeight: 'bold',
    cursor: 'pointer',
    border: 'none',
    minWidth: '160px',
  },
  dashboardContainer: {
    marginTop: '24px',
    backgroundColor: Colors.background,
    padding: '16px',
    borderRadius: '8px',
    border: `1px solid ${Colors.border}`,
  },
  dashboardText: {
    fontSize: '14px',
    color: Colors.text,
    marginBottom: '16px',
    textAlign: 'center' as const,
  },
  dashboardButton: {
    backgroundColor: Colors.text,
    color: Colors.textLight,
    borderRadius: '6px',
    fontSize: '14px',
    padding: '10px 20px',
    textDecoration: 'none',
    textAlign: 'center' as const,
    display: 'block',
    margin: '0 auto',
    fontWeight: 'bold',
    border: 'none',
    cursor: 'pointer',
  },
  note: {
    fontSize: '14px',
    color: Colors.info,
    fontStyle: 'italic',
    textAlign: 'center' as const,
    margin: '16px 0',
  },
  hr: {
    borderColor: Colors.border,
    margin: '0',
  },
  footer: {
    fontSize: '14px',
    color: Colors.info,
    textAlign: 'center' as const,
    padding: '16px 32px 8px',
  },
  helpText: {
    fontSize: '13px',
    color: Colors.info,
    textAlign: 'center' as const,
    padding: '0 32px 24px',
  },
  helpLink: {
    color: Colors.primaryDark,
    fontWeight: 'bold',
    textDecoration: 'none',
  },
}; 