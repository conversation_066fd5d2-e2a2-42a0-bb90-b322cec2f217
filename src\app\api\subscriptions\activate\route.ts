import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@clerk/nextjs/server';
import db from '@/db/db';

// This endpoint handles manual subscription activation for successful checkouts
// when webhooks might be delayed or haven't processed yet
export async function POST(request: NextRequest) {
  try {
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { sessionId } = await request.json();

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    // Find the database user
    const dbUser = await db.user.findUnique({
      where: { clerkId: user.id },
    });

    if (!dbUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if there's already an active subscription for this user
    const existingSubscription = await db.subscription.findFirst({
      where: {
        userId: dbUser.id,
        status: {
          in: ['active', 'trialing', 'past_due'],
        },
      },
      orderBy: {
        id: 'desc',
      },
    });

    if (existingSubscription) {
      return NextResponse.json({
        success: true,
        message: 'User already has an active subscription',
        subscription: existingSubscription,
      });
    }

    // If no active subscription, create a temporary one for sandbox testing
    // This is a fallback for when webhooks are delayed in sandbox mode
    if (process.env.NEXT_PUBLIC_PADDLE_ENVIRONMENT === 'sandbox') {
      // Find the business plan (fallback plan)
      const fallbackPlan = await db.plan.findFirst({
        where: {
          name: {
            contains: 'Business',
            mode: 'insensitive',
          },
        },
      });

      if (fallbackPlan) {
        const tempSubscription = await db.subscription.create({
          data: {
            provider: 'paddle',
            paddleSubscriptionId: sessionId,
            paddleTransactionId: sessionId,
            name: 'Temporary Subscription',
            email: user.emailAddresses[0]?.emailAddress || '',
            status: 'active',
            statusFormatted: 'Active',
            price: fallbackPlan.price,
            userId: dbUser.id,
            planId: fallbackPlan.id,
            renewsAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
          },
        });

        console.log(`Created temporary subscription for user ${dbUser.id} with session ${sessionId}`);

        return NextResponse.json({
          success: true,
          message: 'Temporary subscription created',
          subscription: tempSubscription,
        });
      }
    }

    return NextResponse.json({
      success: false,
      message: 'No subscription found and unable to create temporary subscription',
    });

  } catch (error) {
    console.error('Error activating subscription:', error);
    return NextResponse.json(
      { error: 'Failed to activate subscription' },
      { status: 500 }
    );
  }
}
