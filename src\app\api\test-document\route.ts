import { NextRequest, NextResponse } from 'next/server';
import { DocumentGenerationEngine } from '@/lib/ai/document-generation-engine';
import { uploadToVercelBlob } from '@/lib/blob';
import { getCurrentUserId } from '@/lib/clerk-helpers';
import db from '@/db/db';

/**
 * Test endpoint for document generation
 * This will create a sample invoice and return a downloadable URL
 */
export async function POST(request: NextRequest) {
  try {
    // For testing purposes, make authentication optional
    const authenticatedUserId = await getCurrentUserId();
    const userId = authenticatedUserId || 'test-user'; // Use test user ID when not authenticated
    
    const body = await request.json();
    const { type = 'invoice', format = 'pdf' } = body;

    // Get sample invoice data
    const sampleInvoiceData = await getSampleInvoiceData(userId);

    // Generate document
    const documents = await DocumentGenerationEngine.generateDocument(userId, {
      type: type as any,
      format: format as any,
      data: sampleInvoiceData,
      options: {
        includeCharts: true,
        includeImages: true,
        includeBranding: true,
        quality: 'high',
        language: 'en',
        rtl: false
      }
    });

    // Upload to blob storage and create download URLs
    const documentsWithUrls = await Promise.all(
      documents.map(async (doc) => {
        try {
          // Determine content type
          const contentTypes = {
            pdf: 'application/pdf',
            excel: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            image: 'image/png'
          };
          
          const extensions = {
            pdf: 'pdf',
            excel: 'xlsx', 
            image: 'png'
          };
          
          const contentType = contentTypes[doc.format as keyof typeof contentTypes] || 'application/octet-stream';
          const extension = extensions[doc.format as keyof typeof extensions] || doc.format;
          
          // Create unique filename
          const timestamp = Date.now();
          const cleanTitle = doc.metadata.title.replace(/[^a-zA-Z0-9-_]/g, '-');
          const blobFilename = `documents/${timestamp}-${cleanTitle}.${extension}`;
          
          // Upload to Vercel Blob storage
          const downloadUrl = await uploadToVercelBlob(blobFilename, doc.buffer, contentType);
          
          return {
            id: doc.id,
            type: doc.type,
            format: doc.format,
            downloadUrl,
            metadata: {
              ...doc.metadata,
              size: doc.buffer.length
            }
          };
        } catch (error) {
          console.error('Error uploading document:', error);
          
          // Fallback: create data URL
          const base64 = doc.buffer.toString('base64');
          const contentType = doc.format === 'pdf' ? 'application/pdf' : 
                             doc.format === 'excel' ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' : 
                             'image/png';
          
          return {
            id: doc.id,
            type: doc.type,
            format: doc.format,
            downloadUrl: `data:${contentType};base64,${base64}`,
            metadata: {
              ...doc.metadata,
              size: doc.buffer.length,
              isDataUrl: true
            }
          };
        }
      })
    );

    return NextResponse.json({
      success: true,
      message: `Generated ${documents.length} document(s) successfully!`,
      documents: documentsWithUrls,
      metadata: {
        documentsGenerated: documents.length,
        totalSize: documents.reduce((sum, doc) => sum + doc.buffer.length, 0),
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Document generation test error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to generate document',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

async function getSampleInvoiceData(userId: string) {
  // Try to get a real invoice from the database
  const invoice = await db.invoice.findFirst({
    where: { userId },
    orderBy: { createdAt: 'desc' }
  });

  if (invoice) {
    return {
      invoiceNumber: invoice.invoiceNumber || `INV-${Date.now()}`,
      vendorName: invoice.vendorName || 'Client Name',
      amount: invoice.amount || 0,
      issueDate: invoice.issueDate || new Date(),
      dueDate: invoice.dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      status: invoice.status || 'PENDING',
      title: invoice.title || 'Professional services',
      lineItems: [
        {
          description: invoice.title || 'Professional services',
          quantity: 1,
          unitPrice: invoice.amount || 0,
          totalPrice: invoice.amount || 0
        }
      ]
    };
  }

  // Return sample data if no invoice exists
  return {
    invoiceNumber: `INV-${Date.now()}`,
    vendorName: 'Sample Client',
    amount: 1500,
    issueDate: new Date(),
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    status: 'PENDING',
    title: 'Web development services',
    lineItems: [
      {
        description: 'Web development consultation',
        quantity: 10,
        unitPrice: 100,
        totalPrice: 1000
      },
      {
        description: 'UI/UX Design',
        quantity: 5,
        unitPrice: 100,
        totalPrice: 500
      }
    ]
  };
}
