import type * as React from "react"
import { cn } from "@/lib/utils"

export function ChartContainer({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) {
  return <div className={cn("rounded-md border bg-card text-card-foreground shadow-sm", className)} {...props} />
}

export function ChartTooltip({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) {
  return <div className={cn("rounded-md border bg-popover text-popover-foreground shadow-sm", className)} {...props} />
}

export function ChartTooltipContent({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) {
  return <div className={cn("p-4", className)} {...props} />
}

export function ChartLegend({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) {
  return <div className={cn("flex items-center", className)} {...props} />
}

interface ChartLegendItemProps extends React.HTMLAttributes<HTMLDivElement> {
  name: string
  color: string
  colorClassName?: string
}

export function ChartLegendItem({ 
  name, 
  color, 
  className, 
  colorClassName,
  ...props 
}: ChartLegendItemProps) {
  return (
    <div className={cn("flex items-center gap-2", className)} {...props}>
      <div className={cn("h-2 w-2 rounded-full", colorClassName)} style={{ backgroundColor: color }} />
      <div className="text-sm text-muted-foreground">{name}</div>
    </div>
  )
}

export const Chart = () => null

