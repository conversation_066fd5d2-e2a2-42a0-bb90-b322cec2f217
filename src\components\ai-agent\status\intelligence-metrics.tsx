'use client';

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

interface IntelligenceMetricsProps {
  intelligence: {
    status: 'excellent' | 'good' | 'fair' | 'poor';
    contextAccuracy: number;
    predictionConfidence: number;
    recommendations: string[];
  };
  getStatusColor: (status: string) => string;
}

export function IntelligenceMetrics({ intelligence, getStatusColor }: IntelligenceMetricsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          AI Intelligence
          <Badge className={getStatusColor(intelligence.status)}>
            {intelligence.status}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <div className="flex justify-between mb-2">
            <span>Context Accuracy</span>
            <span>{(intelligence.contextAccuracy * 100).toFixed(1)}%</span>
          </div>
          <Progress value={intelligence.contextAccuracy * 100} />
        </div>
        <div>
          <div className="flex justify-between mb-2">
            <span>Prediction Confidence</span>
            <span>{(intelligence.predictionConfidence * 100).toFixed(1)}%</span>
          </div>
          <Progress value={intelligence.predictionConfidence * 100} />
        </div>
        {intelligence.recommendations.length > 0 && (
          <div>
            <h4 className="font-semibold mb-2">Recommendations:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
              {intelligence.recommendations.map((rec, index) => (
                <li key={index}>{rec}</li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
}