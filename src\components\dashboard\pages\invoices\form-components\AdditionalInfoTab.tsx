"use client";

import { ChangeEvent } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";

interface AdditionalInfoTabProps {
  financialInfo: {
    subtotal: string;
    tax: string;
    shipping: string;
    discount: string;
    total: string;
  };
  additionalInfo: {
    termsAndConditions: string;
    paymentTerms: string;
    paymentMethod: string;
    paymentDetails: string;
  };
  handleFinancialInfoChange: (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleAdditionalInfoChange: (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
}

export function AdditionalInfoTab({
  financialInfo,
  additionalInfo,
  handleFinancialInfoChange,
  handleAdditionalInfoChange,
}: AdditionalInfoTabProps) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Financial Summary</h3>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="financial-subtotal">Subtotal</Label>
          <Input
            id="financial-subtotal"
            name="subtotal"
            value={financialInfo.subtotal}
            onChange={handleFinancialInfoChange}
            placeholder="0.00"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="financial-tax">Tax</Label>
          <Input
            id="financial-tax"
            name="tax"
            value={financialInfo.tax}
            onChange={handleFinancialInfoChange}
            placeholder="0.00"
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="financial-shipping">Shipping</Label>
          <Input
            id="financial-shipping"
            name="shipping"
            value={financialInfo.shipping}
            onChange={handleFinancialInfoChange}
            placeholder="0.00"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="financial-discount">Discount</Label>
          <Input
            id="financial-discount"
            name="discount"
            value={financialInfo.discount}
            onChange={handleFinancialInfoChange}
            placeholder="0.00"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="financial-total">Total</Label>
        <Input
          id="financial-total"
          name="total"
          value={financialInfo.total}
          onChange={handleFinancialInfoChange}
          placeholder="0.00"
        />
      </div>

      <Separator className="my-4" />

      <h3 className="text-lg font-medium">Payment Information</h3>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="payment-method">Payment Method</Label>
          <Input
            id="payment-method"
            name="paymentMethod"
            value={additionalInfo.paymentMethod}
            onChange={handleAdditionalInfoChange}
            placeholder="e.g., Credit Card, Bank Transfer"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="payment-details">Payment Details</Label>
          <Input
            id="payment-details"
            name="paymentDetails"
            value={additionalInfo.paymentDetails}
            onChange={handleAdditionalInfoChange}
            placeholder="e.g., Account number, Card details"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="payment-terms">Payment Terms</Label>
        <Input
          id="payment-terms"
          name="paymentTerms"
          value={additionalInfo.paymentTerms}
          onChange={handleAdditionalInfoChange}
          placeholder="e.g., Net 30, Due on Receipt"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="terms-conditions">Terms and Conditions</Label>
        <Textarea
          id="terms-conditions"
          name="termsAndConditions"
          value={additionalInfo.termsAndConditions}
          onChange={handleAdditionalInfoChange}
          placeholder="Terms and conditions"
          rows={3}
        />
      </div>
    </div>
  );
}
