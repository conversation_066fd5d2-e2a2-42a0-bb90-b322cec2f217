import { NextResponse } from 'next/server';
import {
  updatePlanLimits,
  checkPlanLimits,
} from '@/lib/update-plan-limits';

export async function POST() {
  try {
    console.log('🚀 Starting plan limits update...');

    // First check current state
    console.log('📋 Current plans before update:');
    await checkPlanLimits();

    // Update the plans
    const result = await updatePlanLimits();

    if (result.success) {
      // Check final state
      console.log('📋 Plans after update:');
      const updatedPlans = await checkPlanLimits();

      return NextResponse.json({
        success: true,
        message: `Successfully updated ${result.updated} plans with usage limits`,
        plans: updatedPlans,
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to update plan limits',
          details: result.error,
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('❌ Error in update-plan-limits API:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details:
          error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const plans = await checkPlanLimits();
    return NextResponse.json({
      success: true,
      plans,
    });
  } catch (error) {
    console.error('❌ Error checking plan limits:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to check plan limits',
        details:
          error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
