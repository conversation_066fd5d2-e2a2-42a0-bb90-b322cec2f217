import React from 'react';
import { 
  Body, 
  Container, 
  Head, 
  Heading, 
  Hr, 
  Html, 
  Preview, 
  Section, 
  Text 
} from '@react-email/components';
import Colors from "../../../components/theme/Colors";

interface FailedReportEmailProps {
  reportName: string;
  errorMessage: string;
  userName?: string;
  reportId?: string;
  scheduledTime?: Date;
}

export const FailedReportEmailTemplate = ({
  reportName,
  errorMessage,
  userName = 'there',
  reportId,
  scheduledTime,
}: FailedReportEmailProps) => {
  const formattedDate = scheduledTime ? scheduledTime.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }) : 'unknown time';

  return (
    <Html>
      <Head />
      <Preview>
        Error generating your report: {reportName}
      </Preview>
      <Body style={styles.body}>
        <Container style={styles.container}>
          <Heading style={{ ...styles.heading, textAlign: 'center', color: Colors.error }}>
            BILLIX
          </Heading>
          
          <Heading style={styles.heading}>
            Report Generation Error
          </Heading>
          
          <Section style={styles.section}>
            <Text style={styles.text}>
              Hello {userName},
            </Text>
            
            <Text style={styles.text}>
              We encountered an error while trying to generate your report <strong>{reportName}</strong>.
            </Text>
            
            <Text style={styles.text}>
              <strong>Error details:</strong>
            </Text>
            
            <Text style={{...styles.text, padding: '12px', backgroundColor: Colors.background, borderLeft: `4px solid ${Colors.error}`}}>
              {errorMessage}
            </Text>
            
            {reportId && (
              <Text style={styles.note}>
                Report ID: {reportId}
              </Text>
            )}
            
            {scheduledTime && (
              <Text style={styles.note}>
                This report was scheduled to run on {formattedDate}.
              </Text>
            )}
            
            <Text style={styles.text}>
              Our team has been notified of this issue. If you need immediate assistance, please contact support.
            </Text>
          </Section>
          
          <Hr style={styles.hr} />
          
          <Text style={styles.footer}>
            &copy; {new Date().getFullYear()} Billix. All rights reserved.
          </Text>
        </Container>
      </Body>
    </Html>
  );
};

const styles = {
  body: {
    backgroundColor: Colors.background,
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  },
  container: {
    margin: '0 auto',
    padding: '20px 0',
    maxWidth: '600px',
  },
  heading: {
    fontSize: '24px',
    fontWeight: 'bold',
    marginTop: '32px',
    color: Colors.text,
  },
  section: {
    backgroundColor: Colors.textLight,
    padding: '32px',
    borderRadius: '8px',
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
    margin: '20px 0',
  },
  text: {
    fontSize: '16px',
    color: Colors.text,
    lineHeight: '24px',
    marginBottom: '14px',
  },
  note: {
    fontSize: '14px',
    color: Colors.info,
    fontStyle: 'italic',
    marginTop: '14px',
    marginBottom: '24px',
  },
  hr: {
    borderColor: Colors.border,
    margin: '26px 0',
  },
  footer: {
    fontSize: '14px',
    color: Colors.info,
    textAlign: 'center' as const,
    marginTop: '20px',
  },
}; 