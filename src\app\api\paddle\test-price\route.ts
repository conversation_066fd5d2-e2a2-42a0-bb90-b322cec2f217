import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

const PADDLE_API_URL = 'https://sandbox-api.paddle.com';
const PADDLE_HEADERS = {
  Authorization: `Bearer ${process.env.PADDLE_API_KEY}`,
  'Content-Type': 'application/json',
};

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const priceId = searchParams.get('priceId');

    if (!priceId) {
      return NextResponse.json(
        { error: 'Price ID is required' },
        { status: 400 }
      );
    }

    console.log('Testing price ID:', priceId);

    // Test if the price ID exists
    const response = await fetch(`${PADDLE_API_URL}/prices/${priceId}`, {
      method: 'GET',
      headers: PADDLE_HEADERS,
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Price ID test failed:', {
        status: response.status,
        error: errorData,
      });
      return NextResponse.json({
        success: false,
        error: `Price ID test failed: ${response.status}`,
        details: errorData,
      });
    }

    const priceData = await response.json();
    console.log('Price ID test successful:', priceData);

    return NextResponse.json({
      success: true,
      data: priceData,
    });
  } catch (error) {
    console.error('Error testing price ID:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
} 