-- CreateTable
CREATE TABLE "InvoiceEnhancementJob" (
    "id" TEXT NOT NULL,
    "invoiceId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "enhancementTypes" TEXT[],
    "startedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),
    "error" TEXT,
    "results" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InvoiceEnhancementJob_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "InvoiceEnhancementJob_userId_idx" ON "InvoiceEnhancementJob"("userId");

-- CreateIndex
CREATE INDEX "InvoiceEnhancementJob_invoiceId_idx" ON "InvoiceEnhancementJob"("invoiceId");

-- CreateIndex
CREATE INDEX "InvoiceEnhancementJob_status_idx" ON "InvoiceEnhancementJob"("status");

-- AddForeignKey
ALTER TABLE "InvoiceEnhancementJob" ADD CONSTRAINT "InvoiceEnhancementJob_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InvoiceEnhancementJob" ADD CONSTRAINT "InvoiceEnhancementJob_invoiceId_fkey" FOREIGN KEY ("invoiceId") REFERENCES "Invoice"("id") ON DELETE CASCADE ON UPDATE CASCADE;
