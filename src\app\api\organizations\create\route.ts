import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import db from '@/db/db';

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const { name } = await req.json();
    if (
      !name ||
      typeof name !== 'string' ||
      name.trim().length === 0
    ) {
      return NextResponse.json(
        { error: 'Organization name is required' },
        { status: 400 }
      );
    }

    // Get the current user
    const user = await db.user.findUnique({
      where: { clerkId: userId },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      );
    }

    // Always create a new organization and add the user as a member
    const dbOrganization = await db.organization.create({
      data: {
        name: name.trim(),
        members: {
          connect: { id: user.id },
        },
      },
    });

    return NextResponse.json({
      success: true,
      organization: {
        id: dbOrganization.id,
        name: dbOrganization.name,
      },
    });
  } catch (error) {
    console.error('Error creating organization:', error);
    return NextResponse.json(
      { error: 'Failed to create organization' },
      { status: 500 }
    );
  }
}
