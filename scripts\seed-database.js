const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Plan data that matches the pricing page
const paddlePlans = [
  // Starter Plan - Monthly
  {
    productId: 1,
    productName: 'Starter',
    variantId: 101,
    paddlePriceId: 'pri_01jz83p3v1rs5r3xskt9z4ww50',
    name: 'Starter',
    description:
      'Perfect for small businesses processing fewer than 100 invoices per month.',
    price: '2900', // $29.00 in cents
    interval: 'month',
    intervalCount: 1,
    sort: 1,
    chatLimit: 5,
    invoiceLimit: 10,
  },
  // Starter Plan - Yearly
  {
    productId: 1,
    productName: 'Starter',
    variantId: 102,
    paddlePriceId: 'pri_01jz83xxfn40khba0cvdw42t1x',
    name: 'Starter (Yearly)',
    description:
      'Perfect for small businesses processing fewer than 100 invoices per month.',
    price: '29000', // $290.00 in cents
    interval: 'year',
    intervalCount: 1,
    sort: 2,
    chatLimit: 5,
    invoiceLimit: 10,
  },
  // Business Plan - Monthly
  {
    productId: 2,
    productName: 'Business',
    variantId: 201,
    paddlePriceId: 'pri_01jz83q8503fhrqh3gwsqa23zb',
    name: 'Business',
    description:
      'Ideal for growing businesses with advanced analytics needs and higher volume.',
    price: '7900', // $79.00 in cents
    interval: 'month',
    intervalCount: 1,
    sort: 3,
    chatLimit: 50,
    invoiceLimit: 100,
  },
  // Business Plan - Yearly
  {
    productId: 2,
    productName: 'Business',
    variantId: 202,
    paddlePriceId: 'pri_01jz83yvxaamh0t9zymtykbqcz',
    name: 'Business (Yearly)',
    description:
      'Ideal for growing businesses with advanced analytics needs and higher volume.',
    price: '79000', // $790.00 in cents
    interval: 'year',
    intervalCount: 1,
    sort: 4,
    chatLimit: 50,
    invoiceLimit: 100,
  },
  // Enterprise Plan - Monthly
  {
    productId: 3,
    productName: 'Enterprise',
    variantId: 301,
    paddlePriceId: 'pri_01jz83r7fw92rb1pb9a3qt7ty3',
    name: 'Enterprise',
    description:
      'Comprehensive solution for large organizations with custom requirements.',
    price: '19900', // $199.00 in cents
    interval: 'month',
    intervalCount: 1,
    sort: 5,
    chatLimit: 500,
    invoiceLimit: 1000,
  },
  // Enterprise Plan - Yearly
  {
    productId: 3,
    productName: 'Enterprise',
    variantId: 302,
    paddlePriceId: 'pri_01jz83zhw7065b2dzyhcbx77h2',
    name: 'Enterprise (Yearly)',
    description:
      'Comprehensive solution for large organizations with custom requirements.',
    price: '199000', // $1990.00 in cents
    interval: 'year',
    intervalCount: 1,
    sort: 6,
    chatLimit: 500,
    invoiceLimit: 1000,
  },
];

async function seedDatabase() {
  console.log('🌱 Starting database seeding...');

  try {
    // First, let's check if plans already exist
    const existingPlans = await prisma.plan.findMany();
    console.log(`Found ${existingPlans.length} existing plans`);

    let createdCount = 0;
    let updatedCount = 0;

    for (const planData of paddlePlans) {
      // Check if plan with this paddlePriceId already exists
      const existingPlan = await prisma.plan.findFirst({
        where: { paddlePriceId: planData.paddlePriceId },
      });

      if (existingPlan) {
        // Update existing plan with correct data
        await prisma.plan.update({
          where: { id: existingPlan.id },
          data: {
            productId: planData.productId,
            productName: planData.productName,
            variantId: planData.variantId,
            paddlePriceId: planData.paddlePriceId,
            name: planData.name,
            description: planData.description,
            price: planData.price,
            interval: planData.interval,
            intervalCount: planData.intervalCount,
            sort: planData.sort,
            chatLimit: planData.chatLimit,
            invoiceLimit: planData.invoiceLimit,
          },
        });
        console.log(
          `✅ Updated plan: ${planData.name} (${planData.paddlePriceId})`
        );
        updatedCount++;
      } else {
        // Create new plan
        await prisma.plan.create({
          data: {
            productId: planData.productId,
            productName: planData.productName,
            variantId: planData.variantId,
            paddlePriceId: planData.paddlePriceId,
            name: planData.name,
            description: planData.description,
            price: planData.price,
            interval: planData.interval,
            intervalCount: planData.intervalCount,
            sort: planData.sort,
            chatLimit: planData.chatLimit,
            invoiceLimit: planData.invoiceLimit,
          },
        });
        console.log(
          `✅ Created plan: ${planData.name} (${planData.paddlePriceId})`
        );
        createdCount++;
      }
    }

    console.log('🎉 Successfully seeded all plans!');
    console.log(
      `📊 Summary: ${createdCount} created, ${updatedCount} updated`
    );

    // Return summary
    const finalPlans = await prisma.plan.findMany({
      orderBy: { sort: 'asc' },
    });

    console.log('\n📋 Final plans in database:');
    finalPlans.forEach((plan) => {
      console.log(
        `  - ${plan.name}: $${(parseInt(plan.price) / 100).toFixed(2)}/${plan.interval} | ${plan.chatLimit} chats, ${plan.invoiceLimit} invoices`
      );
    });

    return {
      success: true,
      created: createdCount,
      updated: updatedCount,
    };
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding
seedDatabase()
  .then((result) => {
    console.log('\n🏁 Seeding completed:', result);
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Seeding failed:', error);
    process.exit(1);
  });
