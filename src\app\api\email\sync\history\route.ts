import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import db from '@/db/db';

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const { userId: clerkUserId } = await auth();
    if (!clerkUserId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the user ID from query params or use authenticated user
    const searchParams = request.nextUrl.searchParams;
    const queryUserId = searchParams.get('userId');
    
    // User is always allowed to access their own history data
    const targetUserId = queryUserId || clerkUserId;

    // Fetch sync history from the database
    // This assumes you have a table for sync history
    // If not, we'll create dummy data for now
    try {
      const syncHistory = await db.emailSyncHistory.findMany({
        where: {
          userId: targetUserId,
          provider: 'gmail',
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 10, // Limit to 10 most recent entries
      });

      return NextResponse.json({
        history: syncHistory.map(item => ({
          id: item.id,
          date: item.createdAt.toISOString(),
          status: item.status,
          count: item.processedCount,
        })),
      });
    } catch (dbError) {
      console.warn('Error fetching sync history, using fallback:', dbError);
      
      // If we don't have the table or there's another DB issue,
      // return some fallback data
      return NextResponse.json({
        history: [
          {
            id: '1',
            date: new Date().toISOString(),
            status: 'success',
            count: 0,
          },
        ],
      });
    }
  } catch (error) {
    console.error("Error fetching sync history:", error);
    return NextResponse.json(
      { error: "Failed to fetch sync history" },
      { status: 500 }
    );
  }
} 