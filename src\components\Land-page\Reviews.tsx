"use client";

import { useEffect, useState } from "react";
import { motion } from "motion/react";
import { <PERSON>, <PERSON>rk<PERSON> } from "lucide-react";

/**
 * @typedef {Object} Testimonial
 * @property {number} id
 * @property {string} quote
 * @property {string} author
 * @property {string} title
 */

type Testimonial = {
  id: number;
  quote: string;
  author: string;
  title: string;
};

export default function TestimonialsAnimation() {
  /** @type {Testimonial[]} */
  const testimonials = [
    {
      id: 1,
      quote:
        "The AI-powered data extraction has cut our invoice processing time by 80%. What used to take hours now takes minutes, and with remarkable accuracy.",
      author: "<PERSON>",
      title: "Finance Director, TechCorp",
    },
    {
      id: 2,
      quote:
        "The financial analytics dashboard gives us insights we never had before. We've identified cost-saving opportunities that would have gone unnoticed.",
      author: "<PERSON>",
      title: "CFO, GrowthPartners",
    },
    {
      id: 3,
      quote:
        "Their anomaly detection feature caught duplicate invoices we would have paid twice. The platform has already paid for itself in prevented errors.",
      author: "<PERSON>",
      title: "Accounts Payable Manager, RetailGiant",
    },
    {
      id: 4,
      quote:
        "The integration with our accounting software was seamless. Now our entire team has access to real-time financial data without manual data entry.",
      author: "<PERSON>",
      title: "Controller, HealthServe",
    },
    {
      id: 5,
      quote:
        "We process over 5,000 invoices monthly, and this platform handles everything flawlessly. The predictive analytics feature has transformed our budgeting process.",
      author: "Jennifer Lee",
      title: "Finance Operations Lead, GlobalTrade",
    },
    {
      id: 6,
      quote:
        "The custom categories and AI training features allow us to adapt the platform to our unique business needs. It's the most flexible solution we've found.",
      author: "Robert Taylor",
      title: "Head of Finance, InnovateMedia",
    },
  ];

  // Create columns of testimonials with different arrangements for responsive design
  const column1 = testimonials.slice(0, 2);
  const column2 = testimonials.slice(2, 4);
  const column3 = testimonials.slice(4, 6);
  // Mobile view combines all testimonials in one scrollable column
  const mobileColumn = [
    testimonials[0],
    testimonials[2],
    testimonials[4],
    testimonials[1],
    testimonials[3],
    testimonials[5],
  ];
                                          
  return (
    <section className="relative w-full bg-black text-white overflow-hidden py-10 sm:py-12 md:py-16 px-4 md:px-6 lg:px-8">
      <div className="relative z-10 max-w-7xl mx-auto">
        {/* Title section */}
        <div className="relative mb-8 sm:mb-10 md:mb-12">
          <div className="inline-flex items-center gap-2 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full bg-neutral-900 border border-neutral-700 mb-3 sm:mb-4">
            <Sparkles className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
            <span className="text-xs sm:text-sm text-white font-medium">
              Testimonials
            </span>
          </div>
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-3 sm:mb-4">
            What Our Customers Say
          </h2>
          <p className="text-sm md:text-base text-gray-400 max-w-lg">
            Real businesses share their experience with our invoice management
            platform.
          </p>

          <div
            className="absolute top-2 right-0 text-[150px] font-bold select-none hidden lg:block z-0 overflow-hidden"
            style={{
              background:
                "linear-gradient(to bottom, rgba(255,255,255,0.2), transparent)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
              color: "transparent",
              maxWidth: "100%",
            }}
          >
            REVIEWS
          </div>
        </div>

        {/* Mobile description - visible only on small screens */}
        <div className="md:hidden mb-6 sm:mb-8">
          <p className="text-xs sm:text-sm text-zinc-400">
            Hear from the businesses that have transformed their financial
            operations with our platform.
          </p>
        </div>

        {/* Mobile view (single column) - visible only on small screens */}
        <div className="block md:hidden">
          <div className="h-[500px] sm:h-[550px] overflow-hidden rounded-lg border border-zinc-800 bg-zinc-900/30 backdrop-blur-sm">
            <TestimonialColumn
              testimonials={mobileColumn}
              delay={0}
              animationDuration={40}
              isMobile={true}
            />
          </div>
        </div>

        {/* Desktop view (three columns) - hidden on small screens */}
        <div className="hidden md:grid grid-cols-3 gap-3 sm:gap-4 h-[450px] md:h-[500px] overflow-hidden">
          {/* Column 1 */}
          <div className="rounded-lg border border-zinc-800 bg-zinc-900/30 backdrop-blur-sm overflow-hidden">
            <TestimonialColumn testimonials={column1} delay={1} />
          </div>

          {/* Column 2 */}
          <div className="rounded-lg border border-zinc-800 bg-zinc-900/30 backdrop-blur-sm overflow-hidden">
            <TestimonialColumn testimonials={column2} delay={0} />
          </div>

          {/* Column 3 */}
          <div className="rounded-lg border border-zinc-800 bg-zinc-900/30 backdrop-blur-sm overflow-hidden">
            <TestimonialColumn testimonials={column3} delay={1.5} />
          </div>
        </div>
      </div>

      {/* Decorative blob */}
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-[630px] h-[150px] sm:h-[220px] md:h-[320px] rounded-full bg-gradient-to-br from-[#ffffff] to-[#395F71] opacity-30 blur-3xl pointer-events-none z-[100]" />
    </section>
  );
}

/**
 * @param {Object} props
 * @param {Testimonial[]} props.testimonials
 * @param {number} props.delay
 * @param {number} props.animationDuration
 * @param {boolean} props.isMobile
 */
function TestimonialColumn({
  testimonials,
  delay = 0,
  animationDuration = 20,
  isMobile = false,
}: {
  testimonials: Testimonial[];
  delay?: number;
  animationDuration?: number;
  isMobile?: boolean;
}) {
  const [items, setItems] = useState<Testimonial[]>([]);

  useEffect(() => {
    // Create an infinite loop of testimonials by duplicating them
    const duplicated = [...testimonials, ...testimonials, ...testimonials];

    // Start the animation after the delay
    const timer = setTimeout(() => {
      setItems(duplicated);
    }, delay * 1000);

    return () => clearTimeout(timer);
  }, [testimonials, delay]);

  return (
    <div className="relative overflow-hidden h-full">
      <motion.div
        initial={{ y: 0 }}
        animate={{
          y: items.length ? "-33.33%" : 0,
        }}
        transition={{
          repeat: Number.POSITIVE_INFINITY,
          repeatType: "loop",
          duration: animationDuration,
          ease: "linear",
          delay: 0,
        }}
        className="space-y-4 sm:space-y-5 p-3 sm:p-4"
      >
        {items.map((testimonial, index) => (
          <motion.div
            key={`${testimonial.id}-${index}`}
            className="bg-zinc-900/80 backdrop-blur-sm border border-zinc-800 rounded-xl p-4 sm:p-5 md:p-6 flex flex-col shadow-lg"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <div className="mb-2 sm:mb-3 text-xl sm:text-2xl text-amber-400">
              &ldquo;
            </div>
            <p className="text-sm sm:text-base text-zinc-300 leading-relaxed mb-4 sm:mb-5 flex-grow">
              {isMobile && testimonial.quote.length > 150
                ? `${testimonial.quote.substring(0, 150)}...`
                : testimonial.quote}
            </p>
            <div className="flex justify-between items-center border-t border-zinc-800 pt-3 mt-auto">
              <div>
                <h3 className="text-sm sm:text-base font-semibold text-white">
                  {testimonial.author}
                </h3>
                <p className="text-xs text-zinc-500">{testimonial.title}</p>
              </div>
              <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center bg-zinc-800">
                <X className="w-3 h-3 sm:w-4 sm:h-4 text-zinc-400" />
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
}
