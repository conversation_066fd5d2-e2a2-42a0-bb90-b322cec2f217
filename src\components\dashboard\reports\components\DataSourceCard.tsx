import { Check } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON>le, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface DataSourceCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  selected: boolean;
  onClick?: () => void;
}

export function DataSourceCard({
  title,
  description,
  icon,
  selected,
  onClick,
}: DataSourceCardProps) {
  return (
    <Card
      className={`cursor-pointer transition-all ${selected ? "border-primary" : ""}`}
      onClick={onClick}
    >
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          {icon}
          {selected && <Badge>Selected</Badge>}
        </div>
        <CardTitle className="mt-2">{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardFooter>
        <Button
          variant={selected ? "default" : "outline"}
          size="sm"
          className="w-full"
          onClick={onClick}
        >
          {selected ? (
            <>
              <Check className="mr-2 h-4 w-4" />
              Selected
            </>
          ) : "Select"}
        </Button>
      </CardFooter>
    </Card>
  );
} 