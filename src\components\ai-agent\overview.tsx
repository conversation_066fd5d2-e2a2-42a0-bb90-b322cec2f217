import { motion } from "motion/react";
import { Sparkles, Receipt, FileText, BarChart2, Calendar } from "lucide-react";
import Colors from "../theme/Colors";

export const Overview = () => {
  return (
    <motion.div
      key="overview"
      className="max-w-4xl mx-auto mt-4 md:mt-8 w-full"
      initial={{ opacity: 0, scale: 0.98 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.98 }}
      transition={{ delay: 0.3 }}
    >
      <div className="rounded-2xl p-6 flex flex-col gap-5 leading-relaxed text-center max-w-2xl mx-auto bg-gradient-to-br from-white via-blue-50/50 to-cyan-50/50 dark:from-[#22304d]/80 dark:via-[#22304d]/60 dark:to-[#0B1739]/40 backdrop-blur-xl border border-blue-200/50 dark:border-[#0097B1]/30 shadow-2xl hover:shadow-blue-500/20 dark:hover:shadow-[#0097B1]/20 transition-all duration-500">
        <div className="flex flex-col items-center gap-4">
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.5, type: "spring", stiffness: 300, damping: 20 }}
            className="size-16 flex items-center rounded-full justify-center ring-2 shrink-0 ring-blue-500/30 dark:ring-[#0097B1]/30 text-white shadow-xl hover:shadow-blue-500/30 dark:hover:shadow-[#0097B1]/30 transition-all duration-300"
            style={{ background: `linear-gradient(135deg, ${Colors.gradients.blueToPurple[0]}, ${Colors.gradients.blueToPurple[1]})` }}
          >
            <Sparkles size={26} />
          </motion.div>
          <h2
            className="text-3xl font-bold bg-clip-text text-transparent"
            style={{ background: `linear-gradient(90deg, ${Colors.gradients.blueToPurple[0]}, ${Colors.gradients.blueToPurple[1]})`, WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}
          >
            Billix AI Agent
          </h2>
        </div>

        <p className="text-lg text-gray-700 dark:text-gray-300">
          Welcome to your financial management assistant. I can help you with:
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.6 }}
            className="flex items-start gap-3 p-4 rounded-xl bg-white/60 dark:bg-[#22304d]/60 border border-blue-200/40 dark:border-[#0097B1]/30 shadow-md hover:shadow-lg hover:shadow-blue-500/10 dark:hover:shadow-[#0097B1]/10 transition-all duration-300 backdrop-blur-sm"
          >
            <Receipt className="mt-1 text-blue-600 dark:text-[#0097B1]" size={22} />
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-gray-100">Invoice Management</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">Create, edit, and organize your invoices</p>
            </div>
          </motion.div>

          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.7 }}
            className="flex items-start gap-3 p-4 rounded-xl bg-white/60 dark:bg-[#22304d]/60 border border-blue-200/40 dark:border-[#0097B1]/30 shadow-md hover:shadow-lg hover:shadow-blue-500/10 dark:hover:shadow-[#0097B1]/10 transition-all duration-300 backdrop-blur-sm"
          >
            <FileText className="mt-1 text-blue-600 dark:text-[#0097B1]" size={22} />
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-gray-100">Report Generation</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">Create financial reports with real-time data</p>
            </div>
          </motion.div>

          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.8 }}
            className="flex items-start gap-3 p-4 rounded-xl bg-white/60 dark:bg-[#22304d]/60 border border-blue-200/40 dark:border-[#0097B1]/30 shadow-md hover:shadow-lg hover:shadow-blue-500/10 dark:hover:shadow-[#0097B1]/10 transition-all duration-300 backdrop-blur-sm"
          >
            <BarChart2 className="mt-1 text-blue-600 dark:text-[#0097B1]" size={22} />
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-gray-100">Financial Analytics</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">Analyze trends and get AI-powered insights</p>
            </div>
          </motion.div>

          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.9 }}
            className="flex items-start gap-3 p-4 rounded-xl bg-white/60 dark:bg-[#22304d]/60 border border-blue-200/40 dark:border-[#0097B1]/30 shadow-md hover:shadow-lg hover:shadow-blue-500/10 dark:hover:shadow-[#0097B1]/10 transition-all duration-300 backdrop-blur-sm"
          >
            <Calendar className="mt-1 text-blue-600 dark:text-[#0097B1]" size={22} />
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-gray-100">Scheduled Reports</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">Automate report generation and delivery</p>
            </div>
          </motion.div>
        </div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.0 }}
          className="flex flex-col items-center gap-3"
        >
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Type a message below to get started with your financial assistant.
          </p>
          <div className="flex flex-wrap justify-center gap-2 text-xs mt-1">
            <span
              className="px-4 py-2 bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900/40 dark:to-cyan-900/40 text-blue-700 dark:text-blue-300 rounded-full cursor-pointer hover:from-blue-200 hover:to-cyan-200 dark:hover:from-blue-800/60 dark:hover:to-cyan-800/60 transition-all duration-300 border border-blue-300/50 dark:border-blue-600/50 shadow-md hover:shadow-lg hover:shadow-blue-500/20 dark:hover:shadow-[#0097B1]/20 font-medium"
              onClick={() => {
                const event = new CustomEvent('set-input-text', {
                  detail: { text: "Generate a cash flow report for this month" }
                });
                window.dispatchEvent(event);
              }}
            >
              Generate a cash flow report
            </span>
            <span
              className="px-4 py-2 bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900/40 dark:to-cyan-900/40 text-blue-700 dark:text-blue-300 rounded-full cursor-pointer hover:from-blue-200 hover:to-cyan-200 dark:hover:from-blue-800/60 dark:hover:to-cyan-800/60 transition-all duration-300 border border-blue-300/50 dark:border-blue-600/50 shadow-md hover:shadow-lg hover:shadow-blue-500/20 dark:hover:shadow-[#0097B1]/20 font-medium"
              onClick={() => {
                const event = new CustomEvent('set-input-text', {
                  detail: { text: "Help me create a new invoice" }
                });
                window.dispatchEvent(event);
              }}
            >
              Create a new invoice
            </span>
            <span
              className="px-4 py-2 bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900/40 dark:to-cyan-900/40 text-blue-700 dark:text-blue-300 rounded-full cursor-pointer hover:from-blue-200 hover:to-cyan-200 dark:hover:from-blue-800/60 dark:hover:to-cyan-800/60 transition-all duration-300 border border-blue-300/50 dark:border-blue-600/50 shadow-md hover:shadow-lg hover:shadow-blue-500/20 dark:hover:shadow-[#0097B1]/20 font-medium"
              onClick={() => {
                const event = new CustomEvent('set-input-text', {
                  detail: { text: "Show me my financial analytics" }
                });
                window.dispatchEvent(event);
              }}
            >
              Show financial analytics
            </span>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};
