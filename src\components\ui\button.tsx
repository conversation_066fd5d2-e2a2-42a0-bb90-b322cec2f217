import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import Colors from '../theme/Colors'

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: `bg-[${Colors.primary}] text-[${Colors.textLight}] hover:bg-[${Colors.primaryDark}]`,
        destructive:
          `bg-[${Colors.error}] text-[${Colors.textLight}] hover:bg-[${Colors.error}]`,
        outline:
          `border border-[${Colors.border}] bg-[${Colors.background}] hover:bg-[${Colors.accent}] hover:text-[${Colors.textLight}]`,
        secondary:
          `bg-[${Colors.accent}] text-[${Colors.textLight}] hover:bg-[${Colors.accent}]`,
        ghost: `hover:bg-[${Colors.accent}] hover:text-[${Colors.textLight}]`,
        link: `text-[${Colors.primary}] underline-offset-4 hover:underline`,
        primaryGradient: `bg-gradient-to-r from-[${Colors.primary}] to-[${Colors.primaryDark}] text-[${Colors.textLight}] hover:from-[${Colors.accent}] hover:to-[${Colors.primaryDark}]`,
        secondaryGradient: `bg-gradient-to-r from-[${Colors.primary}]/10 to-[${Colors.primaryDark}]/10 text-[${Colors.primary}] border border-[${Colors.primary}]/20 hover:from-[${Colors.primary}]/20 hover:to-[${Colors.primaryDark}]/20`,
        blueGlow: `relative bg-gradient-to-r from-[${Colors.primary}] to-[${Colors.primaryDark}] text-[${Colors.textLight}] before:absolute before:inset-0 before:-z-10 before:rounded-md before:bg-gradient-to-r before:from-[${Colors.primary}] before:to-[${Colors.primaryDark}] before:blur-lg before:opacity-70 before:transition-opacity hover:before:opacity-100`,
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "blueGlow",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
