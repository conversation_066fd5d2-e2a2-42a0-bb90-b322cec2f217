'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import {
  PlusIcon,
  CheckIcon,
  EditIcon,
  TrashIcon,
  FileEditIcon,
  CalendarIcon
} from 'lucide-react';

interface LineItem {
  id?: string;
  description: string;
  quantity: number;
  unitPrice: number;
  amount: number;
}

interface InvoiceTemplate {
  id: string;
  name: string;
  description: string;
  lineItems: LineItem[];
}

// Invoice templates for quick creation
const INVOICE_TEMPLATES: InvoiceTemplate[] = [
  {
    id: 'standard',
    name: 'Standard Invoice',
    description: 'A standard invoice template for general use',
    lineItems: [
      { description: 'Product or Service', quantity: 1, unitPrice: 100, amount: 100 }
    ]
  },
  {
    id: 'detailed',
    name: 'Detailed Invoice',
    description: 'A detailed invoice with multiple line items',
    lineItems: [
      { description: 'Product A', quantity: 2, unitPrice: 50, amount: 100 },
      { description: 'Service B', quantity: 1, unitPrice: 150, amount: 150 },
      { description: 'Maintenance', quantity: 3, unitPrice: 75, amount: 225 }
    ]
  },
  {
    id: 'consulting',
    name: 'Consulting Invoice',
    description: 'Template for consulting services',
    lineItems: [
      { description: 'Consulting Hours', quantity: 10, unitPrice: 125, amount: 1250 },
      { description: 'Travel Expenses', quantity: 1, unitPrice: 250, amount: 250 }
    ]
  }
];

interface InvoiceCreatorProps {
  id: string;
  vendorName: string;
  invoiceNumber: string;
  lineItems?: LineItem[];
  total?: {
    amount: number;
    currency: string;
  };
  isComplete?: boolean;
  isEditing?: boolean;
  existingData?: {
    currency?: string;
    issueDate?: string;
    dueDate?: string;
    category?: string;
    notes?: string;
  };
  onTemplateSelect?: (templateId: string) => void;
  onLineItemsChange?: (lineItems: LineItem[]) => void;
  onFieldChange?: (field: string, value: string) => void;
}

export function InvoiceCreator({
  vendorName,
  invoiceNumber,
  lineItems = [],
  total,
  isComplete = false,
  isEditing = false,
  existingData,
  onTemplateSelect,
  onLineItemsChange,
  onFieldChange
}: InvoiceCreatorProps) {
  const [status, setStatus] = useState<'creating' | 'editing' | 'complete'>(
    isComplete ? 'complete' : isEditing ? 'editing' : 'creating'
  );
  const [showTemplateDialog, setShowTemplateDialog] = useState(!isEditing && lineItems.length === 0);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [editingLineItem, setEditingLineItem] = useState<{index: number, item: LineItem} | null>(null);

  // Form fields for interactive editing
  const [formFields, setFormFields] = useState({
    vendorName: vendorName || '',
    invoiceNumber: invoiceNumber || '',
    currency: existingData?.currency || 'USD',
    issueDate: existingData?.issueDate || new Date().toISOString().split('T')[0],
    dueDate: existingData?.dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    category: existingData?.category || '',
    notes: existingData?.notes || '',
  });

  useEffect(() => {
    if (isComplete) {
      setStatus('complete');
    } else if (isEditing) {
      setStatus('editing');
    }
  }, [isComplete, isEditing]);

  // Update form fields when props change
  useEffect(() => {
    setFormFields(prev => ({
      ...prev,
      vendorName: vendorName || prev.vendorName,
      invoiceNumber: invoiceNumber || prev.invoiceNumber,
      currency: existingData?.currency || prev.currency,
      issueDate: existingData?.issueDate || prev.issueDate,
      dueDate: existingData?.dueDate || prev.dueDate,
      category: existingData?.category || prev.category,
      notes: existingData?.notes || prev.notes,
    }));
  }, [vendorName, invoiceNumber, existingData]);

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplate(templateId);
    const template = INVOICE_TEMPLATES.find(t => t.id === templateId);
    if (template && onTemplateSelect) {
      onTemplateSelect(templateId);
    }
    setShowTemplateDialog(false);
  };

  const handleFieldChange = (field: string, value: string) => {
    setFormFields(prev => ({
      ...prev,
      [field]: value
    }));

    if (onFieldChange) {
      onFieldChange(field, value);
    }
  };

  const handleAddLineItem = () => {
    const newLineItem: LineItem = {
      description: 'New Item',
      quantity: 1,
      unitPrice: 0,
      amount: 0
    };

    if (onLineItemsChange) {
      onLineItemsChange([...lineItems, newLineItem]);
    }
  };

  const handleEditLineItem = (index: number) => {
    setEditingLineItem({
      index,
      item: {...lineItems[index]}
    });
  };

  const handleDeleteLineItem = (index: number) => {
    if (onLineItemsChange) {
      const newLineItems = [...lineItems];
      newLineItems.splice(index, 1);
      onLineItemsChange(newLineItems);
    }
  };

  const handleSaveLineItem = () => {
    if (editingLineItem && onLineItemsChange) {
      const newLineItems = [...lineItems];
      // Calculate amount
      const amount = editingLineItem.item.quantity * editingLineItem.item.unitPrice;
      newLineItems[editingLineItem.index] = {
        ...editingLineItem.item,
        amount
      };
      onLineItemsChange(newLineItems);
      setEditingLineItem(null);
    }
  };

  return (
    <>
      <Card className="w-full mb-4 border border-primary/30 shadow-md bg-primary/5">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-lg font-semibold flex items-center">
                <span className="mr-2">
                  {status === 'editing' ? 'Editing Invoice' : 'Creating Invoice'}
                </span>
                {status === 'complete' && (
                  <Badge className="bg-green-500 text-white">
                    <CheckIcon className="h-3 w-3 mr-1" />
                    {isEditing ? 'Updated' : 'Created'}
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                {formFields.invoiceNumber} - {formFields.vendorName}
              </CardDescription>
            </div>

            {(status === 'creating' || status === 'editing') && (
              <div className="flex items-center">
                <div className="flex gap-1.5 mr-2">
                  <motion.div
                    className="h-2 w-2 bg-primary rounded-full"
                    animate={{ opacity: [0.4, 1, 0.4] }}
                    transition={{ duration: 1.5, repeat: Infinity, delay: 0 }}
                  />
                  <motion.div
                    className="h-2 w-2 bg-primary rounded-full"
                    animate={{ opacity: [0.4, 1, 0.4] }}
                    transition={{ duration: 1.5, repeat: Infinity, delay: 0.3 }}
                  />
                  <motion.div
                    className="h-2 w-2 bg-primary rounded-full"
                    animate={{ opacity: [0.4, 1, 0.4] }}
                    transition={{ duration: 1.5, repeat: Infinity, delay: 0.6 }}
                  />
                </div>
                <span className="text-sm text-primary font-medium">
                  {status === 'editing' ? 'Updating...' : 'Processing...'}
                </span>
              </div>
            )}
          </div>
        </CardHeader>

        <CardContent>
          {/* Interactive form fields */}
          {(status === 'creating' || status === 'editing') && onFieldChange && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div>
                <label className="text-sm font-medium mb-1 block">Vendor Name</label>
                <Input
                  value={formFields.vendorName}
                  onChange={(e) => handleFieldChange('vendorName', e.target.value)}
                  placeholder="Vendor name"
                />
              </div>

              <div>
                <label className="text-sm font-medium mb-1 block">Invoice Number</label>
                <Input
                  value={formFields.invoiceNumber}
                  onChange={(e) => handleFieldChange('invoiceNumber', e.target.value)}
                  placeholder="Invoice number"
                />
              </div>

              <div>
                <label className="text-sm font-medium mb-1 block">Issue Date</label>
                <div className="relative">
                  <Input
                    type="date"
                    value={formFields.issueDate}
                    onChange={(e) => handleFieldChange('issueDate', e.target.value)}
                  />
                  <CalendarIcon className="absolute right-3 top-2.5 h-4 w-4 text-muted-foreground" />
                </div>
              </div>

              <div>
                <label className="text-sm font-medium mb-1 block">Due Date</label>
                <div className="relative">
                  <Input
                    type="date"
                    value={formFields.dueDate}
                    onChange={(e) => handleFieldChange('dueDate', e.target.value)}
                  />
                  <CalendarIcon className="absolute right-3 top-2.5 h-4 w-4 text-muted-foreground" />
                </div>
              </div>

              <div>
                <label className="text-sm font-medium mb-1 block">Currency</label>
                <Select
                  value={formFields.currency}
                  onValueChange={(value) => handleFieldChange('currency', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select currency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">USD - US Dollar</SelectItem>
                    <SelectItem value="EUR">EUR - Euro</SelectItem>
                    <SelectItem value="GBP">GBP - British Pound</SelectItem>
                    <SelectItem value="CAD">CAD - Canadian Dollar</SelectItem>
                    <SelectItem value="AUD">AUD - Australian Dollar</SelectItem>
                    <SelectItem value="JPY">JPY - Japanese Yen</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-1 block">Category</label>
                <Input
                  value={formFields.category}
                  onChange={(e) => handleFieldChange('category', e.target.value)}
                  placeholder="Invoice category"
                />
              </div>

              <div className="col-span-1 md:col-span-2">
                <label className="text-sm font-medium mb-1 block">Notes</label>
                <Textarea
                  value={formFields.notes}
                  onChange={(e) => handleFieldChange('notes', e.target.value)}
                  placeholder="Additional notes"
                  rows={3}
                />
              </div>
            </div>
          )}

          <AnimatePresence>
            {lineItems.length > 0 && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="mt-2"
              >
                <div className="flex justify-between items-center mb-2">
                  <h4 className="text-sm font-medium">Line Items</h4>

                  {(status === 'creating' || status === 'editing') && onLineItemsChange && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleAddLineItem}
                      className="flex items-center gap-1"
                    >
                      <PlusIcon className="h-3 w-3" />
                      <span>Add Item</span>
                    </Button>
                  )}
                </div>

                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Description</TableHead>
                      <TableHead className="text-right">Qty</TableHead>
                      <TableHead className="text-right">Unit Price</TableHead>
                      <TableHead className="text-right">Amount</TableHead>
                      {(status === 'creating' || status === 'editing') && onLineItemsChange && (
                        <TableHead className="w-[100px]">Actions</TableHead>
                      )}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {lineItems.map((item, index) => (
                      <TableRow key={item.id || index}>
                        <TableCell>{item.description}</TableCell>
                        <TableCell className="text-right">{item.quantity}</TableCell>
                        <TableCell className="text-right">
                          {formatCurrency(item.unitPrice, total?.currency || formFields.currency)}
                        </TableCell>
                        <TableCell className="text-right">
                          {formatCurrency(item.amount, total?.currency || formFields.currency)}
                        </TableCell>
                        {(status === 'creating' || status === 'editing') && onLineItemsChange && (
                          <TableCell>
                            <div className="flex items-center gap-1 justify-end">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-7 w-7"
                                onClick={() => handleEditLineItem(index)}
                              >
                                <EditIcon className="h-3.5 w-3.5" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-7 w-7 text-red-500"
                                onClick={() => handleDeleteLineItem(index)}
                              >
                                <TrashIcon className="h-3.5 w-3.5" />
                              </Button>
                            </div>
                          </TableCell>
                        )}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </motion.div>
            )}
          </AnimatePresence>

          {total && (
            <div className="mt-4 flex justify-end">
              <div className="bg-primary/10 px-4 py-2 rounded-lg">
                <span className="text-sm text-muted-foreground mr-2">Total:</span>
                <span className="font-bold text-primary">
                  {formatCurrency(total.amount, total.currency)}
                </span>
              </div>
            </div>
          )}
        </CardContent>

        <CardFooter className="pt-2">
          {(status === 'creating' || status === 'editing') && !total && !lineItems.length && (
            <div className="w-full flex justify-center">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowTemplateDialog(true)}
                className="text-primary"
              >
                <FileEditIcon className="h-4 w-4 mr-1" />
                Choose Template
              </Button>
            </div>
          )}

          {(status === 'creating' || status === 'editing') && !total && lineItems.length > 0 && (
            <div className="w-full flex justify-center">
              <Button variant="ghost" size="sm" disabled className="text-primary">
                <PlusIcon className="h-4 w-4 mr-1" />
                {status === 'editing' ? 'Updating invoice...' : 'Creating invoice...'}
              </Button>
            </div>
          )}

          {status === 'complete' && (
            <div className="w-full flex justify-end">
              <span className="text-sm text-green-600">
                Invoice {isEditing ? 'updated' : 'created'} successfully
              </span>
            </div>
          )}
        </CardFooter>
      </Card>

      {/* Template Selection Dialog */}
      <Dialog open={showTemplateDialog} onOpenChange={setShowTemplateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Choose an Invoice Template</DialogTitle>
            <DialogDescription>
              Select a template to start with or create a blank invoice.
            </DialogDescription>
          </DialogHeader>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 my-4">
            {INVOICE_TEMPLATES.map(template => (
              <Card
                key={template.id}
                className={`cursor-pointer hover:border-primary transition-all ${
                  selectedTemplate === template.id ? 'border-primary bg-primary/5' : ''
                }`}
                onClick={() => handleTemplateSelect(template.id)}
              >
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">{template.name}</CardTitle>
                  <CardDescription>{template.description}</CardDescription>
                </CardHeader>
                <CardContent className="pb-2">
                  <p className="text-sm text-muted-foreground">
                    {template.lineItems.length} line item{template.lineItems.length !== 1 ? 's' : ''}
                  </p>
                </CardContent>
              </Card>
            ))}

            <Card
              className={`cursor-pointer hover:border-primary transition-all ${
                selectedTemplate === 'blank' ? 'border-primary bg-primary/5' : ''
              }`}
              onClick={() => handleTemplateSelect('blank')}
            >
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Blank Invoice</CardTitle>
                <CardDescription>Start with an empty invoice</CardDescription>
              </CardHeader>
              <CardContent className="pb-2">
                <p className="text-sm text-muted-foreground">
                  No line items
                </p>
              </CardContent>
            </Card>
          </div>

          <DialogFooter>
            <Button onClick={() => setShowTemplateDialog(false)}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Line Item Edit Dialog */}
      <Dialog open={editingLineItem !== null} onOpenChange={(open) => !open && setEditingLineItem(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Line Item</DialogTitle>
          </DialogHeader>

          {editingLineItem && (
            <div className="space-y-4 my-4">
              <div>
                <label className="text-sm font-medium mb-1 block">Description</label>
                <Input
                  value={editingLineItem.item.description}
                  onChange={(e) => setEditingLineItem({
                    ...editingLineItem,
                    item: {...editingLineItem.item, description: e.target.value}
                  })}
                />
              </div>

              <div>
                <label className="text-sm font-medium mb-1 block">Quantity</label>
                <Input
                  type="number"
                  min="1"
                  value={editingLineItem.item.quantity}
                  onChange={(e) => setEditingLineItem({
                    ...editingLineItem,
                    item: {...editingLineItem.item, quantity: Number(e.target.value)}
                  })}
                />
              </div>

              <div>
                <label className="text-sm font-medium mb-1 block">Unit Price</label>
                <Input
                  type="number"
                  min="0"
                  step="0.01"
                  value={editingLineItem.item.unitPrice}
                  onChange={(e) => setEditingLineItem({
                    ...editingLineItem,
                    item: {...editingLineItem.item, unitPrice: Number(e.target.value)}
                  })}
                />
              </div>

              <div>
                <label className="text-sm font-medium mb-1 block">Total Amount</label>
                <Input
                  type="text"
                  value={formatCurrency(
                    editingLineItem.item.quantity * editingLineItem.item.unitPrice,
                    total?.currency || formFields.currency
                  )}
                  disabled
                />
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setEditingLineItem(null)}>
              Cancel
            </Button>
            <Button onClick={handleSaveLineItem}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
