import { memo } from 'react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { useRecentInvoices } from '../providers/dashboard-data-provider';

export const RecentInvoices = memo(() => {
  const { invoices, loading, error } = useRecentInvoices();

  if (loading)
    return (
      <div className="text-center py-8 text-muted-foreground">
        Loading invoices...
      </div>
    );
  if (error)
    return (
      <div className="text-center text-red-500 py-8">{error}</div>
    );

  return (
    <div className="space-y-6">
      {/* Enhanced header with gradient background */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-blue-50/50 via-white to-indigo-50/50 dark:from-blue-950/20 dark:via-gray-800/50 dark:to-indigo-950/20 p-1">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-transparent to-indigo-500/5 dark:from-blue-400/10 dark:via-transparent dark:to-indigo-400/10" />
        
        {/* Professional scrollable container */}
        <div className="max-h-[440px] overflow-y-auto pr-1 recent-invoices-scroll relative">
          <div className="space-y-3 p-2">
            {/* Enhanced table header */}
            <div className="grid grid-cols-12 gap-4 px-4 py-3 text-xs font-semibold text-gray-600 dark:text-gray-300 bg-gray-50/50 dark:bg-gray-800/30 rounded-xl border border-gray-200/50 dark:border-gray-700/30">
              <div className="col-span-5">VENDOR</div>
              <div className="col-span-2">DATE</div>
              <div className="col-span-3 text-right">AMOUNT</div>
              <div className="col-span-2 text-right">STATUS</div>
            </div>
            
            {/* Enhanced invoice rows */}
            {invoices.map((invoice, index) => (
              <div
                key={invoice.id}
                className="group relative grid grid-cols-12 gap-4 px-4 py-4 bg-white/60 dark:bg-gray-800/40 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/30 rounded-xl hover:bg-white/80 dark:hover:bg-gray-800/60 hover:shadow-lg hover:shadow-blue-500/10 dark:hover:shadow-blue-400/5 transition-all duration-300 hover:scale-[1.01]"
                style={{
                  animationDelay: `${index * 50}ms`,
                }}
              >
                {/* Subtle gradient overlay on hover */}
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/0 via-blue-500/5 to-indigo-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
                
                {/* Vendor Info */}
                <div className="col-span-5 flex items-center gap-3 relative z-10">
                  <div className="relative">
                    <Avatar className="h-12 w-12 rounded-xl bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/40 dark:to-indigo-900/40 text-blue-700 dark:text-blue-300 border-2 border-blue-200/50 dark:border-blue-700/30 shadow-sm">
                      <AvatarFallback className="font-bold bg-transparent text-blue-700 dark:text-blue-300 border-0">
                        {'INV'}
                      </AvatarFallback>
                    </Avatar>
                    {/* Status indicator dot */}
                    <div className={`absolute -top-1 -right-1 w-4 h-4 rounded-full border-2 border-white dark:border-gray-800 ${
                      invoice.status?.toUpperCase() === 'PAID' ? 'bg-emerald-500' :
                      invoice.status?.toUpperCase() === 'PENDING' ? 'bg-amber-500' :
                      invoice.status?.toUpperCase() === 'OVERDUE' ? 'bg-red-500' : 'bg-gray-500'
                    }`} />
                  </div>
                  <div className="min-w-0 flex-1">
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100 truncate text-sm">
                      Invoice #{invoice.id}
                    </h4>
                    <p className="text-xs text-gray-500 dark:text-gray-400 truncate font-mono">
                      #{invoice.id.toString().padStart(8, '0')}
                    </p>
                  </div>
                </div>
                
                {/* Date */}
                <div className="col-span-2 flex items-center relative z-10">
                  <div className="text-sm text-gray-600 dark:text-gray-300 font-medium">
                    {new Date(invoice.createdAt).toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                      year: '2-digit',
                    })}
                  </div>
                </div>
                
                {/* Amount */}
                <div className="col-span-3 flex items-center justify-end relative z-10">
                  <div className="text-right">
                    <p className="font-bold text-gray-900 dark:text-gray-100 text-sm">
                      ${(invoice.amount ?? 0).toLocaleString(undefined, {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      })}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">USD</p>
                  </div>
                </div>
                
                {/* Status */}
                <div className="col-span-2 flex items-center justify-end relative z-10">
                  <StatusBadge status={invoice.status} />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
});
RecentInvoices.displayName = 'RecentInvoices';

const StatusBadge = memo(({ status }: { status: string }) => {
  const getStatusConfig = (status: string) => {
    switch (status?.toUpperCase()) {
      case 'PAID':
        return {
          text: 'Paid',
          icon: '✓',
          className: 'bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-800 border-emerald-300/50 dark:from-emerald-900/40 dark:to-green-900/40 dark:text-emerald-300 dark:border-emerald-600/30 shadow-emerald-500/20',
        };
      case 'PENDING':
        return {
          text: 'Pending',
          icon: '⏳',
          className: 'bg-gradient-to-r from-amber-100 to-yellow-100 text-amber-800 border-amber-300/50 dark:from-amber-900/40 dark:to-yellow-900/40 dark:text-amber-300 dark:border-amber-600/30 shadow-amber-500/20',
        };
      case 'OVERDUE':
        return {
          text: 'Overdue',
          icon: '⚠',
          className: 'bg-gradient-to-r from-red-100 to-rose-100 text-red-800 border-red-300/50 dark:from-red-900/40 dark:to-rose-900/40 dark:text-red-300 dark:border-red-600/30 shadow-red-500/20',
        };
      default:
        return {
          text: 'Unknown',
          icon: '?',
          className: 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-300/50 dark:from-gray-900/40 dark:to-slate-900/40 dark:text-gray-300 dark:border-gray-600/30 shadow-gray-500/20',
        };
    }
  };

  const config = getStatusConfig(status);

  return (
    <div className="relative">
      <span
        className={`inline-flex items-center gap-1.5 rounded-xl border px-3 py-1.5 text-xs font-semibold transition-all duration-200 shadow-sm hover:shadow-md ${config.className}`}
      >
        <span className="text-xs opacity-80">{config.icon}</span>
        {config.text}
      </span>
    </div>
  );
});
StatusBadge.displayName = 'StatusBadge';
