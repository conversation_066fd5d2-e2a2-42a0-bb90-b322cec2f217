import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import db from "@/db/db";
import { z } from "zod";
import { InvoiceStatus } from "@prisma/client";

// Schema for invoice query
const invoiceQuerySchema = z.object({
  query: z.string().optional(),
  filters: z.object({
    vendor: z.string().optional(),
    category: z.string().optional(),
    status: z.string().optional(),
    search: z.string().optional(),
    amountRange: z.tuple([z.number(), z.number()]).optional(),
  }).optional(),
  dateRange: z.object({
    from: z.string(),
    to: z.string()
  }).optional(),
  limit: z.number().optional(),
  offset: z.number().optional(),
});

// GET method for handling URL query parameters
export async function GET(req: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the user from the database
    const user = await db.user.findUnique({
      where: { clerkId: userId },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get query from URL
    const searchParams = req.nextUrl.searchParams;
    const query = searchParams.get("query") || undefined;

    // Support for very large datasets - allow custom high limits
    const limit = searchParams.get("limit")
      ? parseInt(searchParams.get("limit")!, 10)
      : searchParams.get("all") === "true"
        ? undefined
        : 100;

    const offset = searchParams.get("offset") ? parseInt(searchParams.get("offset")!, 10) : 0;

    // Build query conditions
    const conditions: Record<string, unknown> = { userId: user.id };

    // Natural language query processing
    if (query) {
      const queryLower = query.toLowerCase();

      // Simple pattern matching for natural language queries
      if (queryLower.includes("unpaid") || queryLower.includes("pending")) {
        conditions.status = InvoiceStatus.PENDING;
      }

      if (queryLower.includes("paid")) {
        conditions.status = InvoiceStatus.PAID;
      }

      if (queryLower.includes("overdue")) {
        conditions.status = InvoiceStatus.OVERDUE;
      }

      // Handle date-related queries
      if (queryLower.includes("last month")) {
        const now = new Date();
        const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

        conditions.createdAt = {
          gte: lastMonth,
          lte: endOfLastMonth,
        };
      } else if (queryLower.includes("this month")) {
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

        conditions.createdAt = {
          gte: startOfMonth,
          lte: now,
        };
      }

      // Handle amount-related queries
      if (queryLower.includes("over") || queryLower.includes("more than")) {
        const amountMatch = queryLower.match(/(\d+)/);
        if (amountMatch && amountMatch[1]) {
          const amount = parseInt(amountMatch[1], 10);
          conditions.amount = { gte: amount };
        }
      }

      if (queryLower.includes("under") || queryLower.includes("less than")) {
        const amountMatch = queryLower.match(/(\d+)/);
        if (amountMatch && amountMatch[1]) {
          const amount = parseInt(amountMatch[1], 10);
          conditions.amount = { lte: amount };
        }
      }

      // Handle vendor-related queries
      if (queryLower.includes("from") && !queryLower.includes("from last")) {
        const vendorParts = queryLower.split("from")[1].trim().split(" ");
        const vendorName = vendorParts.length > 0 ? vendorParts[0] : "";

        if (vendorName && vendorName !== "last") {
          conditions.OR = [
            { vendorName: { contains: vendorName, mode: "insensitive" } },
            { vendor: { name: { contains: vendorName, mode: "insensitive" } } },
          ];
        }
      }
    }

    // Execute the query
    const invoices = await db.invoice.findMany({
      where: conditions,
      include: {
        vendor: true,
        category: true,
        lineItems: true,
      },
      orderBy: { createdAt: "desc" },
      ...(limit ? { take: limit } : {}),
      skip: offset,
    });

    // Get total count
    const totalCount = await db.invoice.count({
      where: conditions,
    });

    return NextResponse.json({
      invoices,
      totalCount,
      query,
    });
  } catch (error) {
    console.error("Error querying invoices:", error);
    return NextResponse.json(
      { error: "Failed to query invoices" },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the user from the database
    const user = await db.user.findUnique({
      where: { clerkId: userId },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Parse the query
    const body = await req.json();
    const parsedQuery = invoiceQuerySchema.parse(body);

    // Build query conditions
    const conditions: Record<string, unknown> = { userId: user.id };

    // Apply filters
    if (parsedQuery.filters) {
      if (parsedQuery.filters.vendor && parsedQuery.filters.vendor !== 'all') {
        conditions.vendorId = parsedQuery.filters.vendor;
      }

      if (parsedQuery.filters.category && parsedQuery.filters.category !== 'all') {
        conditions.categoryId = parsedQuery.filters.category;
      }

      if (parsedQuery.filters.status && parsedQuery.filters.status !== 'all') {
        conditions.status = parsedQuery.filters.status;
      }

      if (parsedQuery.filters.search) {
        conditions.OR = [
          { invoiceNumber: { contains: parsedQuery.filters.search, mode: "insensitive" } },
          { vendorName: { contains: parsedQuery.filters.search, mode: "insensitive" } },
          { description: { contains: parsedQuery.filters.search, mode: "insensitive" } },
          { vendor: { name: { contains: parsedQuery.filters.search, mode: "insensitive" } } },
        ];
      }

      if (parsedQuery.filters.amountRange && parsedQuery.filters.amountRange.length === 2) {
        conditions.amount = {
          gte: parsedQuery.filters.amountRange[0],
          lte: parsedQuery.filters.amountRange[1],
        };
      }
    }

    // Apply date range
    if (parsedQuery.dateRange) {
      conditions.createdAt = {
        gte: new Date(parsedQuery.dateRange.from),
        lte: new Date(parsedQuery.dateRange.to),
      };
    }

    // Natural language query processing
    if (parsedQuery.query) {
      const query = parsedQuery.query.toLowerCase();

      // Simple pattern matching for natural language queries
      if (query.includes("unpaid") || query.includes("pending")) {
        conditions.status = InvoiceStatus.PENDING;
      }

      if (query.includes("paid")) {
        conditions.status = InvoiceStatus.PAID;
      }

      if (query.includes("overdue")) {
        conditions.status = InvoiceStatus.OVERDUE;
      }

      // Handle date-related queries
      if (query.includes("last month")) {
        const now = new Date();
        const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

        conditions.createdAt = {
          gte: lastMonth,
          lte: endOfLastMonth,
        };
      } else if (query.includes("this month")) {
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

        conditions.createdAt = {
          gte: startOfMonth,
          lte: now,
        };
      }

      // Handle amount-related queries
      if (query.includes("over") || query.includes("more than")) {
        const amountMatch = query.match(/(\d+)/);
        if (amountMatch && amountMatch[1]) {
          const amount = parseInt(amountMatch[1], 10);
          conditions.amount = { gte: amount };
        }
      }

      if (query.includes("under") || query.includes("less than")) {
        const amountMatch = query.match(/(\d+)/);
        if (amountMatch && amountMatch[1]) {
          const amount = parseInt(amountMatch[1], 10);
          conditions.amount = { lte: amount };
        }
      }

      // Handle vendor-related queries
      if (query.includes("from") && !query.includes("from last")) {
        const vendorParts = query.split("from")[1].trim().split(" ");
        const vendorName = vendorParts.length > 0 ? vendorParts[0] : "";

        if (vendorName && vendorName !== "last") {
          conditions.OR = [
            { vendorName: { contains: vendorName, mode: "insensitive" } },
            { vendor: { name: { contains: vendorName, mode: "insensitive" } } },
          ];
        }
      }
    }

    // Execute the query - support for unlimited data retrieval
    const invoices = await db.invoice.findMany({
      where: conditions,
      include: {
        vendor: true,
        category: true,
        lineItems: true,
      },
      orderBy: { createdAt: "desc" },
      ...(parsedQuery.limit ? { take: parsedQuery.limit } : {}),
      skip: parsedQuery.offset || 0,
    });

    // Get total count
    const totalCount = await db.invoice.count({
      where: conditions,
    });

    return NextResponse.json({
      invoices,
      totalCount,
      query: parsedQuery.query,
    });
  } catch (error) {
    console.error("Error querying invoices:", error);
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 });
    }
    return NextResponse.json(
      { error: "Failed to query invoices" },
      { status: 500 }
    );
  }
} 