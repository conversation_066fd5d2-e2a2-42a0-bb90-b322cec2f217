"use client";

import { useEffect } from "react";

type KeyCombination = {
  key: string;
  ctrl?: boolean;
  alt?: boolean;
  shift?: boolean;
  meta?: boolean;
};

type ShortcutHandler = (e: KeyboardEvent) => void;

type ShortcutDefinition = {
  combo: KeyCombination;
  handler: ShortcutHandler;
  preventDefault?: boolean;
};

export function useKeyboardShortcuts(shortcuts: ShortcutDefinition[]) {
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Skip if the event target is an input, textarea, or has role="textbox"
      if (
        e.target instanceof HTMLElement &&
        (e.target.tagName === "INPUT" ||
          e.target.tagName === "TEXTAREA" ||
          e.target.getAttribute("role") === "textbox" ||
          e.target.isContentEditable)
      ) {
        return;
      }

      for (const shortcut of shortcuts) {
        const { combo, handler, preventDefault = true } = shortcut;

        const keyMatches = e.key.toLowerCase() === combo.key.toLowerCase();
        const ctrlMatches =
          combo.ctrl === undefined || e.ctrlKey === combo.ctrl;
        const altMatches = combo.alt === undefined || e.altKey === combo.alt;
        const shiftMatches =
          combo.shift === undefined || e.shiftKey === combo.shift;
        const metaMatches =
          combo.meta === undefined || e.metaKey === combo.meta;

        if (
          keyMatches &&
          ctrlMatches &&
          altMatches &&
          shiftMatches &&
          metaMatches
        ) {
          if (preventDefault) {
            e.preventDefault();
          }
          handler(e);
          break;
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [shortcuts]);
}
