'use client';

import type { ChatRequestOptions, Message, CreateMessage } from 'ai';
import { AnimatePresence, motion } from 'motion/react';
import { memo, useState } from 'react';

import type { Vote } from '@prisma/client';

/**
 * Parse message content that might be in different formats
 * @param content The message content which could be a string, array, or JSON string
 * @returns A clean string representation of the content
 */
function parseMessageContent(content: unknown): string {
  // If it's already a string, check if it's JSON
  if (typeof content === 'string') {
    try {
      // Try to parse it as JSON
      const parsed = JSON.parse(content);

      // If it's an array of objects with text property
      if (Array.isArray(parsed)) {
        return parsed.reduce((acc, item) => {
          if (
            item &&
            typeof item === 'object' &&
            'type' in item &&
            (item as { type: string }).type === 'text'
          ) {
            return acc + ((item as { text?: string }).text || '');
          }
          return acc;
        }, '');
      }

      // If it's an object with a text property
      if (parsed && typeof parsed === 'object' && 'text' in parsed) {
        const textValue = (parsed as { text: unknown }).text;
        return typeof textValue === 'string' ? textValue : '';
      }

      // If it's just a string in JSON format
      if (typeof parsed === 'string') {
        return parsed;
      }

      // Fallback to stringifying the parsed object
      return JSON.stringify(parsed);
    } catch {
      // If it's not valid JSON, just return the string
      return content;
    }
  }

  // If it's an array (like from the AI's response)
  if (Array.isArray(content)) {
    return content.reduce((acc, item) => {
      if (
        item &&
        typeof item === 'object' &&
        'type' in item &&
        (item as { type: string }).type === 'text'
      ) {
        return acc + ((item as { text?: string }).text || '');
      }
      return acc;
    }, '');
  }

  // If it's an object, try to extract text
  if (content && typeof content === 'object') {
    if ('text' in content) {
      const textValue = (content as { text: unknown }).text;
      return typeof textValue === 'string' ? textValue : '';
    }
  }

  // Fallback to string conversion
  return String(content || '');
}

import { DocumentToolCall, DocumentToolResult } from './document';
import { PencilEditIcon, SparklesIcon } from './icons';
import { Markdown } from './markdown';
import { MessageActions } from './message-actions';
import { PreviewAttachment } from './preview-attachment';
import { Weather } from './weather';
import equal from 'fast-deep-equal';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from '@/components/ui/tooltip';
import { MessageEditor } from './message-editor';
import { DocumentPreview } from './document-preview';
import { MessageReasoning } from './message-reasoning';
import { cn, formatCurrency } from '@/lib/utils';
import { cx } from 'class-variance-authority';
import { InvoiceList } from './invoice-list';
import { InvoiceDisplay } from './invoice';
import { InvoiceCreator } from './invoice-creator';
import { FolderList } from './folder-list';
import { ReportPreview } from './report-preview';
import {
  ReportToolResult,
  ReportEmailToolCall,
  ReportEmailToolResult,
} from './report';
import { InvoiceStats } from './invoice-stats';

interface AnalyzedItem {
  name?: string;
  category?: string;
  vendor?: string;
  amount: number | string;
}

interface CurrencyBreakdownItem {
  count: number;
  amount: number;
}

const PurePreviewMessage = ({
  chatId,
  message,
  vote,
  isLoading,
  setMessages,
  reload,
  isReadonly,
}: {
  chatId: string;
  message: Message;
  vote: Vote | undefined;
  isLoading: boolean;
  setMessages: (
    messages: Message[] | ((messages: Message[]) => Message[])
  ) => void;
  reload: (
    chatRequestOptions?: ChatRequestOptions
  ) => Promise<string | null | undefined>;
  isReadonly: boolean;
  append?: (
    message: Message | CreateMessage,
    chatRequestOptions?: ChatRequestOptions
  ) => Promise<string | null | undefined>;
}) => {
  const [mode, setMode] = useState<'view' | 'edit'>('view');

  return (
    <AnimatePresence>
      <motion.div
        className="w-full mx-auto max-w-3xl px-4 group/message"
        initial={{ y: 10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{
          type: 'spring',
          stiffness: 260,
          damping: 20,
          duration: 0.3,
        }}
        data-role={message.role}
      >
        <div
          className={cn(
            'flex gap-4 w-full group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl',
            {
              'w-full': mode === 'edit',
              'group-data-[role=user]/message:w-fit': mode !== 'edit',
            }
          )}
        >
          {message.role === 'assistant' && (
            <div className="size-10 flex items-center rounded-full justify-center ring-1 shrink-0 ring-blue-500/30 dark:ring-[#0097B1]/30 bg-gradient-to-br from-blue-100 to-cyan-100 dark:from-[#22304d] dark:to-[#22304d] text-blue-600 dark:text-white shadow-lg hover:shadow-blue-500/20 dark:hover:shadow-[#0097B1]/20 transition-all duration-300">
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.1, duration: 0.3 }}
                className="translate-y-px"
              >
                <SparklesIcon size={16} />
              </motion.div>
            </div>
          )}

          <div className="flex flex-col gap-4 w-full">
            {message.experimental_attachments && (
              <div className="flex flex-row justify-end gap-2">
                {message.experimental_attachments.map(
                  (attachment) => (
                    <PreviewAttachment
                      key={attachment.url}
                      attachment={attachment}
                    />
                  )
                )}
              </div>
            )}

            {message.reasoning && (
              <MessageReasoning
                isLoading={isLoading}
                reasoning={message.reasoning}
              />
            )}

            {(message.content || message.reasoning) &&
              mode === 'view' && (
                <div className="flex flex-row gap-2 items-start">
                  {message.role === 'user' && !isReadonly && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            className="px-2 h-fit rounded-full text-muted-foreground opacity-0 group-hover/message:opacity-100 hover:bg-background/80 hover:text-primary transition-all duration-200"
                            onClick={() => {
                              setMode('edit');
                            }}
                          >
                            <PencilEditIcon />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Edit message</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}

                  <div
                    className={cn('flex flex-col gap-4', {
                      'bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 text-white px-5 py-3.5 rounded-2xl shadow-lg border border-blue-500/30 hover:shadow-blue-500/20 transition-all duration-300':
                        message.role === 'user',
                      'bg-white/80 dark:bg-[#22304d]/80 border border-blue-200/50 dark:border-[#0097B1]/30 px-5 py-3.5 rounded-2xl shadow-md backdrop-blur-sm hover:shadow-lg hover:shadow-blue-500/10 dark:hover:shadow-[#0097B1]/10 transition-all duration-300':
                        message.role === 'assistant',
                    })}
                  >
                    <Markdown>
                      {parseMessageContent(message.content)}
                    </Markdown>
                  </div>
                </div>
              )}

            {message.content && mode === 'edit' && (
              <div className="flex flex-row gap-2 items-start">
                <div className="size-8" />

                <MessageEditor
                  key={message.id}
                  message={message}
                  setMode={setMode}
                  setMessages={setMessages}
                  reload={reload}
                />
              </div>
            )}

            {message.toolInvocations &&
              message.toolInvocations.length > 0 && (
                <div className="flex flex-col gap-4">
                  {message.toolInvocations.map((toolInvocation) => {
                    const { toolName, toolCallId, state, args } =
                      toolInvocation;

                    if (state === 'result') {
                      const { result } = toolInvocation;

                      return (
                        <div key={toolCallId}>
                          {toolName === 'getWeather' ? (
                            <Weather weatherAtLocation={result} />
                          ) : toolName === 'createDocument' ? (
                            <DocumentPreview
                              isReadonly={isReadonly}
                              result={result}
                            />
                          ) : toolName === 'updateDocument' ? (
                            <DocumentToolResult
                              type="update"
                              result={result}
                              isReadonly={isReadonly}
                            />
                          ) : toolName === 'requestSuggestions' ? (
                            <DocumentToolResult
                              type="request-suggestions"
                              result={result}
                              isReadonly={isReadonly}
                            />
                          ) : toolName === 'listInvoices' ? (
                            <InvoiceList
                              invoices={result.invoices}
                              total={result.total}
                            />
                          ) : toolName === 'getInvoiceDetails' ? (
                            <InvoiceDisplay
                              invoice={result}
                              isDetailed={true}
                            />
                          ) : toolName === 'createInvoice' ? (
                            <InvoiceCreator
                              id={result.id}
                              vendorName={result.vendorName}
                              invoiceNumber={result.number}
                              isComplete={true}
                              isEditing={result.isEdited}
                            />
                          ) : toolName === 'generateReport' ? (
                            <ReportPreview
                              isReadonly={isReadonly}
                              result={{
                                id: result.id,
                                title: result.title,
                                reportType: result.reportType,
                                format: result.format,
                                fileUrl: result.fileUrl,
                              }}
                            />
                          ) : toolName === 'downloadReport' ? (
                            <ReportToolResult
                              type="create"
                              result={{
                                id: result.reportId,
                                title: result.title,
                                reportType: result.reportType || '',
                                format: result.format,
                                fileUrl: result.fileUrl,
                              }}
                              isReadonly={isReadonly}
                            />
                          ) : toolName === 'emailReport' ? (
                            <ReportEmailToolResult
                              reportTitle={result.title}
                              recipients={result.emailAddresses}
                            />
                          ) : toolName === 'scheduleReport' ? (
                            <div className="bg-card/50 border border-border/40 px-5 py-3.5 rounded-2xl shadow-sm">
                              <p>
                                Report &quot;{result.title}&quot;
                                scheduled successfully.
                              </p>
                              <p className="text-sm text-muted-foreground mt-1">
                                Next run:{' '}
                                {new Date(
                                  result.nextRunTime
                                ).toLocaleString()}
                              </p>
                            </div>
                          ) : toolName === 'cancelScheduledReport' ? (
                            <div className="bg-card/50 border border-border/40 px-5 py-3.5 rounded-2xl shadow-sm">
                              <p>
                                Report schedule &quot;{result.title}
                                &quot; cancelled successfully.
                              </p>
                            </div>
                          ) : toolName === 'listFolders' ? (
                            <FolderList
                              folders={result.folders}
                              total={result.total}
                            />
                          ) : toolName === 'getInvoiceStats' ? (
                            <InvoiceStats stats={result} />
                          ) : toolName === 'getVendorSpendAnalysis' ||
                            toolName === 'getCategoryBreakdown' ? (
                            <div className="bg-card/50 border border-border/40 px-5 py-3.5 rounded-2xl shadow-sm">
                              {result.message && (
                                <p className="mb-2">
                                  {result.message}
                                </p>
                              )}
                              {result.data &&
                                Array.isArray(result.data) && (
                                  <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                                    {result.data.map(
                                      (
                                        item: AnalyzedItem,
                                        index: number
                                      ) => (
                                        <div
                                          key={index}
                                          className="flex justify-between"
                                        >
                                          <span>
                                            {item.name ||
                                              item.category ||
                                              item.vendor}
                                            :
                                          </span>
                                          <span className="font-medium">
                                            {typeof item.amount ===
                                            'number'
                                              ? formatCurrency(
                                                  item.amount
                                                )
                                              : item.amount}
                                          </span>
                                        </div>
                                      )
                                    )}
                                  </div>
                                )}
                            </div>
                          ) : (
                            <div className="bg-card/50 border border-border/40 px-5 py-3.5 rounded-2xl shadow-sm">
                              {/* Format the result in a user-friendly way */}
                              {typeof result === 'object' &&
                              result !== null ? (
                                <div className="flex flex-col gap-2">
                                  {/* If there's a message property, show it first */}
                                  {result.message && (
                                    <p>{result.message}</p>
                                  )}

                                  {/* If there's a total property with count and amount, show a summary */}
                                  {result.total &&
                                    typeof result.total ===
                                      'object' &&
                                    'count' in result.total &&
                                    'amount' in result.total && (
                                      <p>
                                        <span className="font-medium">
                                          Total:
                                        </span>{' '}
                                        {result.total.count}{' '}
                                        {result.total.count === 1
                                          ? 'item'
                                          : 'items'}
                                        {typeof result.total
                                          .amount === 'number' && (
                                          <span>
                                            {' '}
                                            worth $
                                            {result.total.amount.toLocaleString(
                                              undefined,
                                              {
                                                minimumFractionDigits: 2,
                                                maximumFractionDigits: 2,
                                              }
                                            )}
                                          </span>
                                        )}
                                      </p>
                                    )}

                                  {/* If there are status breakdowns like paid, pending, etc. */}
                                  {[
                                    'paid',
                                    'pending',
                                    'overdue',
                                    'cancelled',
                                  ].some(
                                    (key) =>
                                      key in result &&
                                      typeof result[key] === 'object'
                                  ) && (
                                    <div className="grid grid-cols-2 gap-2 mt-1">
                                      {[
                                        'paid',
                                        'pending',
                                        'overdue',
                                        'cancelled',
                                      ].map((status) => {
                                        if (
                                          !(status in result) ||
                                          typeof result[status] !==
                                            'object'
                                        )
                                          return null;
                                        const statusData = result[
                                          status
                                        ] as {
                                          count: number;
                                          amount?: number;
                                        };
                                        return (
                                          <div
                                            key={status}
                                            className="flex justify-between"
                                          >
                                            <span className="capitalize">
                                              {status}:
                                            </span>
                                            <span>
                                              {statusData.count}
                                              {typeof statusData.amount ===
                                                'number' && (
                                                <span className="text-muted-foreground ml-1">
                                                  ($
                                                  {statusData.amount.toLocaleString(
                                                    undefined,
                                                    {
                                                      minimumFractionDigits: 2,
                                                      maximumFractionDigits: 2,
                                                    }
                                                  )}
                                                  )
                                                </span>
                                              )}
                                            </span>
                                          </div>
                                        );
                                      })}
                                    </div>
                                  )}

                                  {/* If there's a currencyBreakdown */}
                                  {result.currencyBreakdown &&
                                    typeof result.currencyBreakdown ===
                                      'object' && (
                                      <div className="mt-2">
                                        <p className="text-sm font-medium mb-1">
                                          Currency Breakdown:
                                        </p>
                                        <div className="grid grid-cols-2 gap-2">
                                          {Object.entries(
                                            result.currencyBreakdown
                                          ).map(
                                            ([currency, data]) => {
                                              const breakdownData =
                                                data as CurrencyBreakdownItem;
                                              return (
                                                <div
                                                  key={currency}
                                                  className="flex justify-between"
                                                >
                                                  <span>
                                                    {currency}:
                                                  </span>
                                                  <span>
                                                    {
                                                      breakdownData.count
                                                    }
                                                    {typeof breakdownData.amount ===
                                                      'number' && (
                                                      <span className="text-muted-foreground ml-1">
                                                        ($
                                                        {breakdownData.amount.toLocaleString(
                                                          undefined,
                                                          {
                                                            minimumFractionDigits: 2,
                                                            maximumFractionDigits: 2,
                                                          }
                                                        )}
                                                        )
                                                      </span>
                                                    )}
                                                  </span>
                                                </div>
                                              );
                                            }
                                          )}
                                        </div>
                                      </div>
                                    )}
                                </div>
                              ) : (
                                <p>{String(result)}</p>
                              )}
                            </div>
                          )}
                        </div>
                      );
                    }
                    return (
                      <div
                        key={toolCallId}
                        className={cx({
                          skeleton: ['getWeather'].includes(toolName),
                        })}
                      >
                        {toolName === 'getWeather' ? (
                          <Weather />
                        ) : toolName === 'createDocument' ? (
                          <DocumentPreview
                            isReadonly={isReadonly}
                            args={args}
                          />
                        ) : toolName === 'updateDocument' ? (
                          <DocumentToolCall
                            type="update"
                            args={args}
                            isReadonly={isReadonly}
                          />
                        ) : toolName === 'requestSuggestions' ? (
                          <DocumentToolCall
                            type="request-suggestions"
                            args={args}
                            isReadonly={isReadonly}
                          />
                        ) : toolName === 'createInvoice' ? (
                          <InvoiceCreator
                            id={args.id || 'pending'}
                            vendorName={args.vendorName}
                            invoiceNumber={args.invoiceNumber}
                            isEditing={!!args.existingInvoiceId}
                            existingData={{
                              currency: args.currency,
                              issueDate: args.issueDate,
                              dueDate: args.dueDate,
                              category: args.category,
                              notes: args.notes,
                            }}
                          />
                        ) : toolName === 'generateReport' ? (
                          <ReportPreview
                            isReadonly={isReadonly}
                            args={{
                              title: args.title,
                              reportType: args.reportType,
                            }}
                            progress={{
                              step: args.progress?.step || 1,
                              totalSteps:
                                args.progress?.totalSteps || 5,
                              message:
                                args.progress?.message ||
                                'Generating report...',
                              percentage:
                                args.progress?.percentage || 20,
                            }}
                          />
                        ) : toolName === 'emailReport' ? (
                          <ReportEmailToolCall
                            reportTitle={
                              args.reportId
                                ? `Report ${args.reportId}`
                                : 'Report'
                            }
                            recipients={args.emailAddresses}
                          />
                        ) : toolName === 'scheduleReport' ? (
                          <div className="bg-card/50 border border-border/40 px-5 py-3.5 rounded-2xl shadow-sm">
                            <p>
                              Scheduling report &quot;{args.title}
                              &quot;...
                            </p>
                          </div>
                        ) : toolName === 'cancelScheduledReport' ? (
                          <div className="bg-card/50 border border-border/40 px-5 py-3.5 rounded-2xl shadow-sm">
                            <p>Cancelling report schedule...</p>
                          </div>
                        ) : null}
                      </div>
                    );
                  })}
                </div>
              )}

            {!isReadonly && (
              <MessageActions
                key={`action-${message.id}`}
                chatId={chatId}
                message={message}
                vote={vote}
                isLoading={isLoading}
              />
            )}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export const PreviewMessage = memo(
  PurePreviewMessage,
  (prevProps, nextProps) => {
    if (prevProps.isLoading !== nextProps.isLoading) return false;
    if (prevProps.message.reasoning !== nextProps.message.reasoning)
      return false;
    if (prevProps.message.content !== nextProps.message.content)
      return false;
    if (
      !equal(
        prevProps.message.toolInvocations,
        nextProps.message.toolInvocations
      )
    )
      return false;
    if (!equal(prevProps.vote, nextProps.vote)) return false;

    return true;
  }
);

export const ThinkingMessage = () => {
  const role = 'assistant';

  return (
    <motion.div
      className="w-full mx-auto max-w-3xl px-4 group/message"
      initial={{ y: 10, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{
        type: 'spring',
        stiffness: 260,
        damping: 20,
        duration: 0.3,
        delay: 0.2,
      }}
      data-role={role}
    >
      <div className="flex gap-4 w-full">
        <div className="size-10 flex items-center rounded-full justify-center ring-1 shrink-0 ring-blue-500/30 dark:ring-[#0097B1]/30 bg-gradient-to-br from-blue-100 to-cyan-100 dark:from-[#22304d] dark:to-[#22304d] text-blue-600 dark:text-white shadow-lg hover:shadow-blue-500/20 dark:hover:shadow-[#0097B1]/20 transition-all duration-300">
          <motion.div
            animate={{
              rotate: [0, 15, 0, -15, 0],
              scale: [1, 1.1, 1, 1.1, 1],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              repeatType: 'loop',
            }}
          >
            <SparklesIcon size={16} />
          </motion.div>
        </div>

        <div className="flex flex-col gap-2 w-full">
          <div className="bg-white/80 dark:bg-[#22304d]/80 border border-blue-200/50 dark:border-[#0097B1]/30 px-5 py-3.5 rounded-2xl shadow-md backdrop-blur-sm w-fit">
            <div className="flex items-center gap-3 text-blue-600 dark:text-[#0097B1]">
              <div className="flex gap-1.5">
                <motion.div
                  className="h-2 w-2 bg-blue-500 dark:bg-[#0097B1] rounded-full"
                  animate={{ opacity: [0.4, 1, 0.4] }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    delay: 0,
                  }}
                />
                <motion.div
                  className="h-2 w-2 bg-blue-500 dark:bg-[#0097B1] rounded-full"
                  animate={{ opacity: [0.4, 1, 0.4] }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    delay: 0.3,
                  }}
                />
                <motion.div
                  className="h-2 w-2 bg-blue-500 dark:bg-[#0097B1] rounded-full"
                  animate={{ opacity: [0.4, 1, 0.4] }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    delay: 0.6,
                  }}
                />
              </div>
              <span className="font-medium">Thinking...</span>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};
