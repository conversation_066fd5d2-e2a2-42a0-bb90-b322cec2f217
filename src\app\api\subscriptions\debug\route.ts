import { NextResponse } from 'next/server';
import { currentUser } from '@clerk/nextjs/server';
import db from '@/db/db';

export async function GET() {
  try {
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Find the database user
    const dbUser = await db.user.findUnique({
      where: { clerkId: user.id },
      include: {
        organizations: true,
      },
    });

    if (!dbUser) {
      return NextResponse.json({
        user: null,
        subscriptions: [],
        debug: {
          clerkId: user.id,
          email: user.emailAddresses[0]?.emailAddress,
          message: 'User not found in database',
        },
      });
    }

    // Get all subscriptions for this user
    const allSubscriptions = await db.subscription.findMany({
      where: {
        userId: dbUser.id,
      },
      include: {
        plan: true,
      },
      orderBy: {
        id: 'desc',
      },
    });

    // Find active subscription
    const activeSubscription = allSubscriptions.find(sub => 
      ['active', 'trialing', 'past_due'].includes(sub.status)
    );

    return NextResponse.json({
      user: {
        id: dbUser.id,
        clerkId: dbUser.clerkId,
        email: dbUser.email,
        role: dbUser.role,
        status: dbUser.status,
        organizationCount: dbUser.organizations.length,
      },
      subscriptions: allSubscriptions.map(sub => ({
        id: sub.id,
        provider: sub.provider,
        status: sub.status,
        statusFormatted: sub.statusFormatted,
        paddleSubscriptionId: sub.paddleSubscriptionId,
        paddleTransactionId: sub.paddleTransactionId,
        planName: sub.plan?.name || 'Unknown Plan',
        price: sub.price,
        renewsAt: sub.renewsAt,
        endsAt: sub.endsAt,
        createdAt: sub.createdAt,
        updatedAt: sub.updatedAt,
      })),
      activeSubscription: activeSubscription ? {
        id: activeSubscription.id,
        status: activeSubscription.status,
        planName: activeSubscription.plan?.name || 'Unknown Plan',
        paddleSubscriptionId: activeSubscription.paddleSubscriptionId,
      } : null,
      debug: {
        totalSubscriptions: allSubscriptions.length,
        hasActiveSubscription: !!activeSubscription,
        environment: process.env.NEXT_PUBLIC_PADDLE_ENVIRONMENT,
        timestamp: new Date().toISOString(),
      },
    });

  } catch (error) {
    console.error('Error in debug endpoint:', error);
    return NextResponse.json(
      { error: 'Failed to fetch debug information' },
      { status: 500 }
    );
  }
}
