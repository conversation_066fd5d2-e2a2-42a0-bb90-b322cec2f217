'use client';

import { useEffect, useState, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Loader2, CheckCircle } from "lucide-react";

interface Subscription {
  id: number;
  status: string;
  plan: {
    name: string;
  };
  // Add other fields as needed
}

function ConfirmationPageContent() {
  const [loading, setLoading] = useState(true);
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [redirecting, setRedirecting] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Get return URL from search params
  const returnUrl = searchParams.get('return_url');

  useEffect(() => {
    // Fallback redirect after 30 seconds to prevent users from getting stuck
    const fallbackTimeout = setTimeout(() => {
      if (!redirecting) {
        console.log('Fallback redirect triggered after 30 seconds');
        setRedirecting(true);
        const targetUrl = returnUrl ? decodeURIComponent(returnUrl) : '/dashboard/subscription';
        router.push(targetUrl);
      }
    }, 30000);

    const fetchSubscription = async () => {
      try {
        setError(null);
        console.log('Fetching subscription status...');
        
        // First try to activate subscription manually if we have a session ID
        const sessionId = searchParams.get('session_id');
        if (sessionId && !subscription) {
          try {
            console.log('Attempting manual subscription activation...');
            const activateResponse = await fetch('/api/subscriptions/activate', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ sessionId }),
            });
            const activateResult = await activateResponse.json();
            console.log('Manual activation result:', activateResult);
          } catch (activateError) {
            console.error('Manual activation failed:', activateError);
          }
        }
        
        const res = await fetch("/api/subscriptions");
        if (!res.ok) {
          throw new Error("Failed to fetch subscription status");
        }
        const data: Subscription[] = await res.json();
        console.log('Subscription data received:', data);
        // Find an active subscription
        const active = data.find((sub) => sub.status === "active");
        console.log('Active subscription found:', active);
        if (active) {
          setSubscription(active);
          setLoading(false);
          
          // Auto-redirect after a short delay if return URL is provided
          if (returnUrl && !redirecting) {
            console.log('Redirecting to:', returnUrl);
            setRedirecting(true);
            clearTimeout(fallbackTimeout);
            setTimeout(() => {
              router.push(decodeURIComponent(returnUrl));
            }, 2000);
          }
        } else {
          setSubscription(null);
          setLoading(true);
        }
      } catch (err: unknown) {
        console.error('Error fetching subscription:', err);
        setError(err instanceof Error ? err.message : "Unknown error");
        setLoading(false);
      }
    };

    // Initial fetch
    fetchSubscription();
    // Poll every 3 seconds
    const interval = setInterval(fetchSubscription, 3000);

    return () => {
      clearInterval(interval);
      clearTimeout(fallbackTimeout);
    };
  }, [returnUrl, router, redirecting]);

  const handleRedirect = () => {
    if (returnUrl) {
      router.push(decodeURIComponent(returnUrl));
    } else {
      router.push("/dashboard/subscription");
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <h2 className="text-xl font-semibold mb-2">Processing your subscription...</h2>
        <p className="text-muted-foreground mb-4">
          Please wait while we confirm your payment and activate your subscription.
        </p>
        {error && (
          <div className="text-red-500 mb-2">{error}</div>
        )}
      </div>
    );
  }

  if (subscription) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <CheckCircle className="h-12 w-12 text-green-500 mb-4" />
        <h2 className="text-2xl font-bold mb-2">Subscription Activated!</h2>
        <p className="text-muted-foreground mb-4">
          Your subscription to the <span className="font-semibold">{subscription.plan.name}</span> plan is now active.
        </p>
        {redirecting ? (
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Redirecting...</span>
          </div>
        ) : (
          <Button onClick={handleRedirect}>
            {returnUrl ? "Continue to Dashboard" : "Go to Subscription Dashboard"}
          </Button>
        )}
      </div>
    );
  }

  // If not loading and no subscription found
  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh]">
      <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
      <h2 className="text-xl font-semibold mb-2">Still processing...</h2>
      <p className="text-muted-foreground mb-4">
        We haven&apos;t detected an active subscription yet. Please wait a moment or contact support if this takes too long.
      </p>
      {error && (
        <div className="text-red-500 mb-2">{error}</div>
      )}
    </div>
  );
}

function ConfirmationPageFallback() {
  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh]">
      <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
      <h2 className="text-xl font-semibold mb-2">Loading...</h2>
      <p className="text-muted-foreground mb-4">
        Please wait while we prepare the confirmation page.
      </p>
    </div>
  );
}

export default function ConfirmationPage() {
  return (
    <Suspense fallback={<ConfirmationPageFallback />}>
      <ConfirmationPageContent />
    </Suspense>
  );
}
