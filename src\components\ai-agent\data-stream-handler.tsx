'use client';

import { useChat } from 'ai/react';
import { useEffect, useRef } from 'react';
import { artifactDefinitions, ArtifactKind } from './artifact';
import { Suggestion } from '@prisma/client';
import { initialArtifactData, useArtifact } from '@/hooks/use-artifact';

// Define specific content types
type ReportGenerationStartContent = {
  id: string;
  title: string;
  reportType: string;
  format: string;
};

type ReportGenerationProgressContent = {
  step: number;
  totalSteps: number;
  message: string;
  percentage: number;
};

type ReportGenerationCompleteContent = {
  id: string;
  title: string;
  reportType: string;
  format: string;
  fileUrl: string;
  createdAt: string;
};

// For report-generation-error and report-email-error, content has a 'message' and an optional 'error' string.
// The tool 'generate-report.ts' sends { message: '...', error: '...' } for report-generation-error.
// The tool 'send-report-email.ts' sends { message: '...', error: '...' } for report-email-error.
// However, the DataStreamHandler accesses delta.content.error (e.g. reportError: delta.content.error)
// Let's make a generic error content type that fits both the source and usage.
type StreamErrorContent = {
  error?: string;       // As used in the handler
  message?: string;     // As sent by the tools
};

type ReportEmailStartContent = {
  reportId: string;
  emailAddresses: string[];
};

type ReportEmailCompleteContent = {
  reportId: string;
  emailAddresses: string[];
  sentAt: string;
};

export type DataStreamDelta = {
  type:
    | 'text-delta'
    | 'code-delta'
    | 'sheet-delta'
    | 'image-delta'
    | 'title'
    | 'id'
    | 'suggestion'
    | 'clear'
    | 'finish'
    | 'kind'
    | 'report-generation-start'
    | 'report-generation-progress'
    | 'report-generation-complete'
    | 'report-generation-error'
    | 'report-email-start'
    | 'report-email-complete'
    | 'report-email-error';
  content:
    | string // For text-delta, title, id, clear
    | Suggestion // For suggestion
    | ArtifactKind // For kind
    | ReportGenerationStartContent
    | ReportGenerationProgressContent
    | ReportGenerationCompleteContent
    | StreamErrorContent // For report-generation-error and report-email-error
    | ReportEmailStartContent
    | ReportEmailCompleteContent
    | Record<string, unknown>; // Fallback for any other object shapes like code-delta, sheet-delta, image-delta if they are objects
};

export function DataStreamHandler({ id }: { id: string }) {
  const { data: dataStream } = useChat({ id });
  const { artifact, setArtifact, setMetadata } = useArtifact();
  const lastProcessedIndex = useRef(-1);

  useEffect(() => {
    if (!dataStream?.length) return;

    const newDeltas = dataStream.slice(lastProcessedIndex.current + 1);
    lastProcessedIndex.current = dataStream.length - 1;

    (newDeltas as DataStreamDelta[]).forEach((delta: DataStreamDelta) => {
      const artifactDefinition = artifactDefinitions.find(
        (artifactDefinition) => artifactDefinition.kind === artifact.kind,
      );

      if (artifactDefinition?.onStreamPart) {
        artifactDefinition.onStreamPart({
          streamPart: delta,
          setArtifact,
          // @ts-expect-error - Type compatibility between SWR mutator and React state setter
          setMetadata,
        });
      }

      setArtifact((draftArtifact) => {
        if (!draftArtifact) {
          return { ...initialArtifactData, status: 'streaming' };
        }

        switch (delta.type) {
          case 'id':
            return {
              ...draftArtifact,
              documentId: delta.content as string,
              status: 'streaming',
            };

          case 'title':
            return {
              ...draftArtifact,
              title: delta.content as string,
              status: 'streaming',
            };

          case 'kind':
            return {
              ...draftArtifact,
              kind: delta.content as ArtifactKind,
              status: 'streaming',
            };

          case 'clear':
            return {
              ...draftArtifact,
              content: '',
              status: 'streaming',
            };

          case 'finish':
            return {
              ...draftArtifact,
              status: 'idle',
            };

          // Handle report generation events
          case 'report-generation-start':
            const rgStartContent = delta.content as ReportGenerationStartContent;
            return {
              ...draftArtifact,
              reportId: rgStartContent.id,
              title: rgStartContent.title,
              reportType: rgStartContent.reportType,
              format: rgStartContent.format,
              status: 'streaming',
            };

          case 'report-generation-progress':
            const rgProgressContent = delta.content as ReportGenerationProgressContent;
            return {
              ...draftArtifact,
              reportProgress: {
                step: rgProgressContent.step,
                totalSteps: rgProgressContent.totalSteps,
                message: rgProgressContent.message,
                percentage: rgProgressContent.percentage,
              },
              status: 'streaming',
            };

          case 'report-generation-complete':
            const rgCompleteContent = delta.content as ReportGenerationCompleteContent;
            // Only auto-open if not manually closed by user
            const shouldOpen = !draftArtifact.manuallyClosed;
            return {
              ...draftArtifact,
              reportId: rgCompleteContent.id,
              title: rgCompleteContent.title,
              reportType: rgCompleteContent.reportType,
              format: rgCompleteContent.format,
              fileUrl: rgCompleteContent.fileUrl,
              isVisible: shouldOpen, // Only open automatically if not manually closed
              isMinimized: !shouldOpen, // Minimize if not showing
              status: 'idle',
            };

          case 'report-generation-error':
            const rgErrorContent = delta.content as StreamErrorContent;
            return {
              ...draftArtifact,
              reportError: rgErrorContent.error,
              status: 'idle',
            };

          // Handle report email events
          case 'report-email-start':
            const reStartContent = delta.content as ReportEmailStartContent;
            return {
              ...draftArtifact,
              reportId: reStartContent.reportId,
              emailAddresses: reStartContent.emailAddresses,
              status: 'streaming',
            };

          case 'report-email-complete':
            const reCompleteContent = delta.content as ReportEmailCompleteContent;
            return {
              ...draftArtifact,
              reportId: reCompleteContent.reportId,
              emailAddresses: reCompleteContent.emailAddresses,
              emailSentAt: reCompleteContent.sentAt,
              status: 'idle',
            };

          case 'report-email-error':
            const reErrorContent = delta.content as StreamErrorContent;
            return {
              ...draftArtifact,
              reportError: reErrorContent.error,
              status: 'idle',
            };

          default:
            return draftArtifact;
        }
      });
    });
  }, [dataStream, setArtifact, setMetadata, artifact]);

  return null;
}
