import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { runManualEmailSync } from "@/lib/services/gmail-service";

export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const { userId: clerkUserId } = await auth();
    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await request.json();
    const { userId } = body;

    // User is always allowed to sync their own emails
    const targetUserId = userId || clerkUserId;

    // Run manual email sync
    const result = await runManualEmailSync(targetUserId);

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error running manual email sync:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to run email sync' 
      },
      { status: 500 }
    );
  }
} 