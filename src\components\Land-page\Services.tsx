/* eslint-disable @next/next/no-img-element */
import React from "react";
import { Sparkles } from "lucide-react";

function Services() {
  return (
    <>
      <div className="bg-black p-4 overflow-hidden">
        <div className="max-w-7xl mx-auto">
          {/* New title section */}
          <div className="relative mb-6 sm:mb-8 md:mb-12">
            <div className="inline-flex items-center gap-2 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full bg-neutral-900 border border-neutral-700 mb-3 sm:mb-4">
              <Sparkles className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
              <span className="text-xs sm:text-sm text-white font-medium">
                Core Services
              </span>
            </div>
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-2">
              Invoice Intelligence
            </h2>
            <p className="text-sm md:text-base text-gray-400 max-w-lg">
              Powerful AI tools to automate and enhance your invoice management
              workflow.
            </p>

            <div
              className="absolute top-2 right-0 text-[150px] font-bold select-none hidden lg:block z-0 overflow-hidden"
              style={{
                background:
                  "linear-gradient(to bottom, rgba(255,255,255,0.2), transparent)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                backgroundClip: "text",
                color: "transparent",
                maxWidth: "100%",
              }}
            >
              SERVICES
            </div>
          </div>
        </div>
      </div>

      <div className="bg-black flex items-center justify-center p-3 sm:p-4">
        <div className="border border-neutral-600 bg-neutral-950 rounded-2xl sm:rounded-3xl p-1 sm:p-2 w-full max-w-7xl mx-auto">
          <div className="flex flex-col md:flex-row gap-2 sm:gap-3 w-full">
            {/* First Section */}
            <div className="flex-1 relative h-[400px] sm:h-[450px] md:h-[500px] w-full bg-neutral-900/60 backdrop-blur-xl rounded-[20px] shadow-[0_0_0_0,inset_0_0_30px_rgba(200,200,200,0.1)] border border-neutral-700 text-gray-100 p-3 sm:p-4">
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-2 sm:mb-4 text-white">
                Financial Analytics Dashboard
              </h2>
              <p className="text-xs sm:text-sm md:text-base text-gray-400 mb-4 sm:mb-6 md:mb-8">
                Access comprehensive financial insights with our interactive
                dashboard featuring cash flow analysis, vendor performance
                metrics, spending trends, and anomaly detection to make
                data-driven decisions.
              </p>
              <div className="w-full h-40 sm:h-48 md:h-60 rounded-lg"></div>
              <img
                src="/imgs/chart.png"
                alt="Analytics Dashboard Preview"
                className="absolute bottom-3 sm:bottom-4 right-3 sm:right-4 w-10/12 h-48 sm:h-56 md:h-72 rounded-lg object-cover"
              />
            </div>

            {/* Second Section */}
            <div className="flex-1 relative h-[400px] sm:h-[450px] md:h-[500px] w-full bg-neutral-900/60 backdrop-blur-xl rounded-[20px] shadow-[0_0_0_0,inset_0_0_30px_rgba(200,200,200,0.1)] border border-neutral-700 text-gray-100 p-3 sm:p-4">
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-2 sm:mb-4 text-white">
                AI-Powered Invoice Processing
              </h2>
              <p className="text-xs sm:text-sm md:text-base text-gray-400 mb-4 sm:mb-6 md:mb-8">
                Automatically extract data from invoices with our AI technology.
                Process multiple invoice formats, classify documents, detect
                fraud patterns, and export to any format with a single click.
              </p>
              <div className="w-full h-40 sm:h-48 md:h-60 rounded-lg"></div>
              <img
                src="/imgs/phone.png"
                alt="Invoice Processing Preview"
                className="absolute bottom-3 sm:bottom-4 right-3 sm:right-4 w-11/12 h-48 sm:h-56 md:h-72 rounded-lg object-cover"
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default Services;
