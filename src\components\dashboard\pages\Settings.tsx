'use client';

import DashboardLayout from '@/components/layout/DashboardLayout';
import {
  Tabs,
  Ta<PERSON>Content,
  Ta<PERSON>List,
  TabsTrigger,
} from '@/components/ui/tabs';
import { useState } from 'react';
import SecurityTab from '@/components/dashboard/settings/SecurityTab';
import AdvancedTab from '@/components/dashboard/settings/AdvancedTab';
import ApiKeysTab from '@/components/dashboard/settings/ApiKeysTab';


const Settings = () => {
  const [activeTab, setActiveTab] = useState('account');

  return (
    <DashboardLayout>
      <div className="min-h-screen flex-col bg-background dark:bg-[#0B1739] w-full space-y-6">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">
            Settings
          </h1>
          <p className="text-muted-foreground">
            Manage your account settings and preferences.
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4" id="settings-tabs">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="account">Account</TabsTrigger>
            <TabsTrigger value="advanced">Advanced</TabsTrigger>
            <TabsTrigger value="api-keys">API Keys</TabsTrigger>
          </TabsList>

          <TabsContent value="account">
            <SecurityTab />
          </TabsContent>

          <TabsContent value="advanced">
            <AdvancedTab />
          </TabsContent>

          <TabsContent value="api-keys">
            <ApiKeysTab />
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default Settings;
