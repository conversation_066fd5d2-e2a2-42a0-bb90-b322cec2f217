"use client";

import { ChangeEvent } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

interface CustomerInfoTabProps {
  customerInfo: {
    name: string;
    address: string;
    email: string;
    phone: string;
  };
  handleCustomerInfoChange: (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
}

export function CustomerInfoTab({
  customerInfo,
  handleCustomerInfoChange,
}: CustomerInfoTabProps) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Customer Information</h3>

      <div className="space-y-2">
        <Label htmlFor="customer-name">Customer Name</Label>
        <Input
          id="customer-name"
          name="name"
          value={customerInfo.name}
          onChange={handleCustomerInfoChange}
          placeholder="Customer name"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="customer-address">Address</Label>
        <Textarea
          id="customer-address"
          name="address"
          value={customerInfo.address}
          onChange={handleCustomerInfoChange}
          placeholder="Customer address"
          rows={2}
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="customer-email">Email</Label>
          <Input
            id="customer-email"
            name="email"
            type="email"
            value={customerInfo.email}
            onChange={handleCustomerInfoChange}
            placeholder="<EMAIL>"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="customer-phone">Phone</Label>
          <Input
            id="customer-phone"
            name="phone"
            value={customerInfo.phone}
            onChange={handleCustomerInfoChange}
            placeholder="Phone number"
          />
        </div>
      </div>
    </div>
  );
}
