'use client'

import { <PERSON><PERSON>p<PERSON><PERSON>, Mail, MapPin, Phone } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "../ui/textarea_landing";

export default function ContactPage() {
  const handleEmailClick = () => {
    window.location.href = "mailto:<EMAIL>";
  };

  const handlePhoneClick = () => {
    window.location.href = "tel:+970597974929";
  };

  const handleLocationClick = () => {
    window.open("https://maps.google.com/?q=Hebron,Palestine", "_blank");
  };

  return (
    <div className="bg-black text-white relative overflow-hidden">
      {/* Background text */}
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none opacity-10 select-none">
        <h1 className="text-[12vw] font-bold tracking-tighter">CONTACT</h1>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8 sm:py-12 md:py-16 relative z-10">
        <div className="grid lg:grid-cols-2 gap-4 sm:gap-6 md:gap-8 mt-4">
          {/* Left column - Text and contact info */}
          <div className="space-y-4 sm:space-y-6 md:space-y-8">
            <div className="space-y-2 sm:space-y-4">
              <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold">
                Get in touch
              </h1>
              <p className="text-sm sm:text-base text-gray-400 max-w-md">
                Have questions about our AI-powered invoice management solution? We&apos;re here to help!
              </p>
            </div>

            <div className="space-y-4 sm:space-y-6">
              {/* Email */}
              <button
                onClick={handleEmailClick}
                className="w-full relative bg-neutral-900/60 backdrop-blur-xl rounded-xl p-4 sm:p-6 flex items-center shadow-[0_0_0_0,inset_0_0_30px_rgba(200,200,200,0.1)] border border-neutral-700 hover:bg-neutral-800/60 transition-colors"
              >
                <div className="w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 bg-neutral-950 rounded-lg flex items-center justify-center mr-3 sm:mr-4 md:mr-6 flex-shrink-0">
                  <Mail className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6" />
                </div>
                <div className="flex-1 min-w-0 text-left">
                  <p className="text-xs sm:text-sm text-gray-400">Email us</p>
                  <p className="font-medium text-sm sm:text-base md:text-lg truncate">
                    <EMAIL>
                  </p>
                </div>
                <ArrowUpRight className="w-4 h-4 sm:w-5 sm:h-5 ml-auto flex-shrink-0" />
              </button>

              {/* Phone */}
              <button
                onClick={handlePhoneClick}
                className="w-full relative bg-neutral-900/60 backdrop-blur-xl rounded-xl p-4 sm:p-6 flex items-center shadow-[0_0_0_0,inset_0_0_30px_rgba(200,200,200,0.1)] border border-neutral-700 hover:bg-neutral-800/60 transition-colors"
              >
                <div className="w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 bg-neutral-950 rounded-lg flex items-center justify-center mr-3 sm:mr-4 md:mr-6 flex-shrink-0">
                  <Phone className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6" />
                </div>
                <div className="flex-1 min-w-0 text-left">
                  <p className="text-xs sm:text-sm text-gray-400">Call us</p>
                  <p className="font-medium text-sm sm:text-base md:text-lg truncate">
                    (+970) 597974929
                  </p>
                </div>
                <ArrowUpRight className="w-4 h-4 sm:w-5 sm:h-5 ml-auto flex-shrink-0" />
              </button>

              {/* Location */}
              <button
                onClick={handleLocationClick}
                className="w-full relative bg-neutral-900/60 backdrop-blur-xl rounded-xl p-4 sm:p-6 flex items-center shadow-[0_0_0_0,inset_0_0_30px_rgba(200,200,200,0.1)] border border-neutral-700 hover:bg-neutral-800/60 transition-colors"
              >
                <div className="w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 bg-neutral-950 rounded-lg flex items-center justify-center mr-3 sm:mr-4 md:mr-6 flex-shrink-0">
                  <MapPin className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6" />
                </div>
                <div className="flex-1 min-w-0 text-left">
                  <p className="text-xs sm:text-sm text-gray-400">Our location</p>
                  <p className="font-medium text-sm sm:text-base md:text-lg truncate">
                    Hebron, Palestine
                  </p>
                </div>
                <ArrowUpRight className="w-4 h-4 sm:w-5 sm:h-5 ml-auto flex-shrink-0" />
              </button>
            </div>
          </div>

          {/* Right column - Form */}
          <div className="relative bg-neutral-900/60 backdrop-blur-xl rounded-xl p-4 sm:p-5 shadow-[0_0_0_0,inset_0_0_30px_rgba(200,200,200,0.1)] border border-neutral-700">
            <form className="space-y-3 sm:space-y-4">
              <div>
                <Input
                  type="text"
                  placeholder="Name"
                  className="bg-neutral-800/60 border-0 h-10 sm:h-12 md:h-14 text-white text-sm sm:text-base placeholder:text-gray-500"
                />
              </div>
              <div>
                <Input
                  type="email"
                  placeholder="Email"
                  className="bg-neutral-800/60 border-0 h-10 sm:h-12 md:h-14 text-white text-sm sm:text-base placeholder:text-gray-500"
                />
              </div>
              <div>
                <Textarea
                  placeholder="Message"
                  className="bg-gray-800/60 min-h-[180px] sm:min-h-[200px] md:min-h-[240px] border border-neutral-700 text-white text-sm sm:text-base placeholder:text-gray-500 resize-none"
                />
              </div>
              <Button
                variant="default"
                className="w-full bg-white text-black hover:bg-gray-200 h-10 sm:h-12 md:h-14 mt-2 sm:mt-4 text-sm sm:text-base font-medium"
              >
                Submit
              </Button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
