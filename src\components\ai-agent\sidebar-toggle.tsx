"use client"

import type { ComponentProps } from "react"
import { motion } from "motion/react"

import { useSidebar } from "@/components/ui/sidebar"
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip"

import { SidebarRightIcon } from "./icons"
import { But<PERSON> } from "@/components/ui/button"

export function SidebarToggle({}: ComponentProps<typeof Button>) {
  const { toggleSidebar } = useSidebar()

  return (
    <Tooltip delayDuration={0}>
      <TooltipTrigger asChild>
        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
          <Button
            onClick={toggleSidebar}
            variant="outline"
            className="md:px-2.5 md:py-2 h-9 rounded-full border-border/40 bg-background/80 hover:bg-background/90 hover:border-primary/40 hover:shadow-sm transition-all duration-200 backdrop-blur-sm"
          >
            <SidebarRightIcon size={16} />
          </Button>
        </motion.div>
      </TooltipTrigger>
      <TooltipContent align="start">Toggle Sidebar</TooltipContent>
    </Tooltip>
  )
}
