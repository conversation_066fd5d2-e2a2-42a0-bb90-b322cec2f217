'use client';

import { useState } from 'react';
import { motion } from 'motion/react';
import { InvoiceStatus } from '@prisma/client';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from '@/components/ui/alert-dialog';
import {
  <PERSON>Icon,
  ClockIcon,
  FolderIcon,
  MoreVerticalIcon,
  TagIcon,
  TrashIcon,
  XIcon
} from 'lucide-react';

interface LineItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  amount: number;
}

interface InvoiceDisplayProps {
  invoice: {
    id: string;
    number: string;
    vendorName: string;
    amount: number;
    currency: string;
    status: InvoiceStatus;
    category?: string;
    issueDate: string;
    dueDate: string;
    paidDate?: string | null;
    notes?: string;
    lineItems?: LineItem[];
  };
  isDetailed?: boolean;
  onStatusChange?: (invoiceId: string, newStatus: InvoiceStatus) => void;
  onDelete?: (invoiceId: string) => void;
  onAddToFolder?: (invoiceId: string) => void;
  onCategorize?: (invoiceId: string) => void;
}

export function InvoiceDisplay({
  invoice,
  isDetailed = false,
  onStatusChange,
  onDelete,
  onAddToFolder,
  onCategorize
}: InvoiceDisplayProps) {
  const [expanded, setExpanded] = useState(isDetailed);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const getStatusColor = (status: InvoiceStatus) => {
    switch (status) {
      case 'PAID':
        return 'bg-green-500 hover:bg-green-600';
      case 'PENDING':
        return 'bg-yellow-500 hover:bg-yellow-600';
      case 'OVERDUE':
        return 'bg-red-500 hover:bg-red-600';
      default:
        return 'bg-blue-500 hover:bg-blue-600';
    }
  };

  // Helper function to convert full currency names to ISO codes
  const getCurrencyCode = (currency: string): string => {
    // Map of common full currency names to ISO codes
    const currencyMap: Record<string, string> = {
      'United States Dollar': 'USD',
      'US Dollar': 'USD',
      'Dollar': 'USD',
      'Euro': 'EUR',
      'British Pound': 'GBP',
      'Pound Sterling': 'GBP',
      'Japanese Yen': 'JPY',
      'Canadian Dollar': 'CAD',
      'Australian Dollar': 'AUD',
      'Swiss Franc': 'CHF',
      'Chinese Yuan': 'CNY',
      'Indian Rupee': 'INR',
      'Brazilian Real': 'BRL',
      'Mexican Peso': 'MXN',
      'South African Rand': 'ZAR',
    };

    // If the currency is already a valid 3-letter code, return it
    if (currency && currency.length === 3) {
      return currency.toUpperCase();
    }

    // Try to find the currency in the map
    const code = currencyMap[currency];

    // Return the code if found, otherwise default to USD
    return code || 'USD';
  };

  const formatCurrency = (amount: number, currency: string) => {
    // Get the ISO currency code
    const currencyCode = getCurrencyCode(currency);

    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <>
      <Card className="w-full mb-4 border border-border/40 shadow-md hover:shadow-lg transition-all duration-200">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-lg font-semibold">{invoice.number}</CardTitle>
              <CardDescription>{invoice.vendorName}</CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge className={`${getStatusColor(invoice.status)} text-white`}>
                {invoice.status}
              </Badge>

              {(onStatusChange || onDelete || onAddToFolder || onCategorize) && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <MoreVerticalIcon className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>

                    {onStatusChange && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel>Change Status</DropdownMenuLabel>
                        <DropdownMenuItem
                          onClick={() => onStatusChange(invoice.id, 'PENDING')}
                          className="flex items-center gap-2"
                        >
                          <ClockIcon className="h-4 w-4 text-yellow-500" />
                          <span>Mark as Pending</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => onStatusChange(invoice.id, 'PAID')}
                          className="flex items-center gap-2"
                        >
                          <CheckIcon className="h-4 w-4 text-green-500" />
                          <span>Mark as Paid</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => onStatusChange(invoice.id, 'OVERDUE')}
                          className="flex items-center gap-2"
                        >
                          <XIcon className="h-4 w-4 text-red-500" />
                          <span>Mark as Overdue</span>
                        </DropdownMenuItem>
                      </>
                    )}

                    {onCategorize && (
                      <DropdownMenuItem
                        onClick={() => onCategorize(invoice.id)}
                        className="flex items-center gap-2"
                      >
                        <TagIcon className="h-4 w-4" />
                        <span>Categorize</span>
                      </DropdownMenuItem>
                    )}

                    {onAddToFolder && (
                      <DropdownMenuItem
                        onClick={() => onAddToFolder(invoice.id)}
                        className="flex items-center gap-2"
                      >
                        <FolderIcon className="h-4 w-4" />
                        <span>Add to Folder</span>
                      </DropdownMenuItem>
                    )}

                    {onDelete && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => setIsDeleteDialogOpen(true)}
                          className="flex items-center gap-2 text-red-600"
                        >
                          <TrashIcon className="h-4 w-4" />
                          <span>Delete</span>
                        </DropdownMenuItem>
                      </>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="pb-2">
          <div className="flex justify-between mb-2">
            <div>
              <p className="text-sm text-muted-foreground">Amount</p>
              <p className="font-medium">{formatCurrency(invoice.amount, invoice.currency)}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Issue Date</p>
              <p className="font-medium">{formatDate(invoice.issueDate)}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Due Date</p>
              <p className="font-medium">{formatDate(invoice.dueDate)}</p>
            </div>
          </div>

          {invoice.category && (
            <div className="mt-2">
              <Badge variant="outline" className="bg-primary/10 text-primary border-primary/30">
                {invoice.category}
              </Badge>
            </div>
          )}

          {expanded && invoice.lineItems && invoice.lineItems.length > 0 && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="mt-4 overflow-hidden"
            >
              <h4 className="text-sm font-medium mb-2">Line Items</h4>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Description</TableHead>
                    <TableHead className="text-right">Qty</TableHead>
                    <TableHead className="text-right">Unit Price</TableHead>
                    <TableHead className="text-right">Amount</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {invoice.lineItems.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>{item.description}</TableCell>
                      <TableCell className="text-right">{item.quantity}</TableCell>
                      <TableCell className="text-right">{formatCurrency(item.unitPrice, invoice.currency)}</TableCell>
                      <TableCell className="text-right">{formatCurrency(item.amount, invoice.currency)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {invoice.notes && (
                <div className="mt-4">
                  <h4 className="text-sm font-medium mb-1">Notes</h4>
                  <p className="text-sm text-muted-foreground">{invoice.notes}</p>
                </div>
              )}
            </motion.div>
          )}
        </CardContent>
        <CardFooter>
          {invoice.lineItems && invoice.lineItems.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setExpanded(!expanded)}
              className="ml-auto text-primary"
            >
              {expanded ? 'Show Less' : 'Show Details'}
            </Button>
          )}
        </CardFooter>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will delete invoice {invoice.number}. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                onDelete?.(invoice.id);
                setIsDeleteDialogOpen(false);
              }}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
