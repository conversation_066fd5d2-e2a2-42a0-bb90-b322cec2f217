"use client";

import { useEffect, useState } from "react";

interface ScannerAnimationProps {
  imageUrl?: string;
  title?: string;
  subtitle?: string;
}

export function ScannerAnimation({ imageUrl, title = "Processing Document", subtitle = "Extracting invoice data..." }: ScannerAnimationProps) {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) return 0;
        return prev + 1;
      });
    }, 30);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="relative w-full max-w-md mx-auto p-8">
      {/* Scanner Container */}
      <div className="relative rounded-lg overflow-hidden bg-gradient-to-br from-indigo-50 to-purple-50 dark:from-indigo-950/20 dark:to-purple-950/20 shadow-2xl">
        {/* Document Preview */}
        <div className="relative w-full h-64 bg-white dark:bg-gray-900 shadow-inner">
          {imageUrl ? (
            <div className="relative w-full h-full">
              <img
                src={imageUrl}
                alt="Document preview"
                className="w-full h-full object-contain opacity-50"
              />
              <div className="absolute inset-0 bg-gradient-to-b from-transparent to-white/20 dark:to-black/20" />
            </div>
          ) : (
            <div className="flex items-center justify-center w-full h-full">
              <div className="text-center">
                <svg className="w-20 h-20 mx-auto text-gray-300 dark:text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <p className="mt-2 text-sm text-gray-400">Processing document</p>
              </div>
            </div>
          )}

          {/* Scanning Line Animation */}
          <div
            className="absolute left-0 right-0 h-1 bg-gradient-to-r from-transparent via-indigo-500 to-transparent transition-all duration-300 ease-linear scanner-line"
            style={{
              top: `${progress}%`,
              boxShadow: "0 0 20px 5px rgba(99, 102, 241, 0.5)",
            }}
          >
            <div className="absolute inset-0 bg-indigo-400 blur-sm" />
          </div>

          {/* Scanning Effect Overlay */}
          {progress > 0 && progress < 100 && (
            <div
              className="absolute left-0 right-0 pointer-events-none transition-opacity duration-300"
              style={{
                top: 0,
                height: `${progress}%`,
                background: "linear-gradient(to bottom, transparent 0%, rgba(99, 102, 241, 0.1) 90%, transparent 100%)",
                opacity: 1,
              }}
            />
          )}

          {/* Corner Brackets */}
          <div className="absolute top-4 left-4 w-8 h-8 border-l-2 border-t-2 border-indigo-500 animate-pulse" />
          <div className="absolute top-4 right-4 w-8 h-8 border-r-2 border-t-2 border-indigo-500 animate-pulse" />
          <div className="absolute bottom-4 left-4 w-8 h-8 border-l-2 border-b-2 border-indigo-500 animate-pulse" />
          <div className="absolute bottom-4 right-4 w-8 h-8 border-r-2 border-b-2 border-indigo-500 animate-pulse" />

          {/* Grid Pattern Overlay */}
          <div 
            className="absolute inset-0 opacity-5"
            style={{
              backgroundImage: `repeating-linear-gradient(0deg, transparent, transparent 20px, rgba(99, 102, 241, 0.1) 20px, rgba(99, 102, 241, 0.1) 21px),
                               repeating-linear-gradient(90deg, transparent, transparent 20px, rgba(99, 102, 241, 0.1) 20px, rgba(99, 102, 241, 0.1) 21px)`
            }}
          />
        </div>
      </div>

      {/* Status Text */}
      <div className="mt-6 text-center">
        <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-200 fade-in-animation">
          {title}
        </h3>
        <p className="mt-2 text-sm text-gray-600 dark:text-gray-400 fade-in-animation delay-100">
          {subtitle}
        </p>

        {/* Progress Indicator */}
        <div className="mt-4 flex items-center justify-center space-x-2">
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className="w-2 h-2 bg-indigo-500 rounded-full"
                style={{
                  animation: `bounce 1.5s ease-in-out infinite`,
                  animationDelay: `${i * 200}ms`,
                }}
              />
            ))}
          </div>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            Analyzing document structure
          </span>
        </div>
      </div>

      <style jsx>{`
        @keyframes bounce {
          0%, 100% {
            transform: translateY(0);
            opacity: 0.3;
          }
          50% {
            transform: translateY(-8px);
            opacity: 1;
          }
        }
        
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        .fade-in-animation {
          animation: fadeIn 0.5s ease-out forwards;
        }
        
        .delay-100 {
          animation-delay: 100ms;
        }
        
        .scanner-line {
          background: linear-gradient(90deg, 
            transparent 0%, 
            rgba(99, 102, 241, 0.4) 10%,
            rgba(99, 102, 241, 0.8) 50%,
            rgba(99, 102, 241, 0.4) 90%,
            transparent 100%
          );
        }
      `}</style>
    </div>
  );
}