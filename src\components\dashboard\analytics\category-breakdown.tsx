"use client"

import type React from "react"
import { useEffect, useState } from "react"
import { ChartContainer, ChartLegend, ChartTooltip } from "@/components/ui/chart"
import { <PERSON>, Pie, <PERSON><PERSON><PERSON>, ResponsiveContainer } from "recharts"
import { formatCurrency } from "@/lib/utils"
import { getCategoryBreakdown } from "@/lib/actions/analytics"
import type { FilterValues } from "./data-filters"

interface CategoryBreakdownProps {
  dateRange: {
    from: Date
    to: Date
  }
  height?: number
  filters: FilterValues
}

interface CategoryData {
  id: string
  name: string
  value: number
  percentage: number
  color: string
}

export function CategoryBreakdown({ dateRange, filters }: CategoryBreakdownProps) {
  const [data, setData] = useState<CategoryData[]>([])
  const [loading, setLoading] = useState(true)
  const [activeIndex, setActiveIndex] = useState<number | undefined>(undefined)

  useEffect(() => {
    async function loadCategoryData() {
      setLoading(true)
      try {
        const categoryData = await getCategoryBreakdown(dateRange.from, dateRange.to, filters)
        setData(categoryData || [])
      } catch {
        setData([])
      } finally {
        setLoading(false)
      }
    }

    loadCategoryData()
  }, [dateRange, filters])

  const chartConfig = {
    category: {
      label: "Category",
    },
  }

  const renderData = data.length === 1 ? [{ ...data[0], name: `${data[0].name} (100%)` }] : data

  const onPieEnter = (_: unknown, index: number) => {
    setActiveIndex(index)
  }

  const onPieLeave = () => {
    setActiveIndex(undefined)
  }

  const RADIAN = Math.PI / 180
  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: { cx: number, cy: number, midAngle: number, innerRadius: number, outerRadius: number, percent: number, index: number }) => {
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5
    const x = cx + radius * Math.cos(-midAngle * RADIAN)
    const y = cy + radius * Math.sin(-midAngle * RADIAN)

    return percent > 0.05 ? (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? "start" : "end"}
        dominantBaseline="central"
        className="text-sm font-medium"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    ) : null
  }

  return (
    <div className="w-full h-full flex flex-col">
      <ChartContainer
        config={chartConfig}
        className="flex-1 min-h-[300px] p-4 rounded-xl bg-card/50 backdrop-blur-sm border border-primary/10 shadow-md hover:shadow-lg transition-all duration-300"
      >
        <div className="w-full h-full">
          {loading ? (
            <div className="flex h-full items-center justify-center">
              <div className="animate-pulse flex flex-col items-center gap-3">
                <div className="h-10 w-10 rounded-full bg-primary/30 flex items-center justify-center">
                  <div className="h-6 w-6 rounded-full bg-primary/60 animate-ping"></div>
                </div>
                <p className="text-muted-foreground font-medium">Loading chart data...</p>
                <div className="w-48 h-1.5 bg-primary/20 rounded-full overflow-hidden">
                  <div className="h-full bg-primary/60 rounded-full animate-[loading_1.5s_ease-in-out_infinite]" style={{ width: '30%' }}></div>
                </div>
              </div>
            </div>
          ) : data.length === 0 ? (
            <div className="flex h-full items-center justify-center">
              <div className="text-center p-6 rounded-lg border border-dashed border-primary/20 bg-background/50">
                <svg className="w-12 h-12 mx-auto mb-3 text-primary/40" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <p className="text-muted-foreground font-medium mb-1">No category data available</p>
                <p className="text-muted-foreground text-sm">Try adjusting your filters or date range</p>
              </div>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <defs>
                  {renderData.map((entry, index) => (
                    <filter key={`shadow-${index}`} id={`shadow-${index}`} x="-20%" y="-20%" width="140%" height="140%">
                      <feDropShadow dx="0" dy="0" stdDeviation="6" floodColor={entry.color} floodOpacity="0.3" />
                    </filter>
                  ))}
                </defs>
                <Pie
                  data={renderData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={renderCustomizedLabel}
                  innerRadius={70}
                  outerRadius="75%"
                  fill="#8884d8"
                  dataKey="value"
                  animationDuration={1800}
                  animationBegin={300}
                  animationEasing="ease-in-out"
                  onMouseEnter={onPieEnter}
                  onMouseLeave={onPieLeave}
                  paddingAngle={3}
                  cornerRadius={4}
                >
                  {renderData.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={entry.color}
                      filter={activeIndex === index ? `url(#shadow-${index})` : undefined}
                      stroke={activeIndex === index ? "white" : entry.color}
                      strokeWidth={activeIndex === index ? 4 : 2}
                      strokeOpacity={activeIndex === index ? 0.9 : 0.7}
                      className="transition-all duration-300 ease-in-out"
                      style={{
                        transform: activeIndex === index ? "scale(1.08)" : "scale(1)",
                        transformOrigin: "center",
                        opacity: activeIndex === undefined || activeIndex === index ? 1 : 0.7,
                        cursor: "pointer",
                      }}
                    />
                  ))}
                </Pie>
                <ChartTooltip
                  content={({ active, payload }) => {
                    if (active && payload && payload.length) {
                      const data = payload[0].payload
                      return (
                        <div className="rounded-lg border bg-card/95 backdrop-blur-sm p-4 shadow-lg animate-in fade-in zoom-in-95 duration-200">
                          <div className="flex items-center gap-2 mb-2 pb-2 border-b">
                            <div
                              className="h-4 w-4 rounded-full"
                              style={{ backgroundColor: data.color }}
                            />
                            <span className="font-bold text-foreground text-lg">{data.name}</span>
                          </div>
                          <div className="grid grid-cols-2 gap-3">
                            <div className="flex flex-col">
                              <span className="text-xs uppercase text-muted-foreground font-medium">Amount</span>
                              <span className="font-bold text-foreground">{formatCurrency(data.value)}</span>
                            </div>
                            <div className="flex flex-col">
                              <span className="text-xs uppercase text-muted-foreground font-medium">Percentage</span>
                              <span className="font-bold text-foreground">{data.percentage}%</span>
                            </div>
                          </div>
                        </div>
                      )
                    }
                    return null
                  }}
                  wrapperStyle={{ outline: 'none' }}
                />
              </PieChart>
            </ResponsiveContainer>
          )}
          {!loading && data.length > 0 && (
            <div className="mt-6">
              <ChartLegend
                verticalAlign="bottom"
                content={({ payload }) => {
                  if (payload && payload.length) {
                    return (
                      <div className="flex flex-wrap justify-center gap-3 pt-2">
                        {payload.map((entry, index) => (
                          <div
                            key={`item-${index}`}
                            className={`flex items-center gap-2 px-4 py-2 rounded-full bg-background/70 backdrop-blur-sm border border-primary/10 shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer ${activeIndex === index ? 'ring-2 ring-primary/30 scale-105' : ''}`}
                            onMouseEnter={() => setActiveIndex(index)}
                            onMouseLeave={() => setActiveIndex(undefined)}
                            style={{
                              borderColor: activeIndex === index ? entry.color : 'hsl(var(--primary) / 0.1)',
                            }}
                          >
                            <div
                              className="h-3.5 w-3.5 rounded-full shadow-inner"
                              style={{
                                backgroundColor: entry.color,
                                boxShadow: `0 0 0 2px ${entry.color}20`
                              }}
                            />
                            <span className="text-sm font-medium">{entry.value}</span>
                          </div>
                        ))}
                      </div>
                    )
                  }
                  return null
                }}
              />
            </div>
          )}
        </div>
      </ChartContainer>
    </div>
  )
}