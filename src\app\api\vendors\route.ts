import { NextRequest, NextResponse } from "next/server";
import { auth, currentUser } from "@clerk/nextjs/server";
import db from "@/db/db";

// GET all vendors
export async function GET() {
  try {
    const { userId: clerkUserId } = await auth();
    
    if (!clerkUserId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the user from database
    const dbUser = await db.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    if (!dbUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get vendors for the current user
    const vendors = await db.vendor.findMany({
      where: { userId: dbUser.id },
      orderBy: { name: 'asc' },
    });

    return NextResponse.json(vendors);
  } catch {
    return NextResponse.json(
      { error: "Failed to fetch vendors" },
      { status: 500 }
    );
  }
}

// POST create a new vendor
export async function POST(req: NextRequest) {
  try {
    const { userId: clerkUserId } = await auth();
    
    if (!clerkUserId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the user from database using clerkId
    const dbUser = await db.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    // Determine the database user ID
    let databaseUserId: string;

    // If user doesn't exist in the database, create it
    if (!dbUser) {
      const clerkUser = await currentUser();
      if (!clerkUser) {
        return NextResponse.json({ error: "User authentication failed" }, { status: 401 });
      }

      // Create the user in our database
      const newUser = await db.user.create({
        data: {
          clerkId: clerkUserId,
          email: clerkUser.emailAddresses[0]?.emailAddress || "",
          firstName: clerkUser.firstName,
          lastName: clerkUser.lastName,
          profileImageUrl: clerkUser.imageUrl,
          role: "USER",
        },
      });
      
      databaseUserId = newUser.id;
    } else {
      databaseUserId = dbUser.id;
    }

    const { name, email, phone, website, address, notes, logoUrl } = await req.json();

    if (!name) {
      return NextResponse.json(
        { error: "Name is required" },
        { status: 400 }
      );
    }

    const existingVendor = await db.vendor.findFirst({
      where: { name, userId: databaseUserId },
    });

    if (existingVendor) {
      return NextResponse.json(
        { error: "Vendor with this name already exists" },
        { status: 400 }
      );
    }

    const vendor = await db.vendor.create({
      data: {
        name,
        email,
        phone,
        website,
        address,
        notes,
        logoUrl,
        userId: databaseUserId,
      },
    });

    return NextResponse.json(vendor, { status: 201 });
  } catch {
    return NextResponse.json(
      { error: "Failed to create vendor" },
      { status: 500 }
    );
  }
} 