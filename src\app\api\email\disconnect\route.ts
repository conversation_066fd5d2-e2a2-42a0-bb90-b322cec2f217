import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { disconnectGmail } from "@/lib/services/gmail-service";

export async function POST(request: NextRequest) {
  try {
    // Get the authenticated user from Clerk
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the provider from query parameters
    const provider = request.nextUrl.searchParams.get("provider");
    if (!provider || !["gmail", "outlook"].includes(provider)) {
      return NextResponse.json({ error: "Invalid provider" }, { status: 400 });
    }

    if (provider === "gmail") {
      // Disconnect Gmail
      await disconnectGmail(userId);
      return NextResponse.json({ success: true });
    } else {
      // Outlook not implemented yet
      return NextResponse.json({ error: "Provider not supported yet" }, { status: 400 });
    }
  } catch (error) {
    console.error("Error disconnecting email:", error);
    return NextResponse.json(
      { error: "Failed to disconnect email account" },
      { status: 500 }
    );
  }
} 