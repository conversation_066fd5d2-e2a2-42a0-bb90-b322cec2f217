'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { FileIcon, DownloadIcon, MailIcon, CalendarIcon, LoaderIcon } from './icons';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'lucide-react';
import { toast } from 'sonner';
import { <PERSON><PERSON><PERSON> as BarChartComponent } from '@/components/ui/bar-chart';
import { <PERSON><PERSON><PERSON> as LineChartComponent } from '@/components/ui/line-chart';
import { <PERSON><PERSON><PERSON> as PieChartComponent } from '@/components/ui/pie-chart';
import { Separator } from '@/components/ui/separator';

interface ReportData {
  id: string;
  reportId: string;
  dataPoint: string;
  value: number;
  label: string;
  category?: string;
}

interface Report {
  id: string;
  title: string;
  description?: string;
  reportType: string;
  fileUrl: string;
  createdAt: string;
  startDate?: string;
  endDate?: string;
  data?: ReportData[];
}

interface ReportViewerProps {
  reportId: string;
  title: string;
  reportType: string;
  format: string;
  fileUrl?: string;
  onClose: () => void;
  onEmail?: () => void;
  onSchedule?: () => void;
}

// Helper function to get appropriate X-axis label based on report type
function getXAxisLabel(reportType: string): string {
  switch (reportType) {
    case 'CASH_FLOW':
    case 'PROFIT_LOSS':
    case 'SALES':
      return 'Time Period';
    case 'TAX':
      return 'Month';
    case 'BALANCE_SHEET':
      return 'Category';
    case 'VENDOR_ANALYSIS':
      return 'Vendor';
    case 'CATEGORY_ANALYSIS':
    case 'CATEGORY_BREAKDOWN':
      return 'Category';
    default:
      return 'Data Points';
  }
}

// Helper function to get appropriate Y-axis label based on report type
function getYAxisLabel(reportType: string): string {
  switch (reportType) {
    case 'CASH_FLOW':
      return 'Cash Flow Amount';
    case 'PROFIT_LOSS':
      return 'Profit/Loss Amount';
    case 'SALES':
      return 'Sales Amount';
    case 'TAX':
      return 'Tax Amount';
    case 'BALANCE_SHEET':
      return 'Balance Amount';
    case 'EXPENSES':
      return 'Expense Amount';
    default:
      return 'Value';
  }
}

// Helper function to determine if a reference line should be shown
function shouldShowReferenceLine(reportType: string): boolean {
  return ['CASH_FLOW', 'PROFIT_LOSS', 'SALES'].includes(reportType);
}

// Helper function to calculate reference line value (e.g., average)
function getReferenceLineValue(data: ReportData[]): number {
  // Filter data to get only relevant data points for averaging
  const relevantData = data.filter(item =>
    ['monthly_income', 'monthly_expense', 'monthly_profit', 'monthly_sales'].includes(item.dataPoint)
  );

  if (relevantData.length === 0) return 0;

  // Calculate average
  const sum = relevantData.reduce((acc, item) => acc + item.value, 0);
  return sum / relevantData.length;
}

// Helper function to get reference line label
function getReferenceLineLabel(reportType: string): string {
  switch (reportType) {
    case 'CASH_FLOW':
      return 'Average Cash Flow';
    case 'PROFIT_LOSS':
      return 'Average Profit/Loss';
    case 'SALES':
      return 'Average Sales';
    default:
      return 'Average';
  }
}

// Helper function to get total label for pie charts
function getTotalLabel(reportType: string): string {
  switch (reportType) {
    case 'BALANCE_SHEET':
      return 'Total Assets';
    case 'CATEGORY_BREAKDOWN':
      return 'Total';
    case 'EXPENSES':
      return 'Total Expenses';
    case 'SALES':
      return 'Total Sales';
    case 'TAX':
      return 'Total Tax';
    default:
      return 'Total';
  }
}

// Helper function to format data point names for display
function formatDataPoint(dataPoint: string): string {
  return dataPoint
    .replace(/_/g, ' ')
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Helper function to format values based on data point type
function formatValue(value: number, dataPoint?: string): string {
  // Format as percentage for data points that represent percentages
  if (dataPoint && (
    dataPoint.includes('percent') ||
    dataPoint.includes('margin') ||
    dataPoint.includes('ratio') ||
    dataPoint.includes('growth') ||
    dataPoint.includes('rate')
  )) {
    return `${value.toLocaleString(undefined, {
      minimumFractionDigits: 1,
      maximumFractionDigits: 2
    })}%`;
  }

  // Format as currency for financial values
  if (dataPoint && (
    dataPoint.includes('amount') ||
    dataPoint.includes('income') ||
    dataPoint.includes('expense') ||
    dataPoint.includes('revenue') ||
    dataPoint.includes('cost') ||
    dataPoint.includes('profit') ||
    dataPoint.includes('loss') ||
    dataPoint.includes('assets') ||
    dataPoint.includes('liabilities') ||
    dataPoint.includes('equity') ||
    dataPoint.includes('sales') ||
    dataPoint.includes('tax') ||
    dataPoint.includes('cashflow')
  )) {
    return value.toLocaleString(undefined, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  }

  // Default formatting for other values
  return value.toLocaleString();
}

export function ReportViewer({
  reportId,
  title,
  reportType,
  format,
  fileUrl,
  onClose,
  onEmail,
  onSchedule,
}: ReportViewerProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [report, setReport] = useState<Report | null>(null);
  const [visualizationType, setVisualizationType] = useState<string>('bar');

  // Helper function to determine the visualization type based on the report
  function getVisualizationType(reportType: string): string {
    // Default visualization based on report type
    switch (reportType) {
      case 'CASH_FLOW':
      case 'PROFIT_LOSS':
        return 'line';
      case 'BALANCE_SHEET':
      case 'CATEGORY_BREAKDOWN':
        return 'pie';
      default:
        return 'bar';
    }
  }

  // Helper function to prepare data for bar and line charts
  function prepareChartData(data: ReportData[]) {
    // Group data by label
    const groupedData: Record<string, Record<string, number>> = {};

    // Get unique data points
    const dataPoints = new Set<string>();

    // Process data
    data.forEach(item => {
      if (!groupedData[item.label]) {
        groupedData[item.label] = {};
      }

      groupedData[item.label][item.dataPoint] = item.value;
      dataPoints.add(item.dataPoint);
    });

    // Convert to chart format
    return Object.entries(groupedData).map(([label, values]) => {
      const result: Record<string, string | number> = { name: label };

      // Add all data points (even if zero)
      dataPoints.forEach(dataPoint => {
        result[dataPoint] = values[dataPoint] || 0;
      });

      // For simple charts, add a generic value
      if (dataPoints.size === 1) {
        const dataPoint = Array.from(dataPoints)[0];
        result.value = values[dataPoint] || 0;
      } else {
        // For multi-series charts, ensure we have a value field
        result.value = Object.values(values).reduce((sum, val) => sum + val, 0);
      }

      return result;
    });
  }

  // Helper function to prepare data for pie charts
  function preparePieData(data: ReportData[]) {
    // For pie charts, we need to simplify the data
    // We'll use the most relevant data points based on report type

    // Filter to the most relevant data points
    const relevantData = data.filter(item =>
      ['total_income', 'total_expense', 'total_profit', 'assets', 'liabilities',
       'equity', 'total_sales', 'total_tax_amount', 'category_sales', 'category_profit',
       'asset_category', 'liability_category', 'total_category_income', 'total_category_expense']
      .includes(item.dataPoint)
    );

    // If we have category breakdowns, use those
    const categoryData = relevantData.filter(item => item.category);

    if (categoryData.length > 0) {
      return categoryData.map(item => ({
        name: item.category || item.label,
        value: item.value
      }));
    }

    // Otherwise use the main data points
    return relevantData.map(item => ({
      name: formatDataPoint(item.dataPoint),
      value: item.value
    }));
  }

  // Helper function to get summary items for the details tab
  interface SummaryItem {
    label: string;
    value: string;
  }

  function getSummaryItems(data: ReportData[], reportType: string): SummaryItem[] {
    const summaryItems: SummaryItem[] = [];

    // Find key metrics based on report type
    switch (reportType) {
      case 'BALANCE_SHEET': {
        const assets = data.find(item => item.dataPoint === 'assets');
        const liabilities = data.find(item => item.dataPoint === 'liabilities');
        const equity = data.find(item => item.dataPoint === 'equity');
        const assetRatio = data.find(item => item.dataPoint === 'asset_ratio');

        if (assets) summaryItems.push({ label: 'Total Assets', value: formatValue(assets.value, 'assets') });
        if (liabilities) summaryItems.push({ label: 'Total Liabilities', value: formatValue(liabilities.value, 'liabilities') });
        if (equity) summaryItems.push({ label: 'Total Equity', value: formatValue(equity.value, 'equity') });
        if (assetRatio) summaryItems.push({ label: 'Asset to Liability Ratio', value: formatValue(assetRatio.value, 'ratio') });
        break;
      }

      case 'PROFIT_LOSS': {
        const totalIncome = data.find(item => item.dataPoint === 'total_income');
        const totalExpense = data.find(item => item.dataPoint === 'total_expense');
        const totalProfit = data.find(item => item.dataPoint === 'total_profit');
        const profitMargin = data.find(item => item.dataPoint === 'total_profit_margin');

        if (totalIncome) summaryItems.push({ label: 'Total Income', value: formatValue(totalIncome.value, 'income') });
        if (totalExpense) summaryItems.push({ label: 'Total Expenses', value: formatValue(totalExpense.value, 'expense') });
        if (totalProfit) summaryItems.push({ label: 'Net Profit/Loss', value: formatValue(totalProfit.value, 'profit') });
        if (profitMargin) summaryItems.push({ label: 'Profit Margin', value: formatValue(profitMargin.value, 'margin') });
        break;
      }

      case 'CASH_FLOW': {
        const totalCashFlow = data.find(item => item.dataPoint === 'total_cashflow');
        const avgMonthlyCashFlow = data.find(item => item.dataPoint === 'average_monthly_cashflow');
        const cashFlowTrend = data.find(item => item.dataPoint === 'cashflow_trend');

        if (totalCashFlow) summaryItems.push({ label: 'Total Cash Flow', value: formatValue(totalCashFlow.value, 'cashflow') });
        if (avgMonthlyCashFlow) summaryItems.push({ label: 'Avg. Monthly Cash Flow', value: formatValue(avgMonthlyCashFlow.value, 'cashflow') });
        if (cashFlowTrend) summaryItems.push({ label: 'Cash Flow Trend', value: formatValue(cashFlowTrend.value, 'percent') });
        break;
      }

      case 'TAX': {
        const totalTaxableAmount = data.find(item => item.dataPoint === 'total_taxable_amount');
        const totalTaxAmount = data.find(item => item.dataPoint === 'total_tax_amount');
        const effectiveTaxRate = data.find(item => item.dataPoint === 'effective_tax_rate');

        if (totalTaxableAmount) summaryItems.push({ label: 'Taxable Amount', value: formatValue(totalTaxableAmount.value, 'amount') });
        if (totalTaxAmount) summaryItems.push({ label: 'Tax Amount', value: formatValue(totalTaxAmount.value, 'tax') });
        if (effectiveTaxRate) summaryItems.push({ label: 'Effective Tax Rate', value: formatValue(effectiveTaxRate.value, 'rate') });
        break;
      }

      case 'SALES': {
        const totalSales = data.find(item => item.dataPoint === 'total_sales');
        const averageSale = data.find(item => item.dataPoint === 'average_sale');
        const salesGrowth = data.find(item => item.dataPoint === 'sales_growth');

        if (totalSales) summaryItems.push({ label: 'Total Sales', value: formatValue(totalSales.value, 'sales') });
        if (averageSale) summaryItems.push({ label: 'Average Sale', value: formatValue(averageSale.value, 'sales') });
        if (salesGrowth) summaryItems.push({ label: 'Sales Growth', value: formatValue(salesGrowth.value, 'growth') });
        break;
      }

      default: {
        // For other report types, find general summary data
        const totalAmount = data.find(item => item.dataPoint === 'total_amount');
        const averageAmount = data.find(item => item.dataPoint === 'average_amount');
        const count = data.find(item => item.dataPoint === 'count');

        if (totalAmount) summaryItems.push({ label: 'Total Amount', value: formatValue(totalAmount.value, 'amount') });
        if (averageAmount) summaryItems.push({ label: 'Average Amount', value: formatValue(averageAmount.value, 'amount') });
        if (count) summaryItems.push({ label: 'Count', value: formatValue(count.value) });
        break;
      }
    }

    // If we couldn't find specific summary items, use the first few data points
    if (summaryItems.length === 0) {
      data.slice(0, 4).forEach(item => {
        summaryItems.push({
          label: formatDataPoint(item.dataPoint),
          value: formatValue(item.value, item.dataPoint)
        });
      });
    }

    return summaryItems;
  }

  useEffect(() => {
    // Set initial visualization type based on report type
    setVisualizationType(getVisualizationType(reportType));

    // Fetch report data
    const fetchReportData = async () => {
      try {
        setIsLoading(true);

        // Use a different endpoint to get the report data as JSON
        const response = await fetch(`/api/reports/${reportId}/data`);

        if (!response.ok) {
          // If the data endpoint fails, we'll still show the PDF preview
          setIsLoading(false);
          return;
        }

        const contentType = response.headers.get('content-type');

        // Check if the response is JSON
        if (contentType && contentType.includes('application/json')) {
          const reportData = await response.json();
          setReport(reportData);
        }

        setIsLoading(false);
      } catch {
        setIsLoading(false);
      }
    };

    fetchReportData();
  }, [reportId, reportType]);

  const handleDownload = () => {
    if (fileUrl) {
      window.open(fileUrl, '_blank');
    } else {
      toast.error('Report file is not available for download');
    }
  };

  const handleEmail = () => {
    if (onEmail) {
      onEmail();
      toast.success('Preparing to email report...');
    }
  };

  const handleSchedule = () => {
    if (onSchedule) {
      onSchedule();
      toast.success('Preparing to schedule report...');
    }
  };

  return (
    <div className="flex flex-col h-full">
      <Card className="flex flex-col h-full border-0 rounded-none">
        <CardHeader className="pb-4">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-xl">{title}</CardTitle>
              <CardDescription>
                {reportType} Report • {format}
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownload}
                disabled={!fileUrl || isLoading}
              >
                <DownloadIcon className="h-4 w-4 mr-2" />
                Download
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleEmail}
                disabled={!fileUrl || isLoading}
              >
                <MailIcon className="h-4 w-4 mr-2" />
                Email
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSchedule}
                disabled={isLoading}
              >
                <CalendarIcon className="h-4 w-4 mr-2" />
                Schedule
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="flex-grow overflow-auto p-0">
          <Tabs defaultValue="preview" className="h-full">
            <TabsList className="grid w-full grid-cols-2 px-6">
              <TabsTrigger value="preview">Preview</TabsTrigger>
              <TabsTrigger value="details">Details</TabsTrigger>
            </TabsList>
            <TabsContent value="preview" className="h-[calc(100%-40px)] p-6">
              {isLoading ? (
                <div className="flex flex-col items-center justify-center h-full">
                  <LoaderIcon className="h-8 w-8 animate-spin text-primary mb-4" />
                  <p className="text-muted-foreground">Loading report preview...</p>
                </div>
              ) : error ? (
                <div className="flex flex-col items-center justify-center h-full">
                  <p className="text-destructive mb-2">Error loading report preview</p>
                  <p className="text-muted-foreground text-sm">{error}</p>
                </div>
              ) : report?.data && report.data.length > 0 ? (
                <div className="h-full flex flex-col">
                  <Tabs defaultValue={visualizationType} className="w-full h-full">
                    <TabsList className="tabs-container grid w-full grid-cols-3 rounded-lg p-1">
                      <TabsTrigger
                        value="bar"
                        onClick={() => setVisualizationType('bar')}
                        className="tab-trigger flex items-center gap-2 rounded-md"
                      >
                        <BarChart className="h-4 w-4" />
                        Bar Chart
                      </TabsTrigger>
                      <TabsTrigger
                        value="line"
                        onClick={() => setVisualizationType('line')}
                        className="tab-trigger flex items-center gap-2 rounded-md"
                      >
                        <LineChart className="h-4 w-4" />
                        Line Chart
                      </TabsTrigger>
                      <TabsTrigger
                        value="pie"
                        onClick={() => setVisualizationType('pie')}
                        className="tab-trigger flex items-center gap-2 rounded-md"
                      >
                        <PieChart className="h-4 w-4" />
                        Pie Chart
                      </TabsTrigger>
                    </TabsList>

                    <div className="h-[350px] w-full mt-6">
                      <TabsContent value="bar" className="h-full">
                        <BarChartComponent
                          data={prepareChartData(report.data)}
                          x="name"
                          y="value"
                          xLabel={getXAxisLabel(reportType)}
                          yLabel={getYAxisLabel(reportType)}
                          showLegend={true}
                          gradientBars={true}
                          showValues={true}
                          animationDuration={1200}
                          roundedBars={true}
                        />
                      </TabsContent>

                      <TabsContent value="line" className="h-full">
                        <LineChartComponent
                          data={prepareChartData(report.data)}
                          x="name"
                          y="value"
                          xLabel={getXAxisLabel(reportType)}
                          yLabel={getYAxisLabel(reportType)}
                          showLegend={true}
                          showDots={true}
                          fillArea={true}
                          gradientFill={true}
                          animationDuration={1500}
                          curveType="monotone"
                          strokeWidth={3}
                          showReferenceLine={shouldShowReferenceLine(reportType)}
                          referenceLineValue={getReferenceLineValue(report.data)}
                          referenceLineLabel={getReferenceLineLabel(reportType)}
                        />
                      </TabsContent>

                      <TabsContent value="pie" className="h-full">
                        <PieChartComponent
                          data={preparePieData(report.data)}
                          nameKey="name"
                          valueKey="value"
                          donut={true}
                          showLegend={true}
                          paddingAngle={3}
                          gradientColors={true}
                          showActiveShape={true}
                          showTotal={true}
                          totalLabel={getTotalLabel(reportType)}
                          animationDuration={1200}
                          outerRadius={130}
                        />
                      </TabsContent>
                    </div>

                    <Separator className="my-6" />

                    <div className="overflow-x-auto rounded-lg border shadow-sm">
                      <table className="w-full table-auto">
                        <thead>
                          <tr className="bg-muted/50">
                            <th className="px-4 py-3 text-left font-medium text-muted-foreground">Label</th>
                            <th className="px-4 py-3 text-left font-medium text-muted-foreground">Category</th>
                            <th className="px-4 py-3 text-left font-medium text-muted-foreground">Data Point</th>
                            <th className="px-4 py-3 text-right font-medium text-muted-foreground">Value</th>
                          </tr>
                        </thead>
                        <tbody>
                          {report.data.slice(0, 10).map((item: ReportData, index: number) => (
                            <tr
                              key={index}
                              className={`border-b transition-colors hover:bg-muted/20 ${index % 2 === 0 ? 'bg-background' : 'bg-muted/10'}`}
                            >
                              <td className="px-4 py-3 text-sm">{item.label || '-'}</td>
                              <td className="px-4 py-3 text-sm">
                                <span className="inline-flex items-center rounded-full bg-primary/10 px-2.5 py-0.5 text-xs font-medium text-primary">
                                  {item.category || 'Uncategorized'}
                                </span>
                              </td>
                              <td className="px-4 py-3 text-sm font-medium">{formatDataPoint(item.dataPoint)}</td>
                              <td className="px-4 py-3 text-right text-sm font-semibold">
                                {formatValue(item.value, item.dataPoint)}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </Tabs>
                </div>
              ) : fileUrl ? (
                <div className="h-full flex flex-col items-center">
                  {format === 'PDF' ? (
                    <div className="w-full h-full flex flex-col">
                      <div className="bg-muted/20 p-3 mb-3 rounded-lg border flex items-center justify-between">
                        <div className="flex items-center">
                          <FileIcon className="h-5 w-5 text-red-500 mr-2" />
                          <span className="text-sm font-medium">PDF Preview</span>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleDownload}
                        >
                          <DownloadIcon className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                      </div>
                      <iframe
                        src={`${fileUrl}#toolbar=0&navpanes=0`}
                        className="w-full flex-grow border rounded-md"
                        title={title}
                        onError={() => setError('Failed to load PDF preview')}
                      /> 
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-full">
                      <FileIcon className="h-16 w-16 text-green-500 mb-4" />
                      <p className="text-center">
                        Excel preview is not available.
                        <br />
                        Please download the file to view it.
                      </p>
                      <Button
                        variant="default"
                        size="sm"
                        className="mt-4"
                        onClick={handleDownload}
                      >
                        <DownloadIcon className="h-4 w-4 mr-2" />
                        Download Excel
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-full">
                  <FileIcon className="h-16 w-16 text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">No preview available</p>
                </div>
              )}
            </TabsContent>
            <TabsContent value="details" className="h-[calc(100%-40px)] p-6 overflow-y-auto">
              <div className="space-y-6">
                <div className="bg-muted/20 p-4 rounded-lg border">
                  <h3 className="text-lg font-medium mb-3">Report Information</h3>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="text-sm text-muted-foreground">Report ID</div>
                    <div className="text-sm font-mono">{reportId}</div>
                    <div className="text-sm text-muted-foreground">Title</div>
                    <div className="text-sm font-medium">{title}</div>
                    <div className="text-sm text-muted-foreground">Type</div>
                    <div className="text-sm">
                      <span className="inline-flex items-center rounded-full bg-primary/10 px-2.5 py-0.5 text-xs font-medium text-primary">
                        {reportType}
                      </span>
                    </div>
                    <div className="text-sm text-muted-foreground">Format</div>
                    <div className="text-sm">
                      <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-100">
                        {format}
                      </span>
                    </div>
                    <div className="text-sm text-muted-foreground">Created</div>
                    <div className="text-sm">{new Date().toLocaleString()}</div>
                    {report?.startDate && (
                      <>
                        <div className="text-sm text-muted-foreground">Start Date</div>
                        <div className="text-sm">{new Date(report.startDate).toLocaleDateString()}</div>
                      </>
                    )}
                    {report?.endDate && (
                      <>
                        <div className="text-sm text-muted-foreground">End Date</div>
                        <div className="text-sm">{new Date(report.endDate).toLocaleDateString()}</div>
                      </>
                    )}
                  </div>
                </div>

                {report?.description && (
                  <div className="bg-muted/20 p-4 rounded-lg border">
                    <h3 className="text-lg font-medium mb-2">Description</h3>
                    <p className="text-sm">{report.description}</p>
                  </div>
                )}

                {report?.data && report.data.length > 0 && (
                  <div className="bg-muted/20 p-4 rounded-lg border">
                    <h3 className="text-lg font-medium mb-2">Report Summary</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {getSummaryItems(report.data, reportType).map((item, index) => (
                        <div key={index} className="bg-background p-3 rounded-md border shadow-sm">
                          <div className="text-xs text-muted-foreground uppercase tracking-wide">{item.label}</div>
                          <div className="text-lg font-semibold mt-1">{item.value}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="bg-muted/20 p-4 rounded-lg border">
                  <h3 className="text-lg font-medium mb-3">Actions</h3>
                  <div className="flex flex-col gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="justify-start hover:bg-primary/10"
                      onClick={handleDownload}
                      disabled={!fileUrl}
                    >
                      <DownloadIcon className="h-4 w-4 mr-2" />
                      Download Report
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="justify-start hover:bg-blue-50 dark:hover:bg-blue-950"
                      onClick={handleEmail}
                      disabled={!fileUrl}
                    >
                      <MailIcon className="h-4 w-4 mr-2" />
                      Email Report
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="justify-start hover:bg-amber-50 dark:hover:bg-amber-950"
                      onClick={handleSchedule}
                    >
                      <CalendarIcon className="h-4 w-4 mr-2" />
                      Schedule Regular Delivery
                    </Button>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="pt-4 pb-6 flex justify-end">
          <Button variant="ghost" onClick={onClose}>
            Close
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
