'use client';

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

interface UserProfileMetricsProps {
  userSpecific?: {
    profileCompleteness: number;
    conversationQuality: number;
    businessInsights: number;
    recommendations: string[];
  };
}

export function UserProfileMetrics({ userSpecific }: UserProfileMetricsProps) {
  if (!userSpecific) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <p className="text-gray-600">User profile data not available</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Your AI Profile</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <div className="flex justify-between mb-2">
            <span>Profile Completeness</span>
            <span>{(userSpecific.profileCompleteness * 100).toFixed(1)}%</span>
          </div>
          <Progress value={userSpecific.profileCompleteness * 100} />
        </div>
        <div>
          <div className="flex justify-between mb-2">
            <span>Conversation Quality</span>
            <span>{(userSpecific.conversationQuality * 100).toFixed(1)}%</span>
          </div>
          <Progress value={userSpecific.conversationQuality * 100} />
        </div>
        <div>
          <div className="flex justify-between mb-2">
            <span>Business Insights</span>
            <span>{(userSpecific.businessInsights * 100).toFixed(1)}%</span>
          </div>
          <Progress value={userSpecific.businessInsights * 100} />
        </div>
        {userSpecific.recommendations.length > 0 && (
          <div>
            <h4 className="font-semibold mb-2">Personalized Recommendations:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
              {userSpecific.recommendations.map((rec, index) => (
                <li key={index}>{rec}</li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
}