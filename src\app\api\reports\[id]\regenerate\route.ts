import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import db from "@/db/db";
import { generateReportData } from "@/lib/actions/report-data-generator";

export async function POST(request: NextRequest, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    const { userId } = await auth();
    const id = params.id;

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!id) {
      return NextResponse.json({ error: 'Missing report ID' }, { status: 400 });
    }

    // Find the report
    const report = await db.report.findUnique({
      where: {
        id,
        userId
      }
    });

    if (!report) {
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }

    // Delete existing report data
    await db.reportData.deleteMany({
      where: { reportId: id }
    });

    // Regenerate report data
    await generateReportData(
      userId,
      id,
      report.reportType,
      report.startDate,
      report.endDate,
      undefined // No currency filter
    );

    // Get the updated report with data
    const updatedReport = await db.report.findUnique({
      where: { id },
      include: { data: true }
    });

    return NextResponse.json({
      success: true,
      message: 'Report data regenerated successfully',
      dataCount: updatedReport?.data?.length || 0
    });
  } catch (error) {
    console.error('Error regenerating report data:', error);
    return NextResponse.json(
      { error: 'Failed to regenerate report data' },
      { status: 500 }
    );
  }
}
