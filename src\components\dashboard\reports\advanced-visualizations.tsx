"use client";

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tabs,
  TabsContent,
  Ta<PERSON>List,
  TabsTrigger,
} from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>hart,
  AreaChart,
  ComposedChart,
  ScatterChart,
  Bar,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Pie,
  Scatter,
  Area,
  Cell,
} from 'recharts';
import Colors from "../../theme/Colors";

// Sample data for demonstration
const sampleData = [
  { name: 'Jan', value: 4000, category: 'A', secondaryValue: 2400 },
  { name: 'Feb', value: 3000, category: 'B', secondaryValue: 1398 },
  { name: 'Mar', value: 2000, category: 'C', secondaryValue: 9800 },
  { name: 'Apr', value: 2780, category: 'D', secondaryValue: 3908 },
  { name: 'May', value: 1890, category: 'E', secondaryValue: 4800 },
  { name: 'Jun', value: 2390, category: 'F', secondaryValue: 3800 },
  { name: 'Jul', value: 3490, category: 'G', secondaryValue: 4300 },
];

const COLORS = Colors.chartColors;

interface AdvancedVisualizationsProps {
  data?: { name: string; value: number; category?: string; secondaryValue?: number }[];
  chartType?: string;
  className?: string;
}

export function AdvancedVisualizations({
  data = sampleData,
  chartType = 'bar',
  className,
}: AdvancedVisualizationsProps) {
  const [selectedChartType, setSelectedChartType] = useState(chartType);
  const [showLegend, setShowLegend] = useState(true);
  const [showGrid, setShowGrid] = useState(true);
  const [showTooltip, setShowTooltip] = useState(true);
  const [colorScheme, setColorScheme] = useState<string[]>(COLORS);
  const [stackedView, setStackedView] = useState(false);
  const [dataKeys, setDataKeys] = useState<string[]>(['value']);
  const [chartHeight, setChartHeight] = useState(400);

  // Function to render the selected chart type
  const renderChart = () => {
    switch (selectedChartType) {
      case 'bar':
        return (
          <ResponsiveContainer width="100%" height={chartHeight}>
            <BarChart data={data}>
              {showGrid && <CartesianGrid strokeDasharray="3 3" />}
              <XAxis dataKey="name" />
              <YAxis />
              {showTooltip && <Tooltip />}
              {showLegend && <Legend />}
              {dataKeys.map((key, index) => (
                <Bar 
                  key={key}
                  dataKey={key} 
                  fill={colorScheme[index % colorScheme.length]} 
                  stackId={stackedView ? "a" : undefined}
                />
              ))}
            </BarChart>
          </ResponsiveContainer>
        );
        
      case 'line':
        return (
          <ResponsiveContainer width="100%" height={chartHeight}>
            <LineChart data={data}>
              {showGrid && <CartesianGrid strokeDasharray="3 3" />}
              <XAxis dataKey="name" />
              <YAxis />
              {showTooltip && <Tooltip />}
              {showLegend && <Legend />}
              {dataKeys.map((key, index) => (
                <Line 
                  key={key}
                  type="monotone" 
                  dataKey={key} 
                  stroke={colorScheme[index % colorScheme.length]} 
                  activeDot={{ r: 8 }}
                />
              ))}
            </LineChart>
          </ResponsiveContainer>
        );
        
      case 'area':
        return (
          <ResponsiveContainer width="100%" height={chartHeight}>
            <AreaChart data={data}>
              {showGrid && <CartesianGrid strokeDasharray="3 3" />}
              <XAxis dataKey="name" />
              <YAxis />
              {showTooltip && <Tooltip />}
              {showLegend && <Legend />}
              {dataKeys.map((key, index) => (
                <Area 
                  key={key}
                  type="monotone" 
                  dataKey={key} 
                  stackId={stackedView ? "a" : undefined}
                  fill={colorScheme[index % colorScheme.length]} 
                  stroke={colorScheme[index % colorScheme.length]}
                />
              ))}
            </AreaChart>
          </ResponsiveContainer>
        );
        
      case 'pie':
        return (
          <ResponsiveContainer width="100%" height={chartHeight}>
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={true}
                outerRadius={chartHeight / 3}
                fill={colorScheme[0]}
                dataKey={dataKeys[0]}
                nameKey="name"
                label={({name, percent}) => `${name}: ${(percent * 100).toFixed(0)}%`}
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={colorScheme[index % colorScheme.length]} />
                ))}
              </Pie>
              {showTooltip && <Tooltip />}
              {showLegend && <Legend />}
            </PieChart>
          </ResponsiveContainer>
        );
        
      case 'scatter':
        return (
          <ResponsiveContainer width="100%" height={chartHeight}>
            <ScatterChart>
              {showGrid && <CartesianGrid strokeDasharray="3 3" />}
              <XAxis type="number" dataKey={dataKeys[0]} name={dataKeys[0]} />
              <YAxis type="number" dataKey={dataKeys.length > 1 ? dataKeys[1] : 'secondaryValue'} name={dataKeys.length > 1 ? dataKeys[1] : 'secondaryValue'} />
              {showTooltip && <Tooltip cursor={{ strokeDasharray: '3 3' }} />}
              {showLegend && <Legend />}
              <Scatter 
                name="Data Points" 
                data={data} 
                fill={colorScheme[0]}
              />
            </ScatterChart>
          </ResponsiveContainer>
        );
        
      case 'composed':
        return (
          <ResponsiveContainer width="100%" height={chartHeight}>
            <ComposedChart data={data}>
              {showGrid && <CartesianGrid strokeDasharray="3 3" />}
              <XAxis dataKey="name" />
              <YAxis />
              {showTooltip && <Tooltip />}
              {showLegend && <Legend />}
              <Bar dataKey={dataKeys[0]} fill={colorScheme[0]} />
              {dataKeys.length > 1 && (
                <Line 
                  type="monotone" 
                  dataKey={dataKeys[1]} 
                  stroke={colorScheme[1]}
                />
              )}
              {dataKeys.length > 2 && (
                <Area 
                  type="monotone" 
                  dataKey={dataKeys[2]} 
                  fill={colorScheme[2]}
                  stroke={colorScheme[2]}
                />
              )}
            </ComposedChart>
          </ResponsiveContainer>
        );

      default:
        return (
          <ResponsiveContainer width="100%" height={chartHeight}>
            <BarChart data={data}>
              {showGrid && <CartesianGrid strokeDasharray="3 3" />}
              <XAxis dataKey="name" />
              <YAxis />
              {showTooltip && <Tooltip />}
              {showLegend && <Legend />}
              <Bar dataKey={dataKeys[0]} fill={colorScheme[0]} />
            </BarChart>
          </ResponsiveContainer>
        );
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Advanced Chart Visualization</CardTitle>
        <CardDescription>
          Customize your data visualization with advanced options
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="preview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="preview">Chart Preview</TabsTrigger>
            <TabsTrigger value="options">Customize</TabsTrigger>
          </TabsList>
          
          <TabsContent value="preview" className="space-y-4">
            {renderChart()}
          </TabsContent>
          
          <TabsContent value="options" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="chart-type">Chart Type</Label>
                  <Select 
                    value={selectedChartType} 
                    onValueChange={setSelectedChartType}
                  >
                    <SelectTrigger id="chart-type">
                      <SelectValue placeholder="Select chart type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="bar">Bar Chart</SelectItem>
                      <SelectItem value="line">Line Chart</SelectItem>
                      <SelectItem value="area">Area Chart</SelectItem>
                      <SelectItem value="pie">Pie Chart</SelectItem>
                      <SelectItem value="scatter">Scatter Plot</SelectItem>
                      <SelectItem value="composed">Composed Chart</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="data-keys">Data Series</Label>
                  <Select 
                    value={dataKeys.join(',')}
                    onValueChange={(val) => setDataKeys(val.split(','))}
                  >
                    <SelectTrigger id="data-keys">
                      <SelectValue placeholder="Select data series" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="value">Value</SelectItem>
                      <SelectItem value="value,secondaryValue">Value & Secondary</SelectItem>
                      <SelectItem value="secondaryValue">Secondary Value</SelectItem>
                      <SelectItem value="value,secondaryValue,category">All Values</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="color-scheme">Color Scheme</Label>
                  <Select 
                    value={colorScheme.join(',')}
                    onValueChange={(val) => setColorScheme(val.split(','))}
                  >
                    <SelectTrigger id="color-scheme">
                      <SelectValue placeholder="Select color scheme" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={COLORS.slice(0,7).join(",")}>Default</SelectItem>
                      <SelectItem value={COLORS.slice(7,15).join(",")}>Vibrant</SelectItem>
                      <SelectItem value={COLORS.slice(15,22).join(",")}>Tableau</SelectItem>
                      <SelectItem value={COLORS.slice(22,27).join(",")}>Earth Tones</SelectItem>
                      <SelectItem value={COLORS.slice(27,35).join(",")}>D3 Colors</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="chart-height">Chart Height</Label>
                  <span className="text-sm text-muted-foreground">{chartHeight}px</span>
                </div>
                <Slider
                  id="chart-height"
                  min={200}
                  max={800}
                  step={50}
                  value={[chartHeight]}
                  onValueChange={(values) => setChartHeight(values[0])}
                />

                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="show-legend"
                      checked={showLegend}
                      onCheckedChange={setShowLegend}
                    />
                    <Label htmlFor="show-legend">Show Legend</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="show-grid"
                      checked={showGrid}
                      onCheckedChange={setShowGrid}
                    />
                    <Label htmlFor="show-grid">Show Grid</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="show-tooltip"
                      checked={showTooltip}
                      onCheckedChange={setShowTooltip}
                    />
                    <Label htmlFor="show-tooltip">Show Tooltip</Label>
                  </div>
                  
                  {(selectedChartType === 'bar' || selectedChartType === 'area') && (
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="stacked-view"
                        checked={stackedView}
                        onCheckedChange={setStackedView}
                      />
                      <Label htmlFor="stacked-view">Stacked View</Label>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={() => {
          setShowLegend(true);
          setShowGrid(true);
          setShowTooltip(true);
          setStackedView(false);
          setChartHeight(400);
          setColorScheme(COLORS);
        }}>
          Reset Defaults
        </Button>
        <Button>Apply to Report</Button>
      </CardFooter>
    </Card>
  );
} 