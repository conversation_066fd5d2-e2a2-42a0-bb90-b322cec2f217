import { NextRequest, NextResponse } from 'next/server';
import {
  verifyPaddleWebhook,
  processPaddleWebhook,
} from '@/actions/paddle-actions';

export async function POST(req: NextRequest) {
  // Get the raw request body and signature
  const rawBody = await req.text();
  const signature = req.headers.get('paddle-signature');

  // Log webhook request details without sensitive data
  let eventType = 'Unknown';
  let eventId = 'Unknown';
  try {
    const bodyPreview = JSON.parse(rawBody);
    eventType = bodyPreview.event_type || 'Unknown event';
    eventId = bodyPreview.event_id || 'Not provided';
  } catch {
    // No need to log parse errors in production
  }

  // SECURITY: Verify the signature if webhook secret is configured
  if (signature) {
    const isValid = verifyPaddleWebhook(rawBody, signature);
    if (!isValid) {
      return NextResponse.json(
        { success: false, message: 'Invalid webhook signature' },
        { status: 401 }
      );
    }
  }

  // Parse the webhook payload
  let payload;
  try {
    payload = JSON.parse(rawBody);
  } catch {
    return NextResponse.json(
      { success: false, message: 'Invalid JSON payload' },
      { status: 400 }
    );
  }

  if (!payload.event_type) {
    return NextResponse.json(
      {
        success: false,
        message: 'Invalid payload: missing event_type',
      },
      { status: 400 }
    );
  }

  // Process the webhook event
  try {
    const result = await processPaddleWebhook(payload);

    if (result.success) {
      // No need to log success in production
    } else {
      // Don't return error status to prevent retries for application logic errors
    }
  } catch {
    // Don't return error status to prevent retries for application logic errors
  }

  // Always acknowledge receipt of the webhook with a 200 OK response
  return NextResponse.json(
    {
      success: true,
      message: 'Paddle webhook received and acknowledged',
      eventId: eventId,
      eventType: eventType,
    },
    { status: 200 }
  );
}

// Add OPTIONS method handler for CORS support
export async function OPTIONS() {
  return NextResponse.json(
    {},
    {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers':
          'Content-Type, paddle-signature',
        'Access-Control-Max-Age': '86400',
      },
    }
  );
}
