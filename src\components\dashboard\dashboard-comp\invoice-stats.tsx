import { memo } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>le,
} from '@/components/ui/card';
import {
  FileText,
  DollarSign,
  AlertCircle,
  TrendingUp,
} from 'lucide-react';
import { useInvoiceStats } from '../providers/dashboard-data-provider';

interface InvoiceStatsData {
  totalCount: number;
  totalAmount: number;
  paidCount: number;
  paidAmount: number;
  pendingCount: number;
  pendingAmount: number;
  overdueCount: number;
  overdueAmount: number;
  processingEfficiency: number;
  efficiencyChange: number;
  pendingInvoices: number;
}

// Smart number formatting function
function formatLargeNumber(value: number): {
  formatted: string;
  suffix: string;
} {
  if (value >= **********) {
    return {
      formatted: (value / **********).toFixed(1),
      suffix: 'B',
    };
  } else if (value >= 1000000) {
    return {
      formatted: (value / 1000000).toFixed(1),
      suffix: 'M',
    };
  } else if (value >= 1000) {
    return {
      formatted: (value / 1000).toFixed(1),
      suffix: 'K',
    };
  } else {
    return {
      formatted: value.toFixed(2),
      suffix: '',
    };
  }
}

export const InvoiceStats = memo(() => {
  const { stats, loading, error } = useInvoiceStats();

  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="rounded-[24px] animate-pulse">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="h-4 bg-muted rounded w-24"></div>
              <div className="h-4 w-4 bg-muted rounded"></div>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="h-8 bg-muted rounded w-32 mb-2"></div>
              <div className="h-3 bg-muted rounded w-20"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-red-500 py-8">{error}</div>
    );
  }

  if (!stats) return null;

  const totalRevenueFormatted = formatLargeNumber(
    stats.totalAmount ?? 0
  );
  const pendingAmountFormatted = formatLargeNumber(
    stats.pendingAmount ?? 0
  );

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card className="relative rounded-[24px] border-primary/30 dark:border-primary/20 bg-gradient-to-br from-white via-blue-50/30 to-primary/5 dark:from-gray-900 dark:via-blue-950/20 dark:to-primary/10 hover:shadow-xl hover:shadow-primary/10 dark:hover:shadow-primary/5 transition-all duration-500 hover:scale-105 overflow-hidden group">
        {/* Animated background glow */}
        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-primary to-transparent" />
        
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 border-b border-primary/10 dark:border-primary/5 relative z-10">
          <CardTitle className="text-sm font-semibold text-gray-600 dark:text-gray-300">
            Total Invoices
          </CardTitle>
          <div className="rounded-full p-3 bg-gradient-to-br from-primary/15 to-primary/25 dark:from-primary/10 dark:to-primary/20 shadow-lg">
            <FileText className="h-5 w-5 text-primary drop-shadow-sm" />
          </div>
        </CardHeader>
        <CardContent className="pt-4 relative z-10">
          <div className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-1">
            {stats.totalCount.toLocaleString()}
          </div>
          <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">
            All time invoices
          </p>
        </CardContent>
      </Card>

      <Card className="relative rounded-[24px] border-emerald-400/30 dark:border-emerald-500/20 bg-gradient-to-br from-white via-emerald-50/40 to-emerald-100/20 dark:from-gray-900 dark:via-emerald-950/30 dark:to-emerald-900/10 hover:shadow-xl hover:shadow-emerald-500/15 dark:hover:shadow-emerald-500/10 transition-all duration-500 hover:scale-105 overflow-hidden group">
        {/* Animated background effects */}
        <div className="absolute inset-0 bg-gradient-to-r from-emerald-400/10 via-transparent to-emerald-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-emerald-500 to-transparent" />
        <div className="absolute -top-10 -right-10 w-20 h-20 bg-emerald-400/10 rounded-full blur-2xl group-hover:bg-emerald-400/20 transition-colors duration-500" />
        
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 border-b border-emerald-400/10 dark:border-emerald-500/5 relative z-10">
          <CardTitle className="text-sm font-semibold text-gray-600 dark:text-gray-300">
            Total Revenue
          </CardTitle>
          <div className="rounded-full p-3 bg-gradient-to-br from-emerald-400/20 to-emerald-500/30 dark:from-emerald-500/15 dark:to-emerald-600/25 shadow-lg">
            <DollarSign className="h-5 w-5 text-emerald-600 dark:text-emerald-400 drop-shadow-sm" />
          </div>
        </CardHeader>
        <CardContent className="pt-4 relative z-10">
          <div className="flex items-baseline gap-2 mb-2">
            <span className="text-4xl font-bold text-emerald-600 dark:text-emerald-400">
              ${totalRevenueFormatted.formatted}
            </span>
            {totalRevenueFormatted.suffix && (
              <span className="text-xl font-semibold text-emerald-600/80 dark:text-emerald-400/80">
                {totalRevenueFormatted.suffix}
              </span>
            )}
          </div>
          <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">
            ${(stats.totalAmount ?? 0).toLocaleString()} total
          </p>
        </CardContent>
      </Card>

      <Card className="relative rounded-[24px] border-amber-400/30 dark:border-amber-500/20 bg-gradient-to-br from-white via-amber-50/40 to-amber-100/20 dark:from-gray-900 dark:via-amber-950/30 dark:to-amber-900/10 hover:shadow-xl hover:shadow-amber-500/15 dark:hover:shadow-amber-500/10 transition-all duration-500 hover:scale-105 overflow-hidden group">
        {/* Animated background effects */}
        <div className="absolute inset-0 bg-gradient-to-r from-amber-400/10 via-transparent to-amber-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-amber-500 to-transparent" />
        <div className="absolute -top-8 -right-8 w-16 h-16 bg-amber-400/15 rounded-full blur-xl group-hover:bg-amber-400/25 transition-colors duration-500 animate-pulse" />
        
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 border-b border-amber-400/10 dark:border-amber-500/5 relative z-10">
          <CardTitle className="text-sm font-semibold text-gray-600 dark:text-gray-300">
            Pending Payments
          </CardTitle>
          <div className="rounded-full p-3 bg-gradient-to-br from-amber-400/20 to-amber-500/30 dark:from-amber-500/15 dark:to-amber-600/25 shadow-lg">
            <AlertCircle className="h-5 w-5 text-amber-600 dark:text-amber-400 drop-shadow-sm" />
          </div>
        </CardHeader>
        <CardContent className="pt-4 relative z-10">
          <div className="flex items-baseline gap-2 mb-2">
            <span className="text-4xl font-bold text-amber-600 dark:text-amber-400">
              ${pendingAmountFormatted.formatted}
            </span>
            {pendingAmountFormatted.suffix && (
              <span className="text-xl font-semibold text-amber-600/80 dark:text-amber-400/80">
                {pendingAmountFormatted.suffix}
              </span>
            )}
          </div>
          <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">
            ${(stats.pendingAmount ?? 0).toLocaleString()} pending
          </p>
        </CardContent>
      </Card>

      <Card className="relative rounded-[24px] border-blue-400/30 dark:border-blue-500/20 bg-gradient-to-br from-white via-blue-50/40 to-blue-100/20 dark:from-gray-900 dark:via-blue-950/30 dark:to-blue-900/10 hover:shadow-xl hover:shadow-blue-500/15 dark:hover:shadow-blue-500/10 transition-all duration-500 hover:scale-105 overflow-hidden group">
        {/* Animated background effects */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-400/10 via-transparent to-blue-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-blue-500 to-transparent" />
        <div className="absolute -bottom-8 -left-8 w-16 h-16 bg-blue-400/15 rounded-full blur-xl group-hover:bg-blue-400/25 transition-colors duration-500" />
        
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 border-b border-blue-400/10 dark:border-blue-500/5 relative z-10">
          <CardTitle className="text-sm font-semibold text-gray-600 dark:text-gray-300">
            Processing Efficiency
          </CardTitle>
          <div className="rounded-full p-3 bg-gradient-to-br from-blue-400/20 to-blue-500/30 dark:from-blue-500/15 dark:to-blue-600/25 shadow-lg">
            <TrendingUp className="h-5 w-5 text-blue-600 dark:text-blue-400 drop-shadow-sm" />
          </div>
        </CardHeader>
        <CardContent className="pt-4 relative z-10">
          <div className="text-4xl font-bold text-blue-600 dark:text-blue-400 mb-2">
            {stats.processingEfficiency}%
          </div>
          <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">
            Payment completion rate
          </p>
        </CardContent>
      </Card>
    </div>
  );
});
