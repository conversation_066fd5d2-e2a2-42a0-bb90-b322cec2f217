'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Download, FileText, Table, Image, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

export default function TestDocumentPage() {
  const [loading, setLoading] = useState(false);
  const [documents, setDocuments] = useState<any[]>([]);
  const [format, setFormat] = useState('pdf');
  const [type, setType] = useState('invoice');

  const generateDocument = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/test-document', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ type, format }),
      });

      const data = await response.json();

      if (data.success) {
        setDocuments(data.documents);
        toast.success(`Generated ${data.documents.length} document(s) successfully!`);
      } else {
        throw new Error(data.error || 'Failed to generate document');
      }
    } catch (error) {
      console.error('Error generating document:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to generate document');
    } finally {
      setLoading(false);
    }
  };

  const downloadDocument = (doc: any) => {
    if (doc.downloadUrl.startsWith('data:')) {
      // Handle data URL
      const link = document.createElement('a');
      link.href = doc.downloadUrl;
      link.download = `${doc.metadata.title}.${doc.format === 'excel' ? 'xlsx' : doc.format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      // Handle regular URL
      window.open(doc.downloadUrl, '_blank');
    }
  };

  const getIcon = (format: string) => {
    switch (format) {
      case 'pdf':
        return <FileText className="w-5 h-5" />;
      case 'excel':
        return <Table className="w-5 h-5" />;
      case 'image':
        return <Image className="w-5 h-5" />;
      default:
        return <FileText className="w-5 h-5" />;
    }
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Document Generation Test</h1>
        <p className="text-muted-foreground mt-2">
          Test the AI Agent's document generation capabilities
        </p>
      </div>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Generate Document</CardTitle>
          <CardDescription>
            Create sample invoices and reports in different formats
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Document Type</label>
              <Select value={type} onValueChange={setType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="invoice">Invoice</SelectItem>
                  <SelectItem value="report">Financial Report</SelectItem>
                  <SelectItem value="dashboard">Dashboard</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Format</label>
              <Select value={format} onValueChange={setFormat}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pdf">PDF</SelectItem>
                  <SelectItem value="excel">Excel</SelectItem>
                  <SelectItem value="image">Image</SelectItem>
                  <SelectItem value="all">All Formats</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Button 
            onClick={generateDocument} 
            disabled={loading}
            className="w-full"
          >
            {loading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <FileText className="w-4 h-4 mr-2" />
                Generate Document
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {documents.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Generated Documents</CardTitle>
            <CardDescription>
              Click to download or view your generated documents
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {documents.map((doc, index) => (
                <div 
                  key={doc.id} 
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      {getIcon(doc.format)}
                    </div>
                    <div>
                      <h3 className="font-medium">{doc.metadata.title}</h3>
                      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                        <Badge variant="secondary">{doc.format.toUpperCase()}</Badge>
                        <span>{formatFileSize(doc.metadata.size)}</span>
                        {doc.metadata.pages && (
                          <span>{doc.metadata.pages} pages</span>
                        )}
                        {doc.metadata.sheets && (
                          <span>{doc.metadata.sheets} sheets</span>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => downloadDocument(doc)}
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
