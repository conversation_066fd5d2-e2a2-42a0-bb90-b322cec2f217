'use client';

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

interface CacheMetricsProps {
  cache: {
    status: 'excellent' | 'good' | 'fair' | 'poor';
    hitRate: number;
    size: number;
    recommendations: string[];
  };
  getStatusColor: (status: string) => string;
}

export function CacheMetrics({ cache, getStatusColor }: CacheMetricsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Caching System
          <Badge className={getStatusColor(cache.status)}>
            {cache.status}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <div className="flex justify-between mb-2">
            <span>Hit Rate</span>
            <span>{(cache.hitRate * 100).toFixed(1)}%</span>
          </div>
          <Progress value={cache.hitRate * 100} />
        </div>
        <div>
          <div className="flex justify-between mb-2">
            <span>Cache Size</span>
            <span>{cache.size} entries</span>
          </div>
          <Progress value={Math.min(100, (cache.size / 100) * 100)} />
        </div>
        {cache.recommendations.length > 0 && (
          <div>
            <h4 className="font-semibold mb-2">Recommendations:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
              {cache.recommendations.map((rec, index) => (
                <li key={index}>{rec}</li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
}