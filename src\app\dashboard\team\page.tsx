import React from 'react';
import Team from '@/components/dashboard/pages/Team';
import { getTeamMembers, getPendingInvites, getTeamStatistics } from '@/lib/actions/team';
import { auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';

export default async function TeamPage() {
  const { userId } = await auth();
  
  // Redirect if user is not authenticated
  if (!userId) {
    redirect('/sign-in');
  }
  
  // Fetch team data in parallel
  const [membersResult, invitesResult, statisticsResult] = await Promise.all([
    getTeamMembers(),
    getPendingInvites(),
    getTeamStatistics()
  ]);
  
  return (
    <Team 
      members={membersResult.members || []} 
      invites={invitesResult.invites || []} 
      statistics={statisticsResult.statistics || {
        totalMembers: 0,
        activeMembers: 0,
        pendingInvites: 0,
        roleDistribution: {}
      }}
      initialErrors={{
        members: membersResult.error,
        invites: invitesResult.error,
        statistics: statisticsResult.error
      }}
    />
  );
}
