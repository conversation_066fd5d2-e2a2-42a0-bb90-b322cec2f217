'use client';

import DashboardLayout from '@/components/layout/DashboardLayout';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { toast } from 'sonner';
import { UserProfile } from '@/lib/services/user-service';

// Use environment variable for backend base URL (same as AdvancedTab)
const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL;
if (!backendUrl) {
  throw new Error('NEXT_PUBLIC_BACKEND_URL environment variable is not configured');
}

export default function DashboardSupport() {
  const { user } = useUser();
  const [form, setForm] = useState({
    name: '',
    email: '',
    phone_number: '',
    message: '',
  });
  const [dbUser, setDbUser] = useState<UserProfile | null>(null);
  const [userLoading, setUserLoading] = useState(true);

  // Auto-fill form fields when user data is available
  useEffect(() => {
    if (user) {
      const fullName =
        `${user.firstName || ''} ${user.lastName || ''}`.trim();
      const userEmail = user.primaryEmailAddress?.emailAddress || '';

      setForm((prev) => ({
        ...prev,
        name: fullName || prev.name,
        email: userEmail || prev.email,
      }));
    }
  }, [user]);

  // Fetch dbUser from /api/user/profile
  useEffect(() => {
    async function fetchDbUser() {
      setUserLoading(true);
      try {
        const res = await fetch('/api/user/profile');
        if (!res.ok) throw new Error('Failed to fetch user');
        const data = await res.json();
        setDbUser(data);
      } catch {
        setDbUser(null);
      } finally {
        setUserLoading(false);
      }
    }
    fetchDbUser();
  }, []);

  const [submitted, setSubmitted] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setForm({ ...form, [e.target.name]: e.target.value });
    setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (
      !form.name ||
      !form.email ||
      !form.phone_number ||
      !form.message
    ) {
      setError('Please fill in all fields.');
      return;
    }
    if (!dbUser?.id) {
      setError('User not authenticated.');
      return;
    }
    setLoading(true);
    setError('');
    setSubmitted(false);
    try {
      // Call backend API with dbUser.id in the URL
      const res = await fetch(`${backendUrl}/api/v1/help-support/${dbUser.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: form.name,
          phone_number: form.phone_number,
          email: form.email,
          message: form.message,
        }),
      });
      if (!res.ok) {
        throw new Error('Failed to submit.');
      }
      setSubmitted(true);
      setForm({ name: '', email: '', phone_number: '', message: '' });
      toast.success('Your message has been submitted successfully!');
    } catch {
      setError('Submission failed. Please try again.');
      toast.error('Failed to submit your message. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (userLoading) {
    return <div>Loading...</div>;
  }

  return (
    <DashboardLayout>
      <div className="min-h-screen flex-col bg-background dark:bg-[#0B1739] w-full space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Help & Support
          </h1>
          <p className="text-muted-foreground">
            Need assistance? Fill out the form below and our team will
            get back to you as soon as possible.
          </p>
        </div>

        <Card className="max-w-2xl">
          <CardHeader>
            <CardTitle>Contact Support</CardTitle>
            <CardDescription>
              Send us a message and we&apos;ll respond within 24 hours
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form className="space-y-4" onSubmit={handleSubmit}>
              <div className="space-y-2">
                <label htmlFor="name" className="text-sm font-medium">
                  Name
                </label>
                <Input
                  id="name"
                  type="text"
                  name="name"
                  placeholder="Your full name"
                  value={form.name}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <label
                  htmlFor="email"
                  className="text-sm font-medium"
                >
                  Email
                </label>
                <Input
                  id="email"
                  type="email"
                  name="email"
                  placeholder="<EMAIL>"
                  value={form.email}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <label
                  htmlFor="phone_number"
                  className="text-sm font-medium"
                >
                  Phone Number
                </label>
                <Input
                  id="phone_number"
                  type="tel"
                  name="phone_number"
                  placeholder="Your phone number"
                  value={form.phone_number}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <label
                  htmlFor="message"
                  className="text-sm font-medium"
                >
                  Message
                </label>
                <Textarea
                  id="message"
                  name="message"
                  placeholder="Describe your issue or question in detail..."
                  value={form.message}
                  onChange={handleChange}
                  className="min-h-[120px] resize-none"
                  required
                />
              </div>

              {error && (
                <div className="text-red-500 text-sm bg-red-50 dark:bg-red-950/20 p-3 rounded-md border border-red-200 dark:border-red-800">
                  {error}
                </div>
              )}                                                                          

              {submitted && !error && (
                <div className="text-green-600 text-sm bg-green-50 dark:bg-green-950/20 p-3 rounded-md border border-green-200 dark:border-green-800">
                  Thank you! Your message has been submitted
                  successfully.
                </div>
              )}

              <Button
                type="submit"
                className="w-full bg-[#0a1733] text-white border border-[#1a2747] rounded-lg py-2 px-6 hover:bg-[#162447] transition-colors"
                disabled={loading}
              >
                {loading ? 'Submitting...' : 'Submit Message'}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
