-- CreateTable
CREATE TABLE "users_api_key" (
    "users_api_key_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "api_key" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expires_at" TIMESTAMP(3),

    CONSTRAINT "users_api_key_pkey" PRIMARY KEY ("users_api_key_id")
);

-- CreateTable
CREATE TABLE "help_and_support" (
    "id" TEXT NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "phone_number" VARCHAR(255),
    "user_id" VARCHAR(255),
    "email" VARCHAR(255) NOT NULL,
    "message" TEXT NOT NULL,
    "status" VARCHAR(50) NOT NULL DEFAULT 'open',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "help_and_support_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tools" (
    "tool_id" TEXT NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "description" TEXT,
    "tool_config" JSONB,
    "sql_template" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "tools_pkey" PRIMARY KEY ("tool_id")
);

-- CreateIndex
CREATE UNIQUE INDEX "users_api_key_api_key_key" ON "users_api_key"("api_key");

-- CreateIndex
CREATE INDEX "users_api_key_api_key_idx" ON "users_api_key"("api_key");

-- CreateIndex
CREATE INDEX "users_api_key_user_id_idx" ON "users_api_key"("user_id");

-- CreateIndex
CREATE INDEX "help_and_support_status_idx" ON "help_and_support"("status");

-- CreateIndex
CREATE INDEX "help_and_support_user_id_idx" ON "help_and_support"("user_id");

-- CreateIndex
CREATE INDEX "help_and_support_created_at_idx" ON "help_and_support"("created_at");

-- CreateIndex
CREATE INDEX "tools_name_idx" ON "tools"("name");

-- AddForeignKey
ALTER TABLE "users_api_key" ADD CONSTRAINT "users_api_key_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
