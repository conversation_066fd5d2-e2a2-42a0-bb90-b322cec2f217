'use client';

import React from 'react';
import { toast } from 'sonner';
import { MessageSquare, FileText, Zap, ArrowUp } from 'lucide-react';

interface UsageToastProps {
  type: 'chat' | 'invoice';
  current: number;
  limit: number;
  isAtLimit: boolean;
  onUpgrade?: () => void;
}

export const showUsageToast = ({
  type,
  current,
  limit,
  isAtLimit,
  onUpgrade,
}: UsageToastProps) => {
  const icon = type === 'chat' ? MessageSquare : FileText;
  const featureText =
    type === 'chat' ? 'chat messages' : 'invoice uploads';
  const percentage = Math.round((current / limit) * 100);

  if (isAtLimit) {
    toast.error(
      `${type === 'chat' ? 'Chat' : 'Invoice'} Limit Reached`,
      {
        description: `You've used all ${limit} ${featureText} for this month. Upgrade to continue.`,
        duration: 10000,
        action: onUpgrade
          ? {
              label: 'Upgrade',
              onClick: onUpgrade,
            }
          : undefined,
        icon: <Zap className="h-4 w-4" />,
      }
    );
  } else if (percentage >= 80) {
    toast.warning(
      `${type === 'chat' ? 'Chat' : 'Invoice'} Limit Warning`,
      {
        description: `You've used ${current} of ${limit} ${featureText} (${percentage}%). Consider upgrading soon.`,
        duration: 8000,
        action: onUpgrade
          ? {
              label: 'Upgrade',
              onClick: onUpgrade,
            }
          : undefined,
        icon: React.createElement(icon, { className: 'h-4 w-4' }),
      }
    );
  }
};

export const showUpgradeSuccessToast = () => {
  toast.success('Upgrade Successful!', {
    description:
      'Your usage limits have been increased. You can now continue using Billix.',
    duration: 5000,
    icon: <ArrowUp className="h-4 w-4" />,
  });
};

export const showUsageResetToast = () => {
  toast.info('Usage Reset', {
    description:
      'Your monthly usage has been reset. You can now use all features again.',
    duration: 5000,
    icon: <Zap className="h-4 w-4" />,
  });
};
