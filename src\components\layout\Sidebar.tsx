'use client';

import React, { JSX, useEffect, useState } from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { usePathname } from 'next/navigation';
import {
  ChevronLeft,
  LayoutDashboard,
  FileText,
  Upload,
  Settings,
  Users,
  HelpCircle,
  CreditCard,
  MessageSquare,
  FileOutput,
  ClipboardList,
  Bot,
  Receipt,
  Building2,
  LineChart,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { motion } from 'motion/react';
import Colors from '@/components/theme/Colors';

interface SidebarProps {
  className?: string;
  collapsed: boolean;
  onToggle: () => void;
  isMobile: boolean;
  visible: boolean;
}

interface SidebarItemProps {
  href: string;
  icon: JSX.Element;
  label: string;
  collapsed: boolean;
  active: boolean;
}

interface UsageItemProps {
  icon: JSX.Element;
  label: string;
  value: string;
  tooltip?: string;
  isFeature?: boolean;
}

interface UsageStats {
  chatUsage: number;
  invoiceUsage: number;
  chatLimit: number;
  invoiceLimit: number;
  resetDate: Date;
  daysUntilReset: number;
}

function NavItem({
  href,
  icon,
  label,
  collapsed,
  active,
}: SidebarItemProps) {
  // Pre-compute class names to avoid dynamic class generation during render
  const linkClassName = cn(
    'flex items-center gap-3 px-3 py-3 rounded-lg transition-all duration-200',
    active
      ? 'text-white font-medium'
      : 'text-gray-700 hover:text-blue-800 hover:bg-blue-50/60 dark:text-gray-300 dark:hover:text-white dark:hover:bg-[#0B1739]/80'
  );

  const iconClassName = cn(
    'flex-shrink-0',
    active ? 'text-[#00B2FF]' : 'text-gray-500 dark:text-gray-400'
  );

  return (
    <Link
      href={href}
      className={linkClassName}
      style={
        active ? { backgroundColor: Colors.sidebarActiveBg } : {}
      }
    >
      <div className={iconClassName}>{icon}</div>
      {!collapsed && (
        <span className="text-sm font-medium">{label}</span>
      )}
    </Link>
  );
}

const SidebarItem = ({
  icon,
  label,
  value,
  tooltip,
  isFeature,
}: UsageItemProps) => {
  // Pre-compute these classes to avoid re-renders
  const itemClassName = cn(
    'flex items-center justify-between p-2 hover:bg-blue-50/50 dark:hover:bg-[#0B1739]/50 rounded-md transition-colors'
  );

  const iconClassName = cn(
    'flex-shrink-0',
    'text-gray-500 dark:text-gray-400'
  );

  const labelClassName = cn(
    'text-sm font-medium',
    'text-gray-700 dark:text-gray-300'
  );

  return (
    <div className={itemClassName}>
      <div className="flex items-center gap-2">
        <div className={iconClassName}>{icon}</div>
        <span className={labelClassName}>{label}</span>
        {isFeature && (
          <Badge
            variant="outline"
            className="bg-transparent border-blue-200 text-blue-700 dark:border-[#00B2FF]/30 dark:text-[#00B2FF]"
          >
            Feature
          </Badge>
        )}
      </div>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge
            variant="secondary"
            className="bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300"
          >
            {value}
          </Badge>
        </TooltipTrigger>
        {tooltip && (
          <TooltipContent side="right" className="max-w-[200px]">
            <p className="text-xs">{tooltip}</p>
          </TooltipContent>
        )}
      </Tooltip>
    </div>
  );
};

export function Sidebar({
  className,
  collapsed,
  onToggle,
  isMobile,
  visible,
}: SidebarProps) {
  const pathname = usePathname();
  const [usageStats, setUsageStats] = useState<UsageStats | null>(
    null
  );
  const [isLoadingUsage, setIsLoadingUsage] = useState(true);
  const [hasError, setHasError] = useState(false);

  // Fetch usage stats
  useEffect(() => {
    const fetchUsageStats = async () => {
      try {
        setIsLoadingUsage(true);
        setHasError(false);

        const response = await fetch('/api/usage/stats');
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.stats) {
            setUsageStats({
              ...data.stats,
              resetDate: new Date(data.stats.resetDate),
            });
          } else {
            setHasError(true);
          }
        } else {
          setHasError(true);
        }
      } catch {
        setHasError(true);
      } finally {
        setIsLoadingUsage(false);
      }
    };

    fetchUsageStats();
  }, []);

  // Create usage items based on fetched data
  const usageItems: UsageItemProps[] = [];

  if (isLoadingUsage) {
    // Loading state
    usageItems.push(
      {
        icon: <FileText className="w-4 h-4" />,
        label: 'Documents Used',
        value: '...',
        tooltip: 'Loading usage information...',
      },
      {
        icon: <MessageSquare className="w-4 h-4" />,
        label: 'Assistants Used',
        value: '...',
        tooltip: 'Loading usage information...',
        isFeature: true,
      }
    );
  } else if (hasError || !usageStats) {
    // Error state - show fallback
    usageItems.push(
      {
        icon: <FileText className="w-4 h-4" />,
        label: 'Documents Used',
        value: 'N/A',
        tooltip:
          'Unable to load usage data. Please try refreshing the page.',
      },
      {
        icon: <MessageSquare className="w-4 h-4" />,
        label: 'Assistants Used',
        value: 'N/A',
        tooltip:
          'Unable to load usage data. Please try refreshing the page.',
        isFeature: true,
      }
    );
  } else {
    // Success state - show real data
    usageItems.push(
      {
        icon: <FileText className="w-4 h-4" />,
        label: 'Documents Used',
        value: `${usageStats.invoiceUsage || 0} / ${usageStats.invoiceLimit || 0}`,
        tooltip: `You have used ${usageStats.invoiceUsage || 0} out of your ${usageStats.invoiceLimit || 0} document allowance this month. Resets in ${usageStats.daysUntilReset || 0} days.`,
      },
      {
        icon: <MessageSquare className="w-4 h-4" />,
        label: 'Assistants Used',
        value: `${usageStats.chatUsage || 0} / ${usageStats.chatLimit || 0}`,
        tooltip: `You have used ${usageStats.chatUsage || 0} out of your ${usageStats.chatLimit || 0} chat allowance this month. Resets in ${usageStats.daysUntilReset || 0} days.`,
        isFeature: true,
      }
    );
  }

  // Navigation items
  const navItems = [
    {
      label: 'Dashboard',
      href: '/dashboard',
      icon: <LayoutDashboard className="w-5 h-5" />,
    },
    {
      label: 'Documents',
      href: '/dashboard/invoices',
      icon: <Receipt className="w-5 h-5" />,
    },
    {
      label: 'Upload',
      href: '/dashboard/upload',
      icon: <Upload className="w-5 h-5" />,
    },
    {
      label: 'Analytics',
      href: '/dashboard/analysis',
      icon: <LineChart className="w-5 h-5" />,
    },
    {
      label: 'Management',
      href: '/dashboard/management',
      icon: <Building2 className="w-5 h-5" />,
    },
    {
      label: 'Export Invoices',
      href: '/dashboard/exports',
      icon: <FileOutput className="w-5 h-5" />,
    },
    {
      label: 'Reports',
      href: '/dashboard/reports',
      icon: <ClipboardList className="w-5 h-5" />,
    },
    {
      label: 'Billix Agent',
      href: '/dashboard/chat',
      icon: <Bot className="w-5 h-5" />,
    },
    {
      label: 'Subscriptions',
      href: '/dashboard/subscription',
      icon: <CreditCard className="w-5 h-5" />,
    },
    {
      label: 'Team',
      href: '/dashboard/team',
      icon: <Users className="w-5 h-5" />,
    },
    {
      label: 'Settings',
      href: '/dashboard/settings',
      icon: <Settings className="w-5 h-5" />,
    },
    {
      label: 'Help & Support',
      href: '/dashboard/support',
      icon: <HelpCircle className="w-5 h-5" />,
    },
  ];

  // Only show if visible on mobile
  if (isMobile && !visible) {
    return null;
  }

  return (
    <motion.div
      initial={false}
      animate={{ width: collapsed ? 70 : 250 }}
      transition={{ duration: 0.3 }}
      className={cn(
        'h-full border-r border-gray-200 dark:border-[color:var(--sidebar-border)] bg-white dark:bg-[color:var(--sidebar)] dark:dashboard-gradient-bg backdrop-blur-md flex flex-col z-30',
        isMobile && 'shadow-xl',
        className
      )}
    >
      <div className="flex items-center justify-between h-16 px-3 border-b border-gray-200 dark:border-gray-800">
        {!collapsed && (
          <div className="flex items-center gap-2">
            <div className="bg-blue-100 dark:bg-[color:var(--primary)] p-1.5 rounded-md">
              <FileText className="h-5 w-5 text-blue-600 dark:text-[color:var(--primary-foreground)]" />
            </div>
            <span className="text-lg font-bold text-gray-900 dark:text-[color:var(--sidebar-foreground)]">
              Billix
            </span>
          </div>
        )}
        <button
          onClick={onToggle}
          className={cn(
            'w-8 h-8 rounded-full flex items-center justify-center text-gray-600 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 transition-colors',
            !collapsed && 'ml-auto'
          )}
        >
          <ChevronLeft
            className={cn(
              'h-5 w-5 transition-transform',
              collapsed && 'rotate-180'
            )}
          />
        </button>
      </div>

      <div className="flex-1 overflow-y-auto py-4 px-3">
        <div className="space-y-1 py-2">
          {navItems.map((item, index) => (
            <NavItem
              key={index}
              href={item.href}
              label={item.label}
              icon={item.icon}
              active={
                item.href === '/dashboard'
                  ? pathname === '/dashboard'
                  : pathname === item.href ||
                    pathname.startsWith(`${item.href}/`)
              }
              collapsed={collapsed}
            />
          ))}
        </div>
      </div>

      {/* Usage Statistics Section */}
      <div
        className={cn(
          'mt-auto mb-4 px-3',
          collapsed ? 'hidden' : 'block'
        )}
      >
        <div className="border-t border-gray-200 dark:border-gray-800 pt-4">
          <h4 className="text-xs uppercase font-semibold text-gray-600 dark:text-gray-400 mb-3 px-2">
            Monthly Usage
          </h4>
          <div className="space-y-1.5">
            {usageItems.map((stat, index) => (
              <SidebarItem
                key={index}
                icon={stat.icon}
                label={stat.label}
                value={stat.value}
                tooltip={stat.tooltip}
                isFeature={stat.isFeature}
              />
            ))}
          </div>
        </div>
      </div>
    </motion.div>
  );
}
