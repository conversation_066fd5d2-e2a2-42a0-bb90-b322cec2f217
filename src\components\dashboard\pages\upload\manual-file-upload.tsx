'use client';

import { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { FileUp, Check, RefreshCw } from 'lucide-react';
import { extractInvoiceData } from '@/actions/extract-invoice';
import { useInvoiceDatabase } from '@/hooks/use-invoice-db';
import { useToast } from '@/components/ui/use-toast';
import { Progress } from '@/components/ui/progress';
import { FileWithEmailMetadata } from '@/app/api/cron/sync-emails/route';

export function ManualFileUpload({
  files,
  autoProcess = true,
}: {
  files: File[];
  autoProcess?: boolean;
}) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [processedCount, setProcessedCount] = useState(0);
  const [success, setSuccess] = useState(false);
  const [hasAutoProcessed, setHasAutoProcessed] = useState(false);
  const [processedSuccessfully, setProcessedSuccessfully] =
    useState(0);
  const { saveInvoice } = useInvoiceDatabase();
  const { toast } = useToast();

  const processFiles = useCallback(async () => {
    if (isProcessing || !files.length) return;

    setIsProcessing(true);
    setProgress(0);
    setProcessedCount(0);
    setProcessedSuccessfully(0);
    setSuccess(false);

    try {
      let processed = 0;

      // Show toast at the start
      toast({
        title: 'Processing Started',
        description: `Processing ${files.length} ${autoProcess ? 'email attachments' : 'files'} automatically...`,
      });

      // Process each file
      for (const file of files) {
        // Create form data
        const formData = new FormData();
        formData.append('file', file);

        // Check if this is from email and add metadata
        if ((file as FileWithEmailMetadata).emailMetadata) {
          formData.append(
            'emailMetadata',
            JSON.stringify(
              (file as FileWithEmailMetadata).emailMetadata
            )
          );
        }

        // Extract data
        const result = await extractInvoiceData(formData);

        // Handle both single and batch extraction results
        const hasError =
          'error' in result
            ? result.error
            : 'errors' in result &&
              result.errors &&
              result.errors.length > 0;
        if (hasError) {
          const errorMsg =
            'error' in result
              ? result.error
              : 'errors' in result
                ? result.errors?.[0]
                : 'Unknown error';
          toast({
            title: 'Extraction Error',
            description: `Failed to extract data from ${file.name}: ${errorMsg}`,
            variant: 'destructive',
          });
        } else if (result.data) {
          // Extract single invoice data (for single file processing)
          const invoiceData = Array.isArray(result.data)
            ? result.data[0]
            : result.data;

          if (!invoiceData) {
            continue;
          }

          // Save to database
          const saveResult = await saveInvoice(invoiceData);

          if (saveResult.success) {
            processed++;
            setProcessedSuccessfully(processed);
          } else {
            toast({
              title: 'Save Error',
              description: `Failed to save data from ${file.name}: ${saveResult.error}`,
              variant: 'destructive',
            });
          }

        }

        // Update progress
        setProcessedCount((prevCount) => prevCount + 1);
        setProgress(
          Math.round(((processed + 1) / files.length) * 100)
        );
      }

      // Show success message
      if (processed > 0) {
        setSuccess(true);
        const message = `Successfully processed ${processed} invoices.`;

        toast({
          title: 'Processing Complete',
          description: message,
        });
      } else {
        toast({
          title: 'Processing Failed',
          description:
            'Failed to process any invoices. Please try again.',
          variant: 'destructive',
        });
      }
    } catch {
      toast({
        title: 'Processing Error',
        description:
          'An unexpected error occurred during processing.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  }, [files, isProcessing, autoProcess, saveInvoice, toast]);

  // Auto-process files when they're received
  useEffect(() => {
    if (
      autoProcess &&
      files.length > 0 &&
      !isProcessing &&
      !hasAutoProcessed
    ) {
      setHasAutoProcessed(true);
      processFiles();
    }
  }, [
    files,
    autoProcess,
    isProcessing,
    hasAutoProcessed,
    processFiles,
  ]);

  return (
    <Card className="border-green-200 dark:border-green-900">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">
          {autoProcess ? 'Automatic' : 'Manual'} Invoice Processing
          {autoProcess && (
            <span className="text-sm text-muted-foreground ml-2">
              (No clicks needed)
            </span>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="text-sm">
            <p className="mb-2">
              {files.length} file{files.length !== 1 ? 's' : ''}{' '}
              {isProcessing
                ? 'being processed'
                : success
                  ? 'processed'
                  : 'ready for processing'}
            </p>
            <ul className="text-xs text-muted-foreground space-y-1">
              {files.map((file, i) => (
                <li key={i} className="truncate">
                  {file.name} ({(file.size / 1024).toFixed(1)}KB)
                </li>
              ))}
            </ul>
          </div>

          {isProcessing && (
            <div className="space-y-2">
              <Progress value={progress} className="h-2" />
              <p className="text-xs text-center text-muted-foreground">
                <RefreshCw className="h-3 w-3 inline-block mr-1 animate-spin" />
                Processing file {processedCount} of {files.length}...
              </p>
            </div>
          )}

          {success && !isProcessing && (
            <div className="rounded-md bg-green-50 dark:bg-green-950/30 p-3 text-green-800 dark:text-green-300 text-sm flex items-start">
              <Check className="h-5 w-5 flex-shrink-0 mr-2 text-green-500 dark:text-green-400" />
              <div>
                <p className="font-medium">Processing complete</p>
                <p>
                  {processedSuccessfully > 0 ? (
                    <>
                      Your invoices have been successfully processed.
                    </>
                  ) : (
                    <>No invoices processed. Please try again.</>
                  )}
                </p>
              </div>
            </div>
          )}

          {!success &&
            !isProcessing &&
            autoProcess &&
            hasAutoProcessed && (
              <div className="text-sm text-amber-600 dark:text-amber-400 text-center">
                <p>Automatic processing completed with errors</p>
              </div>
            )}

          <Button
            onClick={processFiles}
            className="w-full"
            disabled={isProcessing || !files.length}
          >
            <FileUp className="mr-2 h-4 w-4" />
            {isProcessing
              ? `Processing... (${processedCount}/${files.length})`
              : `${autoProcess ? 'Manually Re-Process' : 'Process'} ${files.length} Invoice${files.length !== 1 ? 's' : ''}`}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

