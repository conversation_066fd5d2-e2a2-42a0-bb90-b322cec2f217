/**
 * Team invitation system configuration
 * Centralized configuration for team invitation settings
 */

export interface TeamInvitationConfig {
  useClerkInvitations: boolean;
  requireEmailSetup: boolean;
  invitationExpiryDays: number;
}

/**
 * Get team invitation configuration from environment variables
 */
export function getTeamInvitationConfig(): TeamInvitationConfig {
  return {
    // Whether to use Clerk's built-in invitation system
    // Set NEXT_PUBLIC_USE_CLERK_INVITATIONS=true to enable
    useClerkInvitations:
      process.env.NEXT_PUBLIC_USE_CLERK_INVITATIONS === 'true',

    // Whether email setup is required for custom invitations
    // Set NEXT_PUBLIC_REQUIRE_EMAIL_SETUP=false to disable email requirement
    requireEmailSetup:
      process.env.NEXT_PUBLIC_REQUIRE_EMAIL_SETUP !== 'false',

    // Number of days before invitations expire (default: 7)
    // Set NEXT_PUBLIC_INVITATION_EXPIRY_DAYS to customize
    invitationExpiryDays: parseInt(
      process.env.NEXT_PUBLIC_INVITATION_EXPIRY_DAYS || '7',
      10
    ),
  };
}

/**
 * Check if Clerk invitations are enabled
 */
export function isClerkInvitationsEnabled(): boolean {
  return process.env.NEXT_PUBLIC_USE_CLERK_INVITATIONS === 'true';
}

/**
 * Check if email setup is required
 */
export function isEmailSetupRequired(): boolean {
  return process.env.NEXT_PUBLIC_REQUIRE_EMAIL_SETUP !== 'false';
}

/**
 * Get invitation expiry in days
 */
export function getInvitationExpiryDays(): number {
  return parseInt(
    process.env.NEXT_PUBLIC_INVITATION_EXPIRY_DAYS || '7',
    10
  );
}

/**
 * Validate team invitation environment configuration
 */
export function validateTeamConfig(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  const config = getTeamInvitationConfig();

  // Check if using custom invitations but missing email config
  if (!config.useClerkInvitations && config.requireEmailSetup) {
    if (!process.env.RESEND_API_KEY) {
      errors.push(
        'RESEND_API_KEY is required for custom email invitations'
      );
    }
    if (!process.env.EMAIL_FROM) {
      warnings.push('EMAIL_FROM not set - will use default sender');
    }
    if (!process.env.NEXT_PUBLIC_APP_URL) {
      errors.push(
        'NEXT_PUBLIC_APP_URL is required for invitation links'
      );
    }
  }

  // Check Clerk configuration
  if (config.useClerkInvitations) {
    if (!process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY) {
      errors.push(
        'NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY is required for Clerk invitations'
      );
    }
    if (!process.env.CLERK_SECRET_KEY) {
      errors.push(
        'CLERK_SECRET_KEY is required for Clerk invitations'
      );
    }
  }

  // Validate expiry days
  if (
    config.invitationExpiryDays < 1 ||
    config.invitationExpiryDays > 30
  ) {
    warnings.push(
      'INVITATION_EXPIRY_DAYS should be between 1 and 30 days'
    );
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}
