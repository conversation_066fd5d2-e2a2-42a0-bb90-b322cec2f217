'use client';
import { useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { motion } from 'motion/react';
import { Sparkles } from 'lucide-react';

import { SidebarHistory } from '@/components/ai-agent/sidebar-history';
import { SidebarUserNav } from '@/components/ai-agent/sidebar-user-nav';
import { Button } from '@/components/ui/button';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  useSidebar,
} from '@/components/ui/sidebar';
import Link from 'next/link';
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from '@/components/ui/tooltip';
import { PlusIcon } from './icons';
import Colors from "../theme/Colors";

export function AppSidebar() {
  const router = useRouter();
  const { setOpenMobile } = useSidebar();
  const { isSignedIn } = useUser();

  return (
    <TooltipProvider>
      <Sidebar side="right" className={`group-data-[side=right]:border-l-0 bg-gradient-to-b from-[${Colors.gradients.darkBlue[0]}] via-[${Colors.gradients.darkBlue[1]}] to-[${Colors.gradients.darkBlue[2]}] backdrop-blur-md border-l border-border/30 hide-scrollbar`}>
        <SidebarHeader className="py-4 px-2">
          <SidebarMenu>
            <div className="flex flex-row justify-between items-center px-2">
              <Link
                href="/"
                onClick={() => {
                  setOpenMobile(false);
                }}
                className="flex flex-row gap-2 items-center"
              >
                <motion.div
                  className={`flex items-center justify-center size-8 rounded-full bg-gradient-to-br from-[${Colors.gradients.blueToPurple[0]}] to-[${Colors.gradients.blueToPurple[1]}] text-white shadow-md`}
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  <Sparkles size={16} />
                </motion.div>
                <span className="text-lg font-bold px-1 cursor-pointer bg-gradient-to-r from-[${Colors.gradients.blueToPurple[0]}] to-[${Colors.gradients.blueToPurple[1]}] text-transparent bg-clip-text">
                  Billix Agent
                </span>
              </Link>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    type="button"
                    className="p-2 h-fit rounded-full border-border/40 hover:border-primary/40 hover:bg-primary/10 hover:text-primary transition-all duration-200"
                    onClick={() => {
                      setOpenMobile(false);
                      router.push('/dashboard/chat');
                      router.refresh();
                    }}
                  >
                    <PlusIcon />
                  </Button>
                </TooltipTrigger>
                <TooltipContent align="end">New Chat</TooltipContent>
              </Tooltip>
            </div>
          </SidebarMenu>
        </SidebarHeader>
        <SidebarContent className="px-2 hide-scrollbar">
          <SidebarHistory />
        </SidebarContent>
        <SidebarFooter className="p-2 border-t border-border/20">
          {isSignedIn && <SidebarUserNav />}
        </SidebarFooter>
      </Sidebar>
    </TooltipProvider>
  );
}
