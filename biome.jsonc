{
  "$schema": "https://biomejs.dev/schemas/1.9.4/schema.json",
  "files": {
    "ignoreUnknown": false,
    "ignore": [
      "**/pnpm-lock.yaml",
      "lib/db/migrations",
      "lib/editor/react-renderer.tsx",
      "node_modules",
      ".next",
      "public",
      ".vercel"
    ]
  },
  "vcs": {
    "enabled": true,
    "clientKind": "git",
    "defaultBranch": "main",
    "useIgnoreFile": true
  },
  "formatter": {
    "enabled": true,
    "formatWithErrors": false,
    "indentStyle": "space",
    "indentWidth": 2,
    "lineEnding": "lf",
    "lineWidth": 80,
    "attributePosition": "auto"
  },
  "linter": {
    "enabled": true,
    "rules": {
      "recommended": true,
      "a11y": {
        "useHtmlLang": "warn", // Not in recommended ruleset, turning on manually
        "noHeaderScope": "warn", // Not in recommended ruleset, turning on manually
        "useValidAriaRole": {
          "level": "warn",
          "options": {
            "ignoreNonDom": false,
            "allowInvalidRoles": ["none", "text"]
          }
        },
        "useSemanticElements": "warn", // Re-enabled: Accessibility improvement
        "noSvgWithoutTitle": "warn", // Re-enabled: Accessibility improvement
        "useMediaCaption": "warn", // Re-enabled: Accessibility improvement
        "noAutofocus": "warn", // Re-enabled: Accessibility improvement
        "noBlankTarget": "warn", // Re-enabled: Accessibility improvement
        "useFocusableInteractive": "warn", // Re-enabled: Accessibility improvement
        "useAriaPropsForRole": "warn", // Re-enabled: Accessibility improvement
        "useKeyWithClickEvents": "warn" // Re-enabled: Accessibility improvement
      },
      "complexity": {
        "noUselessStringConcat": "warn", // Not in recommended ruleset, turning on manually
        "noForEach": "off", // forEach is too familiar to ban
        "noUselessSwitchCase": "warn", // Re-enabled: Code structure improvement
        "noUselessThisAlias": "warn" // Re-enabled: Code structure improvement
      },
      "correctness": {
        "noUnusedImports": "warn", // Not in recommended ruleset, turning on manually
        "useArrayLiterals": "warn", // Not in recommended ruleset, turning on manually
        "noNewSymbol": "warn", // Not in recommended ruleset, turning on manually
        "useJsxKeyInIterable": "warn", // Re-enabled: React list rendering optimization
        "useExhaustiveDependencies": "warn", // Re-enabled: React hooks dependency checking
        "noUnnecessaryContinue": "warn" // Re-enabled: Code structure improvement
      },
      "security": {
        "noDangerouslySetInnerHtml": "warn" // Re-enabled: XSS protection
      },
      "style": {
        "useFragmentSyntax": "warn", // Not in recommended ruleset, turning on manually
        "noYodaExpression": "warn", // Not in recommended ruleset, turning on manually
        "useDefaultParameterLast": "warn", // Not in recommended ruleset, turning on manually
        "useExponentiationOperator": "off", // Obscure and arguably not easily readable
        "noUnusedTemplateLiteral": "off", // Stylistic opinion
        "noUselessElse": "warn" // Re-enabled: Code structure improvement
      },
      "suspicious": {
        "noExplicitAny": "warn" // Re-enabled: TypeScript type safety
      },
      "nursery": {
        "noStaticElementInteractions": "warn",
        "noHeadImportInDocument": "warn",
        "noDocumentImportInPage": "warn",
        "noDuplicateElseIf": "warn",
        "noIrregularWhitespace": "warn",
        "useValidAutocomplete": "warn"
      }
    }
  },
  "javascript": {
    "jsxRuntime": "reactClassic",
    "formatter": {
      "jsxQuoteStyle": "double",
      "quoteProperties": "asNeeded",
      "trailingCommas": "all",
      "semicolons": "always",
      "arrowParentheses": "always",
      "bracketSpacing": true,
      "bracketSameLine": false,
      "quoteStyle": "single",
      "attributePosition": "auto"
    }
  },
  "json": {
    "formatter": {
      "enabled": true,
      "trailingCommas": "none"
    },
    "parser": {
      "allowComments": true,
      "allowTrailingCommas": false
    }
  },
  "css": {
    "formatter": { "enabled": false },
    "linter": { "enabled": false }
  },
  "organizeImports": { "enabled": false },
  "overrides": [
    // Playwright requires an object destructure, even if empty
    // https://github.com/microsoft/playwright/issues/30007
    {
      "include": ["playwright/**"],
      "linter": {
        "rules": {
          "correctness": {
            "noEmptyPattern": "off"
          }
        }
      }
    }
  ]
}
