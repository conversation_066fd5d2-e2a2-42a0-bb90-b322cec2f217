// src/components/theme/Colors.tsx

// Hardcoded color palette for the entire app
// Use these color codes throughout the app for consistency

const Colors = {
  primary: '#4945b9', // Up main
  primaryDark: '#343388', // Up dark
  accent: '#0e90d3', // Down main
  accentDark: '#122d5b', // Down dark
  background: '#F9FAFB', // Example: gray-50
  backgroundDark: '#18181B', // Example: zinc-900
  text: '#18181B', // Example: zinc-900
  textLight: '#F9FAFB', // Example: gray-50
  border: '#E5E7EB', // Example: gray-200
  error: '#EF4444', // Example: red-500
  success: '#22C55E', // Example: green-500
  warning: '#FACC15', // Example: yellow-400
  info: '#0EA5E9', // Example: sky-500
  // Add more as needed
  // Gradients and chart palettes
  gradients: {
    blueToPurple: ['#00BBFF', '#4000FF'],
    blueToCyan: ['#00BBFF', '#00F6FF'],
    darkBlue: ['#081028', '#0B1739', '#081028'],
    // Add more as needed
  },
  chartColors: [
    '#4361ee', '#4cc9f0', '#f72585', '#4d908e', '#f8961e', '#90be6d',
    '#577590', '#43aa8b', '#f94144', '#f3722c', '#277da1', '#9d4edd',
    '#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d', '#ffc658',
    '#003f5c', '#2f4b7c', '#665191', '#a05195', '#d45087', '#f95d6a', '#ff7c43', '#ffa600',
    '#4e79a7', '#f28e2c', '#e15759', '#76b7b2', '#59a14f', '#edc949', '#af7aa1',
    '#264653', '#2a9d8f', '#e9c46a', '#f4a261', '#e76f51', '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f'
  ],
  brand: {
    googleBlue: '#4285F4',
    googleGreen: '#34A853',
    googleYellow: '#FBBC05',
    googleRed: '#EA4335',
    trustpilotGreen: '#00b67a',
    // Add more as needed
  },
  sidebarActiveBg: '#1B3848', // Sidebar active background (dark mode)
  sidebarActiveText: '#1e3a8a', // Sidebar active text (light mode)
};

export default Colors; 