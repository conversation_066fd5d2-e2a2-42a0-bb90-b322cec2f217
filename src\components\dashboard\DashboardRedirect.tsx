'use client';

import { useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

interface DashboardRedirectProps {
  returnUrl?: string;
}

export default function DashboardRedirect({
  returnUrl = '/dashboard',
}: DashboardRedirectProps) {
  const { isSignedIn, isLoaded } = useUser();
  const router = useRouter();

  useEffect(() => {
    if (!isLoaded) return;

    if (!isSignedIn) {
      // Redirect to sign-in with return URL
      const signInUrl = `/sign-in?redirect_url=${encodeURIComponent(returnUrl)}`;
      router.push(signInUrl);
      return;
    }

    // Check subscription status
    const checkSubscription = async () => {
      try {
        const response = await fetch('/api/subscriptions/active');
        const data = await response.json();

        if (data.isActive) {
          // User has active subscription, go to dashboard
          router.push(returnUrl);
        } else {
          // No active subscription, redirect to pricing
          const pricingUrl = `/pricing?return_url=${encodeURIComponent(returnUrl)}`;
          router.push(pricingUrl);
        }
      } catch {
        const pricingUrl = `/pricing?return_url=${encodeURIComponent(returnUrl)}`;
        router.push(pricingUrl);
        toast.error("Failed to check subscription status");
      }
    };

    checkSubscription();
  }, [isSignedIn, isLoaded, returnUrl, router]);

  // Show loading state
  return (
    <div className="flex items-center justify-center min-h-screen bg-black">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
        <span className="text-white">Checking access...</span>
      </div>
    </div>
  );
}
