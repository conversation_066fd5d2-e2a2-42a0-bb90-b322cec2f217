import type React from 'react';
import { auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import { syncUserWithDatabase } from '@/lib/actions/user';
import { checkSubscriptionWithBypass } from '@/lib/subscription-check';

// Add a type for the user with organizations
type UserWithOrganizations = {
  id: string;
  clerkId: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  profileImageUrl: string | null;
  createdAt: Date;
  updatedAt: Date;
  role: string;
  lastActive: Date | null;
  status: string;
  organizations: { id: string; name: string }[];
};

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Check if user is authenticated
  const { userId } = await auth();

  if (!userId) {
    redirect('/sign-in');
  }

  // Ensure user exists in database
  let user: UserWithOrganizations | null = null;

  try {
    user = (await syncUserWithDatabase()) as UserWithOrganizations;
  } catch {
    // Swallow error silently for production readiness
    user = null;
  }

  // If user doesn't have an organization, redirect to onboarding
  if (
    !user ||
    !user.organizations ||
    user.organizations.length === 0
  ) {
    redirect('/onboarding');
  }

  // Check subscription status (with automatic bypass for subscription-related pages)
  // This provides instant redirects for better UX without loading states
  try {
    await checkSubscriptionWithBypass('/dashboard');
  } catch (error) {
    // If subscription check fails, continue - user will be redirected by the function
    console.error('Subscription check failed in dashboard layout:', error);
  }

  return (
    <div className="flex min-h-screen flex-col bg-background dark:bg-[#0B1739]">
      <main className="flex-1">{children}</main>
    </div>
  );
}
