"use server";

import {
  ExtractedInvoiceData,
  InvoiceFieldConfig,
  DocumentType,
} from "../types";

interface AnthropicProcessingResult {
  extractedData: ExtractedInvoiceData;
  confidence: number;
  confidenceByField?: Record<string, number>;
  suggestedCategories?: string[];
  vendorSuggestions?: string[];
}

/**
 * Process a document with Anthropic Claude API
 */
export async function processWithAnthropic(
  fileUrl: string,
  fieldsToExtract: InvoiceFieldConfig[],
  fileType: string,
  documentType: DocumentType = "INVOICE",
  organizationData?: {
    name?: string;
    industry?: string;
    size?: string;
    invoiceVolume?: string;
  },
  aiSettings?: {
    customInstructions?: string;
    confidenceThreshold?: number;
    preferredCategories?: string[];
    sampleInvoiceUrls?: string[];
  }
): Promise<AnthropicProcessingResult> {
  try {
    // Check if Anthropic API key is configured
    if (!process.env.ANTHROPIC_API_KEY) {
      throw new Error("Anthropic API key is not configured");
    }

    const fieldsDescription = fieldsToExtract
      .map((field) => `${field.label} (${field.description})`)
      .join("\n");

    // Build organization context string if data is provided
    let organizationContext = "";
    if (organizationData) {
      organizationContext = `
ORGANIZATION CONTEXT:
- Organization Name: ${organizationData.name || "Unknown"}
- Industry: ${organizationData.industry || "Unknown"}
- Organization Size: ${organizationData.size || "Unknown"}
- Invoice Volume: ${organizationData.invoiceVolume || "Unknown"}

This information should help you determine whether this is a PURCHASE invoice (where the organization is the buyer and needs to pay) or a PAYMENT invoice (where the organization is the seller and will receive payment).
      `;
    }

    // Build custom instructions if provided
    let customInstructions = "";
    if (aiSettings?.customInstructions) {
      customInstructions = `
CUSTOM PROCESSING INSTRUCTIONS:
${aiSettings.customInstructions}
      `;
    }

    // Enhanced prompt with more detailed instructions for Claude
    const prompt = `You are an expert ${documentType.toLowerCase()} data extraction system specializing in document understanding. Extract the following information from this ${fileType}:
      
${fieldsDescription}

${organizationContext}

${customInstructions}

DETAILED INSTRUCTIONS:
1. LANGUAGE HANDLING:
   - Identify the language of the document and extract data in its original form
   - For non-English documents, provide the data in both original and translated form
   - Make note of the detected language with ISO code (e.g., en, fr, es, de)

2. DATES:
   - Always convert dates to ISO format (YYYY-MM-DD)
   - Look for issue date (document date) and due date specifically
   - Be aware of different date formats (MM/DD/YYYY, DD/MM/YYYY, etc.)

3. AMOUNTS & CURRENCY:
   - Extract the TOTAL amount (including taxes) as a numeric value only
   - Identify the currency symbol and provide the ISO code (USD, EUR, GBP, etc.)
   - Extract tax amounts separately
   - For line items, capture: description, quantity, unit price, total price, tax rate, tax amount, discount, and product SKU if available

4. DOCUMENT SOURCE DETAILS:
   - Extract the full vendor/merchant/issuer name (company name)
   - Look for logos, letterheads, or headers to identify the source
   - Note: Some documents may use logos instead of text names

5. DOCUMENT IDENTIFIER:
   - Look for: "Invoice #", "Invoice Number", "Contract ID", "Certificate Number", etc.
   - Extract the exact document identifier
   - Be careful not to confuse with other numbers like account numbers

6. DOCUMENT TYPE & PURPOSE DETECTION:
   - Determine the exact document type (invoice, contract, certificate, letter, etc.)
   - For invoices, determine if this is a PURCHASE invoice (where ${organizationData?.name || "the organization"} is the BUYER and needs to pay) 
     or a PAYMENT invoice (where ${organizationData?.name || "the organization"} is the SELLER and will receive payment)
   
   - Look for these key indicators for invoices:
     * PURCHASE invoice hints (organization pays):
       - "Bill To:" or "Ship To:" field shows ${organizationData?.name || "the organization name"}
       - The organization appears as a customer or recipient
       - Terms like "Payment Due", "Due Date", "Please pay" indicate organization needs to pay
       - The document has a "Customer" number referring to the organization
     
     * PAYMENT invoice hints (organization receives money):
       - "From:" field shows ${organizationData?.name || "the organization name"}
       - Organization logo is prominently displayed in the letterhead
       - Terms like "Invoice issued by", "Sold by", "Vendor:" show the organization as seller
       - Organization's VAT/Tax ID is displayed as the seller

   - Look for the organization name (${organizationData?.name || "organization name"}) in different sections
   - Analyze whether the document is formatted as something TO PAY or as a RECEIPT of payment already made
   - Set documentType field appropriately, with INVOICE_PURCHASE or INVOICE_PAYMENT for invoices

7. LAYOUT & STRUCTURE UNDERSTANDING:
   - Pay attention to the document's visual layout and structure
   - Identify distinct sections like header, body, footer
   - Understand tabular data and form fields
   - Extract data with awareness of its position in the document

8. LINE ITEMS / DETAILED ENTRIES:
   - Extract ALL individual products/services/items listed in detail
   - Include description, quantity, unit price, total price, tax rate, tax amount, discount, and product code (if available)
   - Scan tables carefully for this information
   - Preserve exact descriptions and quantities
   - Convert all numeric values to numbers (not strings)

9. ADVANCED RECOGNITION:
   - Handle handwritten text when possible
   - Be aware of watermarks and background patterns
   - For low-quality images, use context to make best guesses
   - Read text in stamps and seals when relevant

10. CONFIDENCE ASSESSMENT:
    - Provide a confidence score (0-1) for each extracted field
    - If a field is not found or uncertain, mark it as null but explain why
    - For ambiguous fields, provide your best guess with a lower confidence score

OUTPUT FORMAT:
{
  "documentType": "document type (INVOICE, CONTRACT, CERTIFICATE, etc.)",
  "invoiceType": "PURCHASE" or "PAYMENT" (if document is an invoice),
  "invoiceNumber": "extracted identifier",
  "vendorName": "extracted vendor/issuer name",
  "issueDate": "extracted date in YYYY-MM-DD format",
  "dueDate": "extracted date in YYYY-MM-DD format",
  "amount": numeric value only (e.g., 1234.56),
  "currency": "extracted currency code (e.g., USD, EUR)",
  "items": [
    {
      "description": "line item description",
      "quantity": numeric value,
      "unitPrice": numeric value,
      "totalPrice": numeric value,
      "taxRate": optional numeric value,
      "taxAmount": optional numeric value,
      "discount": optional numeric value,
      "productSku": "optional product code/SKU"
    }
  ],
  "tax": numeric value,
  "notes": "extracted notes or payment instructions",
  "language": "detected language code (e.g., en, fr, es)",
  "confidence": confidence score between 0 and 1 for the overall extraction quality,
  "confidenceByField": {
    "invoiceNumber": 0.95,
    "vendorName": 0.98,
    // confidence scores for each extracted field
  }
}

If you cannot determine a value for a field, use null. DO NOT MAKE UP DATA. If you're uncertain, provide your best guess but lower the confidence score accordingly.
`;

    // Call Anthropic API using the fetch API
    const response = await fetch("https://api.anthropic.com/v1/messages", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": process.env.ANTHROPIC_API_KEY,
        "anthropic-version": "2023-06-01",
      },
      body: JSON.stringify({
        model: "claude-3-opus-20240229",
        max_tokens: 4096,
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: prompt,
              },
              {
                type: "image",
                source: {
                  type: "url",
                  url: fileUrl,
                },
              },
            ],
          },
        ],
        temperature: 0.1, // Lower temperature for more deterministic results
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(
        `Anthropic API error: ${error.error?.message || "Unknown error"}`
      );
    }

    const data = await response.json();

    // Extract the response content
    const responseText = data.content?.[0]?.text || "";

    if (!responseText) {
      throw new Error("No response from Anthropic");
    }

    // Extract the JSON part of the response
    const jsonMatch = responseText.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error("Could not parse JSON from Anthropic response");
    }

    const extractedData = JSON.parse(jsonMatch[0]);

    // Handle suggested categories
    const suggestedCategories = extractedData.suggestedCategories || [];

    // Generate vendor suggestions based on the extracted vendor name
    const vendorSuggestions = await suggestVendorsFromAnthropic(
      extractedData.vendorName || "",
      []
    );

    return {
      extractedData,
      confidence: extractedData.confidence || 0.8,
      confidenceByField: extractedData.confidenceByField,
      suggestedCategories,
      vendorSuggestions,
    };
  } catch (error) {
    throw error;
  }
}

/**
 * Generate vendor suggestions based on extracted data
 */
export async function suggestVendorsFromAnthropic(
  vendorName: string,
  existingVendors: string[] = []
): Promise<string[]> {
  if (!vendorName) return existingVendors.slice(0, 5);

  // Start with the extracted vendor
  const suggestions = [vendorName];

  // Find close matches from existing vendors
  if (existingVendors.length > 0) {
    const vendorLower = vendorName.toLowerCase();
    const matches = existingVendors.filter((vendor) => {
      const vendorLowerExisting = vendor.toLowerCase();
      return (
        vendorLowerExisting.includes(vendorLower) ||
        vendorLower.includes(vendorLowerExisting)
      );
    });

    suggestions.push(...matches);
  }

  // Remove duplicates and return
  return [...new Set(suggestions)].slice(0, 5);
}
