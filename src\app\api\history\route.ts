import { currentUser } from '@clerk/nextjs/server';
import db from '@/db/db';

export async function GET() {
  const user = await currentUser();

  if (!user) {
    return Response.json('Unauthorized!', { status: 401 });
  }

  // Get user from database by email
  const dbUser = await db.user.findUnique({
    where: { email: user.emailAddresses[0].emailAddress }
  });

  if (!dbUser) {
    return Response.json('User not found', { status: 404 });
  }

  // Get chats for user
  const chats = await db.chat.findMany({
    where: { userId: dbUser.id },
    orderBy: { createdAt: 'desc' }
  });

  return Response.json(chats);
}
