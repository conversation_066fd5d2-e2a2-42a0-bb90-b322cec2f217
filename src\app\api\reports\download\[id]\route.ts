import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import db from '@/db/db';

export async function GET(req: NextRequest, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    const { userId: clerkUserId } = await auth();
    
    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Get the user from the database
    const user = await db.user.findUnique({
      where: { clerkId: clerkUserId },
    });
    
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    
    const { id } = params;
    
    // Find the report
    const report = await db.report.findUnique({
      where: { 
        id,
        userId: user.id 
      }
    });
    
    if (!report) {
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }
    
    // Check if the report has a file URL
    if (!report.fileUrl) {
      // If not, redirect to the generate endpoint
      const url = new URL(req.url);
      const format = url.searchParams.get('format') || 'pdf';
      return NextResponse.redirect(new URL(`/api/reports/${id}?format=${format}`, req.url));
    }
    
    // Log the download
    await db.reportDownload.create({
      data: {
        reportId: id,
        userId: user.id,
        downloadedAt: new Date(),
      },
    });
    
    // Redirect to the file URL
    return NextResponse.redirect(report.fileUrl);
  } catch (error) {
    console.error('Error downloading report:', error);
    return NextResponse.json(
      { error: 'Failed to download report' },
      { status: 500 }
    );
  }
}
