'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Brain, 
  Clock, 
  Zap, 
  TrendingUp, 
  Database,
  CheckCircle,
  AlertCircle,
  Activity
} from 'lucide-react';

interface SystemOverviewProps {
  status: {
    overall: 'excellent' | 'good' | 'fair' | 'poor';
    systemHealth: {
      version: string;
      uptime: string;
    };
    components: {
      performance: {
        avgResponseTime: number;
        successRate: number;
      };
      cache: {
        hitRate: number;
      };
    };
  };
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'excellent': return 'text-green-600 bg-green-100';
    case 'good': return 'text-blue-600 bg-blue-100';
    case 'fair': return 'text-yellow-600 bg-yellow-100';
    case 'poor': return 'text-red-600 bg-red-100';
    default: return 'text-gray-600 bg-gray-100';
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'excellent': return <CheckCircle className="h-4 w-4" />;
    case 'good': return <CheckCircle className="h-4 w-4" />;
    case 'fair': return <AlertCircle className="h-4 w-4" />;
    case 'poor': return <AlertCircle className="h-4 w-4" />;
    default: return <Activity className="h-4 w-4" />;
  }
};

export function SystemOverview({ status }: SystemOverviewProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Billix AI System v{status.systemHealth.version}
          </div>
          <Badge className={getStatusColor(status.overall)}>
            {getStatusIcon(status.overall)}
            {status.overall.toUpperCase()}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <Clock className="h-8 w-8 mx-auto mb-2 text-blue-600" />
            <p className="text-sm text-gray-600">Uptime</p>
            <p className="font-semibold">{status.systemHealth.uptime}</p>
          </div>
          <div className="text-center">
            <Zap className="h-8 w-8 mx-auto mb-2 text-green-600" />
            <p className="text-sm text-gray-600">Response Time</p>
            <p className="font-semibold">{status.components.performance.avgResponseTime}ms</p>
          </div>
          <div className="text-center">
            <TrendingUp className="h-8 w-8 mx-auto mb-2 text-purple-600" />
            <p className="text-sm text-gray-600">Success Rate</p>
            <p className="font-semibold">{(status.components.performance.successRate * 100).toFixed(1)}%</p>
          </div>
          <div className="text-center">
            <Database className="h-8 w-8 mx-auto mb-2 text-orange-600" />
            <p className="text-sm text-gray-600">Cache Hit Rate</p>
            <p className="font-semibold">{(status.components.cache.hitRate * 100).toFixed(1)}%</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}