"use client"

import React from "react"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>Bar<PERSON>hart,
  CartesianGrid,
  XAxis,
  YAxis,
  Cell,
  ResponsiveContainer,
  LabelList
} from "recharts"

import { cn } from "@/lib/utils"
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent
} from "./chart"
import Colors from '../theme/Colors'

export interface BarChartProps {
  data: Record<string, unknown>[]
  x: string
  y: string | string[]
  className?: string
  colors?: string[]
  showLegend?: boolean
  showGrid?: boolean
  stackBars?: boolean
  horizontal?: boolean
  height?: number | string
  xLabel?: string
  yLabel?: string
  showValues?: boolean
  gradientBars?: boolean
  animationDuration?: number
  roundedBars?: boolean
  barGap?: number
}

// Enhanced color palette with gradients
const defaultColors = [
  { main: Colors.primary, gradient: [Colors.primary, Colors.primaryDark] },
  { main: Colors.accent, gradient: [Colors.accent, Colors.primary] },
  { main: Colors.error, gradient: [Colors.error, Colors.warning] },
  { main: Colors.success, gradient: [Colors.success, Colors.primary] },
  { main: Colors.warning, gradient: [Colors.warning, Colors.accent] },
  { main: Colors.info, gradient: [Colors.info, Colors.primary] },
];

export function BarChart({
  data = [],
  x,
  y,
  className,
  colors,
  showLegend = true,
  showGrid = true,
  stackBars = false,
  horizontal = false,
  xLabel,
  yLabel,
  showValues = false,
  gradientBars = true,
  animationDuration = 1000,
  roundedBars = true,
  barGap = 4
}: BarChartProps) {
  const yFields = Array.isArray(y) ? y : [y]
  const colorPalette = colors ? colors.map(color => ({ main: color, gradient: [color, color] })) : defaultColors;

  // Create chart config from the y fields
  const chartConfig = yFields.reduce((acc, field, index) => {
    acc[field] = {
      label: field.replace(/_/g, ' ').replace(/^\w/, c => c.toUpperCase()),
      color: colorPalette[index % colorPalette.length].main
    }
    return acc
  }, {} as Record<string, { label: string, color: string }>)

  // Create unique gradient IDs for each bar
  const gradientIds = yFields.map((field, index) =>
    `bar-gradient-${field.replace(/[^a-zA-Z0-9]/g, '')}-${index}`
  );

  return (
    <ChartContainer className={cn("rounded-lg", className)} config={chartConfig}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsBarChart
          data={data}
          layout={horizontal ? "vertical" : "horizontal"}
          margin={{ top: 20, right: 20, bottom: 25, left: 25 }}
          barGap={barGap}
          barCategoryGap={barGap * 2}
        >
          {/* Define gradients for bars */}
          <defs>
            {yFields.map((field, index) => (
              <linearGradient
                key={gradientIds[index]}
                id={gradientIds[index]}
                x1="0" y1="0" x2="0" y2="1"
              >
                <stop
                  offset="0%"
                  stopColor={colorPalette[index % colorPalette.length].gradient[0]}
                  stopOpacity={0.9}
                />
                <stop
                  offset="100%"
                  stopColor={colorPalette[index % colorPalette.length].gradient[1]}
                  stopOpacity={0.7}
                />
              </linearGradient>
            ))}
          </defs>

          {showGrid && (
            <CartesianGrid
              strokeDasharray="3 3"
              vertical={!horizontal}
              horizontal={horizontal}
              stroke={Colors.border}
              strokeOpacity={0.6}
            />
          )}

          {horizontal ? (
            <>
              <YAxis
                dataKey={x}
                type="category"
                axisLine={false}
                tickLine={false}
                tick={{ fill: Colors.text, fontSize: 12 }}
              />
              <XAxis
                type="number"
                axisLine={false}
                tickLine={false}
                tick={{ fill: Colors.text, fontSize: 12 }}
              />
            </>
          ) : (
            <>
              <XAxis
                dataKey={x}
                axisLine={false}
                tickLine={false}
                tick={{ fill: Colors.text, fontSize: 12 }}
                label={xLabel ? {
                  value: xLabel,
                  position: "bottom",
                  offset: 15,
                  style: { fill: Colors.text, fontSize: 14, fontWeight: 500 }
                } : undefined}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{ fill: Colors.text, fontSize: 12 }}
                label={yLabel ? {
                  value: yLabel,
                  angle: -90,
                  position: "left",
                  offset: -10,
                  style: { fill: Colors.text, fontSize: 14, fontWeight: 500 }
                } : undefined}
              />
            </>
          )}

          <ChartTooltip
            content={<ChartTooltipContent />}
            cursor={{ fill: 'rgba(0, 0, 0, 0.05)' }}
            wrapperStyle={{
              backgroundColor: Colors.background,
              border: `1px solid ${Colors.border}`,
              borderRadius: '6px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
            }}
          />

          {showLegend && (
            <ChartLegend
              content={<ChartLegendContent />}
              verticalAlign="top"
              align="center"
              wrapperStyle={{ paddingBottom: '10px' }}
            />
          )}

          {yFields.map((field, index) => (
            <Bar
              key={field}
              dataKey={field}
              fill={gradientBars ? `url(#${gradientIds[index]})` : colorPalette[index % colorPalette.length].main}
              stackId={stackBars ? "stack" : undefined}
              radius={roundedBars ? [4, 4, 0, 0] : [0, 0, 0, 0]}
              barSize={horizontal ? 20 : undefined}
              isAnimationActive={true}
              animationDuration={animationDuration}
              animationEasing="ease-in-out"
            >
              {showValues && (
                <LabelList
                  dataKey={field}
                  position="top"
                  style={{
                    fill: Colors.text,
                    fontSize: 11,
                    fontWeight: 500
                  }}
                  formatter={(value: number) => value.toLocaleString()}
                />
              )}

              {!stackBars && data.map((entry, entryIndex) => (
                <Cell
                  key={`cell-${entryIndex}`}
                  cursor="pointer"
                />
              ))}
            </Bar>
          ))}
        </RechartsBarChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}
