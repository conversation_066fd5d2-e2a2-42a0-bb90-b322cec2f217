"use client"

import * as React from "react"
import * as TabsPrimitive from "@radix-ui/react-tabs"
import Colors from "../theme/Colors"

import { cn } from "@/lib/utils"

const Tabs = TabsPrimitive.Root

const TabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>
>(({ className, ...props }, ref) => {
  return (
    <TabsPrimitive.List
      ref={ref}
      data-slot="tabs-list"
      className={cn(
        "inline-flex h-10 w-fit items-center justify-center rounded-lg p-1",
              // Clean, theme-aware background
      "bg-gray-50 dark:bg-gray-800/50",
      // Subtle borders that work well in both themes
      "border border-gray-200/50 dark:border-gray-700/50",
      // Light shadows
      "shadow-sm",
        className,
      )}
      {...props}
    />
  )
})
TabsList.displayName = TabsPrimitive.List.displayName

const TabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Trigger
    ref={ref}
    data-slot="tabs-trigger"
    className={cn(
      "inline-flex h-8 items-center justify-center gap-2 rounded-md px-3 py-1.5 text-sm font-medium whitespace-nowrap",
      // Enhanced transition with multiple properties
      "transition-all duration-200 ease-in-out",
      // Clean focus states using theme colors
      "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/50 focus-visible:ring-offset-1",
      // Disabled states
      "disabled:pointer-events-none disabled:opacity-50",
      // Inactive state - good contrast in both modes
      "text-gray-600 dark:text-slate-200",
      // Hover states - subtle and accessible
      "hover:text-gray-900 dark:hover:text-white",
      "hover:bg-gray-100 dark:hover:bg-blue-900/20",
      // Active state - uses theme colors but with better contrast
      "data-[state=active]:bg-primary data-[state=active]:text-primary-foreground",
      "data-[state=active]:shadow-sm data-[state=active]:font-medium",
      // Icon styling with theme-aware colors
      "[&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
      "data-[state=active]:[&_svg]:text-primary-foreground",
      className,
    )}
    {...props}
  />
))
TabsTrigger.displayName = TabsPrimitive.Trigger.displayName

const TabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Content
    ref={ref}
    className={cn(
      "mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
      className,
    )}
    {...props}
  />
))
TabsContent.displayName = TabsPrimitive.Content.displayName

export { Tabs, TabsList, TabsTrigger, TabsContent }
