'use server';

import { auth } from '@clerk/nextjs/server';
import { createOrUpdateOrganization } from '../services/user-service';
import { AISettings, OnboardingData } from '../types';
import { revalidatePath } from 'next/cache';

export async function completeOnboarding(data: OnboardingData) {
  try {
    const { userId: clerkUserId } = await auth();
    
    if (!clerkUserId) {
      throw new Error('Unauthorized: User not authenticated');
    }
    
    // First make sure the user exists in our database
    const { syncUserWithDatabase } = await import('./user');
    const dbUser = await syncUserWithDatabase();
    
    if (!dbUser || !dbUser.id) {
      throw new Error('Failed to find or create user in database');
    }

    // Create or update organization
    const organization = await createOrUpdateOrganization(dbUser.id, {
      name: data.organization.name,
      industry: data.organization.industry,
      size: data.organization.size,
      invoiceVolume: data.organization.invoiceVolume,
    });
    
    // Save AI settings directly to database instead of using the API
    const db = (await import('@/db/db')).default;
    
    // Extract document types and formats if provided
    const documentTypes = data.organization.documentTypes || [];
    const commonFormats = data.organization.commonFormats || [];
    
    // Extract custom vocabulary terms if provided
    const organizationVocabulary = data.organization.vocabularyTerms || [];
    
    // Prepare the extraction preferences based on organization details
    const extractionPreferences: Record<string, unknown> = {};
    
    // If industry specific preferences are available
    if (data.organization.industry) {
      // Set industry-specific preferences
      switch (data.organization.industry.toLowerCase()) {
        case 'healthcare':
          extractionPreferences.priorityFields = ['patientId', 'serviceDate', 'procedureCode'];
          extractionPreferences.dateFormat = 'MM/DD/YYYY';
          break;
        case 'construction':
          extractionPreferences.priorityFields = ['projectId', 'workOrder', 'materialCosts'];
          extractionPreferences.lineItemImportance = 'high';
          break;
        case 'technology':
          extractionPreferences.priorityFields = ['subscriptionId', 'planType', 'billingPeriod'];
          extractionPreferences.recurringBilling = true;
          break;
        default:
          extractionPreferences.standardFields = true;
      }
    }
    
    // Apply organization size preferences
    if (data.organization.size) {
      extractionPreferences.batchSize = data.organization.size === 'large' ? 'high' : 
                                       data.organization.size === 'medium' ? 'medium' : 'low';
    }
    
    // Apply invoice volume preferences
    if (data.organization.invoiceVolume) {
      extractionPreferences.processingPriority = data.organization.invoiceVolume === 'high' ? 'speed' : 'accuracy';
    }
    
    const aiSettings = {
      customInstructions: data.aiSettings.customInstructions,
      confidenceThreshold: data.aiSettings.confidenceThreshold,
      preferredCategories: data.aiSettings.preferredCategories || [],
      sampleInvoiceUrls: data.aiSettings.sampleInvoiceUrls || [],
      documentTypes: documentTypes,
      commonFormats: commonFormats,
      organizationVocabulary: organizationVocabulary,
      extractionPreferences: extractionPreferences,
      enableContinuousLearning: true,
      userId: dbUser.id
    };
    
    // Check if settings already exist
    const existingSettings = await db.aISettings.findUnique({
      where: { userId: dbUser.id }
    });
    
    if (existingSettings) {
      // Update existing settings
      await db.aISettings.update({
        where: { userId: dbUser.id },
        data: aiSettings as AISettings
      });
    } else {
      // Create new settings
      await db.aISettings.create({
        data: {
          customInstructions: aiSettings.customInstructions,
          confidenceThreshold: aiSettings.confidenceThreshold,
          preferredCategories: aiSettings.preferredCategories,
          sampleInvoiceUrls: aiSettings.sampleInvoiceUrls,
          documentTypes: aiSettings.documentTypes,
          commonFormats: aiSettings.commonFormats,
          organizationVocabulary: aiSettings.organizationVocabulary,
          extractionPreferences: JSON.stringify(aiSettings.extractionPreferences),
          enableContinuousLearning: aiSettings.enableContinuousLearning,
          userId: dbUser.id
        }
      });
    }
    
    // Revalidate paths to ensure fresh data
    revalidatePath('/dashboard');
    revalidatePath('/settings');

    return { success: true, organizationId: organization.id };
  } catch (error) {
    return { error: error instanceof Error ? error.message : 'Failed to complete onboarding process' };
  }
} 