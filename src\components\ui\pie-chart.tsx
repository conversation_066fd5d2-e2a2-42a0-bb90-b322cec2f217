"use client"

import React from "react"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Cell,
  ResponsiveContainer,
  Sector,
  Label
} from "recharts"

import { cn } from "@/lib/utils"
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent
} from "./chart"
import Colors from "../theme/Colors"

export interface PieChartProps {
  data: Array<Record<string, string | number>>
  className?: string
  colors?: string[]
  nameKey?: string
  valueKey?: string
  showLegend?: boolean
  innerRadius?: number
  outerRadius?: number
  paddingAngle?: number
  dataLabels?: boolean
  donut?: boolean
  height?: number | string
  animationDuration?: number
  activeIndex?: number
  showActiveShape?: boolean
  showPercentage?: boolean
  showTotal?: boolean
  totalLabel?: string
  gradientColors?: boolean
  strokeWidth?: number
}

// Enhanced color palette with gradients
const defaultColors = [
  "#4361ee", "#4cc9f0", "#f72585", "#4d908e", "#f8961e", "#90be6d",
  "#577590", "#43aa8b", "#f94144", "#f3722c", "#277da1", "#9d4edd"
];

interface ActiveShapeProps {
  cx: number;
  cy: number;
  innerRadius: number;
  outerRadius: number;
  startAngle: number;
  endAngle: number;
  fill: string;
  percent: number;
  name: string;
  payload: Record<string, string | number>;
  value: number;
}

// Custom active shape for enhanced visualization
const renderActiveShape = (props: ActiveShapeProps) => {
  const {
    cx, cy, innerRadius, outerRadius, startAngle, endAngle,
    fill, percent, name
  } = props;

  return (
    <g>
      <Sector
        cx={cx}
        cy={cy}
        innerRadius={innerRadius}
        outerRadius={outerRadius + 6}
        startAngle={startAngle}
        endAngle={endAngle}
        fill={fill}
        stroke={Colors.textLight}
        strokeWidth={2}
      />
      <Sector
        cx={cx}
        cy={cy}
        startAngle={startAngle}
        endAngle={endAngle}
        innerRadius={outerRadius + 8}
        outerRadius={outerRadius + 12}
        fill={fill}
        stroke={Colors.textLight}
        strokeWidth={1}
      />
      {innerRadius > 0 && (
        <text x={cx} y={cy} textAnchor="middle" dominantBaseline="central">
          <tspan x={cx} y={cy - 10} fill={Colors.text} fontSize={14} fontWeight="500">
            {name}
          </tspan>
          <tspan x={cx} y={cy + 10} fill={Colors.info} fontSize={12}>
            {`${(percent * 100).toFixed(1)}%`}
          </tspan>
        </text>
      )}
    </g>
  );
};

interface CustomizedLabelProps {
  cx: number;
  cy: number;
  midAngle: number;
  innerRadius: number;
  outerRadius: number;
  percent: number;
  name: string;
}

export function PieChart({
  data = [],
  className,
  colors,
  nameKey = "name",
  valueKey = "value",
  showLegend = true,
  innerRadius = 0,
  outerRadius = 80,
  paddingAngle = 2,
  dataLabels = false,
  donut = false,
  animationDuration = 1000,
  activeIndex,
  showActiveShape = false,
  showPercentage = true,
  showTotal = false,
  totalLabel = "Total",
  gradientColors = true,
  strokeWidth = 2
}: PieChartProps) {
  const [activeIdx, setActiveIdx] = React.useState<number | undefined>(activeIndex);
  const colorPalette = colors || defaultColors;

  // Calculate total value for percentage and total display
  const total = data.reduce((sum, entry) => sum + (Number(entry[valueKey]) || 0), 0);

  // Create chart config
  const chartConfig = data.reduce((acc, item, index) => {
    acc[item[nameKey] as string] = {
      label: item[nameKey] as string,
      color: colorPalette[index % colorPalette.length]
    }
    return acc
  }, {} as Record<string, { label: string, color: string }>)

  // Create unique gradient IDs for each slice
  const gradientIds = data.map((_, index) => `pie-gradient-${index}`);

  // Custom label formatter for percentage display
  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, name }: CustomizedLabelProps) => {
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * Math.PI / 180);
    const y = cy + radius * Math.sin(-midAngle * Math.PI / 180);

    return (
      <text
        x={x}
        y={y}
        fill="#fff"
        textAnchor="middle"
        dominantBaseline="central"
        fontSize={12}
        fontWeight="500"
        stroke="#fff"
        strokeWidth={0.5}
        paintOrder="stroke"
      >
        {showPercentage ? `${(percent * 100).toFixed(0)}%` : name}
      </text>
    );
  };

  return (
    <ChartContainer className={cn("rounded-lg", className)} config={chartConfig}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsPieChart
          margin={{ top: 20, right: 20, bottom: 20, left: 20 }}
          onMouseLeave={() => setActiveIdx(undefined)}
        >
          {/* Define gradients for pie slices */}
          {gradientColors && (
            <defs>
              {data.map((_, index) => (
                <radialGradient
                  key={gradientIds[index]}
                  id={gradientIds[index]}
                  cx="50%"
                  cy="50%"
                  r="50%"
                  fx="50%"
                  fy="50%"
                >
                  <stop
                    offset="0%"
                    stopColor={colorPalette[index % colorPalette.length]}
                    stopOpacity={0.9}
                  />
                  <stop
                    offset="100%"
                    stopColor={colorPalette[index % colorPalette.length]}
                    stopOpacity={0.7}
                  />
                </radialGradient>
              ))}
            </defs>
          )}

          <ChartTooltip
            content={<ChartTooltipContent indicator="dot" />}
            wrapperStyle={{
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              border: `1px solid ${Colors.border}`,
              borderRadius: '6px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
            }}
          />

          {showLegend && (
            <ChartLegend
              content={<ChartLegendContent />}
              verticalAlign="bottom"
              align="center"
              wrapperStyle={{ paddingBottom: '10px' }}
            />
          )}

          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={dataLabels && !showActiveShape}
            label={dataLabels && !showActiveShape ? renderCustomizedLabel : false}
            innerRadius={donut ? Math.max(0, outerRadius - 30) : innerRadius}
            outerRadius={outerRadius}
            paddingAngle={paddingAngle}
            dataKey={valueKey}
            nameKey={nameKey}
            animationDuration={animationDuration}
            animationEasing="ease-in-out"
            activeIndex={activeIdx}
            activeShape={showActiveShape ? (renderActiveShape as (props: unknown) => React.JSX.Element) : undefined}
            onMouseEnter={(_, index) => setActiveIdx(index)}
          >
            {data.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={gradientColors ? `url(#${gradientIds[index]})` : colorPalette[index % colorPalette.length]}
                stroke="#fff"
                strokeWidth={strokeWidth}
                cursor="pointer"
              />
            ))}

            {/* Show total in the center for donut charts */}
            {showTotal && donut && (
              <Label
                content={({ viewBox }) => {
                  const { cx, cy } = viewBox as { cx: number; cy: number };
                  return (
                    <g>
                      <text x={cx} y={cy - 10} textAnchor="middle" dominantBaseline="central" fill={Colors.text} fontSize={14} fontWeight="500">
                        {totalLabel}
                      </text>
                      <text x={cx} y={cy + 10} textAnchor="middle" dominantBaseline="central" fill={Colors.info} fontSize={12}>
                        {total.toLocaleString()}
                      </text>
                    </g>
                  );
                }}
                position="center"
              />
            )}
          </Pie>
        </RechartsPieChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}