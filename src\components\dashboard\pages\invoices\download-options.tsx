"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { FileImage, FileText, Download, Loader2, FileSpreadsheet } from "lucide-react";
import { toast } from "sonner";
import { generateInvoicePDF, generateInvoiceExcel } from "@/lib/generators/invoice-generator";

interface DownloadOptionsProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  invoice: {
    id: string;
    invoiceNumber?: string | null;
    originalFileUrl?: string | null;
    [key: string]: unknown;
  };
}

// Define a type that matches what the enhanced generators expect
interface EnhancedInvoiceForExport {
  id: string;
  invoiceNumber?: string | null;
  status?: string;
  amount?: number | null;
  currency?: string | null;
  issueDate?: Date | null;
  dueDate?: Date | null;
  vendorName?: string | null;
  notes?: string | null;
  categoryId?: string | null;
  extractedData?: Record<string, unknown>;
  lineItems?: Array<Record<string, unknown>>;
  createdAt: Date;
  updatedAt: Date;
  [key: string]: unknown;
}

export function DownloadOptions({
  isOpen,
  onOpenChange,
  invoice,
}: DownloadOptionsProps) {
  const [activeTab, setActiveTab] = useState("image");
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedFields, setSelectedFields] = useState({
    includeVendorDetails: true,
    includeCustomerDetails: true,
    includeLineItems: true,
    includeFinancialDetails: true,
    includePaymentDetails: true,
    includeNotes: true,
    includeMetadata: false,
    includeAuditInfo: false,
  });

  const handleFieldChange = (field: string, checked: boolean) => {
    setSelectedFields((prev) => ({
      ...prev,
      [field]: checked,
    }));
  };

  const downloadOriginalImage = () => {
    if (!invoice.originalFileUrl) {
      toast.error("No original document available to download.");
      return;
    }

    // Create a temporary anchor element
    const link = document.createElement("a");
    link.href = invoice.originalFileUrl;

    // Extract filename from URL or use invoice number
    const filename =
      invoice.originalFileUrl.split("/").pop() ||
      `invoice-${invoice.invoiceNumber || invoice.id}.pdf`;

    link.setAttribute("download", filename);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    onOpenChange(false);
  };

  const generatePdf = async () => {
    try {
      setIsGenerating(true);

      // Convert invoice to enhanced format
      const enhancedInvoice = {
        ...invoice,
        extractedData: invoice.extractedData as Record<string, unknown> | undefined
      } as EnhancedInvoiceForExport;
      
      // Generate PDF with selected options
      const fileBuffer = await generateInvoicePDF([enhancedInvoice], selectedFields);
      
      // Create a blob from the buffer
      const blob = new Blob([new Uint8Array(fileBuffer)], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);

      // Create a temporary anchor element to download the file
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute(
        "download",
        `invoice-${invoice.invoiceNumber || invoice.id}.pdf`
      );
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the URL object
      URL.revokeObjectURL(url);

      toast.success("Your invoice PDF has been generated and downloaded.");

      onOpenChange(false);
    } catch (error) {
      console.error("PDF generation error:", error);
      toast.error("Failed to generate PDF. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  const generateExcel = async () => {
    try {
      setIsGenerating(true);

      // Convert invoice to enhanced format
      const enhancedInvoice = {
        ...invoice,
        extractedData: invoice.extractedData as Record<string, unknown> | undefined
      } as EnhancedInvoiceForExport;
      
      // Generate Excel with selected options
      const fileBuffer = await generateInvoiceExcel([enhancedInvoice], selectedFields);
      
      // Create a blob from the buffer
      const blob = new Blob([new Uint8Array(fileBuffer)], { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });
      const url = URL.createObjectURL(blob);

      // Create a temporary anchor element to download the file
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute(
        "download",
        `invoice-${invoice.invoiceNumber || invoice.id}.xlsx`
      );
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the URL object
      URL.revokeObjectURL(url);

      toast.success("Your invoice Excel file has been generated and downloaded.");

      onOpenChange(false);
    } catch (error) {
      console.error("Excel generation error:", error);
      toast.error("Failed to generate Excel file. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Download Invoice</DialogTitle>
          <DialogDescription>
            Choose how you want to download this invoice.
          </DialogDescription>
        </DialogHeader>

        <Tabs
          defaultValue={activeTab}
          onValueChange={setActiveTab}
          className="w-full"
        >
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="image" disabled={!invoice.originalFileUrl}>
              <FileImage className="mr-2 h-4 w-4" />
              Original
            </TabsTrigger>
            <TabsTrigger value="pdf">
              <FileText className="mr-2 h-4 w-4" />
              PDF
            </TabsTrigger>
            <TabsTrigger value="excel">
              <FileSpreadsheet className="mr-2 h-4 w-4" />
              Excel Export
            </TabsTrigger>
          </TabsList>

          <TabsContent value="image" className="mt-4">
            <Card>
              <CardContent className="pt-6">
                <div className="text-center mb-4">
                  <FileImage className="h-12 w-12 mx-auto text-primary/80" />
                  <h3 className="mt-2 font-medium">
                    Download Original Document
                  </h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    Download the original invoice document as it was uploaded.
                  </p>
                </div>

                {!invoice.originalFileUrl && (
                  <div className="bg-amber-50 dark:bg-amber-950/20 text-amber-800 dark:text-amber-300 p-3 rounded-md text-sm mb-4">
                    No original document is available for this invoice.
                  </div>
                )}

                <Button
                  className="w-full"
                  onClick={downloadOriginalImage}
                  disabled={!invoice.originalFileUrl}
                >
                  <Download className="mr-2 h-4 w-4" />
                  Download Original
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="pdf" className="mt-4">
            <Card>
              <CardContent className="pt-6">
                <div className="text-center mb-4">
                  <FileText className="h-12 w-12 mx-auto text-primary/80" />
                  <h3 className="mt-2 font-medium">Generate PDF</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    Create a comprehensive PDF with all invoice details, line items, and professional formatting.
                  </p>
                </div>

                <div className="space-y-4 mb-6">
                  <div className="text-sm font-medium">Include in PDF:</div>

                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeVendorDetails"
                        checked={selectedFields.includeVendorDetails}
                        onCheckedChange={(checked) =>
                          handleFieldChange("includeVendorDetails", checked as boolean)
                        }
                      />
                      <Label htmlFor="includeVendorDetails">Vendor Information</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeCustomerDetails"
                        checked={selectedFields.includeCustomerDetails}
                        onCheckedChange={(checked) =>
                          handleFieldChange("includeCustomerDetails", checked as boolean)
                        }
                      />
                      <Label htmlFor="includeCustomerDetails">Customer Information</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeLineItems"
                        checked={selectedFields.includeLineItems}
                        onCheckedChange={(checked) =>
                          handleFieldChange("includeLineItems", checked as boolean)
                        }
                      />
                      <Label htmlFor="includeLineItems">Line Items & Details</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeFinancialDetails"
                        checked={selectedFields.includeFinancialDetails}
                        onCheckedChange={(checked) =>
                          handleFieldChange("includeFinancialDetails", checked as boolean)
                        }
                      />
                      <Label htmlFor="includeFinancialDetails">Financial Breakdown</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includePaymentDetails"
                        checked={selectedFields.includePaymentDetails}
                        onCheckedChange={(checked) =>
                          handleFieldChange("includePaymentDetails", checked as boolean)
                        }
                      />
                      <Label htmlFor="includePaymentDetails">Payment Information</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeNotes"
                        checked={selectedFields.includeNotes}
                        onCheckedChange={(checked) =>
                          handleFieldChange("includeNotes", checked as boolean)
                        }
                      />
                      <Label htmlFor="includeNotes">Notes & Terms</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeMetadata"
                        checked={selectedFields.includeMetadata}
                        onCheckedChange={(checked) =>
                          handleFieldChange("includeMetadata", checked as boolean)
                        }
                      />
                      <Label htmlFor="includeMetadata">Processing Metadata</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeAuditInfo"
                        checked={selectedFields.includeAuditInfo}
                        onCheckedChange={(checked) =>
                          handleFieldChange("includeAuditInfo", checked as boolean)
                        }
                      />
                      <Label htmlFor="includeAuditInfo">Audit Information</Label>
                    </div>
                  </div>
                </div>

                <Button
                  className="w-full"
                  onClick={generatePdf}
                  disabled={isGenerating}
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Generating PDF...
                    </>
                  ) : (
                    <>
                      <FileText className="mr-2 h-4 w-4" />
                      Generate PDF
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="excel" className="mt-4">
            <Card>
              <CardContent className="pt-6">
                <div className="text-center mb-4">
                  <FileSpreadsheet className="h-12 w-12 mx-auto text-primary/80" />
                  <h3 className="mt-2 font-medium">Generate Excel Export</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    Create a comprehensive Excel workbook with multiple sheets containing all invoice data, line items, vendor details, and analytics.
                  </p>
                </div>

                <div className="space-y-4 mb-6">
                  <div className="text-sm font-medium">Excel Export Features:</div>
                  
                  <div className="bg-muted/30 p-3 rounded-md">
                    <div className="text-sm space-y-2">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span>Invoice Summary Sheet</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span>Comprehensive Line Items</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                        <span>Vendor & Customer Details</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                        <span>Financial Analysis</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                        <span>Payment Information</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-pink-500 rounded-full"></div>
                        <span>Notes & Terms</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="excelIncludeVendorDetails"
                        checked={selectedFields.includeVendorDetails}
                        onCheckedChange={(checked) =>
                          handleFieldChange("includeVendorDetails", checked as boolean)
                        }
                      />
                      <Label htmlFor="excelIncludeVendorDetails">Vendor Details Sheet</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="excelIncludeCustomerDetails"
                        checked={selectedFields.includeCustomerDetails}
                        onCheckedChange={(checked) =>
                          handleFieldChange("includeCustomerDetails", checked as boolean)
                        }
                      />
                      <Label htmlFor="excelIncludeCustomerDetails">Customer Details Sheet</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="excelIncludeFinancialDetails"
                        checked={selectedFields.includeFinancialDetails}
                        onCheckedChange={(checked) =>
                          handleFieldChange("includeFinancialDetails", checked as boolean)
                        }
                      />
                      <Label htmlFor="excelIncludeFinancialDetails">Financial Analysis Sheet</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="excelIncludePaymentDetails"
                        checked={selectedFields.includePaymentDetails}
                        onCheckedChange={(checked) =>
                          handleFieldChange("includePaymentDetails", checked as boolean)
                        }
                      />
                      <Label htmlFor="excelIncludePaymentDetails">Payment Details Sheet</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="excelIncludeNotes"
                        checked={selectedFields.includeNotes}
                        onCheckedChange={(checked) =>
                          handleFieldChange("includeNotes", checked as boolean)
                        }
                      />
                      <Label htmlFor="excelIncludeNotes">Notes & Terms Sheet</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="excelIncludeMetadata"
                        checked={selectedFields.includeMetadata}
                        onCheckedChange={(checked) =>
                          handleFieldChange("includeMetadata", checked as boolean)
                        }
                      />
                      <Label htmlFor="excelIncludeMetadata">Metadata Sheet</Label>
                    </div>
                  </div>
                </div>

                <Button
                  className="w-full"
                  onClick={generateExcel}
                  disabled={isGenerating}
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Generating Excel...
                    </>
                  ) : (
                    <>
                      <FileSpreadsheet className="mr-2 h-4 w-4" />
                      Generate Excel Export
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
