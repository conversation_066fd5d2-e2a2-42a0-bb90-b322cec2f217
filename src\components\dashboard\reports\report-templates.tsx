"use client";

import { useState, useEffect } from "react";
import type React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Users,
  DollarSign,
  Plus,
  Loader2,
} from "lucide-react";
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { getReportTemplates, createReportFromTemplate } from "@/lib/actions/reports";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

// Define a simplified template interface that matches what we need
interface ReportTemplate {
  id: string;
  name: string;
  description?: string | null;
  type: string;
  visualizationType: string;
  isAIGenerated: boolean;
  createdAt: Date;
}

interface TemplateCardProps {
  template: ReportTemplate;
  loading?: boolean;
  onUseTemplate: (templateId: string, name: string, description?: string) => Promise<void>;
}

function TemplateCard({ template, loading, onUseTemplate }: TemplateCardProps) {
  const [open, setOpen] = useState(false);
  const [name, setName] = useState(template.name);
  const [description, setDescription] = useState(template.description || "");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (!name.trim()) {
      toast.error("Please enter a report name");
      return;
    }

    setIsSubmitting(true);
    try {
      await onUseTemplate(template.id, name, description);
      setOpen(false);
      toast.success("Report created from template");
    } catch {
      toast.error("Failed to create report from template");
    } finally {
      setIsSubmitting(false);
    }
  };

  let icon = <BarChart3 className="h-10 w-10 text-blue-500" />;
  switch (template.type) {
    case "EXPENSES":
      icon = <DollarSign className="h-10 w-10 text-emerald-500" />;
      break;
    case "VENDOR_ANALYSIS":
      icon = <Users className="h-10 w-10 text-purple-500" />;
      break;
    case "CATEGORY_ANALYSIS":
      icon = <PieChart className="h-10 w-10 text-orange-500" />;
      break;
    case "CASH_FLOW":
      icon = <LineChart className="h-10 w-10 text-blue-500" />;
      break;
    default:
      icon = <BarChart3 className="h-10 w-10 text-gray-500" />;
  }

  return (
    <Card className="flex h-full flex-col">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-4">
            <div className="rounded-full bg-gray-100 p-3">{icon}</div>
            <div>
              <CardTitle className="text-lg">{template.name}</CardTitle>
              {template.isAIGenerated && (
                <Badge className="mt-1">AI Generated</Badge>
              )}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="flex-1">
        <CardDescription className="text-sm">
          {template.description || "No description provided"}
        </CardDescription>
      </CardContent>
      <CardFooter className="pt-2">
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogTrigger asChild>
            <Button className="w-full" disabled={loading}>
              Use Template
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create Report from Template</DialogTitle>
              <DialogDescription>
                Customize your report details before creating it from the {template.name} template.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Report Name</Label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Enter report name"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description">Description (Optional)</Label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Enter report description"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSubmit} disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Create Report"
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardFooter>
    </Card>
  );
}

export function ReportTemplates() {
  const [templates, setTemplates] = useState<ReportTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    async function fetchTemplates() {
      try {
        const data = await getReportTemplates();
        setTemplates(data.map(item => ({
          ...item,
          type: item.reportType
        })));
      } catch {
        toast.error("Failed to load report templates");
      } finally {
        setLoading(false);
      }
    }

    fetchTemplates();
  }, []);

  const handleUseTemplate = async (templateId: string, name: string, description?: string) => {
    try {
      // Calculate a date range for the last 30 days as default
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30);
      
      const dateRange = {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString()
      };
      
      const report = await createReportFromTemplate(templateId, {
        name,
        description,
        dateRange
      });
      
      // Navigate to the new report
      router.push(`/dashboard/reports/view/${report.id}`);
    } catch (error) {
      throw error; // Rethrow to be handled by the caller
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-medium">Report Templates</h2>
          <Button disabled>
            <Plus className="mr-2 h-4 w-4" />
            Create Template
          </Button>
        </div>
        <div className="flex justify-center items-center py-20">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      </div>
    );
  }

  // If no templates are available, show default templates or empty state
  const displayTemplates = templates.length > 0 ? templates : [
    {
      id: "default-expenses",
      name: "Expenses Report",
      description: "Overview of expenses with category breakdown",
      type: "EXPENSES",
      visualizationType: "bar",
      isAIGenerated: false,
      createdAt: new Date(),
    },
    {
      id: "default-vendors",
      name: "Vendor Analysis",
      description: "Analysis of spending by vendor",
      type: "VENDOR_ANALYSIS",
      visualizationType: "pie",
      isAIGenerated: false,
      createdAt: new Date(),
    },
    {
      id: "default-categories",
      name: "Category Analysis",
      description: "Breakdown of spending by category",
      type: "CATEGORY_ANALYSIS",
      visualizationType: "pie",
      isAIGenerated: false,
      createdAt: new Date(),
    },
    {
      id: "default-cashflow",
      name: "Cash Flow Analysis",
      description: "Monthly cash flow trends and projections",
      type: "CASH_FLOW",
      visualizationType: "line",
      isAIGenerated: false,
      createdAt: new Date(),
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-medium">Report Templates</h2>
        <Button onClick={() => router.push("/dashboard/reports/new?template=true")}>
          <Plus className="mr-2 h-4 w-4" />
          Create Template
        </Button>
      </div>

      <Tabs defaultValue="all" className="w-full">
        <TabsList className="grid w-full grid-cols-4 lg:w-auto">
          <TabsTrigger value="all">All Templates</TabsTrigger>
          <TabsTrigger value="financial">Financial</TabsTrigger>
          <TabsTrigger value="operations">Operations</TabsTrigger>
          <TabsTrigger value="custom">Custom</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-6">
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {displayTemplates.map((template) => (
              <TemplateCard 
                key={template.id} 
                template={template} 
                loading={loading}
                onUseTemplate={handleUseTemplate}
              />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="financial" className="mt-6">
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {displayTemplates
              .filter((t) => ["EXPENSES", "CASH_FLOW"].includes(t.type))
              .map((template) => (
                <TemplateCard 
                  key={template.id} 
                  template={template}
                  loading={loading}
                  onUseTemplate={handleUseTemplate}
                />
              ))}
          </div>
        </TabsContent>

        <TabsContent value="operations" className="mt-6">
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {displayTemplates
              .filter((t) => ["VENDOR_ANALYSIS", "CATEGORY_ANALYSIS"].includes(t.type))
              .map((template) => (
                <TemplateCard 
                  key={template.id} 
                  template={template}
                  loading={loading}
                  onUseTemplate={handleUseTemplate}
                />
              ))}
          </div>
        </TabsContent>

        <TabsContent value="custom" className="mt-6">
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {displayTemplates
              .filter((t) => t.isAIGenerated)
              .map((template) => (
                <TemplateCard 
                  key={template.id} 
                  template={template}
                  loading={loading}
                  onUseTemplate={handleUseTemplate}
                />
              ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
