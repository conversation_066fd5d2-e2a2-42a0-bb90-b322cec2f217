'use client';

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  CheckCircle2,
  AlertCircle,
  XCircle,
  Share2,
  Trash2,
} from 'lucide-react';
import { InvoiceStatus } from '@/lib/types';

interface InvoiceActionsCardProps {
  invoice: {
    status: InvoiceStatus;
  };
  isUpdating: boolean;
  onStatusUpdate: (status: InvoiceStatus) => void;
  onShare?: () => void;
  onDelete: () => void;
}

export function InvoiceActionsCard({
  invoice,
  isUpdating,
  onStatusUpdate,
  onShare,
  onDelete,
}: InvoiceActionsCardProps) {
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">Actions</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        <Button
          variant="outline"
          className="w-full justify-start"
          disabled={invoice.status === 'PAID' || isUpdating}
          onClick={() => onStatusUpdate('PAID')}
        >
          <CheckCircle2 className="mr-2 h-4 w-4" />
          Mark as Paid
        </Button>
        <Button
          variant="outline"
          className="w-full justify-start"
          disabled={invoice.status === 'OVERDUE' || isUpdating}
          onClick={() => onStatusUpdate('OVERDUE')}
        >
          <AlertCircle className="mr-2 h-4 w-4" />
          Mark as Overdue
        </Button>
        <Button
          variant="outline"
          className="w-full justify-start"
          disabled={invoice.status === 'CANCELLED' || isUpdating}
          onClick={() => onStatusUpdate('CANCELLED')}
        >
          <XCircle className="mr-2 h-4 w-4" />
          Mark as Cancelled
        </Button>
        <Separator className="my-2" />
        <Button
          variant="outline"
          className="w-full justify-start"
          onClick={onShare}
        >
          <Share2 className="mr-2 h-4 w-4" />
          Share Invoice
        </Button>
        <Button
          variant="destructive"
          className="w-full justify-start"
          onClick={onDelete}
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Delete Invoice
        </Button>
      </CardContent>
    </Card>
  );
}