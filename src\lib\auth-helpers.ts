import { getCurrentUserId, isAuthorized } from '@/lib/clerk-helpers';

/**
 * Gets the current authenticated user ID or throws an error if not authenticated
 * @returns The current user ID
 * @throws Error if the user is not authenticated
 */
export async function getAuthenticatedUserId(): Promise<string> {
  const userId = await getCurrentUserId();

  if (!userId) {
    throw new Error('You must be authenticated to perform this action');
  }

  return userId;
}

/**
 * Checks if the user has permission to access a resource
 * @param resourceUserId The user ID associated with the resource
 * @returns True if the user has permission, false otherwise
 */
export async function hasPermission(resourceUserId: string): Promise<boolean> {
  return await isAuthorized(resourceUserId);
}

/**
 * Throws an error if the user doesn't have permission to access a resource
 * @param resourceUserId The user ID associated with the resource
 * @throws Error if the user doesn't have permission
 */
export async function checkPermission(resourceUserId: string): Promise<void> {
  const hasAccess = await hasPermission(resourceUserId);
  if (!hasAccess) {
    throw new Error('You do not have permission to access this resource');
  }
}
