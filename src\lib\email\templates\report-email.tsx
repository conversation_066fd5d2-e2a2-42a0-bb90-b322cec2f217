import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  <PERSON>,
  <PERSON><PERSON>,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from "@react-email/components";
import { Tailwind } from "@react-email/tailwind";

interface ReportEmailProps {
  reportName: string;
  pdfUrl: string;
  excelUrl: string;
  userName?: string;
  message?: string;
  appUrl?: string;
}

export const ReportEmail = ({
  reportName,
  pdfUrl,
  excelUrl,
  userName = "",
  message = "",
  appUrl = "https://billix.io",
}: ReportEmailProps) => {
  const previewText = `Your ${reportName} report is ready`;

  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Tailwind>
        <Body className="bg-white font-sans">
          <Container className="mx-auto py-5 px-5 max-w-md">
            <Img
              src={`${appUrl}/imgs/logo.png`}
              width="120"
              height="40"
              alt="Billix"
              className="mx-auto my-6"
            />
            <Heading className="text-xl font-bold text-center text-gray-800 my-6">
              Your Report is Ready
            </Heading>
            <Section className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6">
              <Text className="text-gray-700 text-base mb-4">
                {userName ? `Hello ${userName},` : "Hello,"}
              </Text>
              <Text className="text-gray-700 text-base mb-4">
                Your report <strong>{reportName}</strong> has been generated and
                is ready for you to view or download.
              </Text>
              {message && (
                <Text className="text-gray-700 text-base mb-4">{message}</Text>
              )}
              <Text className="text-gray-700 text-base mb-4">
                You can download the report in your preferred format using the
                links below:
              </Text>
              <Section className="mb-6 text-center">
                <Button
                  href={pdfUrl}
                  className="bg-blue-600 rounded-md text-white py-3 px-4 text-sm font-medium no-underline inline-block mx-2"
                >
                  Download PDF
                </Button>
                <Button
                  href={excelUrl}
                  className="bg-emerald-600 rounded-md text-white py-3 px-4 text-sm font-medium no-underline inline-block mx-2"
                >
                  Download Excel
                </Button>
              </Section>
              <Text className="text-gray-700 text-base">
                Alternatively, you can view all your reports directly in the
                application:
              </Text>
              <Section className="text-center my-6">
                <Button
                  href={`${appUrl}/dashboard/reports`}
                  className="bg-gray-800 rounded-md text-white py-3 px-6 text-sm font-medium no-underline inline-block"
                >
                  View in Dashboard
                </Button>
              </Section>
            </Section>
            <Hr className="border-gray-200 my-6" />
            <Text className="text-gray-500 text-sm text-center">
              © {new Date().getFullYear()} Billix, Inc. All rights reserved.
            </Text>
            <Text className="text-gray-500 text-xs text-center mt-4">
              If you didn&apos;t request this report, please ignore this email or{" "}
              <Link href={`${appUrl}/help`} className="text-blue-600 underline">
                contact support
              </Link>{" "}
              if you have questions.
            </Text>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
}; 