import { ArrowR<PERSON> } from "lucide-react";
import {
  <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import {
  Select, SelectContent, SelectItem, SelectTrigger, SelectValue
} from "@/components/ui/select";
import { FormData } from "../custom-report-builder";
import { ReportType } from "@prisma/client";
// Use string type for report types to avoid Prisma enum issues

interface ReportInfoStepProps {
  formData: FormData;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleSelectChange: (id: string, value: string) => void;
  onNext: () => void;
}

export function ReportInfoStep({
  formData,
  handleInputChange,
  handleSelectChange,
  onNext
}: ReportInfoStepProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Report Information</CardTitle>
        <CardDescription>
          Enter the basic information for your custom report
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid gap-4 sm:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="report-name">Report Name</Label>
            <Input
              id="report-name"
              placeholder="Enter report name"
              value={formData.name}
              onChange={handleInputChange}
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="report-type">Report Type</Label>
            <Select
              value={formData.type}
              onValueChange={(value) => handleSelectChange('type', value as ReportType)}
            >
              <SelectTrigger id="report-type">
                <SelectValue placeholder="Select report type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="EXPENSES">Expenses Report</SelectItem>
                <SelectItem value="VENDOR_ANALYSIS">Vendor Analysis</SelectItem>
                <SelectItem value="CATEGORY_ANALYSIS">Category Analysis</SelectItem>
                <SelectItem value="CASH_FLOW">Cash Flow Analysis</SelectItem>
                <SelectItem value="CUSTOM">Custom Report</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="report-description">Description</Label>
          <Textarea
            id="report-description"
            placeholder="Enter report description"
            value={formData.description}
            onChange={handleInputChange}
          />
        </div>

        <div className="grid gap-4 sm:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="dateRange">Date Range</Label>
            <Select
              value={formData.dateRange}
              onValueChange={(value) => handleSelectChange('dateRange', value)}
            >
              <SelectTrigger id="dateRange">
                <SelectValue placeholder="Select date range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="current-month">Current Month</SelectItem>
                <SelectItem value="previous-month">
                  Previous Month
                </SelectItem>
                <SelectItem value="current-quarter">
                  Current Quarter
                </SelectItem>
                <SelectItem value="previous-quarter">
                  Previous Quarter
                </SelectItem>
                <SelectItem value="year-to-date">Year to Date</SelectItem>
                <SelectItem value="custom">Custom Range</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              Reports are based on when invoices were added to the system, not their issue date.
            </p>
          </div>
          <div className="space-y-2">
            <Label htmlFor="outputFormat">Output Format</Label>
            <Select
              value={formData.outputFormat}
              onValueChange={(value) => handleSelectChange('outputFormat', value)}
            >
              <SelectTrigger id="outputFormat">
                <SelectValue placeholder="Select format" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="excel">Excel</SelectItem>
                <SelectItem value="pdf">PDF</SelectItem>
                <SelectItem value="both">Both</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" disabled>
          Back
        </Button>
        <Button onClick={onNext}>
          Next
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  );
}