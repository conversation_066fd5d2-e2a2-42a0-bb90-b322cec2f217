import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import db from '@/db/db';

export async function GET(req: NextRequest, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    // Ensure params is properly awaited
    const id = params.id;

    const { userId: clerkUserId } = await auth();

    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the user from the database
    const user = await db.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Find the report with its data
    const report = await db.report.findUnique({
      where: {
        id,
        userId: user.id
      },
      include: {
        data: true,
      }
    });

    if (!report) {
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }

    // Check if we have data
    if (!report.data || report.data.length === 0) {
      console.warn(`No data found for report ${id}. Attempting to regenerate data.`);

      // Try to regenerate the data
      try {
        await db.reportData.deleteMany({
          where: { reportId: id }
        });

        // Regenerate the report data
        const reportData = await import('@/lib/actions/report-data-generator');
        await reportData.generateReportData(
          user.id,
          id,
          report.reportType,
          report.startDate,
          report.endDate,
          undefined // No currency filter
        );

        // Fetch the report again with the new data
        const updatedReport = await db.report.findUnique({
          where: { id },
          include: { data: true }
        });

        if (updatedReport && updatedReport.data && updatedReport.data.length > 0) {
          return NextResponse.json(updatedReport);
        } else {
          return NextResponse.json(
            { error: 'Failed to generate report data' },
            { status: 500 }
          );
        }
      } catch (error) {
        console.error(`Error regenerating data for report ${id}:`, error);
        return NextResponse.json(
          { error: 'Failed to regenerate report data' },
          { status: 500 }
        );
      }
    }

    // Return the report with its data
    return NextResponse.json(report);
  } catch (error) {
    console.error('Error fetching report data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch report data' },
      { status: 500 }
    );
  }
}
