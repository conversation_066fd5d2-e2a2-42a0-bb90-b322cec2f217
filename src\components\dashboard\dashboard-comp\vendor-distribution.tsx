import {
  ChartLegend,
  ChartLegendItem,
} from '@/components/dashboard/dashboard-comp/chart';
import {
  <PERSON>,
  Pie,
  Pie<PERSON>hart,
  ResponsiveContainer,
  Tooltip,
} from 'recharts';
import { useEffect, useState } from 'react';
import { useTheme } from 'next-themes';
import { useDateFilter } from '../providers/date-filter-provider';

export function VendorDistribution() {
  const [data, setData] = useState<{ name: string; value: number }[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { theme } = useTheme();
  const { getDateRangeForAPI } = useDateFilter();

  // Enhanced theme-aware colors with better vibrancy and contrast
  const isDark = theme === 'dark';

  const getThemeColors = () => {
    if (isDark) {
      return [
        '#00d4ff', // Bright cyan
        '#ff6b6b', // Bright red
        '#4ecdc4', // Teal
        '#45b7d1', // Light blue
        '#96ceb4', // Mint green
        '#feca57', // Golden yellow
        '#ff9ff3', // Pink
        '#a29bfe', // Lavender
        '#fd79a8', // Rose
        '#e17055', // Orange
      ];
    } else {
      return [
        '#0066cc', // Deep blue
        '#e74c3c', // Red
        '#16a085', // Dark teal
        '#2980b9', // Blue
        '#27ae60', // Green
        '#f39c12', // Orange
        '#8e44ad', // Purple
        '#2c3e50', // Dark gray
        '#e91e63', // Pink
        '#ff5722', // Deep orange
      ];
    }
  };

  const COLORS = getThemeColors();

  useEffect(() => {
    setLoading(true);

    // Build API URL with date range parameters
    let apiUrl = '/api/invoices/stats';
    const dateRange = getDateRangeForAPI();
    if (dateRange) {
      const params = new URLSearchParams({
        from: dateRange.from,
        to: dateRange.to,
      });
      apiUrl += `?${params.toString()}`;
    }

    fetch(apiUrl)
      .then(async (res) => {
        if (!res.ok) throw new Error('Failed to fetch vendor data');
        const apiData = await res.json();
        const chartData = (apiData.topVendors || []).map(
          (v: { name: string; count: number }) => ({
            name: v.name,
            value: v.count,
          })
        );
        setData(chartData);
        setError(null);
      })
      .catch((err) => setError(err.message))
      .finally(() => setLoading(false));
  }, [getDateRangeForAPI]);

  if (loading)
    return (
      <div className="text-center py-8 text-muted-foreground">
        Loading vendors...
      </div>
    );
  if (error)
    return (
      <div className="text-center text-destructive py-8">{error}</div>
    );

  // Enhanced glow effects based on theme
  const glowColor = isDark ? '#00d4ff' : '#0066cc';
  const secondaryGlow = isDark ? '#ff6b6b' : '#e74c3c';

  return (
    <div className="h-full flex flex-col items-center justify-center px-4 py-6">
      {/* Enhanced multi-layer glow effects */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-2xl">
        <div
          className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-32 h-32 rounded-full opacity-12 blur-[50px] animate-pulse"
          style={{ backgroundColor: glowColor }}
        />
        <div
          className="absolute left-1/3 top-2/3 -translate-x-1/2 -translate-y-1/2 w-20 h-20 rounded-full opacity-8 blur-[30px] animate-pulse"
          style={{
            backgroundColor: secondaryGlow,
            animationDelay: '1.5s',
            animationDuration: '3s'
          }}
        />
      </div>

      {/* Centered chart container */}
      <div className="w-full max-w-[280px] aspect-square relative z-10 flex items-center justify-center">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius="75%"
              innerRadius="40%"
              fill="hsl(var(--muted))"
              dataKey="value"
              strokeWidth={2}
              stroke={isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}
              paddingAngle={2}
            >
              {data.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={COLORS[index % COLORS.length]}
                  className="transition-all duration-300 hover:opacity-80 hover:scale-105 cursor-pointer"
                  style={{
                    filter: `drop-shadow(0 2px 4px ${COLORS[index % COLORS.length]}40)`,
                  }}
                />
              ))}
            </Pie>
            <Tooltip
              content={<CustomTooltip active={false} payload={[]} />}
            />
          </PieChart>
        </ResponsiveContainer>
      </div>

      {/* Centered legend at the bottom */}
      <div className="mt-4 relative z-10 flex justify-center">
        <ChartLegend className="justify-center gap-3 flex-wrap max-w-xs">
          {data.map((entry, index) => (
            <ChartLegendItem
              key={`legend-${index}`}
              name={entry.name}
              color={COLORS[index % COLORS.length]}
              className="text-xs font-medium hover:text-foreground transition-colors cursor-pointer px-2 py-1 rounded-lg hover:bg-background/50"
              colorClassName="w-2.5 h-2.5 rounded-full shadow-sm"
              style={{
                boxShadow: `0 0 6px ${COLORS[index % COLORS.length]}40`
              }}
            />
          ))}
        </ChartLegend>
      </div>
    </div>
  );
}

function CustomTooltip({
  active,
  payload,
}: {
  active: boolean;
  payload: Array<{
    name: string;
    value: number;
    payload: { fill: string };
  }>;
}) {
  if (active && payload && payload.length) {
    const item = payload[0];
    return (
      <div className="relative">
        <div className="rounded-2xl border border-white/20 dark:border-gray-700/50 bg-white/95 dark:bg-gray-800/95 backdrop-blur-md p-4 shadow-2xl shadow-black/10 dark:shadow-black/30 min-w-[180px]">
          <div className="font-semibold text-gray-900 dark:text-gray-100 mb-3 text-center">
            {item.name}
          </div>

          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-2">
              <div
                className="w-4 h-4 rounded-full shadow-lg"
                style={{
                  backgroundColor: item.payload.fill,
                  boxShadow: `0 0 8px ${item.payload.fill}60`
                }}
              />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Count
              </span>
            </div>
            <div className="font-bold text-lg text-gray-900 dark:text-gray-100">
              {item.value}
            </div>
          </div>

          {/* Percentage calculation if available */}
          <div className="mt-2 text-center">
            <div className="text-xs text-gray-600 dark:text-gray-400">
              {/* You can add percentage calculation here */}
              Invoice count
            </div>
          </div>

          {/* Decorative border gradient */}
          <div
            className="absolute inset-0 rounded-2xl opacity-30 pointer-events-none"
            style={{
              background: `linear-gradient(135deg, ${item.payload.fill}20, transparent, ${item.payload.fill}10)`
            }}
          />
        </div>
      </div>
    );
  }

  return null;
}
