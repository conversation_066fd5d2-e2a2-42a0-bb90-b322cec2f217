import db from '@/db/db';
import { CacheEngine } from './cache-engine';

interface AdvancedAnalytics {
  financialHealth: {
    score: number; // 0-100
    status: 'excellent' | 'good' | 'fair' | 'poor';
    factors: string[];
    trends: 'improving' | 'stable' | 'declining';
  };
  businessInsights: {
    revenueGrowth: number;
    profitMargin: number;
    cashFlowHealth: number;
    vendorDiversity: number;
    paymentEfficiency: number;
  };
  recommendations: Array<{
    category: 'revenue' | 'costs' | 'efficiency' | 'risk';
    priority: 'high' | 'medium' | 'low';
    title: string;
    description: string;
    impact: string;
    effort: 'low' | 'medium' | 'high';
  }>;
  forecasts: {
    nextMonthRevenue: number;
    nextMonthExpenses: number;
    cashFlowProjection: number;
    confidence: number;
  };
  benchmarks: {
    industryComparison: string;
    performanceRating: number;
    keyMetrics: Record<string, { value: number; benchmark: number; status: 'above' | 'below' | 'at' }>;
  };
}

/**
 * Advanced Analytics Engine for comprehensive business intelligence
 */
export class AnalyticsEngine {
  
  /**
   * Generate comprehensive business analytics
   */
  static async generateAdvancedAnalytics(userId: string): Promise<AdvancedAnalytics> {
    const cacheKey = CacheEngine.generateKey('advanced-analytics', userId);
    
    return CacheEngine.getOrSet(
      cacheKey,
      'user-context', // 5-minute cache
      async () => {
        const [
          financialHealth,
          businessInsights,
          recommendations,
          forecasts,
          benchmarks
        ] = await Promise.all([
          this.calculateFinancialHealth(userId),
          this.generateBusinessInsights(userId),
          this.generateRecommendations(userId),
          this.generateForecasts(userId),
          this.generateBenchmarks(userId)
        ]);

        return {
          financialHealth,
          businessInsights,
          recommendations,
          forecasts,
          benchmarks
        };
      }
    );
  }

  /**
   * Calculate overall financial health score
   */
  private static async calculateFinancialHealth(userId: string): Promise<AdvancedAnalytics['financialHealth']> {
    try {
      const [invoiceStats, recentInvoices] = await Promise.all([
        db.invoice.aggregate({
          where: { userId },
          _count: { id: true },
          _sum: { amount: true }
        }),
        db.invoice.findMany({
          where: { 
            userId,
            createdAt: { gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) }
          }
        })
      ]);

      const totalInvoices = invoiceStats._count.id;
      const totalRevenue = invoiceStats._sum.amount || 0;
      
      // Calculate key health indicators
      const overdueRate = recentInvoices.filter(i => i.status === 'OVERDUE').length / Math.max(recentInvoices.length, 1);
      const avgInvoiceValue = totalRevenue / Math.max(totalInvoices, 1);
      const recentGrowth = this.calculateRecentGrowth(recentInvoices);
      const paymentEfficiency = 1 - overdueRate;
      const diversityScore = this.calculateVendorDiversity(recentInvoices);

      // Calculate composite score (0-100)
      const healthScore = Math.round(
        (paymentEfficiency * 30) + 
        (Math.min(recentGrowth / 0.2, 1) * 25) + 
        (diversityScore * 20) + 
        (Math.min(avgInvoiceValue / 1000, 1) * 25)
      ) * 100;

      const factors: string[] = [];
      if (overdueRate < 0.1) factors.push('Low overdue rate');
      if (recentGrowth > 0.1) factors.push('Strong revenue growth');
      if (diversityScore > 0.7) factors.push('Good vendor diversity');
      if (avgInvoiceValue > 500) factors.push('Healthy invoice values');

      let status: 'excellent' | 'good' | 'fair' | 'poor';
      if (healthScore >= 80) status = 'excellent';
      else if (healthScore >= 60) status = 'good';
      else if (healthScore >= 40) status = 'fair';
      else status = 'poor';

      const trends = recentGrowth > 0.05 ? 'improving' : recentGrowth < -0.05 ? 'declining' : 'stable';

      return {
        score: healthScore,
        status,
        factors,
        trends
      };
    } catch (error) {
      console.error('Financial health calculation error:', error);
      return {
        score: 50,
        status: 'fair',
        factors: ['Insufficient data'],
        trends: 'stable'
      };
    }
  }

  /**
   * Generate detailed business insights
   */
  private static async generateBusinessInsights(userId: string): Promise<AdvancedAnalytics['businessInsights']> {
    try {
      const recentInvoices = await db.invoice.findMany({
        where: { 
          userId,
          createdAt: { gte: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000) }
        }
      });

      const revenueGrowth = this.calculateRevenueGrowth(recentInvoices);
      const profitMargin = this.estimateProfitMargin(recentInvoices);
      const cashFlowHealth = this.calculateCashFlowHealth(recentInvoices);
      const vendorDiversity = this.calculateVendorDiversity(recentInvoices);
      const paymentEfficiency = this.calculatePaymentEfficiency(recentInvoices);

      return {
        revenueGrowth,
        profitMargin,
        cashFlowHealth,
        vendorDiversity,
        paymentEfficiency
      };
    } catch (error) {
      console.error('Business insights generation error:', error);
      return {
        revenueGrowth: 0,
        profitMargin: 0,
        cashFlowHealth: 0.5,
        vendorDiversity: 0.5,
        paymentEfficiency: 0.5
      };
    }
  }

  /**
   * Generate actionable recommendations
   */
  private static async generateRecommendations(userId: string): Promise<AdvancedAnalytics['recommendations']> {
    const recommendations: AdvancedAnalytics['recommendations'] = [];
    
    try {
      const recentInvoices = await db.invoice.findMany({
        where: { 
          userId,
          createdAt: { gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) }
        }
      });

      // Analyze for recommendations
      const overdueRate = recentInvoices.filter(i => i.status === 'OVERDUE').length / Math.max(recentInvoices.length, 1);
      const vendorConcentration = this.calculateVendorConcentration(recentInvoices);
      const avgPaymentTime = this.calculateAveragePaymentTime(recentInvoices);

      if (overdueRate > 0.2) {
        recommendations.push({
          category: 'efficiency',
          priority: 'high',
          title: 'Implement Automated Payment Reminders',
          description: `${Math.round(overdueRate * 100)}% of your invoices are overdue. Automated reminders could improve cash flow.`,
          impact: 'Reduce overdue invoices by 40-60%',
          effort: 'low'
        });
      }

      if (vendorConcentration > 0.6) {
        recommendations.push({
          category: 'risk',
          priority: 'medium',
          title: 'Diversify Vendor Base',
          description: 'High vendor concentration creates supply chain risk. Consider alternative suppliers.',
          impact: 'Reduce business risk and potentially lower costs',
          effort: 'medium'
        });
      }

      if (avgPaymentTime > 45) {
        recommendations.push({
          category: 'efficiency',
          priority: 'medium',
          title: 'Optimize Payment Terms',
          description: 'Average payment time is high. Consider offering early payment discounts.',
          impact: 'Improve cash flow by 15-25%',
          effort: 'low'
        });
      }

      // Revenue optimization recommendations
      const revenueGrowth = this.calculateRevenueGrowth(recentInvoices);
      if (revenueGrowth < 0.05) {
        recommendations.push({
          category: 'revenue',
          priority: 'high',
          title: 'Focus on Revenue Growth',
          description: 'Revenue growth is below optimal levels. Consider new customer acquisition or upselling.',
          impact: 'Increase revenue by 10-30%',
          effort: 'high'
        });
      }

    } catch (error) {
      console.error('Recommendations generation error:', error);
    }

    return recommendations.slice(0, 5); // Top 5 recommendations
  }

  /**
   * Generate financial forecasts
   */
  private static async generateForecasts(userId: string): Promise<AdvancedAnalytics['forecasts']> {
    try {
      const historicalData = await db.invoice.findMany({
        where: { 
          userId,
          createdAt: { gte: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000) }
        },
        orderBy: { createdAt: 'asc' }
      });

      const monthlyRevenue = this.groupByMonth(historicalData);
      const trend = this.calculateTrend(monthlyRevenue);
      
      const lastMonthRevenue = monthlyRevenue[monthlyRevenue.length - 1]?.total || 0;
      const nextMonthRevenue = Math.max(0, lastMonthRevenue * (1 + trend));
      
      // Estimate expenses (simplified - could be enhanced with actual expense data)
      const estimatedExpenses = nextMonthRevenue * 0.7; // Assume 70% expense ratio
      const cashFlowProjection = nextMonthRevenue - estimatedExpenses;
      
      // Calculate confidence based on data consistency
      const confidence = this.calculateForecastConfidence(monthlyRevenue);

      return {
        nextMonthRevenue,
        nextMonthExpenses: estimatedExpenses,
        cashFlowProjection,
        confidence
      };
    } catch (error) {
      console.error('Forecast generation error:', error);
      return {
        nextMonthRevenue: 0,
        nextMonthExpenses: 0,
        cashFlowProjection: 0,
        confidence: 0.1
      };
    }
  }

  /**
   * Generate industry benchmarks
   */
  private static async generateBenchmarks(userId: string): Promise<AdvancedAnalytics['benchmarks']> {
    // Simplified benchmarks - in production, this would use industry data
    const keyMetrics = {
      'Payment Efficiency': { value: 0.8, benchmark: 0.85, status: 'below' as const },
      'Revenue Growth': { value: 0.12, benchmark: 0.15, status: 'below' as const },
      'Vendor Diversity': { value: 0.7, benchmark: 0.6, status: 'above' as const },
      'Cash Flow Health': { value: 0.75, benchmark: 0.7, status: 'above' as const }
    };

    return {
      industryComparison: 'Small Business Services',
      performanceRating: 7.2,
      keyMetrics
    };
  }

  // Helper methods
  private static calculateRecentGrowth(invoices: any[]): number {
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);

    const recent = invoices.filter(i => i.createdAt >= thirtyDaysAgo);
    const previous = invoices.filter(i => i.createdAt >= sixtyDaysAgo && i.createdAt < thirtyDaysAgo);

    const recentTotal = recent.reduce((sum, i) => sum + (i.amount || 0), 0);
    const previousTotal = previous.reduce((sum, i) => sum + (i.amount || 0), 0);

    return previousTotal > 0 ? (recentTotal - previousTotal) / previousTotal : 0;
  }

  private static calculateVendorDiversity(invoices: any[]): number {
    const vendors = new Set(invoices.map(i => i.vendorName).filter(Boolean));
    const totalInvoices = invoices.length;
    
    if (totalInvoices === 0) return 0;
    
    // Calculate Herfindahl-Hirschman Index for diversity
    const vendorCounts = invoices.reduce((acc, inv) => {
      if (inv.vendorName) {
        acc[inv.vendorName] = (acc[inv.vendorName] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const hhi = Object.values(vendorCounts)
      .map(count => Math.pow(count / totalInvoices, 2))
      .reduce((sum, val) => sum + val, 0);

    return Math.max(0, 1 - hhi); // Convert to diversity score (higher = more diverse)
  }

  private static calculateRevenueGrowth(invoices: any[]): number {
    const monthlyData = this.groupByMonth(invoices);
    if (monthlyData.length < 2) return 0;

    const recent = monthlyData.slice(-3); // Last 3 months
    const previous = monthlyData.slice(-6, -3); // Previous 3 months

    const recentAvg = recent.reduce((sum, m) => sum + m.total, 0) / recent.length;
    const previousAvg = previous.reduce((sum, m) => sum + m.total, 0) / previous.length;

    return previousAvg > 0 ? (recentAvg - previousAvg) / previousAvg : 0;
  }

  private static estimateProfitMargin(invoices: any[]): number {
    // Simplified profit margin estimation
    // In a real scenario, this would use actual cost data
    const totalRevenue = invoices.reduce((sum, i) => sum + (i.amount || 0), 0);
    const estimatedCosts = totalRevenue * 0.7; // Assume 70% cost ratio
    return totalRevenue > 0 ? (totalRevenue - estimatedCosts) / totalRevenue : 0;
  }

  private static calculateCashFlowHealth(invoices: any[]): number {
    const pending = invoices.filter(i => i.status === 'PENDING');
    const overdue = invoices.filter(i => i.status === 'OVERDUE');
    const paid = invoices.filter(i => i.status === 'PAID');

    const pendingAmount = pending.reduce((sum, i) => sum + (i.amount || 0), 0);
    const overdueAmount = overdue.reduce((sum, i) => sum + (i.amount || 0), 0);
    const totalAmount = invoices.reduce((sum, i) => sum + (i.amount || 0), 0);

    if (totalAmount === 0) return 0.5;

    const healthScore = (pendingAmount - overdueAmount * 2) / totalAmount;
    return Math.max(0, Math.min(1, healthScore + 0.5));
  }

  private static calculatePaymentEfficiency(invoices: any[]): number {
    const totalInvoices = invoices.length;
    if (totalInvoices === 0) return 0.5;

    const paidOnTime = invoices.filter(i => i.status === 'PAID').length;
    return paidOnTime / totalInvoices;
  }

  private static calculateVendorConcentration(invoices: any[]): number {
    const vendorAmounts = invoices.reduce((acc, inv) => {
      if (inv.vendorName) {
        acc[inv.vendorName] = (acc[inv.vendorName] || 0) + (inv.amount || 0);
      }
      return acc;
    }, {} as Record<string, number>);

    const totalAmount = Object.values(vendorAmounts).reduce((sum, amount) => sum + amount, 0);
    if (totalAmount === 0) return 0;

    const maxVendorAmount = Math.max(...Object.values(vendorAmounts));
    return maxVendorAmount / totalAmount;
  }

  private static calculateAveragePaymentTime(invoices: any[]): number {
    const paidInvoices = invoices.filter(i => i.status === 'PAID' && i.paidDate && i.issueDate);
    if (paidInvoices.length === 0) return 30; // Default assumption

    const paymentTimes = paidInvoices.map(i => {
      const issued = new Date(i.issueDate!);
      const paid = new Date(i.paidDate!);
      return Math.ceil((paid.getTime() - issued.getTime()) / (24 * 60 * 60 * 1000));
    });

    return paymentTimes.reduce((sum, time) => sum + time, 0) / paymentTimes.length;
  }

  private static groupByMonth(invoices: any[]): Array<{ month: string; total: number }> {
    const monthlyData = invoices.reduce((acc, inv) => {
      const month = inv.createdAt.toISOString().slice(0, 7); // YYYY-MM
      acc[month] = (acc[month] || 0) + (inv.amount || 0);
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(monthlyData)
      .map(([month, total]) => ({ month, total }))
      .sort((a, b) => a.month.localeCompare(b.month));
  }

  private static calculateTrend(monthlyData: Array<{ month: string; total: number }>): number {
    if (monthlyData.length < 2) return 0;

    // Simple linear trend calculation
    const values = monthlyData.map(d => d.total);
    const n = values.length;
    const sumX = (n * (n - 1)) / 2;
    const sumY = values.reduce((sum, val) => sum + val, 0);
    const sumXY = values.reduce((sum, val, i) => sum + val * i, 0);
    const sumX2 = (n * (n - 1) * (2 * n - 1)) / 6;

    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    const avgY = sumY / n;

    return avgY > 0 ? slope / avgY : 0; // Return as percentage change
  }

  private static calculateForecastConfidence(monthlyData: Array<{ month: string; total: number }>): number {
    if (monthlyData.length < 3) return 0.3;

    // Calculate coefficient of variation (lower = more predictable = higher confidence)
    const values = monthlyData.map(d => d.total);
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    const stdDev = Math.sqrt(variance);
    const cv = mean > 0 ? stdDev / mean : 1;

    // Convert to confidence score (0-1)
    return Math.max(0.1, Math.min(0.9, 1 - cv));
  }
}
