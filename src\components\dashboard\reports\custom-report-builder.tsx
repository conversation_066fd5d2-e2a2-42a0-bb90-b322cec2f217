"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { createReport, generateReportData, createReportTemplate } from "@/lib/actions/reports";
import { Database, Save, Eye, Check, X } from "lucide-react";
import { StepIndicator } from "./components/StepIndicator";
import { ReportInfoStep } from "./components/ReportInfoStep";
import { DataSelectionStep } from "./components/DataSelectionStep";
import { CalculationsStep } from "./components/CalculationsStep";
import { VisualizationStep } from "./components/VisualizationStep";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { ReportType } from "@prisma/client";

// Define types for data structures
export interface DataField {
  name: string;
  label: string;
  type: string;
}

export interface DataSource {
  name: string;
  label: string;
  description: string;
  fields: DataField[];
  icon: React.ReactNode;
}

export interface FormData {
  name: string;
  description: string;
  type: string; // Use string instead of enum
  dateRange: string;
  outputFormat: string;
  visualizationType: string;
  selectedDataSource: string;
  selectedFields: string[];
  groupBy: string;
  sortBy: string;
  calculations: string[];
}

export function CustomReportBuilder() {
  const router = useRouter();
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);
  const [templateDialogOpen, setTemplateDialogOpen] = useState(false);
  const [templateData, setTemplateData] = useState({
    name: "",
    description: "",
    makeDefault: false
  });

  // Form state
  const [formData, setFormData] = useState<FormData>({
    name: "",
    description: "",
    type: "EXPENSES", // No need for type assertion
    dateRange: "current-month",
    outputFormat: "both",
    visualizationType: "bar",
    selectedDataSource: "invoices",
    selectedFields: [] as string[],
    groupBy: "",
    sortBy: "",
    calculations: [] as string[]
  });

  // Define available data sources
  const dataSources: DataSource[] = [
    {
      name: "invoices",
      label: "Invoices",
      description: "Invoice data including amounts, dates, and vendor information",
      icon: <Database className="h-8 w-8 text-blue-500" />,
      fields: [
        { name: "invoiceNumber", label: "Invoice Number", type: "Text" },
        { name: "createdAt", label: "Date Added to System", type: "Date" },
        { name: "issueDate", label: "Invoice Issue Date", type: "Date" },
        { name: "dueDate", label: "Due Date", type: "Date" },
        { name: "amount", label: "Amount", type: "Currency" },
        { name: "status", label: "Status", type: "Text" },
        { name: "vendorName", label: "Vendor Name", type: "Text" },
        { name: "category", label: "Category", type: "Text" },
        { name: "currency", label: "Currency", type: "Text" },
        { name: "notes", label: "Notes", type: "Text" }
      ]
    },
    {
      name: "vendors",
      label: "Vendors",
      description: "Vendor data including contact information and related invoices",
      icon: <Database className="h-8 w-8 text-emerald-500" />,
      fields: [
        { name: "name", label: "Vendor Name", type: "Text" },
        { name: "email", label: "Email", type: "Text" },
        { name: "phone", label: "Phone", type: "Text" },
        { name: "website", label: "Website", type: "Text" },
        { name: "address", label: "Address", type: "Text" }
      ]
    },
    {
      name: "categories",
      label: "Categories",
      description: "Categories for organizing and classifying invoices",
      icon: <Database className="h-8 w-8 text-purple-500" />,
      fields: [
        { name: "name", label: "Category Name", type: "Text" },
        { name: "description", label: "Description", type: "Text" },
        { name: "color", label: "Color", type: "Text" }
      ]
    }
  ];

  // Fetch data on component mount
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        setDataLoading(true);

        // Fetch data from the server to check if APIs are available
        const apiCheck1 = fetch('/api/categories').then(res => res.json());
        const apiCheck2 = fetch('/api/vendors').then(res => res.json());

        await Promise.all([apiCheck1, apiCheck2]);

      } catch {
        toast.error("Failed to load data sources");
      } finally {
        setDataLoading(false);
      }
    };

    fetchInitialData();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    setFormData({ ...formData, [id.replace('report-', '')]: value });
  };

  const handleSelectChange = (id: string, value: string) => {
    setFormData({ ...formData, [id]: value });
  };

  const handleFieldToggle = (fieldName: string) => {
    setFormData(prev => {
      const fields = [...prev.selectedFields];
      if (fields.includes(fieldName)) {
        return { ...prev, selectedFields: fields.filter(f => f !== fieldName) };
      } else {
        return { ...prev, selectedFields: [...fields, fieldName] };
      }
    });
  };

  const getCurrentDataSource = () => {
    return dataSources.find(ds => ds.name === formData.selectedDataSource) || dataSources[0];
  };

  // Preview function
  const handlePreview = () => {
    // Calculate date range based on selection
    const now = new Date();
    let startDate = new Date();
    const endDate = new Date();

    switch (formData.dateRange) {
      case "current-month":
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case "previous-month":
        startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        endDate.setDate(0); // Last day of previous month
        break;
      case "current-quarter":
        const currentQuarter = Math.floor(now.getMonth() / 3);
        startDate = new Date(now.getFullYear(), currentQuarter * 3, 1);
        break;
      case "previous-quarter":
        const prevQuarter = Math.floor(now.getMonth() / 3) - 1;
        const prevQuarterYear = prevQuarter < 0 ? now.getFullYear() - 1 : now.getFullYear();
        const adjustedQuarter = prevQuarter < 0 ? 3 : prevQuarter;
        startDate = new Date(prevQuarterYear, adjustedQuarter * 3, 1);
        endDate.setFullYear(prevQuarterYear, (adjustedQuarter + 1) * 3, 0);
        break;
      case "year-to-date":
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        // Default to last 30 days
        startDate.setDate(now.getDate() - 30);
    }

    // Format date range for display
    const formattedStartDate = startDate.toLocaleDateString();
    const formattedEndDate = endDate.toLocaleDateString();

    // Show preview information
    toast.info(
      <div className="space-y-2">
        <h3 className="font-medium">Report Preview</h3>
        <div><strong>Name:</strong> {formData.name || 'Untitled Report'}</div>
        <div><strong>Type:</strong> {formData.type}</div>
        <div><strong>Date Range:</strong> {formattedStartDate} to {formattedEndDate}</div>
        <div><strong>Fields:</strong> {formData.selectedFields.length > 0 ? formData.selectedFields.join(', ') : 'None'}</div>
        <div><strong>Visualization:</strong> {formData.visualizationType}</div>
      </div>,
      {
        duration: 5000,
      }
    );
  };

  // Open template dialog
  const openTemplateDialog = () => {
    // Pre-fill the template data with the current form data
    setTemplateData({
      name: formData.name ? `${formData.name} Template` : "",
      description: formData.description,
      makeDefault: false
    });
    setTemplateDialogOpen(true);
  };

  // Save as template function
  const saveAsTemplate = async () => {
    try {
      setLoading(true);

      if (!templateData.name) {
        toast.error("Please enter a name for your template");
        setLoading(false);
        return;
      }

      await createReportTemplate({
        name: templateData.name,
        description: templateData.description,
        type: formData.type as ReportType,
        visualizationType: formData.visualizationType,
        filters: {
          selectedFields: formData.selectedFields,
          groupBy: formData.groupBy,
          sortBy: formData.sortBy,
          calculations: formData.calculations,
          isDefault: templateData.makeDefault
        }
      });

      toast.success("Template saved successfully!");

      // Show confirmation with template details
      toast.info(
        <div className="space-y-2">
          <h3 className="font-medium">Template Saved</h3>
          <div><strong>Name:</strong> {templateData.name}</div>
          <div><strong>Type:</strong> {formData.type}</div>
          <div><strong>Fields:</strong> {formData.selectedFields.length > 0 ? formData.selectedFields.join(', ') : 'None'}</div>
          <div><strong>Visualization:</strong> {formData.visualizationType}</div>
          <p className="text-sm mt-2">You can now use this template to create new reports.</p>
        </div>,
        {
          duration: 5000,
        }
      );

      // Close the dialog
      setTemplateDialogOpen(false);
    } catch {
      toast.error("Failed to save template");
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);

      // Calculate date range based on selection
      const now = new Date();
      let startDate = new Date();
      const endDate = now;

      switch (formData.dateRange) {
        case "current-month":
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case "previous-month":
          startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
          endDate.setDate(0); // Last day of previous month
          break;
        case "current-quarter":
          const currentQuarter = Math.floor(now.getMonth() / 3);
          startDate = new Date(now.getFullYear(), currentQuarter * 3, 1);
          break;
        case "previous-quarter":
          const prevQuarter = Math.floor(now.getMonth() / 3) - 1;
          const prevQuarterYear = prevQuarter < 0 ? now.getFullYear() - 1 : now.getFullYear();
          const adjustedQuarter = prevQuarter < 0 ? 3 : prevQuarter;
          startDate = new Date(prevQuarterYear, adjustedQuarter * 3, 1);
          endDate.setFullYear(prevQuarterYear, (adjustedQuarter + 1) * 3, 0);
          break;
        case "year-to-date":
          startDate = new Date(now.getFullYear(), 0, 1);
          break;
        default:
          // Default to last 30 days
          startDate.setDate(now.getDate() - 30);
      }

      // Create proper filters - don't put sort/group in the where clause
      const filters = {};

      // Store metadata about custom settings
      const reportMetadata = {
        selectedFields: formData.selectedFields,
        groupBy: formData.groupBy || null,
        sortBy: formData.sortBy || null,
        calculations: formData.calculations
      };

      // Create the report with appropriate settings
      const report = await createReport({
        name: formData.name,
        description: formData.description,
        type: formData.type,
        dateRange: {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString()
        },
        filters: {
          ...filters,
          reportMetadata // Store our custom settings here but not directly in where clause
        },
        visualizationType: formData.visualizationType
      });

      // Generate the report data
      await generateReportData(report.id);

      toast.success("Report created successfully!");
      router.push(`/dashboard/reports/view/${report.id}`);
    } catch {
      toast.error("Failed to create report");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-medium">Custom Report Builder</h2>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={openTemplateDialog} disabled={loading}>
            <Save className="mr-2 h-4 w-4" />
            Save as Template
          </Button>
          <Button variant="outline" onClick={handlePreview} disabled={loading}>
            <Eye className="mr-2 h-4 w-4" />
            Preview
          </Button>
        </div>

        {/* Template Dialog */}
        <Dialog open={templateDialogOpen} onOpenChange={setTemplateDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Save as Template</DialogTitle>
              <DialogDescription>
                Create a reusable template from your current report configuration.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="template-name" className="text-right">
                  Name
                </Label>
                <Input
                  id="template-name"
                  value={templateData.name}
                  onChange={(e) => setTemplateData({...templateData, name: e.target.value})}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="template-description" className="text-right">
                  Description
                </Label>
                <Textarea
                  id="template-description"
                  value={templateData.description}
                  onChange={(e) => setTemplateData({...templateData, description: e.target.value})}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <div></div>
                <div className="col-span-3 flex items-center space-x-2">
                  <Checkbox
                    id="make-default"
                    checked={templateData.makeDefault}
                    onCheckedChange={(checked) =>
                      setTemplateData({...templateData, makeDefault: checked as boolean})
                    }
                  />
                  <label
                    htmlFor="make-default"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Make this a default template
                  </label>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setTemplateDialogOpen(false)}>
                <X className="mr-2 h-4 w-4" />
                Cancel
              </Button>
              <Button onClick={saveAsTemplate} disabled={loading}>
                {loading ? (
                  <div className="flex items-center">
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                    Saving...
                  </div>
                ) : (
                  <>
                    <Check className="mr-2 h-4 w-4" />
                    Save Template
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <StepIndicator currentStep={step} />

      {step === 1 && (
        <ReportInfoStep
          formData={formData}
          handleInputChange={handleInputChange}
          handleSelectChange={handleSelectChange}
          onNext={() => {
            if (!formData.name) {
              toast.error("Please enter a report name");
              return;
            }
            setStep(2);
          }}
        />
      )}

      {step === 2 && (
        <DataSelectionStep
          formData={formData}
          dataSources={dataSources}
          dataLoading={dataLoading}
          getCurrentDataSource={getCurrentDataSource}
          handleSelectChange={handleSelectChange}
          handleFieldToggle={handleFieldToggle}
          onBack={() => setStep(1)}
          onNext={() => {
            if (formData.selectedFields.length === 0) {
              toast.error("Please select at least one field");
              return;
            }
            setStep(3);
          }}
        />
      )}

      {step === 3 && (
        <CalculationsStep
          formData={formData}
          setFormData={setFormData}
          handleSelectChange={handleSelectChange}
          onBack={() => setStep(2)}
          onNext={() => setStep(4)}
        />
      )}

      {step === 4 && (
        <VisualizationStep
          formData={formData}
          loading={loading}
          handleSelectChange={handleSelectChange}
          onBack={() => setStep(3)}
          onSubmit={handleSubmit}
        />
      )}
    </div>
  );
}
