export type InvoiceStatus = 'PENDING' | 'PAID' | 'OVERDUE' | 'CANCELED';

export interface LineItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  amount: number;
}

export interface Invoice {
  id: string;
  number: string;
  userId: string;
  vendorName: string;
  amount: number;
  currency: string;
  status: InvoiceStatus;
  category?: string;
  issueDate: Date;
  dueDate: Date;
  paidDate?: Date;
  notes?: string;
  lineItems: LineItem[];
  createdAt: Date;
  updatedAt: Date;
}

export interface InvoiceFolder {
  id: string;
  name: string;
  userId: string;
  invoiceIds: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface InvoiceCategory {
  id: string;
  name: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}
