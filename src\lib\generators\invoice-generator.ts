import { jsPDF } from "jspdf";
import autoTable from "jspdf-autotable";
import * as ExcelJS from "exceljs";
import { loadAmiriFont } from "../fonts/amiri-loader";
import { formatNotes, containsArabic, formatDate } from "../utils/pdf";

// Unified interface for invoice data
interface InvoiceData {
  id: string;
  invoiceNumber?: string | null;
  status?: string;
  amount?: number | null;
  currency?: string | null;
  issueDate?: Date | null;
  dueDate?: Date | null;
  vendorName?: string | null;
  notes?: string | null;
  category?: { name: string; color?: string } | null;
  lineItems?: Array<{
    description?: string | null;
    quantity?: number | null;
    unitPrice?: number | null;
    totalPrice?: number | null;
    taxRate?: number | null;
    taxAmount?: number | null;
    discount?: number | null;
    productSku?: string | null;
    notes?: string | null;
    attributes?: Record<string, unknown>;
    [key: string]: unknown;
  }>;
  extractedData?: {
    vendor?: Record<string, unknown>;
    customer?: Record<string, unknown>;
    financials?: Record<string, unknown>;
    payment?: Record<string, unknown>;
    notes?: string;
    termsAndConditions?: string;
    meta?: {
      language?: string;
      confidence?: { overall: number };
      audit?: Record<string, unknown>;
    };
    lineItems?: Array<Record<string, unknown>>;
    [key: string]: unknown;
  };
  [key: string]: unknown;
}

interface GenerationOptions {
  includeVendorDetails?: boolean;
  includeCustomerDetails?: boolean;
  includeLineItems?: boolean;
  includeFinancialDetails?: boolean;
  includePaymentDetails?: boolean;
  includeNotes?: boolean;
  includeMetadata?: boolean;
  includeAuditInfo?: boolean;
  template?: 'professional' | 'modern' | 'classic';
  pageLayout?: 'portrait' | 'landscape';
}

interface RenderTextOptions {
  bold?: boolean;
  align?: 'left' | 'center' | 'right';
}

/**
 * Unified Invoice Generator for both PDF and Excel
 */
export class InvoiceGenerator {
  /**
   * Generate PDF for invoices
   */
  static async generatePDF(invoices: InvoiceData[], options: GenerationOptions = {}): Promise<Buffer> {
    const doc = new jsPDF({
      orientation: options.pageLayout || 'portrait',
      unit: "mm",
      format: "a4",
      compress: true,
    });

    await this.setupFont(doc);
    
    let currentY = 20;
    const pageHeight = doc.internal.pageSize.height;
    const pageWidth = doc.internal.pageSize.width;
    const margins = { top: 20, bottom: 20, left: 15, right: 15 };

    // Add cover page for multiple invoices
    if (invoices.length > 1) {
      this.addCoverPage(doc, invoices, pageWidth);
      doc.addPage();
      currentY = margins.top;
    }

    // Process each invoice
    for (let i = 0; i < invoices.length; i++) {
      const invoice = invoices[i];
      
      if (i > 0) {
        doc.addPage();
        currentY = margins.top;
      }

      currentY = this.addInvoiceContent(doc, invoice, options, currentY, pageWidth, pageHeight, margins);
    }

    return Buffer.from(doc.output("arraybuffer"));
  }

  /**
   * Generate Excel for invoices
   */
  static async generateExcel(invoices: InvoiceData[], options: GenerationOptions = {}): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();
    workbook.creator = "Invoice Management System";
    workbook.created = new Date();

    // Colors for styling
    const colors = {
      primary: 'FF2E7D32',
      secondary: 'FF1976D2',
      headerBg: 'FF1976D2',
      headerText: 'FFFFFFFF',
      alternateRow: 'FFF5F5F5',
    };

    // Main summary sheet
    this.createSummarySheet(workbook, invoices, colors);
    
    // Line items sheet
    if (options.includeLineItems !== false) {
      this.createLineItemsSheet(workbook, invoices, colors);
    }
    
    // Additional details sheets
    if (options.includeVendorDetails) {
      this.createVendorSheet(workbook, invoices, colors);
    }
    
    if (options.includeCustomerDetails) {
      this.createCustomerSheet(workbook, invoices, colors);
    }
    
    if (options.includeFinancialDetails) {
      this.createFinancialSheet(workbook, invoices, colors);
    }
    
    if (options.includeNotes) {
      this.createNotesSheet(workbook, invoices, colors);
    }

    return Buffer.from(await workbook.xlsx.writeBuffer());
  }

  // PDF Helper Methods
  private static async setupFont(doc: jsPDF): Promise<void> {
    try {
      await loadAmiriFont(doc);
      doc.setFont("Amiri", "normal");
    } catch (error) {
      console.warn("Error loading Amiri font:", error);
      doc.setFont("helvetica", "normal");
    }
  }

  private static renderText(doc: jsPDF, text: string, x: number, y: number, options: RenderTextOptions = {}): void {
    if (options.bold) {
      doc.setFont(containsArabic(text) ? "Amiri" : "helvetica", "bold");
    } else {
      doc.setFont(containsArabic(text) ? "Amiri" : "helvetica", "normal");
    }

    if (containsArabic(text)) {
      doc.setR2L(true);
    } else {
      doc.setR2L(false);
    }

    const alignOptions: any = {};
    if (options.align) {
      alignOptions.align = options.align;
    }

    doc.text(text, x, y, alignOptions);
  }

  private static addCoverPage(doc: jsPDF, invoices: InvoiceData[], pageWidth: number): void {
    doc.setFontSize(28);
    doc.setTextColor(41, 128, 185);
    this.renderText(doc, "Invoice Export Report", pageWidth / 2, 30, { align: "center", bold: true });

    doc.setFontSize(14);
    doc.setTextColor(100, 100, 100);
    this.renderText(doc, `Generated on: ${formatDate(new Date())}`, pageWidth / 2, 50, { align: "center" });
    this.renderText(doc, `Total Invoices: ${invoices.length}`, pageWidth / 2, 65, { align: "center" });

    // Summary statistics
    const totalAmount = invoices.reduce((sum, inv) => sum + (inv.amount || 0), 0);
    
    let y = 100;
    doc.setFontSize(16);
    doc.setTextColor(41, 128, 185);
    this.renderText(doc, "Export Summary", 15, y, { bold: true });
    y += 15;

    doc.setFontSize(12);
    doc.setTextColor(60, 60, 60);
    this.renderText(doc, `Total Amount: ${this.formatCurrency(totalAmount)}`, 15, y);
    y += 8;
    this.renderText(doc, `Average Amount: ${this.formatCurrency(totalAmount / invoices.length)}`, 15, y);
  }

  private static addInvoiceContent(
    doc: jsPDF, 
    invoice: InvoiceData, 
    options: GenerationOptions,
    startY: number,
    pageWidth: number,
    pageHeight: number,
    margins: { top: number; bottom: number; left: number; right: number }
  ): number {
    let currentY = startY;

    // Invoice header
    doc.setFontSize(20);
    doc.setTextColor(41, 128, 185);
    this.renderText(doc, `INVOICE ${invoice.invoiceNumber || invoice.id}`, margins.left, currentY, { bold: true });
    
    // Status badge
    if (invoice.status) {
      this.addStatusBadge(doc, invoice.status, pageWidth - margins.right - 40, currentY - 5);
    }
    
    // Decorative line
    doc.setLineWidth(1);
    doc.setDrawColor(41, 128, 185);
    doc.line(margins.left, currentY + 3, pageWidth - margins.right, currentY + 3);
    currentY += 20;

    // Add quick summary box for accountants
    const lineItems = this.getAllLineItems(invoice);
    if (lineItems.length > 0) {
      currentY = this.addAccountingSummary(doc, invoice, lineItems, currentY, margins, pageWidth);
    }

    // Basic info in two columns
    const leftCol = margins.left;
    const rightCol = pageWidth / 2 + 10;
    let leftY = currentY;
    let rightY = currentY;

    doc.setFontSize(11);
    doc.setTextColor(60, 60, 60);

    // Left column
    if (invoice.invoiceNumber) {
      leftY = this.addLabelValue(doc, "Invoice Number", invoice.invoiceNumber, leftCol, leftY);
    }
    if (invoice.issueDate) {
      leftY = this.addLabelValue(doc, "Issue Date", formatDate(invoice.issueDate), leftCol, leftY);
    }
    if (invoice.dueDate) {
      leftY = this.addLabelValue(doc, "Due Date", formatDate(invoice.dueDate), leftCol, leftY);
    }

    // Right column
    if (invoice.amount !== null && invoice.amount !== undefined) {
      rightY = this.addLabelValue(doc, "Total Amount", 
        this.formatCurrency(invoice.amount, invoice.currency || "USD"), rightCol, rightY);
    }
    if (invoice.currency) {
      rightY = this.addLabelValue(doc, "Currency", invoice.currency, rightCol, rightY);
    }
    if (invoice.category?.name) {
      rightY = this.addLabelValue(doc, "Category", invoice.category.name, rightCol, rightY);
    }

    currentY = Math.max(leftY, rightY) + 15;

    // Check if we need a new page
    if (currentY > pageHeight - 60) {
      doc.addPage();
      currentY = margins.top;
    }

    // Vendor information
    if (options.includeVendorDetails && (invoice.extractedData?.vendor || invoice.vendorName)) {
      currentY = this.addSection(doc, "Vendor Information", invoice.extractedData?.vendor, 
        invoice.vendorName, currentY, margins, pageHeight);
    }

    // Customer information
    if (options.includeCustomerDetails && invoice.extractedData?.customer) {
      currentY = this.addSection(doc, "Customer Information", invoice.extractedData.customer, 
        null, currentY, margins, pageHeight);
    }

    // Line items
    if (options.includeLineItems !== false) {
      currentY = this.addLineItems(doc, invoice, currentY, margins, pageWidth, pageHeight);
    }

    // Financial details
    if (options.includeFinancialDetails && invoice.extractedData?.financials) {
      currentY = this.addSection(doc, "Financial Details", invoice.extractedData.financials, 
        null, currentY, margins, pageHeight);
    }

    // Notes
    if (options.includeNotes && (invoice.notes || invoice.extractedData?.notes)) {
      currentY = this.addNotesSection(doc, invoice, currentY, margins, pageHeight);
    }

    return currentY;
  }

  private static addSection(
    doc: jsPDF, 
    title: string, 
    data: Record<string, unknown> | null | undefined,
    extraInfo: string | null,
    currentY: number,
    margins: { top: number; bottom: number; left: number; right: number },
    pageHeight: number
  ): number {
    // Check space
    if (currentY > pageHeight - 60) {
      doc.addPage();
      currentY = margins.top;
    }

    doc.setFontSize(14);
    doc.setTextColor(41, 128, 185);
    this.renderText(doc, title, margins.left, currentY, { bold: true });
    currentY += 12;

    if (extraInfo) {
      doc.setFontSize(11);
      doc.setTextColor(60, 60, 60);
      currentY = this.addLabelValue(doc, "Name", extraInfo, margins.left, currentY);
    }

    if (data) {
      currentY = this.renderObjectData(doc, data, margins.left, currentY, margins, pageHeight);
    }

    return currentY + 10;
  }

  private static renderObjectData(
    doc: jsPDF,
    obj: Record<string, unknown>,
    x: number,
    startY: number,
    margins: { top: number; bottom: number; left: number; right: number },
    pageHeight: number
  ): number {
    let y = startY;
    
    Object.entries(obj).forEach(([key, value]) => {
      if (value && value !== '' && value !== null && value !== undefined) {
        // Check space
        if (y > pageHeight - 40) {
          doc.addPage();
          y = margins.top;
        }

        const formattedKey = this.formatFieldName(key);
        
        if (typeof value === 'object' && !Array.isArray(value)) {
          // Nested object
          doc.setFontSize(12);
          doc.setTextColor(100, 100, 100);
          this.renderText(doc, formattedKey + ":", x, y, { bold: true });
          y += 8;
          y = this.renderObjectData(doc, value as Record<string, unknown>, x + 10, y, margins, pageHeight);
        } else {
          // Simple value
          y = this.addLabelValue(doc, formattedKey, String(value), x, y);
        }
      }
    });
    
    return y;
  }

  private static addLineItems(
    doc: jsPDF,
    invoice: InvoiceData,
    currentY: number,
    margins: { top: number; bottom: number; left: number; right: number },
    pageWidth: number,
    pageHeight: number
  ): number {
    const lineItems = this.getAllLineItems(invoice);
    if (lineItems.length === 0) return currentY;

    // Check space
    if (currentY > pageHeight - 80) {
      doc.addPage();
      currentY = margins.top;
    }

    doc.setFontSize(14);
    doc.setTextColor(41, 128, 185);
    this.renderText(doc, "Line Items", margins.left, currentY, { bold: true });
    currentY += 12;

    // Standard line items table
    const standardColumns = ['description', 'quantity', 'unitPrice', 'totalPrice'];
    const headers = standardColumns.map(col => this.formatFieldName(col));

    // Prepare standard data with better formatting
    const tableData = lineItems.map(item => {
      return standardColumns.map(col => {
        let value = (item as any)[col];
        if (value === undefined && item.attributes) {
          value = (item.attributes as any)[col];
        }
        
        if (col === 'quantity') {
          return this.formatNumber(Number(value) || 0);
        } else if (col.includes('Price') || col.includes('Amount')) {
          const amount = Number(value) || 0;
          return this.formatCurrency(amount, invoice.currency || 'USD');
        }
        
        return value?.toString() || 'N/A';
      });
    });

    // Add standard line items table
    autoTable(doc, {
      startY: currentY,
      head: [headers],
      body: tableData,
      theme: 'grid',
      headStyles: {
        fillColor: [76, 175, 80],
        textColor: [255, 255, 255],
        fontStyle: 'bold',
        fontSize: 9,
        halign: 'center',
      },
      styles: {
        fontSize: 8,
        cellPadding: 3,
        halign: 'center',
      },
      columnStyles: {
        0: { halign: 'left', cellWidth: 'auto' }, // Description
        1: { halign: 'center', cellWidth: 20 }, // Quantity  
        2: { halign: 'right', cellWidth: 25 }, // Unit Price
        3: { halign: 'right', cellWidth: 25 }, // Total Price
      },
      margin: { left: margins.left, right: margins.right },
    });

    currentY = (doc as any).lastAutoTable.finalY + 15;

    // Add dynamic/additional line item attributes table
    currentY = this.addDynamicLineItemDetails(doc, lineItems, currentY, margins, pageHeight, invoice.currency || 'USD');

    return currentY;
  }

  private static addDynamicLineItemDetails(
    doc: jsPDF,
    lineItems: Array<Record<string, unknown>>,
    currentY: number,
    margins: { top: number; bottom: number; left: number; right: number },
    pageHeight: number,
    currency: string
  ): number {
    // Get all unique keys from line item attributes
    const allAttributeKeys = new Set<string>();
    lineItems.forEach(item => {
      if (item.attributes && typeof item.attributes === 'object') {
        Object.keys(item.attributes).forEach(key => allAttributeKeys.add(key));
      }
    });

    // Filter out standard keys
    const standardKeys = [
      'description', 'quantity', 'unitPrice', 'totalPrice', 'amount',
      'taxRate', 'taxAmount', 'discount', 'productSku'
    ];
    const dynamicKeys = Array.from(allAttributeKeys).filter(
      key => !standardKeys.includes(key)
    );

    if (dynamicKeys.length === 0) return currentY;

    // Check space for additional table
    if (currentY > pageHeight - 100) {
      doc.addPage();
      currentY = margins.top;
    }

    doc.setFontSize(12);
    doc.setTextColor(76, 175, 80);
    this.renderText(doc, "Additional Line Item Details", margins.left, currentY, { bold: true });
    currentY += 10;

    // Create headers: Item + dynamic keys
    const dynamicHeaders = ['Item Description', ...dynamicKeys.map(key => this.formatFieldName(key))];

    // Prepare dynamic data
    const dynamicTableData = lineItems.map((item, index) => {
      const row = [
        (item.description as string) || `Item ${index + 1}`,
        ...dynamicKeys.map(key => {
          const attrs = item.attributes as Record<string, unknown> || {};
          const value = attrs[key];
          
          if (value === null || value === undefined || value === '') {
            return '-';
          }
          
          // Format numbers nicely
          if (typeof value === 'number') {
            if (key.toLowerCase().includes('price') || key.toLowerCase().includes('amount') || key.toLowerCase().includes('cost')) {
              return this.formatCurrency(value, currency);
            } else if (key.toLowerCase().includes('rate') || key.toLowerCase().includes('percent')) {
              return this.formatPercentage(value);
            } else if (key.toLowerCase().includes('quantity') || key.toLowerCase().includes('count')) {
              return this.formatNumber(value);
            } else {
              return this.formatNumber(value);
            }
          }
          
          return String(value);
        })
      ];
      return row;
    });

    if (dynamicTableData.length > 0) {
      autoTable(doc, {
        startY: currentY,
        head: [dynamicHeaders],
        body: dynamicTableData,
        theme: 'striped',
        headStyles: {
          fillColor: [255, 152, 0],
          textColor: [255, 255, 255],
          fontStyle: 'bold',
          fontSize: 8,
          halign: 'center',
        },
        styles: {
          fontSize: 7,
          cellPadding: 2,
          halign: 'center',
        },
        columnStyles: {
          0: { halign: 'left', cellWidth: 'auto' }, // Item Description
        },
        margin: { left: margins.left, right: margins.right },
        tableWidth: 'auto',
        didDrawPage: (data) => {
          // Add a subtle border around the table
          doc.setDrawColor(200, 200, 200);
          doc.setLineWidth(0.5);
        }
      });
      
      currentY = (doc as any).lastAutoTable.finalY + 10;
    }

    return currentY;
  }

  private static addNotesSection(
    doc: jsPDF,
    invoice: InvoiceData,
    currentY: number,
    margins: { top: number; bottom: number; left: number; right: number },
    pageHeight: number
  ): number {
    // Check space
    if (currentY > pageHeight - 60) {
      doc.addPage();
      currentY = margins.top;
    }

    doc.setFontSize(14);
    doc.setTextColor(41, 128, 185);
    this.renderText(doc, "Notes & Terms", margins.left, currentY, { bold: true });
    currentY += 12;

    doc.setFontSize(10);
    doc.setTextColor(60, 60, 60);

    if (invoice.notes) {
      this.renderText(doc, "Invoice Notes:", margins.left, currentY, { bold: true });
      currentY += 6;
      const noteLines = doc.splitTextToSize(invoice.notes, 180);
      noteLines.forEach((line: string) => {
        this.renderText(doc, line, margins.left + 5, currentY);
        currentY += 5;
      });
      currentY += 5;
    }

    if (invoice.extractedData?.termsAndConditions) {
      this.renderText(doc, "Terms & Conditions:", margins.left, currentY, { bold: true });
      currentY += 6;
      const termLines = doc.splitTextToSize(invoice.extractedData.termsAndConditions, 180);
      termLines.forEach((line: string) => {
        this.renderText(doc, line, margins.left + 5, currentY);
        currentY += 5;
      });
    }

    return currentY + 10;
  }

  private static addLabelValue(doc: jsPDF, label: string, value: string, x: number, y: number): number {
    doc.setTextColor(100, 100, 100);
    this.renderText(doc, label + ":", x, y, { bold: true });
    
    doc.setTextColor(60, 60, 60);
    this.renderText(doc, value, x + 45, y);
    
    return y + 7;
  }

  private static addAccountingSummary(
    doc: jsPDF,
    invoice: InvoiceData,
    lineItems: Array<Record<string, unknown>>,
    currentY: number,
    margins: { top: number; bottom: number; left: number; right: number },
    pageWidth: number
  ): number {
    // Quick summary box with light background
    doc.setFillColor(245, 245, 245);
    doc.roundedRect(margins.left, currentY, pageWidth - margins.left - margins.right, 25, 3, 3, 'F');
    
    // Border
    doc.setDrawColor(200, 200, 200);
    doc.setLineWidth(0.5);
    doc.roundedRect(margins.left, currentY, pageWidth - margins.left - margins.right, 25, 3, 3, 'S');
    
    const boxY = currentY + 5;
    doc.setFontSize(10);
    doc.setTextColor(76, 175, 80);
    this.renderText(doc, "ACCOUNTING SUMMARY", margins.left + 5, boxY, { bold: true });
    
    // Calculate totals
    let totalItems = lineItems.length;
    let totalQuantity = 0;
    let calculatedTotal = 0;
    
    lineItems.forEach(item => {
      const qty = Number((item as any).quantity) || 0;
      const total = Number((item as any).totalPrice || (item as any).amount) || 0;
      totalQuantity += qty;
      calculatedTotal += total;
    });

    doc.setFontSize(8);
    doc.setTextColor(60, 60, 60);
    
    const col1X = margins.left + 5;
    const col2X = margins.left + 70;
    const col3X = margins.left + 135;
    const summaryY = boxY + 8;
    
    this.renderText(doc, `Items: ${totalItems}`, col1X, summaryY);
    this.renderText(doc, `Total Qty: ${this.formatNumber(totalQuantity)}`, col2X, summaryY);
    this.renderText(doc, `Calculated: ${this.formatCurrency(calculatedTotal, invoice.currency || 'USD')}`, col3X, summaryY);
    
    // Second row
    const summaryY2 = summaryY + 6;
    const invoiceTotal = invoice.amount || 0;
    const difference = Math.abs(invoiceTotal - calculatedTotal);
    
    this.renderText(doc, `Invoice Total: ${this.formatCurrency(invoiceTotal, invoice.currency || 'USD')}`, col1X, summaryY2);
    
    if (difference > 0.01) {
      doc.setTextColor(244, 67, 54);
      this.renderText(doc, `Difference: ${this.formatCurrency(difference, invoice.currency || 'USD')}`, col2X, summaryY2);
    } else {
      doc.setTextColor(76, 175, 80);
      this.renderText(doc, "✓ Totals Match", col2X, summaryY2);
    }
    
    return currentY + 30;
  }

  private static addStatusBadge(doc: jsPDF, status: string, x: number, y: number): void {
    const colors: Record<string, [number, number, number]> = {
      paid: [76, 175, 80],
      pending: [255, 152, 0],
      overdue: [244, 67, 54],
      cancelled: [158, 158, 158],
    };

    const color = colors[status.toLowerCase()] || [100, 100, 100];
    
    doc.setFillColor(color[0], color[1], color[2]);
    doc.roundedRect(x, y, 35, 8, 2, 2, 'F');
    
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(9);
    this.renderText(doc, status.toUpperCase(), x + 17.5, y + 5.5, { align: "center", bold: true });
  }

  private static getAllLineItems(invoice: InvoiceData): Array<Record<string, unknown>> {
    if (invoice.lineItems && invoice.lineItems.length > 0) {
      return invoice.lineItems;
    }
    
    if (invoice.extractedData?.lineItems && Array.isArray(invoice.extractedData.lineItems)) {
      return invoice.extractedData.lineItems;
    }
    
    return [];
  }

  private static formatCurrency(amount: number, currency = "USD"): string {
    try {
      return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: currency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(amount);
    } catch (error) {
      // Fallback for invalid currency codes
      return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(amount);
    }
  }

  private static formatNumber(value: number): string {
    return new Intl.NumberFormat("en-US", {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(value);
  }

  private static formatPercentage(value: number): string {
    return new Intl.NumberFormat("en-US", {
      style: "percent",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value / 100);
  }

  private static formatFieldName(key: string): string {
    return key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
  }

  // Excel Helper Methods
  private static createSummarySheet(workbook: ExcelJS.Workbook, invoices: InvoiceData[], colors: any): void {
    const sheet = workbook.addWorksheet("Invoice Summary", {
      properties: { tabColor: { argb: colors.primary } },
      views: [{ state: "frozen", ySplit: 1 }]
    });

    const columns: Partial<ExcelJS.Column>[] = [
      { header: "Invoice ID", key: "id", width: 15 },
      { header: "Invoice Number", key: "invoiceNumber", width: 20 },
      { header: "Vendor Name", key: "vendorName", width: 25 },
      { header: "Issue Date", key: "issueDate", width: 15 },
      { header: "Due Date", key: "dueDate", width: 15 },
      { header: "Amount", key: "amount", width: 15 },
      { header: "Currency", key: "currency", width: 10 },
      { header: "Status", key: "status", width: 12 },
      { header: "Category", key: "category", width: 20 },
    ];

    sheet.columns = columns;

    invoices.forEach((invoice, index) => {
      const row = sheet.addRow({
        id: invoice.id,
        invoiceNumber: invoice.invoiceNumber || `INV-${index + 1}`,
        vendorName: invoice.vendorName || "Unknown",
        issueDate: invoice.issueDate ? formatDate(invoice.issueDate) : "N/A",
        dueDate: invoice.dueDate ? formatDate(invoice.dueDate) : "N/A",
        amount: invoice.amount || 0,
        currency: invoice.currency || "USD",
        status: invoice.status || "Unknown",
        category: invoice.category?.name || "Uncategorized",
      });

      // Format amount as currency
      const amountCell = row.getCell('amount');
      amountCell.numFmt = '"$"#,##0.00';
      
      // Color code by status
      this.applyStatusColoring(row.getCell('status'), invoice.status);
    });

    this.styleWorksheet(sheet, colors);
    
    // Add summary statistics for accountants
    this.addExcelSummaryStatistics(sheet, invoices);
  }

  private static createLineItemsSheet(workbook: ExcelJS.Workbook, invoices: InvoiceData[], colors: any): void {
    const sheet = workbook.addWorksheet("Line Items", {
      properties: { tabColor: { argb: colors.secondary } }
    });

    const columns: Partial<ExcelJS.Column>[] = [
      { header: "Invoice Number", key: "invoiceNumber", width: 20 },
      { header: "Vendor", key: "vendorName", width: 25 },
      { header: "Description", key: "description", width: 40 },
      { header: "Quantity", key: "quantity", width: 12 },
      { header: "Unit Price", key: "unitPrice", width: 15 },
      { header: "Total Price", key: "totalPrice", width: 15 },
      { header: "Tax Rate", key: "taxRate", width: 12 },
      { header: "Tax Amount", key: "taxAmount", width: 15 },
      { header: "Discount", key: "discount", width: 15 },
      { header: "Product SKU", key: "productSku", width: 20 },
      { header: "Notes", key: "notes", width: 30 },
    ];

    sheet.columns = columns;

    invoices.forEach(invoice => {
      const lineItems = this.getAllLineItems(invoice);
      
      if (lineItems.length === 0) {
        sheet.addRow({
          invoiceNumber: invoice.invoiceNumber || invoice.id,
          vendorName: invoice.vendorName || "Unknown",
          description: "No line items found",
          quantity: "N/A",
          unitPrice: "N/A",
          totalPrice: "N/A",
          notes: "Check extracted data",
        });
      } else {
        lineItems.forEach(item => {
          const row = sheet.addRow({
            invoiceNumber: invoice.invoiceNumber || invoice.id,
            vendorName: invoice.vendorName || "Unknown",
            description: (item as any).description || "N/A",
            quantity: (item as any).quantity || 0,
            unitPrice: (item as any).unitPrice || 0,
            totalPrice: (item as any).totalPrice || (item as any).amount || 0,
            taxRate: (item as any).taxRate || "N/A",
            taxAmount: (item as any).taxAmount || "N/A",
            discount: (item as any).discount || "N/A",
            productSku: (item as any).productSku || "N/A",
            notes: (item as any).notes || "N/A",
          });

          // Format currency cells
          ['unitPrice', 'totalPrice', 'taxAmount', 'discount'].forEach(key => {
            const cell = row.getCell(key);
            if (typeof cell.value === 'number') {
              cell.numFmt = '"$"#,##0.00';
            }
          });

          // Format percentage cells
          ['taxRate'].forEach(key => {
            const cell = row.getCell(key);
            if (typeof cell.value === 'number') {
              cell.numFmt = '0.00%';
            }
          });
        });
      }
    });

    this.styleWorksheet(sheet, colors);
    
    // Create additional sheet for dynamic line item attributes
    this.createDynamicLineItemsSheet(workbook, invoices, colors);
  }

  private static createDynamicLineItemsSheet(workbook: ExcelJS.Workbook, invoices: InvoiceData[], colors: any): void {
    // First, collect all unique dynamic attribute keys
    const allDynamicKeys = new Set<string>();
    const standardKeys = [
      'description', 'quantity', 'unitPrice', 'totalPrice', 'amount',
      'taxRate', 'taxAmount', 'discount', 'productSku', 'notes'
    ];

    invoices.forEach(invoice => {
      const lineItems = this.getAllLineItems(invoice);
      lineItems.forEach(item => {
        if (item.attributes && typeof item.attributes === 'object') {
          Object.keys(item.attributes).forEach(key => {
            if (!standardKeys.includes(key)) {
              allDynamicKeys.add(key);
            }
          });
        }
      });
    });

    // Only create sheet if there are dynamic attributes
    if (allDynamicKeys.size === 0) return;

    const sheet = workbook.addWorksheet("Additional Line Item Details", {
      properties: { tabColor: { argb: 'FFFF9800' } }
    });

    // Create columns: Invoice info + Item description + dynamic attributes
    const dynamicColumns: Partial<ExcelJS.Column>[] = [
      { header: "Invoice Number", key: "invoiceNumber", width: 20 },
      { header: "Vendor", key: "vendorName", width: 25 },
      { header: "Item Description", key: "itemDescription", width: 40 },
      ...Array.from(allDynamicKeys).map(key => ({
        header: this.formatFieldName(key),
        key: key,
        width: 20
      }))
    ];

    sheet.columns = dynamicColumns;

    // Add data
    invoices.forEach(invoice => {
      const lineItems = this.getAllLineItems(invoice);
      
      if (lineItems.length === 0) return;

      lineItems.forEach((item, index) => {
        const attrs = (item.attributes as Record<string, unknown>) || {};
        const hasAnyDynamicData = Array.from(allDynamicKeys).some(key => 
          attrs[key] !== null && attrs[key] !== undefined && attrs[key] !== ''
        );

        // Only add row if it has dynamic data
        if (hasAnyDynamicData) {
          const rowData: Record<string, unknown> = {
            invoiceNumber: invoice.invoiceNumber || invoice.id,
            vendorName: invoice.vendorName || "Unknown",
            itemDescription: (item as any).description || `Item ${index + 1}`,
          };

          // Add dynamic attribute values
          Array.from(allDynamicKeys).forEach(key => {
            const value = attrs[key];
            if (value === null || value === undefined || value === '') {
              rowData[key] = "N/A";
            } else if (typeof value === 'number') {
              rowData[key] = value;
            } else {
              rowData[key] = String(value);
            }
          });

          const row = sheet.addRow(rowData);

          // Format numeric cells
          Array.from(allDynamicKeys).forEach((key, colIndex) => {
            const cell = row.getCell(colIndex + 4); // +4 for invoice, vendor, description, item
            const value = attrs[key];
            
            if (typeof value === 'number') {
              if (key.toLowerCase().includes('price') || key.toLowerCase().includes('amount') || key.toLowerCase().includes('cost')) {
                cell.numFmt = '"$"#,##0.00';
              } else if (key.toLowerCase().includes('rate') || key.toLowerCase().includes('percent')) {
                cell.numFmt = '0.00%';
              } else {
                cell.numFmt = '#,##0.00';
              }
            }
          });
        }
      });
    });

    this.styleWorksheet(sheet, colors);

    // Add header note
    if (sheet.lastRow?.number && sheet.lastRow.number > 1) {
      const noteRow = sheet.insertRow(1);
      noteRow.getCell(1).value = "Additional Line Item Details - Dynamic attributes extracted from invoice line items";
      noteRow.getCell(1).font = { italic: true, color: { argb: '666666' } };
      sheet.mergeCells(`A1:${String.fromCharCode(65 + dynamicColumns.length - 1)}1`);
    }
  }

  private static createVendorSheet(workbook: ExcelJS.Workbook, invoices: InvoiceData[], colors: any): void {
    const sheet = workbook.addWorksheet("Vendor Details", {
      properties: { tabColor: { argb: 'FFFF9800' } }
    });

    const columns: Partial<ExcelJS.Column>[] = [
      { header: "Invoice Number", key: "invoiceNumber", width: 20 },
      { header: "Vendor Name", key: "vendorName", width: 25 },
      { header: "Vendor Email", key: "vendorEmail", width: 25 },
      { header: "Vendor Phone", key: "vendorPhone", width: 20 },
      { header: "Vendor Address", key: "vendorAddress", width: 40 },
    ];

    sheet.columns = columns;

    invoices.forEach(invoice => {
      const vendorData = invoice.extractedData?.vendor || {};
      sheet.addRow({
        invoiceNumber: invoice.invoiceNumber || invoice.id,
        vendorName: invoice.vendorName || "Unknown",
        vendorEmail: (vendorData as any).email || "N/A",
        vendorPhone: (vendorData as any).phone || "N/A",
        vendorAddress: (vendorData as any).address || "N/A",
      });
    });

    this.styleWorksheet(sheet, colors);
  }

  private static createCustomerSheet(workbook: ExcelJS.Workbook, invoices: InvoiceData[], colors: any): void {
    const sheet = workbook.addWorksheet("Customer Details", {
      properties: { tabColor: { argb: 'FF4CAF50' } }
    });

    const hasCustomerData = invoices.some(inv => inv.extractedData?.customer);
    if (!hasCustomerData) return;

    const columns: Partial<ExcelJS.Column>[] = [
      { header: "Invoice Number", key: "invoiceNumber", width: 20 },
      { header: "Customer Name", key: "customerName", width: 25 },
      { header: "Customer Email", key: "customerEmail", width: 25 },
      { header: "Customer Phone", key: "customerPhone", width: 20 },
      { header: "Customer Address", key: "customerAddress", width: 40 },
    ];

    sheet.columns = columns;

    invoices.forEach(invoice => {
      const customerData = invoice.extractedData?.customer || {};
      if (Object.keys(customerData).length > 0) {
        sheet.addRow({
          invoiceNumber: invoice.invoiceNumber || invoice.id,
          customerName: (customerData as any).name || "Unknown",
          customerEmail: (customerData as any).email || "N/A",
          customerPhone: (customerData as any).phone || "N/A",
          customerAddress: (customerData as any).address || "N/A",
        });
      }
    });

    this.styleWorksheet(sheet, colors);
  }

  private static createFinancialSheet(workbook: ExcelJS.Workbook, invoices: InvoiceData[], colors: any): void {
    const sheet = workbook.addWorksheet("Financial Analysis", {
      properties: { tabColor: { argb: 'FFFF5722' } }
    });

    const columns: Partial<ExcelJS.Column>[] = [
      { header: "Invoice Number", key: "invoiceNumber", width: 20 },
      { header: "Total Amount", key: "totalAmount", width: 15 },
      { header: "Currency", key: "currency", width: 10 },
      { header: "Subtotal", key: "subtotal", width: 15 },
      { header: "Tax Amount", key: "taxAmount", width: 15 },
      { header: "Discount", key: "discount", width: 15 },
    ];

    sheet.columns = columns;

    invoices.forEach(invoice => {
      const financialData = invoice.extractedData?.financials || {};
      const row = sheet.addRow({
        invoiceNumber: invoice.invoiceNumber || invoice.id,
        totalAmount: invoice.amount || 0,
        currency: invoice.currency || "USD",
        subtotal: (financialData as any).subtotal || "N/A",
        taxAmount: (financialData as any).tax || "N/A",
        discount: (financialData as any).discount || "N/A",
      });

      // Format currency cells
      ['totalAmount', 'subtotal', 'taxAmount', 'discount'].forEach(key => {
        const cell = row.getCell(key);
        if (typeof cell.value === 'number') {
          cell.numFmt = '"$"#,##0.00';
        }
      });
    });

    this.styleWorksheet(sheet, colors);
  }

  private static createNotesSheet(workbook: ExcelJS.Workbook, invoices: InvoiceData[], colors: any): void {
    const sheet = workbook.addWorksheet("Notes & Terms", {
      properties: { tabColor: { argb: 'FF9C27B0' } }
    });

    const columns: Partial<ExcelJS.Column>[] = [
      { header: "Invoice Number", key: "invoiceNumber", width: 20 },
      { header: "Vendor Name", key: "vendorName", width: 25 },
      { header: "Invoice Notes", key: "invoiceNotes", width: 40 },
      { header: "Terms & Conditions", key: "termsConditions", width: 50 },
    ];

    sheet.columns = columns;

    invoices.forEach(invoice => {
      const hasNotes = invoice.notes || invoice.extractedData?.notes || invoice.extractedData?.termsAndConditions;
      
      if (hasNotes) {
        sheet.addRow({
          invoiceNumber: invoice.invoiceNumber || invoice.id,
          vendorName: invoice.vendorName || "Unknown",
          invoiceNotes: invoice.notes || "N/A",
          termsConditions: invoice.extractedData?.termsAndConditions || "N/A",
        });
      }
    });

    this.styleWorksheet(sheet, colors);
    
    // Set row height for better text display
    sheet.eachRow((row, rowNumber) => {
      if (rowNumber > 1) {
        row.height = 40;
        row.eachCell((cell) => {
          cell.alignment = { wrapText: true, vertical: 'top' };
        });
      }
    });
  }

  private static addExcelSummaryStatistics(sheet: ExcelJS.Worksheet, invoices: InvoiceData[]): void {
    const lastRow = sheet.lastRow?.number || 0;
    
    // Add some spacing
    const startRow = lastRow + 3;
    
    // Add summary header
    const summaryTitleRow = sheet.getRow(startRow);
    summaryTitleRow.getCell(1).value = "ACCOUNTING SUMMARY";
    summaryTitleRow.getCell(1).font = { bold: true, size: 12, color: { argb: '4CAF50' } };
    
    // Calculate statistics
    const totalInvoices = invoices.length;
    const totalAmount = invoices.reduce((sum, inv) => sum + (inv.amount || 0), 0);
    const avgAmount = totalAmount / totalInvoices;
    
    const totalLineItems = invoices.reduce((sum, inv) => {
      const lineItems = this.getAllLineItems(inv);
      return sum + lineItems.length;
    }, 0);
    
    const statusCounts = invoices.reduce((counts, inv) => {
      const status = inv.status || 'unknown';
      counts[status] = (counts[status] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);
    
    // Add statistics
    let currentRow = startRow + 1;
    
    const stats = [
      ['Total Invoices:', totalInvoices],
      ['Total Amount:', totalAmount],
      ['Average Amount:', avgAmount],
      ['Total Line Items:', totalLineItems],
      ['Average Items per Invoice:', Math.round((totalLineItems / totalInvoices) * 100) / 100],
    ];
    
    stats.forEach(([label, value]) => {
      const row = sheet.getRow(currentRow);
      row.getCell(1).value = label;
      row.getCell(1).font = { bold: true };
      
      if (typeof value === 'number' && label.toString().toLowerCase().includes('amount')) {
        row.getCell(2).value = value;
        row.getCell(2).numFmt = '"$"#,##0.00';
      } else {
        row.getCell(2).value = value;
      }
      
      currentRow++;
    });
    
    // Add status breakdown
    currentRow += 1;
    const statusTitleRow = sheet.getRow(currentRow);
    statusTitleRow.getCell(1).value = "Status Breakdown:";
    statusTitleRow.getCell(1).font = { bold: true };
    currentRow++;
    
    Object.entries(statusCounts).forEach(([status, count]) => {
      const row = sheet.getRow(currentRow);
      row.getCell(1).value = `${status.charAt(0).toUpperCase() + status.slice(1)}:`;
      row.getCell(2).value = count;
      
      // Apply status coloring
      this.applyStatusColoring(row.getCell(1), status);
      
      currentRow++;
    });
  }

  private static applyStatusColoring(cell: ExcelJS.Cell, status?: string): void {
    if (!status) return;
    
    const statusColors: Record<string, { bg: string; text: string }> = {
      paid: { bg: 'C8E6C9', text: '2E7D32' },
      pending: { bg: 'FFF3E0', text: 'F57C00' },
      overdue: { bg: 'FFCDD2', text: 'D32F2F' },
      cancelled: { bg: 'F5F5F5', text: '616161' },
    };

    const colors = statusColors[status.toLowerCase()];
    if (colors) {
      cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: colors.bg } };
      cell.font = { color: { argb: colors.text }, bold: true };
    }
  }

  private static styleWorksheet(sheet: ExcelJS.Worksheet, colors: any): void {
    // Style header row
    const headerRow = sheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: colors.headerText } };
    headerRow.fill = { 
      type: "pattern", 
      pattern: "solid", 
      fgColor: { argb: colors.headerBg } 
    };
    headerRow.alignment = { vertical: "middle", horizontal: "center" };
    headerRow.height = 25;

    // Add borders and alternating row colors
    sheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
      if (rowNumber > 1 && rowNumber % 2 === 0) {
        row.fill = { 
          type: "pattern", 
          pattern: "solid", 
          fgColor: { argb: colors.alternateRow } 
        };
      }

      row.eachCell({ includeEmpty: false }, (cell) => {
        cell.border = {
          top: { style: "thin" },
          left: { style: "thin" },
          bottom: { style: "thin" },
          right: { style: "thin" },
        };
      });
    });
  }
}

// Main export functions
export async function generateInvoicePDF(invoices: InvoiceData[], options: GenerationOptions = {}): Promise<Buffer> {
  return InvoiceGenerator.generatePDF(invoices, options);
}

export async function generateInvoiceExcel(invoices: InvoiceData[], options: GenerationOptions = {}): Promise<Buffer> {
  return InvoiceGenerator.generateExcel(invoices, options);
}