import { jsPD<PERSON> } from "jspdf"
import autoTable, { type CellHookData } from "jspdf-autotable"
import { loadAmiriFont } from "../fonts/amiri-loader"
import { formatNotes, containsArabic, formatDate } from "../utils/pdf"
import type { InvoiceWithExtras, ExtractedData, RenderTextOptions } from "../types/pdf"

// Helper: setup Amiri font and RTL for Arabic
async function setupArabicPdf(doc: jsPDF): Promise<void> {
    try {
        const success = await loadAmiriFont(doc)
        if (success) {
            doc.setFont("Amiri", "normal")
            doc.setR2L(true)
        } else {
            console.warn("Amiri font could not be loaded. Falling back to standard fonts.")
            doc.setFont("helvetica", "normal")
            doc.setR2L(false)
        }
    } catch (error) {
        console.warn("Error loading Amiri font:", error)
        doc.setFont("helvetica", "normal")
        doc.setR2L(false)
    }
}

// Helper: render text with Arabic/RTL support
function renderText(doc: jsPDF, text: string, x: number, y: number, options: RenderTextOptions = {}) {
    const originalFont = doc.getFont()
    
    if (options.bold) {
        doc.setFont(containsArabic(text) ? "Amiri" : "helvetica", "bold")
    } else {
        doc.setFont(containsArabic(text) ? "Amiri" : "helvetica", "normal")
    }

    if (containsArabic(text)) {
        doc.setR2L(true)
    } else {
        doc.setR2L(false)
    }

    if (options.align === "center") {
        doc.text(text, x, y, { align: "center" })
    } else if (options.align === "right") {
        doc.text(text, x, y, { align: "right" })
    } else {
        doc.text(text, x, y)
    }

    // Reset font
    doc.setFont(originalFont.fontName, originalFont.fontStyle)
}

function formatCurrency(amount: number, currency = "USD"): string {
    return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: currency,
    }).format(amount)
}

// Enhanced function to render structured data with proper formatting
function renderStructuredData(
    doc: jsPDF, 
    data: Record<string, any>, 
    startX: number, 
    startY: number, 
    title: string,
    isArabic: boolean = false
): number {
    let yPos = startY

    // Section title
    doc.setFontSize(14)
    doc.setTextColor(41, 128, 185)
    renderText(doc, title, startX, yPos, { align: isArabic ? "right" : "left", bold: true })
    
    // Underline
    doc.setLineWidth(0.5)
    doc.setDrawColor(41, 128, 185)
    const titleWidth = doc.getTextWidth(title)
    doc.line(startX, yPos + 2, startX + titleWidth, yPos + 2)
    yPos += 10

    doc.setFontSize(10)
    doc.setTextColor(60, 60, 60)

    // Render data fields
    Object.entries(data).forEach(([key, value]) => {
        if (value && value !== '' && value !== null && value !== undefined) {
            // Check if we need a new page
            if (yPos > 270) {
                doc.addPage()
                yPos = 20
            }

            const formattedKey = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
            
            if (typeof value === 'object' && !Array.isArray(value)) {
                // Nested object - render as sub-section
                yPos += 5
                doc.setFontSize(12)
                doc.setTextColor(100, 100, 100)
                renderText(doc, formattedKey + ":", startX, yPos, { bold: true })
                yPos += 8
                
                doc.setFontSize(10)
                Object.entries(value).forEach(([subKey, subValue]) => {
                    if (subValue && subValue !== '' && subValue !== null) {
                        const formattedSubKey = subKey.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
                        renderText(doc, `  ${formattedSubKey}:`, startX + 5, yPos, { bold: true })
                        
                        let displayValue = String(subValue)
                        if (subKey.toLowerCase().includes('amount') || subKey.toLowerCase().includes('price') || subKey.toLowerCase().includes('total')) {
                            if (typeof subValue === 'number') {
                                displayValue = formatCurrency(subValue)
                            } else if (typeof subValue === 'string' && !isNaN(parseFloat(subValue))) {
                                displayValue = formatCurrency(parseFloat(subValue))
                            }
                        }
                        
                        const lines = doc.splitTextToSize(displayValue, 120)
                        renderText(doc, lines[0], startX + 50, yPos, { 
                            align: containsArabic(displayValue) ? "right" : "left" 
                        })
                        
                        if (lines.length > 1) {
                            for (let i = 1; i < lines.length; i++) {
                                yPos += 5
                                renderText(doc, lines[i], startX + 50, yPos, { 
                                    align: containsArabic(displayValue) ? "right" : "left" 
                                })
                            }
                        }
                        yPos += 6
                    }
                })
                yPos += 3
            } else if (Array.isArray(value)) {
                // Array - render as list
                renderText(doc, formattedKey + ":", startX, yPos, { bold: true })
                yPos += 6
                value.forEach((item, index) => {
                    if (typeof item === 'object') {
                        renderText(doc, `  ${index + 1}. ${JSON.stringify(item)}`, startX + 5, yPos)
                    } else {
                        renderText(doc, `  ${index + 1}. ${String(item)}`, startX + 5, yPos)
                    }
                    yPos += 5
                })
                yPos += 3
            } else {
                // Simple value
                renderText(doc, formattedKey + ":", startX, yPos, { bold: true })
                
                let displayValue = String(value)
                
                // Format currency values
                if (key.toLowerCase().includes('amount') || key.toLowerCase().includes('price') || key.toLowerCase().includes('total')) {
                    if (typeof value === 'number') {
                        displayValue = formatCurrency(value)
                    } else if (typeof value === 'string' && !isNaN(parseFloat(value))) {
                        displayValue = formatCurrency(parseFloat(value))
                    }
                }
                
                // Format dates
                if (key.toLowerCase().includes('date') && typeof value === 'string') {
                    try {
                        const date = new Date(value)
                        if (!isNaN(date.getTime())) {
                            displayValue = formatDate(date)
                        }
                    } catch {
                        // Keep original value if date parsing fails
                    }
                }
                
                const lines = doc.splitTextToSize(displayValue, 120)
                renderText(doc, lines[0], startX + 50, yPos, { 
                    align: containsArabic(displayValue) ? "right" : "left" 
                })
                
                if (lines.length > 1) {
                    for (let i = 1; i < lines.length; i++) {
                        yPos += 5
                        renderText(doc, lines[i], startX + 50, yPos, { 
                            align: containsArabic(displayValue) ? "right" : "left" 
                        })
                    }
                }
                yPos += 6
            }
        }
    })

    return yPos + 10
}

export async function generatePdf(invoices: InvoiceWithExtras[], fields: string[]): Promise<Buffer> {
    const doc = new jsPDF({ orientation: "portrait", unit: "mm", format: "a4", compress: true })
    await setupArabicPdf(doc)
    
    // Cover page
    doc.setFontSize(24)
    doc.setTextColor(41, 128, 185)
    renderText(doc, "Enhanced Invoice Export", doc.internal.pageSize.width / 2, 30, { align: "center", bold: true })
    
    doc.setFontSize(12)
    doc.setTextColor(100, 100, 100)
    renderText(doc, `Generated on: ${formatDate(new Date())}`, doc.internal.pageSize.width / 2, 45, { align: "center" })
    renderText(doc, `Number of invoices: ${invoices.length}`, doc.internal.pageSize.width / 2, 55, { align: "center" })
    
    // Summary table of all invoices
    if (invoices.length > 1) {
        doc.addPage()
        let yPos = 20
        
        doc.setFontSize(18)
        doc.setTextColor(41, 128, 185)
        renderText(doc, "Invoice Summary", 14, yPos, { bold: true })
        yPos += 15
        
        const summaryData = invoices.map(invoice => [
            invoice.invoiceNumber || invoice.id.substring(0, 8),
            invoice.vendorName || "Unknown Vendor",
            invoice.issueDate ? formatDate(invoice.issueDate) : "N/A",
            invoice.amount ? formatCurrency(invoice.amount, invoice.currency || "USD") : "N/A",
            invoice.status || "N/A"
        ])
        
        autoTable(doc, {
            startY: yPos,
            head: [["Invoice #", "Vendor", "Date", "Amount", "Status"]],
            body: summaryData,
            theme: "striped",
            headStyles: {
                fillColor: [41, 128, 185],
                textColor: [255, 255, 255],
                fontStyle: "bold",
            },
            styles: {
                fontSize: 9,
                cellPadding: 3,
            },
        })
        
        yPos = (doc as any).lastAutoTable.finalY + 20
    }

    // Process each invoice in detail
    for (let index = 0; index < invoices.length; index++) {
        const invoice = invoices[index]
        const extractedData = invoice.extractedData && typeof invoice.extractedData === "object"
            ? (invoice.extractedData as unknown as ExtractedData)
            : undefined

        const isArabic = extractedData?.meta?.language === "ar" ||
            (invoice.vendorName && containsArabic(invoice.vendorName)) ||
            (extractedData?.vendor?.address && containsArabic(extractedData.vendor.address))

        if (index > 0 || invoices.length > 1) {
            doc.addPage()
        }
        
        if (invoices.length === 1) {
            // For single invoice, start fresh
            let yPos = 20
        }
        
        let yPos = invoices.length > 1 ? 20 : (index === 0 ? 80 : 20)

        // Invoice header with enhanced styling
        doc.setFontSize(20)
        doc.setTextColor(41, 128, 185)
        renderText(doc, `Invoice: ${invoice.invoiceNumber || invoice.id}`, 14, yPos, { bold: true })
        
        // Add decorative line
        doc.setLineWidth(1)
        doc.setDrawColor(41, 128, 185)
        doc.line(14, yPos + 3, 200, yPos + 3)
        yPos += 15

        // Basic invoice information in columns
        doc.setFontSize(11)
        const leftCol = 14
        const rightCol = 110

        // Left column - Basic info
        if (fields.includes("invoiceNumber")) {
            doc.setTextColor(100, 100, 100)
            renderText(doc, "Invoice Number:", leftCol, yPos, { bold: true })
            doc.setTextColor(60, 60, 60)
            renderText(doc, invoice.invoiceNumber || "N/A", leftCol + 35, yPos)
            yPos += 6
        }

        if (fields.includes("date")) {
            doc.setTextColor(100, 100, 100)
            renderText(doc, "Issue Date:", leftCol, yPos, { bold: true })
            doc.setTextColor(60, 60, 60)
            renderText(doc, invoice.issueDate ? formatDate(invoice.issueDate) : "N/A", leftCol + 35, yPos)
            yPos += 6
        }

        if (fields.includes("dueDate")) {
            doc.setTextColor(100, 100, 100)
            renderText(doc, "Due Date:", leftCol, yPos, { bold: true })
            doc.setTextColor(60, 60, 60)
            renderText(doc, invoice.dueDate ? formatDate(invoice.dueDate) : "N/A", leftCol + 35, yPos)
            yPos += 6
        }

        // Right column - Status and amounts
        let rightYPos = 35
        if (fields.includes("status")) {
            doc.setTextColor(100, 100, 100)
            renderText(doc, "Status:", rightCol, rightYPos, { bold: true })
            doc.setTextColor(60, 60, 60)
            renderText(doc, invoice.status || "N/A", rightCol + 25, rightYPos)
            rightYPos += 6
        }

        if (fields.includes("amount")) {
            doc.setTextColor(100, 100, 100)
            renderText(doc, "Amount:", rightCol, rightYPos, { bold: true })
            doc.setTextColor(60, 60, 60)
            const amount = invoice.amount ? formatCurrency(invoice.amount, invoice.currency || "USD") : "N/A"
            renderText(doc, amount, rightCol + 25, rightYPos)
            rightYPos += 6
        }

        if (fields.includes("category")) {
            doc.setTextColor(100, 100, 100)
            renderText(doc, "Category:", rightCol, rightYPos, { bold: true })
            doc.setTextColor(60, 60, 60)
            renderText(doc, invoice.category?.name || "Uncategorized", rightCol + 25, rightYPos)
            rightYPos += 6
        }

        yPos = Math.max(yPos, rightYPos) + 10

        // Enhanced extracted data sections
        if (extractedData) {
            // Vendor Information
            if (fields.includes("vendor") && extractedData.vendor) {
                yPos = renderStructuredData(doc, extractedData.vendor, 14, yPos, "Vendor Information", Boolean(isArabic))
            }

            // Customer Information  
            if (fields.includes("customer") && extractedData.customer) {
                yPos = renderStructuredData(doc, extractedData.customer, 14, yPos, "Customer Information", Boolean(isArabic))
            }

            // Financial Information
            if (fields.includes("amount") && extractedData.financials) {
                yPos = renderStructuredData(doc, extractedData.financials, 14, yPos, "Financial Breakdown", Boolean(isArabic))
            }

            // Payment Information
            if (extractedData.payment) {
                yPos = renderStructuredData(doc, extractedData.payment, 14, yPos, "Payment Information", Boolean(isArabic))
            }

            // Enhanced line items with all attributes
            if (fields.includes("lineItems")) {
                // First try to get actual database line items
                let hasLineItems = false
                let lineItemsData: any[] = []

                if (invoice.lineItems && Array.isArray(invoice.lineItems) && invoice.lineItems.length > 0) {
                    hasLineItems = true
                    lineItemsData = invoice.lineItems.map(item => [
                        item.description || "N/A",
                        String(item.quantity || 0),
                        item.unitPrice ? formatCurrency(item.unitPrice, invoice.currency || "USD") : "N/A",
                        item.totalPrice ? formatCurrency(item.totalPrice, invoice.currency || "USD") : "N/A"
                    ])
                } else if (extractedData?.lineItems && Array.isArray(extractedData.lineItems)) {
                    // Fallback to extracted data line items
                    hasLineItems = true
                    lineItemsData = extractedData.lineItems.map((item: any) => [
                        item.description || item.name || "N/A",
                        String(parseFloat(item.quantity) || 1),
                        formatCurrency(parseFloat(item.unitPrice || 0), invoice.currency || "USD"),
                        formatCurrency(parseFloat(item.amount || item.totalPrice || item.total || 0), invoice.currency || "USD")
                    ])
                }

                if (hasLineItems) {
                    if (yPos > 200) {
                        doc.addPage()
                        yPos = 20
                    }

                    doc.setFontSize(14)
                    doc.setTextColor(41, 128, 185)
                    renderText(doc, "Line Items", 14, yPos, { bold: true })
                    doc.setLineWidth(0.5)
                    doc.line(14, yPos + 2, 80, yPos + 2)
                    yPos += 12

                    // Create comprehensive line items table
                    const headers = ["Description", "Qty", "Unit Price", "Total"]

                    autoTable(doc, {
                        startY: yPos,
                        head: [headers],
                        body: lineItemsData,
                        theme: "grid",
                        headStyles: {
                            fillColor: [41, 128, 185],
                            textColor: [255, 255, 255],
                            fontStyle: "bold",
                            halign: isArabic ? "right" : "left",
                        },
                        styles: {
                            fontSize: 9,
                            halign: isArabic ? "right" : "left",
                            cellPadding: 3,
                        },
                        alternateRowStyles: { fillColor: [245, 245, 245] },
                    })

                    yPos = (doc as any).lastAutoTable.finalY + 15

                    // Additional line item details if available from database
                    if (invoice.lineItems && invoice.lineItems.some(item => 
                        item.taxRate || item.taxAmount || item.discount || item.productSku || item.notes
                    )) {
                        if (yPos > 200) {
                            doc.addPage()
                            yPos = 20
                        }

                        doc.setFontSize(12)
                        doc.setTextColor(41, 128, 185)
                        renderText(doc, "Line Item Details", 14, yPos, { bold: true })
                        yPos += 10

                        const detailHeaders = ["Description", "SKU", "Tax Rate", "Tax Amount", "Discount", "Notes"]
                        const detailData = invoice.lineItems.map(item => [
                            item.description || "N/A",
                            item.productSku || "N/A",
                            item.taxRate ? `${(item.taxRate * 100).toFixed(2)}%` : "N/A",
                            item.taxAmount ? formatCurrency(item.taxAmount, invoice.currency || "USD") : "N/A",
                            item.discount ? formatCurrency(item.discount, invoice.currency || "USD") : "N/A",
                            item.notes || "N/A"
                        ])

                        autoTable(doc, {
                            startY: yPos,
                            head: [detailHeaders],
                            body: detailData,
                            theme: "striped",
                            headStyles: {
                                fillColor: [52, 152, 219],
                                textColor: [255, 255, 255],
                                fontStyle: "bold",
                            },
                            styles: { fontSize: 8, cellPadding: 2 },
                        })

                        yPos = (doc as any).lastAutoTable.finalY + 15
                    }
                } else {
                    // Show message when no line items are found
                    if (yPos > 250) {
                        doc.addPage()
                        yPos = 20
                    }

                    doc.setFontSize(12)
                    doc.setTextColor(41, 128, 185)
                    renderText(doc, "Line Items", 14, yPos, { bold: true })
                    yPos += 10

                    doc.setFontSize(10)
                    doc.setTextColor(120, 120, 120)
                    renderText(doc, "No line items found for this invoice.", 14, yPos)
                    renderText(doc, "Check the original document or extracted data for item details.", 14, yPos + 6)
                    yPos += 20
                }
            }

            // Terms and Conditions
            if (fields.includes("termsAndConditions") && extractedData.termsAndConditions) {
                if (yPos > 220) {
                    doc.addPage()
                    yPos = 20
                }

                doc.setFontSize(12)
                doc.setTextColor(41, 128, 185)
                renderText(doc, "Terms and Conditions", 14, yPos, { bold: true })
                yPos += 8

                doc.setFontSize(10)
                doc.setTextColor(60, 60, 60)
                const termsText = String(extractedData.termsAndConditions)
                const splitTerms = doc.splitTextToSize(termsText, 180)
                
                splitTerms.forEach((line: string) => {
                    if (yPos > 280) {
                        doc.addPage()
                        yPos = 20
                    }
                    renderText(doc, line, 14, yPos, { 
                        align: containsArabic(line) ? "right" : "left" 
                    })
                    yPos += 5
                })
                yPos += 10
            }

            // Notes
            if (fields.includes("notes")) {
                const notesText = invoice.notes || extractedData.notes
                if (notesText) {
                    if (yPos > 220) {
                        doc.addPage()
                        yPos = 20
                    }

                    doc.setFontSize(12)
                    doc.setTextColor(41, 128, 185)
                    renderText(doc, "Notes", 14, yPos, { bold: true })
                    yPos += 8

                    doc.setFontSize(10)
                    doc.setTextColor(60, 60, 60)
                    const formattedNotes = formatNotes(notesText)
                    const splitNotes = doc.splitTextToSize(formattedNotes, 180)
                    
                    splitNotes.forEach((line: string) => {
                        if (yPos > 280) {
                            doc.addPage()
                            yPos = 20
                        }
                        renderText(doc, line, 14, yPos, { 
                            align: containsArabic(line) ? "right" : "left" 
                        })
                        yPos += 5
                    })
                    yPos += 10
                }
            }

            // Dynamic/Custom Fields (everything else in extractedData)
            if (fields.includes("dynamicData")) {
                const excludedKeys = ['vendor', 'customer', 'financials', 'payment', 'meta', 'notes', 'termsAndConditions']
                const dynamicData: Record<string, any> = {}
                
                Object.entries(extractedData).forEach(([key, value]) => {
                    if (!excludedKeys.includes(key) && value && value !== '') {
                        dynamicData[key] = value
                    }
                })

                if (Object.keys(dynamicData).length > 0) {
                    if (yPos > 180) {
                        doc.addPage()
                        yPos = 20
                    }
                    yPos = renderStructuredData(doc, dynamicData, 14, yPos, "Additional Information", Boolean(isArabic))
                }
            }

            // Metadata and audit information
            if (fields.includes("metadata") && extractedData.meta) {
                if (yPos > 200) {
                    doc.addPage()
                    yPos = 20
                }

                doc.setFontSize(12)
                doc.setTextColor(41, 128, 185)
                renderText(doc, "Processing Information", 14, yPos, { bold: true })
                yPos += 8

                doc.setFontSize(9)
                doc.setTextColor(120, 120, 120)

                const metaInfo = [
                    `Created: ${formatDate(invoice.createdAt)}`,
                    `Updated: ${formatDate(invoice.updatedAt)}`,
                    `Invoice ID: ${invoice.id}`,
                ]

                if (extractedData.meta.language) {
                    metaInfo.push(`Language: ${extractedData.meta.languageName || extractedData.meta.language}`)
                }

                if (extractedData.meta.confidence?.overall) {
                    metaInfo.push(`Extraction Confidence: ${extractedData.meta.confidence.overall}%`)
                }

                // Check for audit data in meta (it might be nested differently)
                const metaData = extractedData.meta as any
                if (metaData.audit) {
                    metaInfo.push(`Audit Status: ${metaData.audit.status}`)
                    if (metaData.audit.fraudIndicators?.score) {
                        metaInfo.push(`Fraud Score: ${metaData.audit.fraudIndicators.score}`)
                    }
                }

                metaInfo.forEach(info => {
                    renderText(doc, info, 14, yPos)
                    yPos += 5
                })
            }
        }

        // Add invoice separator for multiple invoices
        if (index < invoices.length - 1) {
            yPos += 20
            doc.setLineWidth(0.5)
            doc.setDrawColor(200, 200, 200)
            doc.line(14, yPos, 200, yPos)
        }
    }

    // Add page numbers
    const pageCount = doc.getNumberOfPages()
    for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i)
        doc.setFontSize(8)
        doc.setFont("helvetica", "normal")
        doc.setR2L(false)
        doc.setTextColor(120, 120, 120)
        doc.text(`Page ${i} of ${pageCount}`, doc.internal.pageSize.width / 2, doc.internal.pageSize.height - 8, { align: "center" })
        
        // Add footer line
        doc.setLineWidth(0.1)
        doc.setDrawColor(180, 180, 180)
        doc.line(14, doc.internal.pageSize.height - 15, doc.internal.pageSize.width - 14, doc.internal.pageSize.height - 15)
    }

    return Buffer.from(doc.output("arraybuffer"))
}
