import { currentUser } from '@clerk/nextjs/server';
import db from '@/db/db';

/**
 * Gets the database user ID for the currently authenticated Clerk user
 * Creates a user record in the database if it doesn't exist
 */
export async function getCurrentUserId(): Promise<string | null> {
  try {
    // Get the current user directly from Clerk
    const user = await currentUser();

    if (!user || !user.emailAddresses || user.emailAddresses.length === 0) {
      return null;
    }

    const email = user.emailAddresses[0].emailAddress;

    // Try to find the user in the database
    let dbUser = await db.user.findUnique({
      where: { email }
    });

    // If user doesn't exist in the database, create it
    if (!dbUser) {
      dbUser = await db.user.create({
        data: {
          email,
          clerkId: user.id,
        }
      });
    }

    return dbUser.id;
  } catch (error) {
    throw error;
  }
}

/**
 * Checks if the current user is authorized to access a resource
 * @param resourceUserId The user ID associated with the resource
 */
export async function isAuthorized(resourceUserId: string): Promise<boolean> {
  const currentUserId = await getCurrentUserId();
  return currentUserId === resourceUserId;
}
