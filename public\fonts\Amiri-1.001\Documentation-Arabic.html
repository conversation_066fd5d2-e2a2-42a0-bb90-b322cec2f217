<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="" xml:lang="">
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <meta name="keywords" content="خط, عربی, مطبعة, أمیریة, أمیری, یونیكود, أوبن تیب" />
  <title>الخط الأمیری</title>
  <style>
    code{white-space: pre-wrap;}
    span.smallcaps{font-variant: small-caps;}
    span.underline{text-decoration: underline;}
    div.column{display: inline-block; vertical-align: top; width: 50%;}
    div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
    ul.task-list{list-style: none;}
  </style>
  <link rel="stylesheet" href="Documentation-Arabic.css" />
</head>
<body>
<header id="title-block-header">
<h1 class="title">الخط الأمیری</h1>
</header>
<p>الخط الأمیری خط نسخی موجه لطباعة الكتب و النصوص الطویلة، و هو إحیاء و محاكاة للخط الطباعی الجمیل الذی تمیزت به مطبعة بولاق منذ أوائل القرن العشرین.</p>
<p>یتمیز خط المطابع الأمیریة بجمالیته و مراعاته لفن الخط العربی، بأسلوب نسخی جمیل، و فی ذات الوقت یراعی متطلبات الطباعة و القیود التی تفرضها، من غیر إفراط فی جانب علىٰ حساب الآخر. و لهذا یتمیز بمناسبته للصف الطباعی عموما، و لصف الكتب خصوصا. و قد استُخدِم هذا الخط فی طباعة المصحف الأمیری، و هو من المصاحف القلیلة التی طبعت بالصف المعدنی و لم یخطها خطاط بیده، و هذا یجعله مادة خصبة لبناء خط حاسوبی مناسب لصف النصوص القرآنیة.</p>
<p>یهدف مشروع الخط الأمیری إلىٰ إحیاء تقالید و جمالیات الطباعة العربیة و موائمتها لتقنیات عصر الحواسیب، مع إتاحتها للعموم.</p>
<h1 id="الخصائص">الخصائص</h1>
<p>یدعم الخط الأمیری كل الحروف و الرموز العربیة فی الإصدارة السادسة من معیار <a href="https://unicode.org/versions/Unicode6.0.0" title="یونیكود 6.0">یونیكود</a> مما یجعله مناسبا لكتابة أی لغة من اللغات التی تستخدم الخط العربی و مدعومة فی یونیكود، مثل الكردیة و الفارسیة و الأردیة و الباشتو و الملایویة و الهوسا و الولفیة، علىٰ سبیل المثال لا الحصر. یشمل هذا أیضا كل رموز و علامات الضبط القرآنی فی یونیكود، راجع <a href="#النصوص-القرآنیة">الملاحظات حول النصوص القرآنیة</a> لمزید من التفاصیل.</p>
<h2 id="أوبن-تیب">أوبن تیب</h2>
<p>یستخدم الخط الأمیری تقنیة <a href="https://ar.wikipedia.org/wiki/%D8%A3%D8%A8%D9%86%E2%80%8C_%D8%AA%D9%8A%D8%A8" title="صفحة ویكیبیدیا عن أوبن تیب">أوبن تیب</a> للخطوط الحاسوبیة الذكیة التی تتیح تحكما واسعا فی أشكال الحروف حسب السیاق المحیط بها و فی تموضع علامات التشكیل، و هی تقنیة مدعومة فی أغلب أنظمة التشغیل الحدیثة. یستخدم الخط الأمیری تقنیة أوبن تیب بشكل مكثف قلما تجاریه أی من الخطوط الحاسوبیة الأخرىٰ و لذا قد تحدث مشاكل عند استخدامه مع البرمجیات التی لا تطبّق معیار أوبن تیب بدرجة كافیة.</p>
<h3 id="لفظ-الجلالة">لفظ الجلالة</h3>
<p>یأخد لفظ الجلالة فی خط النسخ شكلا خاصا به یختلف عن الشكل العادی للتتابع لام لام هاء. یدعم الخط الأمیری الشكل الخاص للفظ الجلالة سواء أتی منفردا: ”الله“، أو معطوفا بالفاء: ”فلله“، أو مجرورا باللام: ”لله“.</p>
<p>یضع الخط شدة و ألفا صغیرة علىٰ اللام الثانیة إن لم تكن مُشَكَّلة، أما فی حال شُكلت فيُترك تشكیلها كما هو: ”اللَّه“.</p>
<p>یستخدم الخط مجموعة من القواعد تحدد متىٰ یستخدم الشكل الخاص بلفظ الجلالة لتفادی استخدامه مع الألفاظ التی تتركب من نفس تتابع الحروف (راجع <a href="https://web.archive.org/web/20120724090018/graphics4arab.com/showthread.php?t=3975">هذا النقاش</a> للاطلاع علىٰ تفاصیل أكثر حول أصل الفكرة و صاحبها):</p>
<ul>
<li>یأخذ التتابع <code>لله</code> شكل لفظ الجلالة فی الحالات التالیة:
<ul>
<li>مسبوقا بألف غیر مهموزة أو ألف ممدودة أو ألف وصل: <code>الله</code>، <code>آلله</code>، <code>ٱلله</code>.
<ul>
<li>الألف غیر مسبوقة بأی حرف، أو:</li>
<li>مسبوقة بهمزة أو باء أو تاء أو فاء أو كاف أو هاء أو واو: <code>ءالله</code>، <code>بالله</code>، <code>تالله</code>، <code>فالله</code>، <code>كالله</code>، <code>هالله</code>، <code>والله</code>.</li>
</ul></li>
<li>مسبوقا بفاء: <code>فلله</code>.
<ul>
<li>الفاء غیر مشكلة أو مشكلة بالفتح.</li>
</ul></li>
<li>اللام الأولىٰ غیر مشكلة أو مشكلة بالكسر.</li>
<li>اللام الثانیة غیر مشكلة أو مشكلة بالشدة أو بالشدة و الفتحة أو الشدة و الألف الصغیرة.</li>
</ul></li>
</ul>
<p>فمثلا <code>عبدالله</code> تظهر ”عبدالله“ لأن الألف مسبوقة بدال، و الصواب فصلها بمسافة <code>عبد الله</code> لتظهر ”عبد الله“. و لكتابة كلمة <code>فلَّلَهُ</code> (من الجذر ”فلّ“) یكفی تشكیل اللام الأولىٰ بالشدة لتفادی استخدام شكل لفظ الجلالة: ”فلّله“. و كلمة <code>خالله</code> لن تأخذ شكل لفظ الجلالة ”خالله“، حتىٰ دون تشكیلها.</p>
<h3 id="الأرقام">الأرقام</h3>
<p>یدعم الخط الأمیری أربعة مجموعات من الأرقام العربیة المشرقیة:</p>
<dl>
<dt><span class="tnum" lang="ar">٠١٢٣٤٥٦٧٨٩</span></dt>
<dd>و هی الأرقام المستخدمة فی مصر و غیرها من بلاد المشرق العربی، و یمكن كتابتها باستخدام رموز یونیكود <code>U+0660</code>–<code>U+0669</code> أو مباشرة من لوحة المفاتیح التی تدعمها.
</dd>
<dt><span class="tnum" lang="fa">۰۱۲۳۴۵۶۷۸۹</span></dt>
<dd>و هی الأرقام المستخدمة فی اللغة الفارسیة، و یمكن كتابتها باستخدام رموز یونیكود <code>U+06F0</code>–<code>U+06F9</code>، أو مباشرة من لوحة المفاتیح التی تدعمها.
</dd>
<dt><span class="tnum" lang="ur">۰۱۲۳۴۵۶۷۸۹</span></dt>
<dd>و هی المستخدمة فی اللغة الأردیة. لا تحتوی یونیكود رموزا مستقلة لهذه الأرقام، و لاستخدامها علیك استخدام الأرقام الفارسیة و جعل لغة النص الأردیة و إذا كان البرنامج یدعم خصائص أوبن تیب المتقدمة فستُفعّل التنویعة الأردیة تلقائيًا.
</dd>
<dt><span class="tnum" lang="sd">۰۱۲۳۴۵۶۷۸۹</span></dt>
<dd>و هی الأرقام المستخدمة فی اللغة السندیة و ینطبق علیها ما ینطبق علىٰ الأرقام الأردیة مع استخدام اللغة السندیة بالطبع.
</dd>
<dt><span class="tnum" lang="ks">۰۱۲۳۴۵۶۷۸۹</span></dt>
<dd>مثل سابقتیها، لكن للكشمیریة.
</dd>
</dl>
<h3 id="العلامات-المحیطة-للأرقام">العلامات المحیطة للأرقام</h3>
<p>فی یونیكود عدد من العلامات العربیة الخاصة التی تضم الأرقام داخلها، أی إذا تبعت أی من هذه العلامات برقم أو أكثر فإنها تحیط هذا الرقم. یدعم الخط الأمیری هذا العلامات، و الجدول التالی یسرد كل علامة و رقمها فی یونیكود و أقصی عدد من الأرقام یمكن أن تحیطه كل علامة (أی أرقام أكثر من هذا العدد ستكون خارج العلامة). لاحظ أن الأرقام یجب أن تتبع العلامة مباشرة دون أی فاصل بینها و لا حتىٰ مسافة فارغة.</p>
<table>
<thead>
<tr class="header">
<th style="text-align: right;">           </th>
<th style="text-align: center;">یونیكود</th>
<th style="text-align: center;">عدد الأرقام</th>
<th style="text-align: center;">مثال</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: right;">علامة الرقم</td>
<td style="text-align: center;"><code>U+0600</code></td>
<td style="text-align: center;">٤</td>
<td style="text-align: center;">؀١٢٣٤</td>
</tr>
<tr class="even">
<td style="text-align: right;">علامة السنة</td>
<td style="text-align: center;"><code>U+0601</code></td>
<td style="text-align: center;">٤</td>
<td style="text-align: center;">؁١٢٣٤</td>
</tr>
<tr class="odd">
<td style="text-align: right;">علامة الحاشیة</td>
<td style="text-align: center;"><code>U+0602</code></td>
<td style="text-align: center;">٢</td>
<td style="text-align: center;">؂١٢</td>
</tr>
<tr class="even">
<td style="text-align: right;">علامة الصفحة</td>
<td style="text-align: center;"><code>U+0603</code></td>
<td style="text-align: center;">٣</td>
<td style="text-align: center;">؃١٢٣</td>
</tr>
<tr class="odd">
<td style="text-align: right;">علامة سموت</td>
<td style="text-align: center;"><code>U+0604</code></td>
<td style="text-align: center;">٤</td>
<td style="text-align: center;">؄١٢٣٤</td>
</tr>
<tr class="even">
<td style="text-align: right;">رقم الآیة</td>
<td style="text-align: center;"><code>U+06DD</code></td>
<td style="text-align: center;">٣</td>
<td style="text-align: center;">۝١٢٣</td>
</tr>
</tbody>
</table>
<h3 id="الخصائص-الاختیاریة">الخصائص الاختیاریة</h3>
<p>یحتوی الخط الأمیری علىٰ بعض الخصائص غیر المفعلة مبدئیا و التی یمكن تفعیلها من البرمجیات التی تتیح التحكم فی تفعیل و تعطیل خیارات أوبن تیب.</p>
<dl>
<dt><code>pnum</code> ‏(<em>Proportional Numbers</em>)</dt>
<dd>الأرقام متغیرة العرض. لكل الأرقام فی الخط الأمیری نفس العرض بحیث تظهر بشكل مناسب فی الجداول و غیرها من الاستخدامات التی لا یلائمها أن یختلف العرض من رقم لآخر. عند تفعیل هذه الخاصیة سيُصبح عرض الأرقام متغیرا بما یناسب شكل كل رقم، لاستخدامها فی الحالات التی لا یناسبها العرض الموحد.
</dd>
<dd><table>
<thead>
<tr class="header">
<th style="text-align: center;">ثابتة</th>
<th style="text-align: right;">متغیرة</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: center;"><span class="tnum">٠١٢٣٤٥٦٧٨٩</span></td>
<td style="text-align: right;">٠١٢٣٤٥٦٧٨٩</td>
</tr>
<tr class="even">
<td style="text-align: center;"><span class="tnum">١١١١١١١١١١</span></td>
<td style="text-align: right;">١١١١١١١١١١</td>
</tr>
<tr class="odd">
<td style="text-align: center;"><span class="tnum">٨٨٨٨٨٨٨٨٨٨</span></td>
<td style="text-align: right;">٨٨٨٨٨٨٨٨٨٨</td>
</tr>
</tbody>
</table>
</dd>
<dt><code>ss01</code> ‏(<em>Stylistic Set 1</em>)</dt>
<dd>إذا أتت الباء (أو ما شابهها من الحروف المنقوطة من أسفل مثل پ) بعد راء أو واو أو ما شابههما من الحروف، تتحرك النقطة إلىٰ ما أسفل الراء أو الواو لتفادی التلامس. فمثلا عند تفعیل هذه الخاصیة: <span class="ss01">”وبه“، ”ربها“، ”فربت“</span>، بینما المبدئی هو توسیع المسافة قلیلا بین الراء و الباء: ”وبه“، ”ربها“، ”فربت“.
</dd>
<dt><code>ss02</code> ‏(<em>Stylistic Set 2</em>)</dt>
<dd>عند تفعیل هذه الخاصیة یأخذ تتابع المیم المتوسطة و الألف شكلا خاصا: <span class="ss02">”فیما“، ”لضمان“، ”لقمان“، ”السماء“</span>، بینما المبدئی عدم استعمال أی شكل خاص: ”فیما“، ”لضمان“، ”لقمان“، ”السماء“.
</dd>
<dt><code>ss03</code> ‏(<em>Stylistic Set 3</em>)</dt>
<dd>علامتی @ و &amp; معربتین (<span class="ss03">علامتی @ &amp;</span>).
</dd>
<dt><code>ss04</code> ‏(<em>Stylistic Set 4</em>)</dt>
<dd>عند تفعیل هذه الخاصیة ستُسخدم تنویعة من حرف گاف أقرب لأسلوب خط النسخ من التنویعة المبدئیة الشائعة فی الخطوط الأخرىٰ والمستوحاة من شكل الحرف فی الخط الفارسی، فتحصل علىٰ <span class="ss04">”گ“ و ”‍گ“</span> بدلا من ”گ“ و ”‍گ“.
</dd>
<dt><code>ss05</code> ‏(<em>Stylistic Set 5</em>)</dt>
<dd>عند تفعیل هذه الخاصیة توضع كسرة الحرف المشدد تحت الشدة و لیس الحرف، أی <span class="ss05">”بِّ“</span> بدلا من ”بِّ“.
</dd>
<dt><code>ss06</code> ‏(<em>Stylistic Set 6</em>)</dt>
<dd>تفعیل هذه الخاصیة یمنع إضافة الشدة و الألف الخنجریة تلقائيًا فوق لفظ الجلالة، فمثلا عند كتابة <code>الله</code> و <code>فلله</code> تصبح <span class="ss06">”الله“</span> و <span class="ss06">”فلله“</span> بدلا من ”الله“ و ”فلله“.
</dd>
<dt><code>ss07</code> ‏(<em>Stylistic Set 7</em>)</dt>
<dd>تفعیل هذه الخاصیة یلغی میزة الكشیدة المقوسة، فمثلا <code>محمـــــــد</code> تصبح <span class="ss07">”محمـــــــد“</span> بدلا من ”محمـــــــد“.
</dd>
<dt><code>ss08</code> ‏(<em>Stylistic Set 8</em>)</dt>
<dd>تفعیل هذه الخاصیة یفعل التنویعة المقلوبة من الفاصلة و الفاصلة المنقوطة كالتی تستخدم فی السندیة و الجاویة، فتصبح <span class="ss08">”،؛“</span> بدلا من ”،؛“.
</dd>
</dl>
<h2 id="النصوص-القرآنیة">النصوص القرآنیة</h2>
<p>تدعم یونیكود أغلب الرموز و العلامات المطلوبة فی رسم المصاحف، لكن توجد بعض المشاكل و العلامات المتشابهة و التی نوضح هنا ما نراه أفضل الطرق لترمیز النص القرآنی فی یونیكود، و التی یدعمها الخط الأمیری.</p>
<h3 id="الهمزة-المفردة-ء">الهمزة المفردة (ء)</h3>
<p>علىٰ خلاف قواعد الإملاء الحدیثة، إذا أتت الهمزة المفردة فی وسط الكلمة بین حرفین یتصل أحدهما بالآخر، فإنها لا ترسم علىٰ نبرة كما فی ”شیءا“، أو تقلب ألف مد مثل ”لءادم“، بل ترسم فی الفراغ بین الحرفین دون التأثیر علىٰ اتصالهما. للأسف، الهمزة المفردة فی یونیكود حرف فاصل، أی إنها إن أتت بین حرفین متصلین تفصلهما، لذا إذا استعملت الهمزة المفردة فی الكلمتین السابقتین مع الخطوط العادیة فستظهران ”شی‌ء‌ا“ و ”ل‌ء‌ادم“. یدعم الخط الأمیری الهمزة فی وسط الكلمة بالمخالفة لقواعد یونیكود الحالیة لخطئها، لذا فی تلك الحالات یكفی استخدام الهمزة المفردة (ء) و رمزها <code>U+0621</code> و ستظهر الكلمة بشكلها الصحیح، و نأمل فی المستقبل أن تُصوّب قواعد یونیكود.</p>
<h3 id="الحروف-الصغیرة">الحروف الصغیرة</h3>
<dl>
<dt>الألف الصغیرة (◌ٰ)</dt>
<dd>رسم المصاحف المستخدمة فی بلاد المشرق العربی یحتوی نوعین مختلفین من الألف الصغیرة، الأولىٰ توضع فوق الحرف لتدل علىٰ أنه یقلب ألفا، كما فی ”الصلوٰة“ و ”موسىٰ“ و هذا الحرف موجود فی یونیكود، و رمزه <code>U+0670</code>. الألف الصغیرة الأخرىٰ تدل علىٰ الألف المتروكة فی الرسم العثمانی و لذا تأتی بعد الحرف و لیست فوقه، مثل ”هـٰذا“ و ”ذ ٰلك“، لكن لا تحتوی یونیكود علىٰ رمز مستقل لهذه الألف، بل تكتب علىٰ كشیدة إذا أتت بین حرفین متصلین، و علىٰ مسافة غیر فاصلة <code>U+00A0</code> فی ما عدا ذلك.
</dd>
<dt>الواو (ۥ) و الیاء (ۦ) الصغیرتین</dt>
<dd>تحتوی یونیكود علىٰ واوین و یائین، واحدة مفردة لتستخدم فی آخر الكلمة كما فی ”رسولهۥ“ و ”رسولهۦ“ أو بعد حرف لا یتصل بما بعده مثل ”داوۥد“، و رمزیهما <code>U+06E5</code> و <code>U+06E6</code> علىٰ التوالی، و الأخرىٰ للاستخدام فی وسط الكلمة و توضع فوق تطویل كما فی ”إبراهـۧم“ و ”لیسـࣳءوا“، و رمزیهما <code>U+06E7</code> و <code>U+08F3</code> علىٰ التوالی.
</dd>
</dl>
<h3 id="علامة-المد-اللازم">علامة المد اللازم (◌ۤ)</h3>
<p>تحتوی یونیكود علامتی مدة، الأولىٰ تستخدم مع الألف غیر المهموزة لتكوین ألف المد (آ)، و رمزها <code>U+0653</code>، و هذه لا تأتی فی رسم المصحف، و الأخرىٰ للدلالة علىٰ المد اللازم كما فی ”الۤمۤ“، و رمزها <code>U+06E4</code>، و هی المستخدمة فی رسم المصحف، لذا ینبغی عدم الخط بینهما لأن لكل واحدة خصائص فی الخط تناسب الاستخدام الذی وضعت له.</p>
<h3 id="التنوین">التنوین</h3>
<p>یفرق رسم المصاحف بین نوعین من التنوین، التنوین المتراكب ”◌ً ◌ٌ ◌ٍ“، والتنوین المتتابع ”◌ࣰ ◌ࣱ ◌ࣲ“. التنوین المتراكب هو التنوین المعتاد المتاح فی لوحة المفاتیح و رموزه <code>U+064B</code> و <code>U+064C</code> و <code>U+064D</code> علىٰ التوالی. التنوین المتتابع لم يَضف إلا فی الإصدارة ٦٫١ من معیار یونیكود (صدرت فی ینایر ٢٠١٢)، و رموزه <code>U+08F0</code> و <code>U+08F1</code> و <code>U+08F2</code> علىٰ التوالی.</p>
<h3 id="الیاء">الیاء</h3>
<p>فی أغلب المصاحف لا تنقط الیاء إذا أتت مفردة أو فی آخر الكلمة، أی تكتب ”ی“ و ”‍ی“ (وهو الإملاء الشائع فی مصر أیضا). تحتوی یونیكود علىٰ یاء خاصة رمزها <code>U+06CC</code> لا تختلف عن الیاء العادیة إلا فی عدم نقطها فی هاتین الحالتین، و لذا تناسب هذا الاستخدام. ینبغی عدم الخلط بین هذه الیاء و الألف المقصورة التی لا تنقط بغض النظر عن موقعها فی الكلمة، مثل ”هدىٰهم“ و ”ءاتىٰه“ و ”موسىٰ“.</p>
<h1 id="المشاكل-المعروفة">المشاكل المعروفة</h1>
<p>لن یعمل الخط الأمیری بكامل مزایاه مع البرمجیات التی لا تدعم خصائص أوبن تیب بشكل سلیم، أو لا تدعمها علىٰ الإطلاق.</p>
<p>المساواة بالكشیدة فی میكروسوفت أوفیس غیر متوافقة مع الخط الأمیری، و یجب استخدام نوع مساواة آخر للحصول علىٰ نتائج مقبولة.</p>
</body>
</html>
