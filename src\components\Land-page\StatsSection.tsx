/* eslint-disable @next/next/no-img-element */

export default function StatsSection() {
  return (
    <div className="relative w-full bg-[#000000] py-10 sm:py-16 md:py-20 px-4 md:px-6 lg:px-8 overflow-hidden">
      {/* Grid Background */}
      <div className="absolute inset-0 z-0 overflow-hidden">
        {/* Left to right gradient overlay */}
        <div className="absolute inset-0 bg-black opacity-75 z-10"></div>

        {/* Square Grid - Bigger cells */}
        <div className="h-full w-full">
          <div
            className="h-full w-full grid"
            style={{
              gridTemplateColumns: "repeat(auto-fill, minmax(60px, 1fr))",
              gridTemplateRows: "repeat(auto-fill, minmax(60px, 1fr))",
              gridAutoFlow: "row",
            }}
          >
            {Array.from({ length: 100 }).map((_, i) => (
              <div key={i} className="border border-[#454545]"></div>
            ))}
          </div>
        </div>
      </div>

      {/* Content with gray-950 background */}
      <div className="relative z-20 max-w-6xl mx-auto">
        <div className="p-4 sm:p-6 md:p-10 rounded-t-2xl grid md:grid-cols-2 gap-6 md:gap-10 items-center bg-black">
          <div className="flex flex-col items-center md:items-start gap-4 sm:gap-6 md:gap-8">
            <div className="flex items-center gap-2 text-[#ffffff] text-xs sm:text-sm">
              <div className="h-px w-4 sm:w-6 bg-[#395F71]"></div>
              <span>Statistics</span>
              <div className="h-px w-4 sm:w-6 bg-[#395F71]"></div>
            </div>

            <h2 className="text-white text-2xl sm:text-3xl md:text-4xl font-bold text-center md:text-left leading-tight">
              Trusted by Finance Teams
              <br className="hidden sm:block" />Around the World
            </h2>

            <div className="bg-black text-white p-3 sm:p-4 rounded-md max-w-xs border border-neutral-700">
              <div className="flex items-center gap-2 mb-1 sm:mb-2">
                <img 
                  src="/public/trustpilot-2.svg" 
                  alt="Trustpilot star" 
                  width="24" 
                  height="24"
                  className="text-[#00b67a] w-5 h-5 sm:w-7 sm:h-7"
                />
                <span className="font-semibold text-base sm:text-xl">Trustpilot</span>
              </div>
              <img 
                src="/public/stars-5-1.svg" 
                alt="4.8 out of 5 stars rating" 
                className="h-6 sm:h-8 mb-1"
              />
              <div className="text-xs sm:text-sm">
                <span className="font-semibold">TrustScore 4.8</span> <span className="text-gray-300">2,336 reviews</span>
              </div>
            </div>
          </div>

          {/* Right Column - Stats Only */}
          <div className="grid grid-cols-2 gap-4 sm:gap-6 md:gap-8 relative">
            {/* Large decorative blob */}
            <div className="absolute right-2/4 top-1/2 -translate-y-1/2 w-[60px] sm:w-[80px] md:w-[100px] h-[200px] sm:h-[250px] md:h-[300px] rounded-full bg-[#395F71] blur-[50px] sm:blur-[80px] pointer-events-none"></div>

            <div className="flex flex-col">
              <span className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl bg-gradient-to-r from-[#8EACBB] to-[#395F71] bg-clip-text text-transparent drop-shadow-[0_0_10px_rgba(142,172,187,0.3)]">
                99.8<span className="text-white">%</span>
              </span>
              <span className="text-gray-400 text-xs sm:text-sm mt-1 sm:mt-2">Data Extraction Accuracy</span>
            </div>

            <div className="flex flex-col">
              <span className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl bg-gradient-to-r from-[#395F71] to-[#26414F] bg-clip-text text-transparent drop-shadow-[0_0_10px_rgba(57,95,113,0.3)]">
                80<span className="text-white">%</span>
              </span>
              <span className="text-gray-400 text-xs sm:text-sm mt-1 sm:mt-2">Time Saved on Processing</span>
            </div>

            <div className="flex flex-col">
              <span className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl bg-gradient-to-r from-[#26414F] to-[#8EACBB] bg-clip-text text-transparent drop-shadow-[0_0_10px_rgba(38,65,79,0.3)]">
                21.9M<span className="text-white">+</span>
              </span>
              <span className="text-gray-400 text-xs sm:text-sm mt-1 sm:mt-2">Invoices Processed</span>
            </div>

            <div className="flex flex-col">
              <span className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl bg-gradient-to-r from-[#8EACBB] to-[#26414F] bg-clip-text text-transparent drop-shadow-[0_0_10px_rgba(142,172,187,1.2)]">
                98<span className="text-white">%</span>
              </span>
              <span className="text-gray-400 text-xs sm:text-sm mt-1 sm:mt-2">Client Satisfaction</span>
            </div>
          </div>
        </div>

        {/* Trusted By Section */}
        <div className="text-center max-w-7xl mx-auto rounded-b-2xl bg-black py-8 sm:py-12 md:py-16 px-4 sm:px-8 relative after:absolute after:bottom-0 after:left-0 after:w-full after:h-[2px] after:bg-gradient-to-r after:from-transparent after:via-[#395F71] after:via-50% after:to-transparent -z-[10]">
          <p className="text-gray-300 text-xs sm:text-sm md:text-md mb-8 sm:mb-12 md:mb-16">Trusted by Fortune 500 Companies & Financial Institutions</p>
          <div className="flex flex-wrap justify-center items-center gap-6 sm:gap-8 md:gap-10 lg:gap-12">
            {["FinTech", "GlobalAccounts", "TaxPro", "InvoiceAI", "DataFinance", "FiscalEdge"].map((brand) => (
              <div key={brand} className="text-gray-300 opacity-70 font-semibold text-sm sm:text-lg md:text-xl">
                {brand}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

