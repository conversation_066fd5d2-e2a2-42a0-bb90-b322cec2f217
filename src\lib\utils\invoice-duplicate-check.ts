"use server";

import { extractTextFromDocument } from "@/lib/document-ai";
import db from "@/db/db";
import { auth } from "@clerk/nextjs/server";
import { toast } from "sonner";

// Simple invoice number pattern matcher - adjust as needed for your invoices
const INVOICE_NUMBER_PATTERNS = [
  /invoice\s+(?:no|num|number|#)[:.]?\s*([A-Za-z0-9\-_]{3,20})/i,
  /(?:no|num|number|#)\s*[:.]?\s*([A-Za-z0-9\-_]{3,20})/i,
  /\b(?:inv|invoice)[:\s-]*([A-Za-z0-9\-_]{3,20})\b/i,
  /\b(?:order|reference)[:\s-]*([A-Za-z0-9\-_]{3,20})\b/i,
];

/**
 * Extract a potential invoice number from document text
 */
function extractPotentialInvoiceNumber(text: string): string | null {
  // Try each pattern to find an invoice number
  for (const pattern of INVOICE_NUMBER_PATTERNS) {
    const match = text.match(pattern);
    if (match && match[1]) {
      return match[1].trim();
    }
  }
  return null;
}

/**
 * Check if an invoice with a specific number already exists
 */
async function checkInvoiceNumberExists(invoiceNumber: string, userId: string): Promise<boolean> {
  // Get the user from the database using clerkId
  const user = await db.user.findFirst({
    where: {
      clerkId: userId
    }
  });

  if (!user) {
    return false;
  }

  // Check if an invoice with this number already exists
  const existingInvoice = await db.invoice.findFirst({
    where: {
      invoiceNumber,
      userId: user.id
    }
  });

  return !!existingInvoice;
}

/**
 * Main function to check for duplicate invoices
 * Returns true if duplicate, false if not
 */
export async function checkDuplicateInvoice(file: File): Promise<{
  isDuplicate: boolean;
  invoiceNumber?: string;
  text?: string;
}> {
  try {
    // Get current user
    const { userId } = await auth();
    if (!userId) {
      return { isDuplicate: false };
    }

    // Extract text from the document
    const text = await extractTextFromDocument(file);
    
    // Try to find an invoice number in the text
    const potentialInvoiceNumber = extractPotentialInvoiceNumber(text);
    
    // If no invoice number found, it can't be a duplicate
    if (!potentialInvoiceNumber) {
      return { isDuplicate: false };
    }
    
    // Check if this invoice number already exists in the database
    const isDuplicate = await checkInvoiceNumberExists(potentialInvoiceNumber, userId);
    
    return { 
      text,
      isDuplicate, 
      invoiceNumber: potentialInvoiceNumber 
    };
  } catch {
    toast.error("Failed to check for duplicate invoice");
    return { isDuplicate: false };
  }
} 