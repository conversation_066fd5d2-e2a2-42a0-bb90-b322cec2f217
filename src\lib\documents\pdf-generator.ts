import PDFDocument from 'pdfkit';
import { CacheEngine } from '../ai/cache-engine';

interface InvoiceData {
  invoiceNumber: string;
  vendorName: string;
  amount: number;
  currency: string;
  issueDate: Date;
  dueDate?: Date;
  description?: string;
  items?: InvoiceItem[];
  businessInfo?: BusinessInfo;
}

interface InvoiceItem {
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

interface BusinessInfo {
  name: string;
  address: string;
  email: string;
  phone?: string;
  logo?: string;
}

interface ReportData {
  title: string;
  period: string;
  data: any;
  charts?: ChartData[];
  summary?: string;
}

interface ChartData {
  type: 'bar' | 'line' | 'pie';
  title: string;
  data: any[];
  labels: string[];
}

/**
 * Advanced PDF Generation Engine
 * Creates professional PDFs for invoices, reports, and documents
 */
export class PDFGenerator {
  
  /**
   * Generate invoice PDF with smart templates
   */
  static async generateInvoicePDF(
    invoiceData: InvoiceData,
    template: 'modern' | 'classic' | 'minimal' = 'modern'
  ): Promise<Buffer> {
    const cacheKey = CacheEngine.generateKey('invoice-pdf', invoiceData.invoiceNumber, template);
    
    return CacheEngine.getOrSet(
      cacheKey,
      'action-results', // 10-second cache for PDFs
      async () => {
        const doc = new PDFDocument({ margin: 50 });
        const chunks: Buffer[] = [];

        doc.on('data', chunk => chunks.push(chunk));
        
        await new Promise<void>((resolve) => {
          doc.on('end', resolve);
          
          // Generate PDF content based on template
          switch (template) {
            case 'modern':
              this.generateModernInvoice(doc, invoiceData);
              break;
            case 'classic':
              this.generateClassicInvoice(doc, invoiceData);
              break;
            case 'minimal':
              this.generateMinimalInvoice(doc, invoiceData);
              break;
          }
          
          doc.end();
        });

        return Buffer.concat(chunks);
      }
    );
  }

  /**
   * Generate financial report PDF
   */
  static async generateReportPDF(
    reportData: ReportData,
    includeCharts: boolean = true
  ): Promise<Buffer> {
    const cacheKey = CacheEngine.generateKey('report-pdf', reportData.title, reportData.period);
    
    return CacheEngine.getOrSet(
      cacheKey,
      'action-results',
      async () => {
        const doc = new PDFDocument({ margin: 50 });
        const chunks: Buffer[] = [];

        doc.on('data', chunk => chunks.push(chunk));
        
        await new Promise<void>((resolve) => {
          doc.on('end', resolve);
          
          // Header
          doc.fontSize(24).text(reportData.title, { align: 'center' });
          doc.fontSize(14).text(`Period: ${reportData.period}`, { align: 'center' });
          doc.moveDown(2);

          // Summary
          if (reportData.summary) {
            doc.fontSize(16).text('Executive Summary', { underline: true });
            doc.fontSize(12).text(reportData.summary);
            doc.moveDown();
          }

          // Data sections
          this.generateReportSections(doc, reportData.data);

          // Charts (if requested and available)
          if (includeCharts && reportData.charts) {
            doc.addPage();
            doc.fontSize(18).text('Charts & Visualizations', { underline: true });
            doc.moveDown();
            
            reportData.charts.forEach(chart => {
              this.generateChartPlaceholder(doc, chart);
            });
          }
          
          doc.end();
        });

        return Buffer.concat(chunks);
      }
    );
  }

  /**
   * Generate custom document PDF
   */
  static async generateCustomPDF(
    content: {
      title: string;
      sections: Array<{
        title: string;
        content: string;
        type: 'text' | 'table' | 'list';
      }>;
    },
    styling: {
      headerColor?: string;
      fontSize?: number;
      fontFamily?: string;
    } = {}
  ): Promise<Buffer> {
    const doc = new PDFDocument({ margin: 50 });
    const chunks: Buffer[] = [];

    doc.on('data', chunk => chunks.push(chunk));
    
    await new Promise<void>((resolve) => {
      doc.on('end', resolve);
      
      // Title
      doc.fontSize(styling.fontSize || 24)
         .fillColor(styling.headerColor || '#000000')
         .text(content.title, { align: 'center' });
      doc.moveDown(2);

      // Sections
      content.sections.forEach(section => {
        doc.fontSize(16).fillColor('#000000').text(section.title, { underline: true });
        doc.moveDown(0.5);
        
        switch (section.type) {
          case 'text':
            doc.fontSize(12).text(section.content);
            break;
          case 'list':
            const items = section.content.split('\n');
            items.forEach(item => {
              if (item.trim()) {
                doc.fontSize(12).text(`• ${item.trim()}`);
              }
            });
            break;
          case 'table':
            // Simple table implementation
            doc.fontSize(12).text(section.content);
            break;
        }
        
        doc.moveDown();
      });
      
      doc.end();
    });

    return Buffer.concat(chunks);
  }

  // Template implementations
  private static generateModernInvoice(doc: PDFKit.PDFDocument, data: InvoiceData): void {
    // Header with modern styling
    doc.fillColor('#2563eb')
       .fontSize(28)
       .text('INVOICE', 50, 50);
    
    doc.fillColor('#000000')
       .fontSize(12)
       .text(`Invoice #: ${data.invoiceNumber}`, 400, 60)
       .text(`Date: ${data.issueDate.toLocaleDateString()}`, 400, 80);
    
    if (data.dueDate) {
      doc.text(`Due: ${data.dueDate.toLocaleDateString()}`, 400, 100);
    }

    // Business info
    if (data.businessInfo) {
      doc.fontSize(14).text(data.businessInfo.name, 50, 120);
      doc.fontSize(10).text(data.businessInfo.address, 50, 140);
      if (data.businessInfo.email) {
        doc.text(data.businessInfo.email, 50, 160);
      }
    }

    // Bill to
    doc.fontSize(14).text('Bill To:', 50, 200);
    doc.fontSize(12).text(data.vendorName, 50, 220);

    // Items table
    let yPosition = 280;
    
    // Table header
    doc.fillColor('#f3f4f6')
       .rect(50, yPosition, 500, 25)
       .fill();
    
    doc.fillColor('#000000')
       .fontSize(12)
       .text('Description', 60, yPosition + 8)
       .text('Qty', 300, yPosition + 8)
       .text('Price', 350, yPosition + 8)
       .text('Total', 450, yPosition + 8);

    yPosition += 25;

    // Items
    if (data.items && data.items.length > 0) {
      data.items.forEach(item => {
        doc.text(item.description, 60, yPosition + 8)
           .text(item.quantity.toString(), 300, yPosition + 8)
           .text(`$${item.unitPrice.toFixed(2)}`, 350, yPosition + 8)
           .text(`$${item.total.toFixed(2)}`, 450, yPosition + 8);
        yPosition += 25;
      });
    } else {
      // Single item
      doc.text(data.description || 'Service', 60, yPosition + 8)
         .text('1', 300, yPosition + 8)
         .text(`$${data.amount.toFixed(2)}`, 350, yPosition + 8)
         .text(`$${data.amount.toFixed(2)}`, 450, yPosition + 8);
      yPosition += 25;
    }

    // Total
    yPosition += 20;
    doc.fontSize(16)
       .text('Total:', 400, yPosition)
       .text(`$${data.amount.toFixed(2)}`, 450, yPosition);

    // Footer
    doc.fontSize(10)
       .fillColor('#6b7280')
       .text('Thank you for your business!', 50, yPosition + 60);
  }

  private static generateClassicInvoice(doc: PDFKit.PDFDocument, data: InvoiceData): void {
    // Classic black and white design
    doc.fontSize(24).text('INVOICE', 50, 50);
    doc.fontSize(12)
       .text(`Invoice Number: ${data.invoiceNumber}`, 50, 100)
       .text(`Date: ${data.issueDate.toLocaleDateString()}`, 50, 120);
    
    if (data.dueDate) {
      doc.text(`Due Date: ${data.dueDate.toLocaleDateString()}`, 50, 140);
    }

    doc.text(`Bill To: ${data.vendorName}`, 50, 180);
    doc.text(`Amount: $${data.amount.toFixed(2)}`, 50, 220);
    
    if (data.description) {
      doc.text(`Description: ${data.description}`, 50, 260);
    }
  }

  private static generateMinimalInvoice(doc: PDFKit.PDFDocument, data: InvoiceData): void {
    // Ultra-minimal design
    doc.fontSize(20).text(`Invoice ${data.invoiceNumber}`, 50, 50);
    doc.fontSize(12)
       .text(data.vendorName, 50, 100)
       .text(`$${data.amount.toFixed(2)}`, 50, 120)
       .text(data.issueDate.toLocaleDateString(), 50, 140);
  }

  private static generateReportSections(doc: PDFKit.PDFDocument, data: any): void {
    // Generate report sections based on data structure
    if (typeof data === 'object') {
      Object.entries(data).forEach(([key, value]) => {
        doc.fontSize(14).text(this.formatKey(key), { underline: true });
        doc.fontSize(12).text(this.formatValue(value));
        doc.moveDown();
      });
    } else {
      doc.fontSize(12).text(String(data));
    }
  }

  private static generateChartPlaceholder(doc: PDFKit.PDFDocument, chart: ChartData): void {
    // Placeholder for chart - in production, this would generate actual charts
    doc.fontSize(14).text(chart.title, { underline: true });
    doc.fontSize(12).text(`Chart Type: ${chart.type}`);
    doc.fontSize(10).text(`Data Points: ${chart.data.length}`);
    
    // Draw a simple placeholder rectangle
    doc.rect(50, doc.y + 10, 200, 100).stroke();
    doc.text('Chart Placeholder', 100, doc.y - 40);
    doc.moveDown(8);
  }

  private static formatKey(key: string): string {
    return key.replace(/([A-Z])/g, ' $1')
              .replace(/^./, str => str.toUpperCase())
              .trim();
  }

  private static formatValue(value: any): string {
    if (typeof value === 'number') {
      return value.toLocaleString();
    }
    if (typeof value === 'object') {
      return JSON.stringify(value, null, 2);
    }
    return String(value);
  }
}
