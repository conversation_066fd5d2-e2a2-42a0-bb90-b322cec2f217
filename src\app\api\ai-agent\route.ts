'use server';

import { NextRequest, NextResponse } from 'next/server';
import { BillixAIAgent } from '@/lib/ai/billix-ai-agent';
import { DocumentGenerationEngine } from '@/lib/ai/document-generation-engine';
import { getCurrentUserId } from '@/lib/clerk-helpers';
import { CacheEngine } from '@/lib/ai/cache-engine';
import { PerformanceMonitor } from '@/lib/ai/performance-monitor';
import { uploadToVercelBlob } from '@/lib/blob';
import db from '@/db/db';

interface ChatApiRequest {
  message: string;
  chatId: string;
  context?: {
    page?: string;
    userIntent?: string;
    previousMessages?: any[];
  };
  options?: {
    generateDocuments?: boolean;
    includeInsights?: boolean;
    format?: 'pdf' | 'excel' | 'image' | 'all';
  };
}

interface DocumentGenerationRequest {
  type: 'invoice' | 'report' | 'contract' | 'analysis' | 'dashboard';
  format: 'pdf' | 'excel' | 'image' | 'all';
  data?: any;
  template?: string;
  customizations?: {
    includeCharts?: boolean;
    includeBranding?: boolean;
    quality?: 'draft' | 'standard' | 'high';
    language?: string;
  };
}

/**
 * Enhanced Chat API with AI Agent v3.0
 * Handles intelligent conversations, document generation, and proactive insights
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  let success = false;
  
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Initialize AI Agent if not already done
    await BillixAIAgent.initialize();

    const body: ChatApiRequest = await request.json();
    const { message, chatId, context, options } = body;

    // Validate input
    if (!message || !chatId) {
      return NextResponse.json(
        { error: 'Message and chatId are required' },
        { status: 400 }
      );
    }

    // Process chat with AI Agent
    const response = await BillixAIAgent.processChat({
      userId,
      message,
      chatId,
      context
    });

    // Handle document generation if requested
    if (options?.generateDocuments && (!response.documents || response.documents.length === 0)) {
      const documentType = detectDocumentType(message);
      if (documentType) {
        const documents = await BillixAIAgent.generateDocument(userId, {
          type: documentType,
          format: options.format || 'pdf',
          customizations: options
        });
        
        // Upload documents to blob storage
        const documentsWithUrls = await Promise.all(
          documents.map(async (doc) => {
            const downloadUrl = await createDownloadUrl(doc.buffer, doc.metadata.title, doc.format);
            return {
              id: doc.id,
              type: doc.type,
              format: doc.format,
              downloadUrl,
              metadata: doc.metadata,
              buffer: doc.buffer // Keep buffer for compatibility
            };
          })
        );
        
        response.documents = documentsWithUrls;
      }
    }

    // Also check if the AI agent generated documents and upload them
    if (response.documents && response.documents.length > 0) {
      const documentsWithUrls = await Promise.all(
        response.documents.map(async (doc: any) => {
          if (doc.buffer && !doc.downloadUrl) {
            const downloadUrl = await createDownloadUrl(doc.buffer, doc.metadata.title, doc.format);
            return {
              ...doc,
              downloadUrl
            };
          }
          return doc;
        })
      );
      response.documents = documentsWithUrls;
    }

    // Add performance metrics
    const responseTime = Date.now() - startTime;
    success = true;

    // Record performance
    PerformanceMonitor.recordMetrics({
      responseTime,
      tokenUsage: response.metadata.tokensUsed,
      cacheHitRate: 0.85, // Would be calculated from actual cache hits
      modelUsed: response.metadata.model,
      success: true,
      userId,
      queryType: 'chat'
    });

    return NextResponse.json({
      message: response.message,
      actions: response.actions || [],
      documents: response.documents?.map(doc => ({
        id: doc.id,
        type: doc.type,
        format: doc.format,
        downloadUrl: doc.downloadUrl,
        metadata: doc.metadata
      })) || [],
      suggestions: response.suggestions || [],
      insights: response.insights || [],
      analytics: response.analytics,
      metadata: {
        ...response.metadata,
        responseTime
      }
    });

  } catch (error) {
    console.error('Chat API error:', error);
    
    const responseTime = Date.now() - startTime;
    
    // Record error metrics
    PerformanceMonitor.recordMetrics({
      responseTime,
      tokenUsage: 0,
      cacheHitRate: 0,
      modelUsed: 'error',
      success: false,
      userId: 'unknown',
      queryType: 'chat',
      errorType: error instanceof Error ? error.name : 'UnknownError'
    });

    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: "I apologize, but I encountered an issue. Please try again.",
        metadata: { responseTime, error: true }
      },
      { status: 500 }
    );
  }
}

/**
 * Document Generation Endpoint
 */
export async function PUT(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body: DocumentGenerationRequest = await request.json();
    const { type, format, data, customizations } = body;

    // Generate documents
    const documents = await BillixAIAgent.generateDocument(userId, {
      type,
      format,
      data,
      customizations
    });

    // Convert to downloadable format
    const documentsWithUrls = await Promise.all(
      documents.map(async (doc) => {
        // In production, upload to blob storage and return URL
        const downloadUrl = await createDownloadUrl(doc.buffer, doc.metadata.title, doc.format);
        
        return {
          id: doc.id,
          type: doc.type,
          format: doc.format,
          downloadUrl,
          metadata: {
            ...doc.metadata,
            size: doc.buffer.length
          }
        };
      })
    );

    const responseTime = Date.now() - startTime;

    return NextResponse.json({
      documents: documentsWithUrls,
      metadata: {
        responseTime,
        documentsGenerated: documents.length,
        totalSize: documents.reduce((sum, doc) => sum + doc.buffer.length, 0)
      }
    });

  } catch (error) {
    console.error('Document generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate documents' },
      { status: 500 }
    );
  }
}

/**
 * Analytics and Insights Endpoint
 */
export async function PATCH(request: NextRequest) {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'all';

    let response: any = {};

    if (type === 'insights' || type === 'all') {
      response.insights = await BillixAIAgent.getInsights(userId);
    }

    if (type === 'analytics' || type === 'all') {
      response.analytics = await BillixAIAgent.getAnalytics(userId);
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('Analytics API error:', error);
    return NextResponse.json(
      { error: 'Failed to get analytics' },
      { status: 500 }
    );
  }
}

/**
 * Performance and Health Check Endpoint
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'health';

    if (type === 'performance') {
      const stats = PerformanceMonitor.getPerformanceStats();
      const recommendations = PerformanceMonitor.getOptimizationRecommendations();
      
      return NextResponse.json({
        performance: stats,
        recommendations,
        timestamp: new Date().toISOString()
      });
    }

    if (type === 'health') {
      // System health check
      const health = {
        status: 'healthy',
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        timestamp: new Date().toISOString(),
        services: {
          database: await checkDatabaseHealth(),
          cache: await checkCacheHealth(),
          ai: await checkAIHealth()
        }
      };

      return NextResponse.json(health);
    }

    return NextResponse.json({ error: 'Invalid type parameter' }, { status: 400 });

  } catch (error) {
    console.error('Health check error:', error);
    return NextResponse.json(
      { status: 'unhealthy', error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// Helper functions
function detectDocumentType(message: string): 'invoice' | 'report' | 'contract' | 'analysis' | null {
  const lower = message.toLowerCase();
  
  // Invoice patterns
  if (lower.includes('invoice') || lower.includes('bill') || lower.includes('receipt')) {
    return 'invoice';
  }
  
  // Report patterns
  if (lower.includes('report') || lower.includes('summary') || lower.includes('financial')) {
    return 'report';
  }
  
  // Contract patterns
  if (lower.includes('contract') || lower.includes('agreement') || lower.includes('terms')) {
    return 'contract';
  }
  
  // Analysis patterns
  if (lower.includes('analysis') || lower.includes('analytics') || lower.includes('insights')) {
    return 'analysis';
  }
  
  // PDF/document generation without specific type
  if (lower.includes('pdf') || lower.includes('excel') || lower.includes('document')) {
    return 'invoice'; // Default to invoice for now
  }
  
  // Creation verbs with financial context
  if ((lower.includes('create') || lower.includes('generate') || lower.includes('make')) && 
      (lower.includes('financial') || lower.includes('business') || lower.includes('money'))) {
    return 'report';
  }
  
  return null;
}

async function createDownloadUrl(buffer: Buffer, filename: string, format: string): Promise<string> {
  try {
    // Determine content type and extension
    const contentTypes = {
      pdf: 'application/pdf',
      excel: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      image: 'image/png'
    };
    
    const extensions = {
      pdf: 'pdf',
      excel: 'xlsx', 
      image: 'png'
    };
    
    const contentType = contentTypes[format as keyof typeof contentTypes] || 'application/octet-stream';
    const extension = extensions[format as keyof typeof extensions] || format;
    
    // Create unique filename
    const timestamp = Date.now();
    const cleanFilename = filename.replace(/[^a-zA-Z0-9-_]/g, '-');
    const blobFilename = `documents/${timestamp}-${cleanFilename}.${extension}`;
    
    // Upload to Vercel Blob storage
    const url = await uploadToVercelBlob(blobFilename, buffer, contentType);
    
    if (!url) {
      throw new Error('Failed to upload to blob storage');
    }
    
    return url;
  } catch (error) {
    console.error('Error creating download URL:', error);
    // Fallback: create a data URL (not recommended for large files)
    const base64 = buffer.toString('base64');
    const contentType = format === 'pdf' ? 'application/pdf' : 
                       format === 'excel' ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' : 
                       'image/png';
    return `data:${contentType};base64,${base64}`;
  }
}

async function checkDatabaseHealth(): Promise<{ status: string; responseTime?: number }> {
  try {
    const start = Date.now();
    // Simple database ping
    await db.$queryRaw`SELECT 1`;
    const responseTime = Date.now() - start;
    
    return { status: 'healthy', responseTime };
  } catch (error) {
    return { status: 'unhealthy' };
  }
}

async function checkCacheHealth(): Promise<{ status: string }> {
  try {
    // Test cache operation
    await CacheEngine.set('health-check', 'ok', 'action-results');
    const result = await CacheEngine.get('health-check', 'action-results');
    
    return { status: result === 'ok' ? 'healthy' : 'degraded' };
  } catch (error) {
    return { status: 'unhealthy' };
  }
}

async function checkAIHealth(): Promise<{ status: string }> {
  try {
    // Test AI system initialization
    await BillixAIAgent.initialize();
    return { status: 'healthy' };
  } catch (error) {
    return { status: 'unhealthy' };
  }
}
