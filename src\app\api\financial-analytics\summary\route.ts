import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import db from "@/db/db";
import { z } from "zod";
import {
  getFinancialMetrics,
  getExpenseTrends,
  getVendorAnalysis,
  getCategoryBreakdown,
  getInvoiceStatusAnalysis,
  getAIInsights
} from "@/lib/actions/analytics";

// Define the expected request schema
const requestSchema = z.object({
  dateRange: z.object({
    from: z.string(),
    to: z.string()
  }),
  filters: z.object({
    vendor: z.string().optional(),
    category: z.string().optional(),
    status: z.string().optional(),
    search: z.string().optional(),
    amountRange: z.tuple([z.number(), z.number()]).optional(),
  }).optional(),
});

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the user from the database
    const user = await db.user.findUnique({
      where: { clerkId: userId },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Parse the request body
    const body = await req.json();
    const { dateRange, filters = {} } = requestSchema.parse(body);

    // Convert date strings to Date objects
    const startDate = new Date(dateRange.from);
    const endDate = new Date(dateRange.to);

    // Fetch all analytics data in parallel
    const [
      financialMetrics,
      expenseTrends,
      vendorAnalysis,
      categoryBreakdown,
      invoiceStatusAnalysis,
      aiInsights
    ] = await Promise.all([
      getFinancialMetrics(startDate, endDate, filters),
      getExpenseTrends(startDate, endDate, filters),
      getVendorAnalysis(startDate, endDate, filters),
      getCategoryBreakdown(startDate, endDate, filters),
      getInvoiceStatusAnalysis(startDate, endDate, filters),
      getAIInsights(startDate, endDate, filters)
    ]);

    // Return the combined analytics data
    return NextResponse.json({
      financialMetrics,
      expenseTrends,
      vendorAnalysis,
      categoryBreakdown,
      invoiceStatusAnalysis,
      aiInsights,
      dateRange: {
        from: startDate.toISOString(),
        to: endDate.toISOString()
      }
    });
  } catch (error) {
    console.error("Error fetching financial analytics:", error);
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 });
    }
    return NextResponse.json(
      { error: "Failed to fetch financial analytics" },
      { status: 500 }
    );
  }
}

// GET method for simple summary without filters
export async function GET() {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the user from the database
    const user = await db.user.findUnique({
      where: { clerkId: userId },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Default to last 30 days
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);

    // Use empty filters
    const filters = {};

    // Fetch basic analytics data
    const [
      financialMetrics,
      vendorAnalysis,
      categoryBreakdown,
      invoiceStatusAnalysis
    ] = await Promise.all([
      getFinancialMetrics(startDate, endDate, filters),
      getVendorAnalysis(startDate, endDate, filters),
      getCategoryBreakdown(startDate, endDate, filters),
      getInvoiceStatusAnalysis(startDate, endDate, filters)
    ]);

    // Return simplified analytics data
    return NextResponse.json({
      financialMetrics,
      vendorAnalysis: {
        vendorAnalysis: vendorAnalysis.vendorAnalysis, // Return all vendors
        paymentHistory: vendorAnalysis.paymentHistory // Return all payment history
      },
      categoryBreakdown: categoryBreakdown, // Return all categories
      invoiceStatusAnalysis: {
        statusAnalysis: invoiceStatusAnalysis.statusAnalysis,
        agingData: invoiceStatusAnalysis.agingData
      },
      dateRange: {
        from: startDate.toISOString(),
        to: endDate.toISOString()
      }
    });
  } catch (error) {
    console.error("Error fetching financial analytics summary:", error);
    return NextResponse.json(
      { error: "Failed to fetch financial analytics summary" },
      { status: 500 }
    );
  }
} 