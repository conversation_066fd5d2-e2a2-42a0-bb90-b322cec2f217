'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, <PERSON>Footer, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { toast } from 'sonner';
import { CalendarIcon, CheckIcon } from './icons';

interface ReportSchedulerProps {
  reportId: string;
  title: string;
  reportType: string;
  onSchedule: (scheduleData: ScheduleData) => void;
  onCancel: () => void;
}

export interface ScheduleData {
  reportId: string;
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  dayOfWeek?: number;
  dayOfMonth?: number;
  timeOfDay: string;
  emailAddresses: string[];
  format: 'PDF' | 'EXCEL';
}

export function ReportScheduler({
  reportId,
  title,
  reportType,
  onSchedule,
  onCancel,
}: ReportSchedulerProps) {
  const [frequency, setFrequency] = useState<'daily' | 'weekly' | 'monthly' | 'quarterly'>('weekly');
  const [dayOfWeek, setDayOfWeek] = useState<number>(1); // Monday
  const [dayOfMonth, setDayOfMonth] = useState<number>(1);
  const [timeOfDay, setTimeOfDay] = useState<string>('09:00');
  const [emailInput, setEmailInput] = useState<string>('');
  const [emailAddresses, setEmailAddresses] = useState<string[]>([]);
  const [format, setFormat] = useState<'PDF' | 'EXCEL'>('PDF');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleAddEmail = () => {
    if (!emailInput) return;
    
    // Simple email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(emailInput)) {
      toast.error('Please enter a valid email address');
      return;
    }
    
    if (!emailAddresses.includes(emailInput)) {
      setEmailAddresses([...emailAddresses, emailInput]);
      setEmailInput('');
    } else {
      toast.error('Email already added');
    }
  };

  const handleRemoveEmail = (email: string) => {
    setEmailAddresses(emailAddresses.filter(e => e !== email));
  };

  const handleSubmit = () => {
    if (emailAddresses.length === 0) {
      toast.error('Please add at least one email address');
      return;
    }

    setIsSubmitting(true);

    const scheduleData: ScheduleData = {
      reportId,
      frequency,
      timeOfDay,
      emailAddresses,
      format,
    };

    if (frequency === 'weekly') {
      scheduleData.dayOfWeek = dayOfWeek;
    } else if (frequency === 'monthly' || frequency === 'quarterly') {
      scheduleData.dayOfMonth = dayOfMonth;
    }

    // Simulate API call
    setTimeout(() => {
      onSchedule(scheduleData);
      setIsSubmitting(false);
      toast.success('Report scheduled successfully');
    }, 1000);
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CalendarIcon className="h-5 w-5" />
          Schedule Report Delivery
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="report-title">Report</Label>
          <div id="report-title" className="text-sm font-medium mt-1">
            {title} ({reportType})
          </div>
        </div>

        <div className="space-y-2">
          <Label>Frequency</Label>
          <RadioGroup
            value={frequency}
            onValueChange={(value) => setFrequency(value as 'daily' | 'weekly' | 'monthly' | 'quarterly')}
            className="grid grid-cols-2 gap-2"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="daily" id="daily" />
              <Label htmlFor="daily">Daily</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="weekly" id="weekly" />
              <Label htmlFor="weekly">Weekly</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="monthly" id="monthly" />
              <Label htmlFor="monthly">Monthly</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="quarterly" id="quarterly" />
              <Label htmlFor="quarterly">Quarterly</Label>
            </div>
          </RadioGroup>
        </div>

        {frequency === 'weekly' && (
          <div>
            <Label htmlFor="day-of-week">Day of Week</Label>
            <Select
              value={dayOfWeek.toString()}
              onValueChange={(value) => setDayOfWeek(parseInt(value))}
            >
              <SelectTrigger id="day-of-week">
                <SelectValue placeholder="Select day" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0">Sunday</SelectItem>
                <SelectItem value="1">Monday</SelectItem>
                <SelectItem value="2">Tuesday</SelectItem>
                <SelectItem value="3">Wednesday</SelectItem>
                <SelectItem value="4">Thursday</SelectItem>
                <SelectItem value="5">Friday</SelectItem>
                <SelectItem value="6">Saturday</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        {(frequency === 'monthly' || frequency === 'quarterly') && (
          <div>
            <Label htmlFor="day-of-month">Day of Month</Label>
            <Select
              value={dayOfMonth.toString()}
              onValueChange={(value) => setDayOfMonth(parseInt(value))}
            >
              <SelectTrigger id="day-of-month">
                <SelectValue placeholder="Select day" />
              </SelectTrigger>
              <SelectContent>
                {Array.from({ length: 31 }, (_, i) => (
                  <SelectItem key={i + 1} value={(i + 1).toString()}>
                    {i + 1}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        <div>
          <Label htmlFor="time-of-day">Time of Day</Label>
          <Input
            id="time-of-day"
            type="time"
            value={timeOfDay}
            onChange={(e) => setTimeOfDay(e.target.value)}
          />
        </div>

        <div>
          <Label htmlFor="format">Format</Label>
          <Select
            value={format}
            onValueChange={(value) => setFormat(value as 'PDF' | 'EXCEL')}
          >
            <SelectTrigger id="format">
              <SelectValue placeholder="Select format" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="PDF">PDF</SelectItem>
              <SelectItem value="EXCEL">Excel</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Email Recipients</Label>
          <div className="flex gap-2">
            <Input
              id="email"
              type="email"
              placeholder="Enter email address"
              value={emailInput}
              onChange={(e) => setEmailInput(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  handleAddEmail();
                }
              }}
            />
            <Button type="button" onClick={handleAddEmail} variant="secondary">
              Add
            </Button>
          </div>
          
          {emailAddresses.length > 0 && (
            <div className="mt-2 space-y-2">
              {emailAddresses.map((email) => (
                <div
                  key={email}
                  className="flex items-center justify-between bg-secondary/50 p-2 rounded-md"
                >
                  <span className="text-sm truncate">{email}</span>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveEmail(email)}
                  >
                    &times;
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="ghost" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={handleSubmit} disabled={isSubmitting || emailAddresses.length === 0}>
          {isSubmitting ? (
            <>
              <span className="animate-spin mr-2">⟳</span>
              Scheduling...
            </>
          ) : (
            <>
              <CheckIcon className="mr-2 h-4 w-4" />
              Schedule Report
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
