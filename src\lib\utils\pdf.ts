// PDF-specific utility functions

export function formatNotes(notes: string): string {
  if (!notes) return ""

  if (typeof notes === "string") {
    try {
      const parsed = JSON.parse(notes)
      return typeof parsed === "object" ? JSON.stringify(parsed, null, 2) : notes
    } catch {
      return notes
    }
  } else if (typeof notes === "object") {
    return JSON.stringify(notes, null, 2)
  }

  return String(notes)
}

export function containsArabic(text: string): boolean {
  if (!text) return false
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/
  return arabicRegex.test(text)
}

export function arrayBufferToBase64(buffer: ArrayBuffer): string {
  if (typeof window === "undefined") {
    return ""
  }
  const binary = []
  const bytes = new Uint8Array(buffer)
  for (let i = 0; i < bytes.byteLength; i++) {
    binary.push(String.fromCharCode(bytes[i]))
  }
  return window.btoa(binary.join(""))
}

export function formatDate(date: Date | string): string {
  if (!date) return "N/A"
  try {
    const dateObj = date instanceof Date ? date : new Date(date)
    if (isNaN(dateObj.getTime())) return "N/A"
    return dateObj.toISOString().split("T")[0]
  } catch {
    return String(date)
  }
} 