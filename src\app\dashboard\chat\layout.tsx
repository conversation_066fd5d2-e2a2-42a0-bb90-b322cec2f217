
import Script from 'next/script';

import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs';
import { AppSidebar } from '@/components/ai-agent/app-sidebar';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';

export const experimental_ppr = true;

export default async function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkProvider>
      <Script
        src="https://cdn.jsdelivr.net/pyodide/v0.23.4/full/pyodide.js"
        strategy="beforeInteractive"
      />
      <div className="flex h-full w-full">
        <SidebarProvider defaultOpen={false}>
          <SidebarInset className="flex-1">{children}</SidebarInset>
          <AppSidebar />
        </SidebarProvider>
      </div>
    </ClerkProvider>
  );
}
