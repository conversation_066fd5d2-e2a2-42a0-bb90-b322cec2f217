"use server";

import { currentUser, auth } from "@clerk/nextjs/server";
import db from "@/db/db";

// Define types for Clerk organization memberships
interface OrganizationMembership {
  organization: {
    id: string;
    name: string;
  }
}

// Extend the User type from Clerk
type ClerkUserWithOrg = {
  id: string;
  firstName: string | null;
  lastName: string | null;
  imageUrl: string | null;
  emailAddresses: { id: string; emailAddress: string }[];
  primaryEmailAddressId: string | null;
  organizationMemberships?: OrganizationMembership[];
};

/**
 * Function to create or update a user in the database
 * This will be called when a user signs in or updates their profile
 */
export async function syncUserWithDatabase() {
  try {
    // Get user from Clerk
    const { userId } = await auth();
    const clerkUser = await currentUser() as ClerkUserWithOrg;


    if (!userId || !clerkUser) {
      throw new Error("User not authenticated");
    }

    // Get primary email
    const primaryEmailAddress = clerkUser.emailAddresses.find(
      (email) => email.id === clerkUser.primaryEmailAddressId
    );

    const email =
      primaryEmailAddress?.emailAddress ||
      clerkUser.emailAddresses[0]?.emailAddress ||
      "";

    if (!email) {
      throw new Error("User has no email address");
    }

    // Check if user exists in database
    const existingUser = await db.user.findUnique({
      where: {
        clerkId: userId,
      },
      include: {
        organizations: true,
      },
    });

    if (existingUser) {
      // Update user data if needed
      const updatedUser = await db.user.update({
        where: {
          id: existingUser.id,
        },
        data: {
          firstName: clerkUser.firstName || existingUser.firstName,
          lastName: clerkUser.lastName || existingUser.lastName,
          email: email || existingUser.email,
          profileImageUrl: clerkUser.imageUrl || existingUser.profileImageUrl,
          lastActive: new Date(),
          status: "ACTIVE",
        },
        include: {
          organizations: true,
        },
      });

      // Handle organization membership if available
      if (
        clerkUser.organizationMemberships &&
        clerkUser.organizationMemberships.length > 0
      ) {
        await syncOrganizationMembership(updatedUser.id, clerkUser);
      }

      return updatedUser;
    }

    // Create user if not exists
    const newUser = await db.user.create({
      data: {
        clerkId: userId,
        email: email,
        firstName: clerkUser.firstName || "",
        lastName: clerkUser.lastName || "",
        profileImageUrl: clerkUser.imageUrl || "",
        role: "USER",
        lastActive: new Date(),
      },
      include: {
        organizations: true,
      },
    });

    // Handle organization membership if available
    if (
      clerkUser.organizationMemberships &&
      clerkUser.organizationMemberships.length > 0
    ) {
      await syncOrganizationMembership(newUser.id, clerkUser);
    }

    return newUser;
  } catch (error) {
    throw error;
  }
}

/**
 * Sync organization membership from Clerk to database
 */
async function syncOrganizationMembership(userId: string, clerkUser: ClerkUserWithOrg) {
  try {
    // Process each organization membership
    for (const membership of clerkUser.organizationMemberships || []) {
      const orgName = membership.organization.name;

      // Check if organization exists in database
      let dbOrg = await db.organization.findFirst({
        where: {
          members: {
            some: {
              clerkId: clerkUser.id,
            },
          },
        },
      });

      if (!dbOrg) {
        // Create new organization
        dbOrg = await db.organization.create({
          data: {
            name: orgName,
            members: {
              connect: {
                id: userId,
              },
            },
          },
        });
      } else {
        // Update organization name if changed
        if (dbOrg.name !== orgName) {
          await db.organization.update({
            where: { id: dbOrg.id },
            data: { name: orgName },
          });
        }
      }
    }
  } catch (error) {
    throw error;
  }
}

/**
 * Function to get the current user from the database
 */
export async function getCurrentDbUser() {
  try {
    const clerkUser = await currentUser() as ClerkUserWithOrg | null;

    if (!clerkUser) {
      return null;
    }

    // First try to find by clerkId
    let user = await db.user.findUnique({
      where: { clerkId: clerkUser.id },
      include: {
        organizations: true,
      },
    });

    // If not found, try to find by email
    if (!user) {
      const primaryEmail =
        clerkUser.emailAddresses.find(
          (email) => email.id === clerkUser.primaryEmailAddressId
        )?.emailAddress || clerkUser.emailAddresses[0]?.emailAddress;

      if (primaryEmail) {
        user = await db.user.findUnique({
          where: { email: primaryEmail },
          include: {
            organizations: true,
          },
        });

        // If found by email but clerkId doesn't match, update it
        if (user && user.clerkId !== clerkUser.id) {
          user = await db.user.update({
            where: { id: user.id },
            data: { clerkId: clerkUser.id },
            include: {
              organizations: true,
            },
          });
        }
      }
    }

    // If still not found, create the user
    if (!user) {
      // Call syncUserWithDatabase to create the user
      const newUser = await syncUserWithDatabase();

      // Fetch again with organizations included
      if (newUser) {
        user = await db.user.findUnique({
          where: { id: newUser.id },
          include: {
            organizations: true,
          },
        });
      }
    }

    return user;
  } catch {
    return null;
  }
}

/**
 * Create a new organization for the current user
 */
export async function createOrganization(name: string) {
  try {
    const clerkUser = await currentUser() as ClerkUserWithOrg | null;

    if (!clerkUser) {
      throw new Error("User not authenticated");
    }

    const user = await db.user.findUnique({
      where: { clerkId: clerkUser.id },
    });

    if (!user) {
      throw new Error("User not found in database");
    }

    const organization = await db.organization.create({
      data: {
        name,
        members: {
          connect: {
            id: user.id,
          },
        },
      },
    });

    return organization;
  } catch (error) {
    throw error;
  }
}

/**
 * Fetch the database user ID for a given Clerk ID
 */
export async function getUserIdFromClerkId(
  clerkId: string
): Promise<string | null> {
  try {
    if (!clerkId) return null;

    const user = await db.user.findFirst({
      where: { clerkId },
      select: { id: true },
    });

    return user?.id || null;
  } catch {
    return null;
  }
}

/**
 * Ensure user exists in database and is up to date
 * This is a lightweight version of syncUserWithDatabase that can be called
 * from middleware or other performance-sensitive contexts
 */
export async function ensureUserInDatabase(): Promise<string | null> {
  try {
    const { userId } = await auth();

    if (!userId) {
      return null;
    }

    // Check if user exists in database
    const existingUser = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true, lastActive: true },
    });

    if (existingUser) {
      // Update last active timestamp if it's been more than 15 minutes
      const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);

      if (
        !existingUser.lastActive ||
        existingUser.lastActive < fifteenMinutesAgo
      ) {
        await db.user.update({
          where: { id: existingUser.id },
          data: { lastActive: new Date() },
        });
      }

      return existingUser.id;
    }

    // If user doesn't exist, sync from Clerk
    const newUser = await syncUserWithDatabase();
    return newUser.id;
  } catch {
    return null;
  }
}
