import { auth } from '@clerk/nextjs/server';
import { notFound } from 'next/navigation';
import db from '@/db/db';

/**
 * Get user subscriptions (Paddle-only)
 */
export async function getUserSubscriptions() {
  const { userId } = await auth();

  if (!userId) {
    notFound();
  }

  const user = await db.user.findUnique({
    where: { clerkId: userId },
  });

  if (!user) {
    console.error(
      `User with clerkId ${userId} not found in database`
    );
    return [];
  }

  const userSubscriptions = await db.subscription.findMany({
    where: {
      userId: user.id,
    },
    orderBy: {
      id: 'desc',
    },
  });

  return userSubscriptions;
}

/**
 * Get available plans (from database, no external API needed)
 */
export async function getAvailablePlans() {
  try {
    const plans = await db.plan.findMany({
      orderBy: {
        sort: 'asc',
      },
    });
    return plans;
  } catch (error) {
    console.error('Error fetching plans:', error);
    return [];
  }
}

/**
 * Get user's active subscription status
 */
export async function getUserActiveSubscription() {
  const { userId } = await auth();

  if (!userId) {
    return null;
  }

  const user = await db.user.findUnique({
    where: { clerkId: userId },
  });

  if (!user) {
    return null;
  }

  const activeSubscription = await db.subscription.findFirst({
    where: {
      userId: user.id,
      status: {
        in: ['active', 'trialing', 'past_due'],
      },
    },
    include: {
      plan: true,
    },
    orderBy: {
      id: 'desc',
    },
  });

  return activeSubscription;
}
