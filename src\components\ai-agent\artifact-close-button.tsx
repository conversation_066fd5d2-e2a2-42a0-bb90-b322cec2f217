import { memo } from 'react';
import { CrossIcon } from './icons';
import { Button } from '@/components/ui/button';
import { initialArtifactData, useArtifact } from '@/hooks/use-artifact';
import { UIArtifact } from './artifact';

function PureArtifactCloseButton() {
  const { setArtifact } = useArtifact();

  return (
    <Button
      variant="outline"
      className="h-fit p-2 dark:hover:bg-zinc-700"
      onClick={() => {
        setArtifact((currentArtifact: UIArtifact) => {
          // For reports, minimize but also mark as manually closed
          if (currentArtifact.reportId) {
            return {
              ...currentArtifact,
              isVisible: false,
              isMinimized: true,
              manuallyClosed: true, // Mark that user manually closed it
            };
          }

          // For other artifacts or streaming content
          return currentArtifact.status === 'streaming'
            ? {
                ...currentArtifact,
                isVisible: false,
              }
            : { ...initialArtifactData, status: 'idle' };
        });
      }}
    >
      <CrossIcon size={18} />
    </Button>
  );
}

export const ArtifactCloseButton = memo(PureArtifactCloseButton, () => true);
