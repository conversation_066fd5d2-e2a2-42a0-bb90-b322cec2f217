'use client';

import React, { createContext, useContext, useEffect, useState, useCallback, useMemo } from 'react';
import { useDateFilter } from './date-filter-provider';

interface DashboardData {
  stats: {
    totalCount: number;
    totalAmount: number;
    paidCount: number;
    paidAmount: number;
    pendingCount: number;
    pendingAmount: number;
    overdueCount: number;
    overdueAmount: number;
    processingEfficiency: number;
    efficiencyChange: number;
    pendingInvoices: number;
  } | null;
  chartData: Array<{
    name: string;
    income: number;
    expenses: number;
  }>;
  recentInvoices: Array<{
    id: number;
    amount: number;
    status: string;
    createdAt: string;
  }>;
  topVendors: Array<{
    name: string;
    value: number;
  }>;
  subscription: {
    plan?: { name: string; };
  } | null;
  usage: {
    invoicesUsed: number;
    invoiceLimit: number;
    documentsUsed: number;
    documentLimit: number;
    daysUntilReset: number;
    resetDate: Date;
  } | null;
}

interface DashboardContextType {
  data: DashboardData;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

const DashboardContext = createContext<DashboardContextType | null>(null);

export function DashboardDataProvider({ children }: { children: React.ReactNode }) {
  const [data, setData] = useState<DashboardData>({
    stats: null,
    chartData: [],
    recentInvoices: [],
    topVendors: [],
    subscription: null,
    usage: null,
  });
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { getDateRangeForAPI } = useDateFilter();

  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Build API URL with date range parameters
      let apiUrl = '/api/invoices/stats';
      const dateRange = getDateRangeForAPI();
      if (dateRange) {
        const params = new URLSearchParams({
          from: dateRange.from,
          to: dateRange.to,
        });
        apiUrl += `?${params.toString()}`;
      }

      // Fetch all data in parallel
      const [invoiceResponse, subscriptionResponse, usageResponse] = await Promise.all([
        fetch(apiUrl),
        fetch('/api/subscriptions/active'),
        fetch('/api/usage/stats'),
      ]);

      const results = await Promise.allSettled([
        invoiceResponse.ok ? invoiceResponse.json() : Promise.reject('Failed to fetch invoice data'),
        subscriptionResponse.ok ? subscriptionResponse.json() : Promise.resolve(null),
        usageResponse.ok ? usageResponse.json() : Promise.resolve(null),
      ]);

      const [invoiceResult, subscriptionResult, usageResult] = results;

      // Process invoice data
      let invoiceData = null;
      if (invoiceResult.status === 'fulfilled') {
        invoiceData = invoiceResult.value;
      }

      // Process subscription data
      let subscriptionData = null;
      if (subscriptionResult.status === 'fulfilled') {
        subscriptionData = subscriptionResult.value;
      }

      // Process usage data
      let usageData = null;
      if (usageResult.status === 'fulfilled' && usageResult.value?.success) {
        usageData = usageResult.value.stats;
      }

      // Transform and set data
      setData({
        stats: invoiceData ? {
          totalCount: invoiceData.summary?.total?.count ?? 0,
          totalAmount: invoiceData.summary?.total?.amount ?? 0,
          paidCount: invoiceData.summary?.paid?.count ?? 0,
          paidAmount: invoiceData.summary?.paid?.amount ?? 0,
          pendingCount: invoiceData.summary?.pending?.count ?? 0,
          pendingAmount: invoiceData.summary?.pending?.amount ?? 0,
          overdueCount: invoiceData.summary?.overdue?.count ?? 0,
          overdueAmount: invoiceData.summary?.overdue?.amount ?? 0,
          processingEfficiency: invoiceData.summary?.paidPercentage ?? 0,
          efficiencyChange: 0,
          pendingInvoices: invoiceData.summary?.pending?.count ?? 0,
        } : null,
        
        chartData: invoiceData?.monthly ? invoiceData.monthly.map((m: any) => ({
          name: m.month.slice(0, 3),
          income: m.paid ?? 0,
          expenses: (m.pending ?? 0) + (m.overdue ?? 0),
        })) : [],
        
        recentInvoices: invoiceData?.recentInvoices ? invoiceData.recentInvoices.slice(0, 7) : [],
        
        topVendors: invoiceData?.topVendors ? invoiceData.topVendors.map((v: any) => ({
          name: v.name,
          value: v.count,
        })) : [],
        
        subscription: subscriptionData,
        
        usage: usageData ? {
          ...usageData,
          resetDate: new Date(usageData.resetDate),
        } : null,
      });

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while fetching dashboard data');
    } finally {
      setLoading(false);
    }
  }, [getDateRangeForAPI]);

  // Debounce data fetching to prevent excessive API calls
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchDashboardData();
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [fetchDashboardData]);

  const contextValue = useMemo(() => ({
    data,
    loading,
    error,
    refetch: fetchDashboardData,
  }), [data, loading, error, fetchDashboardData]);

  return (
    <DashboardContext.Provider value={contextValue}>
      {children}
    </DashboardContext.Provider>
  );
}

export function useDashboardData() {
  const context = useContext(DashboardContext);
  if (!context) {
    throw new Error('useDashboardData must be used within a DashboardDataProvider');
  }
  return context;
}

// Individual hooks for specific data pieces
export function useInvoiceStats() {
  const { data, loading, error } = useDashboardData();
  return { stats: data.stats, loading, error };
}

export function useChartData() {
  const { data, loading, error } = useDashboardData();
  return { chartData: data.chartData, loading, error };
}

export function useRecentInvoices() {
  const { data, loading, error } = useDashboardData();
  return { invoices: data.recentInvoices, loading, error };
}

export function useVendorData() {
  const { data, loading, error } = useDashboardData();
  return { vendors: data.topVendors, loading, error };
}

export function useSubscriptionData() {
  const { data, loading, error } = useDashboardData();
  return { subscription: data.subscription, loading, error };
}

export function useUsageData() {
  const { data, loading, error } = useDashboardData();
  return { usage: data.usage, loading, error };
}