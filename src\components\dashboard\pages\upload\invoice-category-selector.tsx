"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { SelectionDropdown } from "./selection-dropdown";
import { toast } from "sonner";
import {
  Briefcase,
  ShoppingCart,
  Laptop,
  Truck,
  CheckCircle
} from "lucide-react";
import type { InvoiceData } from "@/types/invoice";
import { safeToString } from "@/utils/object-to-string";

interface InvoiceCategorySelectorProps {
  data: InvoiceData & { id?: string };
  editedData: InvoiceData & { id?: string };
  onSelect: (type: 'category', value: string) => void;
  onUpdate: (data: InvoiceData & { id?: string }) => void;
}

export function InvoiceCategorySelector({
  data,
  editedData,
  onSelect,
  onUpdate
}: InvoiceCategorySelectorProps) {
  const [isSavingCategory, setIsSavingCategory] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState<{ category: boolean; vendor: boolean }>({
    category: false,
    vendor: false
  });

  // Get category icon based on category name
  const getCategoryIcon = (category: string) => {
    switch (category?.toLowerCase()) {
      case 'retail':
        return <ShoppingCart className="h-4 w-4 text-purple-500" />;
      case 'technology':
        return <Laptop className="h-4 w-4 text-blue-500" />;
      case 'logistics':
        return <Truck className="h-4 w-4 text-orange-500" />;
      default:
        return <Briefcase className="h-4 w-4 text-indigo-500" />;
    }
  };

  // Save category function
  const handleSaveCategory = async (selectedCategory?: string) => {
    try {
      let categoryElement: Element | null = null;

      // If no category is provided, try to get it from the DOM
      if (!selectedCategory) {
        categoryElement = document.querySelector(`[data-selected-category][data-invoice-id="${data.id}"]`) ||
          document.querySelector('[data-selected-category]');
        selectedCategory = categoryElement?.getAttribute('data-value') || undefined;
      }

      if (!selectedCategory) {
        toast.error("Please select a category first.");
        return;
      }

      // Make a deep copy to ensure the update is recognized
      const updatedData = JSON.parse(JSON.stringify(editedData)) as InvoiceData & { id?: string };

      // Ensure meta exists
      if (!updatedData.meta) {
        updatedData.meta = {};
      }

      // Ensure meta and suggestions are properly parsed objects
      if (typeof updatedData.meta === 'string') {
        try {
          updatedData.meta = JSON.parse(updatedData.meta);
        } catch {
          updatedData.meta = {};
        }
      }

      // Ensure suggestions exists
      if (!updatedData.meta!.suggestions) {
        updatedData.meta!.suggestions = {};
      }

      if (typeof updatedData.meta?.suggestions === 'string') {
        try {
          updatedData.meta.suggestions = JSON.parse(updatedData.meta.suggestions);
        } catch {
          updatedData.meta.suggestions = {};
        }
      }

      // Set the selected category directly
      updatedData.meta!.suggestions!.selectedCategory = selectedCategory;

      // Add saving indicator
      setIsSavingCategory(true);

      // Check if this invoice has an ID (means it's already in the database)
      if (updatedData.id) {
        // Call the API to save the metadata
        const response = await fetch(`/api/invoices/${updatedData.id}/metadata`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            selectedCategory: selectedCategory
          })
        });

        if (!response.ok) {
          throw new Error('Failed to save category');
        }

        const result = await response.json();

        // Show success message briefly
        setSaveSuccess(prev => ({ ...prev, category: true }));
        toast.success("Category saved successfully!");

        // Update the parent component with the updated data
        const finalUpdatedData = {
          ...updatedData,
          categoryId: result.data?.categoryId // Include the categoryId from the response
        };
        onUpdate(finalUpdatedData);

        // Force a complete re-render by updating the parent's data
        // This ensures the invoice is properly updated in the processing file card
        if (typeof window !== 'undefined') {
          // Dispatch a custom event to notify other components of the update
          window.dispatchEvent(new CustomEvent('invoiceUpdated', {
            detail: { invoiceId: updatedData.id, data: finalUpdatedData }
          }));
        }
      } else {
        // If no ID yet, we'll update the local state and let the parent component
        // handle saving when the invoice is created
        onUpdate(updatedData);
        setSaveSuccess(prev => ({ ...prev, category: true }));
        toast.success("Category selected. It will be saved when the invoice is created.");

        // Force a complete re-render by updating the parent's data
        if (typeof window !== 'undefined') {
          // Dispatch a custom event to notify other components of the update
          window.dispatchEvent(new CustomEvent('invoiceUpdated', {
            detail: { invoiceId: 'pending', data: updatedData }
          }));
        }
      }

      // Force update the DOM element to ensure it reflects the current selection
      // Get the element again if we didn't get it earlier
      if (!categoryElement) {
        categoryElement = document.querySelector(`[data-selected-category][data-invoice-id="${data.id}"]`) ||
          document.querySelector('[data-selected-category]');
      }

      if (categoryElement) {
        categoryElement.setAttribute('data-value', selectedCategory);
        categoryElement.setAttribute('data-selected-category', selectedCategory);
        categoryElement.setAttribute('data-invoice-id', data.id || '');
      }
    } catch (error) {
      // Only show toast for duplicate or other errors, don't throw or log
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error("Failed to save category");
      }
    } finally {
      setTimeout(() => {
        setIsSavingCategory(false);
        setTimeout(() => setSaveSuccess(prev => ({ ...prev, category: false })), 2000);
      }, 500);
    }
  };



  // Handle case where meta or suggestions might be a string
  if (!data.meta) {
    return null;
  }

  // Parse meta if it's a string
  const metaObj = typeof data.meta === 'string' ? JSON.parse(data.meta) : data.meta;

  // Check if suggestions exists
  if (!metaObj.suggestions) {
    return null;
  }

  // Parse suggestions if it's a string
  const suggestionsObj = typeof metaObj.suggestions === 'string'
    ? JSON.parse(metaObj.suggestions)
    : metaObj.suggestions;

  return (
    <div className="mt-6 space-y-6">
      <h3 className="text-lg font-semibold mb-3">Classification</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Categories Section - Enhanced UI */}
        <div className="flex flex-col p-5 bg-gradient-to-br from-indigo-50/80 to-blue-50/80 dark:from-indigo-950/20 dark:to-blue-950/30 rounded-lg border border-indigo-100 dark:border-indigo-900/50 shadow-sm">
          <div className="flex items-center mb-3">
            <div className="h-8 w-8 rounded-full bg-indigo-100 dark:bg-indigo-900/40 flex items-center justify-center mr-3">
              <Briefcase className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
            </div>
            <span className="font-medium text-lg">Business Category</span>
          </div>

          {suggestionsObj.categories && suggestionsObj.categories.length > 0 ? (
            <div className="space-y-3">
              <div className="flex flex-col space-y-3">
                <div className="flex-1 relative">
                  <SelectionDropdown
                    label="Select Category"
                    options={suggestionsObj.categories}
                    selectedValue={editedData.meta?.suggestions?.selectedCategory || suggestionsObj.selectedCategory}
                    onSelect={(value) => {
                      onSelect('category', value);
                      // Auto-save when a category is selected
                      handleSaveCategory(value);
                    }}
                    type="category"
                    invoiceId={data.id} // Pass the invoice ID to ensure correct selection
                  />
                  {isSavingCategory && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2 z-10">
                      <div className="bg-white dark:bg-gray-800 rounded-full p-1 shadow-sm border">
                        <svg className="animate-spin h-3 w-3 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      </div>
                    </div>
                  )}
                  {saveSuccess.category && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2 z-10">
                      <div className="bg-green-50 dark:bg-green-900/20 rounded-full p-1 shadow-sm border border-green-200 dark:border-green-800">
                        <CheckCircle className="h-3 w-3 text-green-600 dark:text-green-400" />
                      </div>
                    </div>
                  )}
                </div>
                <Button
                  onClick={() => {
                    handleSaveCategory();
                  }}
                  disabled={!editedData.meta?.suggestions?.selectedCategory || isSavingCategory}
                  size="sm"
                  className="bg-indigo-600 hover:bg-indigo-700 text-white disabled:opacity-50 disabled:cursor-not-allowed min-w-[80px]"
                >
                  {isSavingCategory ? (
                    <div className="flex items-center">
                      <svg className="animate-spin h-3 w-3 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Saving...
                    </div>
                  ) : saveSuccess.category ? (
                    <div className="flex items-center">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Saved
                    </div>
                  ) : (
                    "Save"
                  )}
                </Button>
              </div>

              {editedData.meta?.suggestions?.selectedCategory && (
                <div className="flex items-center mt-3 p-3 bg-white dark:bg-indigo-900/20 rounded-md border border-indigo-100 dark:border-indigo-800/30">
                  <div className="h-8 w-8 rounded-full bg-indigo-100 dark:bg-indigo-800/40 flex items-center justify-center mr-3">
                    {getCategoryIcon(editedData.meta.suggestions.selectedCategory)}
                  </div>
                  <div>
                    <span className="text-xs text-indigo-600 dark:text-indigo-400">Selected Category</span>
                    <span className="block font-medium">{safeToString(editedData.meta.suggestions.selectedCategory)}</span>
                  </div>
                </div>
              )}
            </div>
          ) : suggestionsObj.category ? (
            <div className="p-3 bg-white dark:bg-indigo-900/20 rounded-md border border-indigo-100 dark:border-indigo-800/30">
              <span className="text-lg font-semibold">{safeToString(suggestionsObj.category)}</span>
            </div>
          ) : (
            <div className="p-3 bg-white/50 dark:bg-indigo-900/10 rounded-md border border-indigo-100/50 dark:border-indigo-800/20">
              <span className="text-sm text-muted-foreground">No category suggestions available</span>
            </div>
          )}
        </div>


      </div>
    </div>
  );
}
