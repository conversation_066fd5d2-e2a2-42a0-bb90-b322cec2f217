/* eslint-disable @next/next/no-img-element */
import React from 'react';
import { Zap } from 'lucide-react';

const cards = [
  {
    title: "AI-Powered Data Extraction",
    description: "Extract invoice data with 99.8% accuracy using our advanced AI technology. Automatically recognize vendors, dates, amounts, and line items.",
    image: "/imgs/board.png"
  },
  {
    title: "Multi-Source Invoice Upload",
    description: "Upload via file, email integration, or connect directly with accounting software. Process invoices from any source in one unified platform.",
    image: "/imgs/click.png"
  },
  {
    title: "Comprehensive Analytics",
    description: "Gain deep financial insights with interactive dashboards showing cash flow analysis, spending trends, and vendor performance metrics.",
    image: "/imgs/cpuout.png"
  },
  {
    title: "Fraud & Anomaly Detection",
    description: "Automatically identify suspicious patterns, duplicate invoices, and unusual transactions with AI-powered anomaly detection.",
    image: "/imgs/chart4.png"
  },
  {
    title: "Predictive Financial Insights",
    description: "Forecast future expenses, identify cost-saving opportunities, and receive AI-generated recommendations for financial optimization.",
    image: "/imgs/drag&drop.png"
  }
];

function Benefits() {
  return (
    <div className="bg-black text-white py-6 sm:py-8 md:py-10 px-4 relative overflow-hidden">
      <div className="max-w-7xl mx-auto">
        {/* New title section */}
        <div className="relative mb-6 sm:mb-8 md:mb-12">
          <div className="inline-flex items-center gap-2 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full bg-neutral-900 border border-neutral-700 mb-3 sm:mb-4">
            <Zap className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
            <span className="text-xs sm:text-sm text-white font-medium">Key Benefits</span>
          </div>
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-2">Why Choose Our Platform?</h2>
          <p className="text-sm md:text-base text-gray-400 max-w-lg">
            Transform invoice processing with AI-powered automation and financial insights.
          </p>

          <div
            className="absolute top-2 right-0 text-[150px] font-bold select-none hidden lg:block z-0 overflow-hidden"
            style={{
              background: "linear-gradient(to bottom, rgba(255,255,255,0.2), transparent)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
              color: "transparent",
              maxWidth: "100%",
            }}
          >
            BENEFITS
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 mb-3">
          {cards.slice(0, 3).map((card, index) => (
            <div key={index} className="relative h-full bg-neutral-900/60 backdrop-blur-xl rounded-[20px] overflow-hidden shadow-[0_0_0_0,inset_0_0_30px_rgba(200,200,200,0.1)] border border-neutral-700 p-3 sm:p-4">
              <div className="h-40 sm:h-48 md:h-60 rounded-[20px] mb-2 sm:mb-3 flex items-center justify-center overflow-hidden border border-neutral-700">
                <img 
                  src={card.image} 
                  alt={card.title}
                  className="w-full h-full object-cover" 
                />
              </div>
              <div className="p-1 sm:p-2">
                <h3 className="text-base sm:text-lg font-bold mb-1">{card.title}</h3>
                <p className="text-xs sm:text-sm text-gray-400">{card.description}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 relative">
          {cards.slice(3).map((card, index) => (
            <div key={index} className="relative h-full bg-neutral-900/60 backdrop-blur-xl rounded-[20px] overflow-hidden shadow-[0_0_0_0,inset_0_0_30px_rgba(200,200,200,0.1)] border border-neutral-700 p-3 sm:p-4">
              <div className="border-neutral-700 h-36 sm:h-40 md:h-48 rounded-[20px] mb-2 sm:mb-4 flex items-center justify-center overflow-hidden ">
                <img 
                  src={card.image} 
                  alt={card.title}
                  className="w-full h-full object-cover " 
                />
              </div>
              <div className="p-1 sm:p-2">
                <h3 className="text-base sm:text-lg md:text-xl font-bold mb-1 sm:mb-2">{card.title}</h3>
                <p className="text-xs sm:text-sm md:text-base text-gray-400">{card.description}</p>
              </div>
            </div>
          ))}
        </div>
        <div className="absolute bottom-0 left-1/2 top-3/4 -translate-x-1/2 w-[60px] sm:w-[80px] md:w-[100px] h-[100px] sm:h-[120px] md:h-[150px] rounded-full bg-gradient-to-br from-[#ffffff] to-[#ffffff] opacity-20 blur-3xl pointer-events-none" />
      </div>
    </div>
  );
}

export default Benefits;