  import { nanoid } from "nanoid";
import { toast } from "sonner";

export interface EmailAttachment {
  messageId: string;
  subject: string;
  from: string;
  date: string;
  filename: string;
  mimeType: string;
  data: Uint8Array | string;
  messageUrl?: string;
}

/**
 * Convert base64 received from Gmail API to a format that can be processed by the browser
 */
function base64ToArrayBuffer(base64: string): Uint8Array {
  // Gmail API uses URL-safe base64, which needs to be converted to standard base64
  const base64Fixed = base64
    .replace(/-/g, '+')
    .replace(/_/g, '/')
    .replace(/\s/g, '');

  // Handle padding
  const padding = base64Fixed.length % 4;
  const paddedBase64 = padding ? 
    base64Fixed + '='.repeat(4 - padding) : 
    base64Fixed;

  try {
    // Use native decoder if available
    return new Uint8Array(
      [...atob(paddedBase64)].map(c => c.charCodeAt(0))
    );
  } catch {
    return new Uint8Array(0);
  }
}

export async function convertEmailAttachmentsToFiles(
  attachments: EmailAttachment[]
): Promise<File[]> {
  // Use a WeakMap to associate metadata if direct property assignment fails (for browser compatibility)
  const emailMetadataMap = new WeakMap<File, unknown>();
  const files: File[] = [];
  
  for (const attachment of attachments) {
    try {
      // Convert base64 data to a buffer
      let buffer: Uint8Array;
      if (typeof attachment.data === 'string') {
        // Convert base64 string safely
        buffer = base64ToArrayBuffer(attachment.data);
      } else {
        // Already a Uint8Array
        buffer = attachment.data;
      }
      
      // Skip if buffer is empty after decoding
      if (!buffer.length) {
        continue;
      }
      
      // Create a unique filename to avoid collisions
      const fileExtension = attachment.filename.split('.').pop() || '';
      const uniqueFilename = `${attachment.filename.replace(/\.[^/.]+$/, '')}_${nanoid(6)}.${fileExtension}`;
      
      // Create a File object with proper MIME type
      const file = new File(
        [new Uint8Array(buffer)],   // ← wrap your Uint8Array<ArrayBufferLike> in a fresh Uint8Array
        uniqueFilename,
        { type: attachment.mimeType }
      );
      
      // Try to attach metadata directly (works in most browsers, but not all)
      try {
        Object.defineProperty(file, 'emailMetadata', {
          value: {
            messageId: attachment.messageId,
            subject: attachment.subject,
            from: attachment.from,
            date: attachment.date,
            messageUrl: attachment.messageUrl
          },
          enumerable: true
        });
      } catch (e) {
        // Unused variable - silence eslint
        void e;
        
        // Fallback: store in WeakMap
        emailMetadataMap.set(file, {
          messageId: attachment.messageId,
          subject: attachment.subject,
          from: attachment.from,
          date: attachment.date,
          messageUrl: attachment.messageUrl
        });
      }
      
      files.push(file);
    } catch {
      toast.error("Failed to convert email attachments to files");
    }
  }

  // Patch getEmailMetadataFromFile to check WeakMap if needed
  if (typeof window !== 'undefined') {
    (window as unknown as Record<string, unknown>).__emailMetadataMap = emailMetadataMap;
  }
  
  return files;
}

/**
 * Get email metadata from a File if it exists
 */
export async function getEmailMetadataFromFile(file: File): Promise<{
  messageId?: string;
  subject?: string;
  from?: string;
  date?: string;
  messageUrl?: string;
} | null> {
  // Cast to our custom interface
  if ((file as unknown as { emailMetadata?: unknown }).emailMetadata) {
    return (file as unknown as { emailMetadata?: unknown }).emailMetadata as {
      messageId?: string;
      subject?: string;
      from?: string;
      date?: string;
      messageUrl?: string;
    };
  }
  // Fallback: check WeakMap
  if (typeof window !== 'undefined' && (window as unknown as Record<string, unknown>).__emailMetadataMap) {
    const map = (window as unknown as Record<string, unknown>).__emailMetadataMap as WeakMap<File, unknown>;
    return (map.get(file) as {
      messageId?: string;
      subject?: string;
      from?: string;
      date?: string;
      messageUrl?: string;
    }) || null;
  }
  return null;
} 