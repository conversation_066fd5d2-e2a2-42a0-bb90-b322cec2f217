import { NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import db from "@/db/db";

export async function GET() {
  try {
    const user = await currentUser();
    
    if (!user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const userId = user.id;
    
    // First get current user to obtain organization
    const currentUserData = await db.user.findUnique({
      where: { clerkId: userId },
      include: { organizations: true }
    });
    
    if (!currentUserData || !currentUserData.organizations || currentUserData.organizations.length === 0) {
      // User doesn't belong to an organization, return empty team
      return NextResponse.json([]);
    }
    
    // Get the user's primary organization
    const organizationId = currentUserData.organizations[0].id;
    
    // Find all users in the same organization
    const teamMembers = await db.user.findMany({
      where: {
        organizations: {
          some: {
            id: organizationId
          }
        }
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        role: true,
        status: true,
        createdAt: true
      }
    });
    
    // Transform the data for the frontend
    const formattedTeamMembers = teamMembers.map(member => ({
      id: member.id,
      name: `${member.firstName || ''} ${member.lastName || ''}`.trim() || 'Unnamed User',
      email: member.email,
      role: member.role,
      status: member.status,
      joinedAt: member.createdAt
    }));

    return NextResponse.json(formattedTeamMembers);
  } catch (error) {
    console.error("Error fetching team members:", error);
    return NextResponse.json(
      { error: "Failed to fetch team members" },
      { status: 500 }
    );
  }
}
