import { NextResponse } from "next/server";
import { exportReport } from "@/lib/actions/reports";
import { sendReportEmail } from "@/lib/email/send-email";
import db from "@/db/db";

// Vercel Cron Job handler
export async function GET(request: Request) {
  try {
    // Check for authorization header (consider using a secret key)
    const authHeader = request.headers.get("authorization");
    if (!authHeader || authHeader !== `Bearer ${process.env.CRON_SECRET_KEY}`) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Find reports due for generation
    const now = new Date();
    const dueReports = await db.scheduledReport.findMany({
      where: {
        active: true,
        nextRunTime: {
          lte: now,
        },
      },
      include: {
        report: true,
        user: {
          select: {
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });


    const results = [];

    // Process each scheduled report
    for (const scheduledReport of dueReports) {
      try {
        // Generate the report files
        const pdfResult = await exportReport(scheduledReport.reportId, "pdf");
        const excelResult = await exportReport(scheduledReport.reportId, "excel");
        
        // Send email to all recipients
        if (scheduledReport.emailAddresses && scheduledReport.emailAddresses.length > 0) {
          await sendReportEmail({
            recipients: scheduledReport.emailAddresses.split(','),
            reportName: scheduledReport.report.title,
            pdfUrl: pdfResult.fileUrl,
            excelUrl: excelResult.fileUrl,
            userName: `${scheduledReport.user.firstName || ""} ${scheduledReport.user.lastName || ""}`.trim(),
          });
        }

        // Calculate next run date based on frequency
        const nextRunTime = calculateNextRunDate(
          scheduledReport.nextRunTime,
          scheduledReport.frequency
        );

        // Update the scheduled report with the new next run date
        await db.scheduledReport.update({
          where: {
            id: scheduledReport.id,
          },
          data: {
            nextRunTime,
          },
        });

        results.push({
          id: scheduledReport.id,
          reportId: scheduledReport.reportId,
          reportName: scheduledReport.report.title,
          success: true,
          nextRunDate: nextRunTime,
        });
      } catch (error) {
        console.error(
          `Failed to process scheduled report ${scheduledReport.id}:`,
          error
        );
        results.push({
          id: scheduledReport.id,
          reportId: scheduledReport.reportId,
          reportName: scheduledReport.report.title,
          success: false,
          error: (error as Error).message,
        });
      }
    }

    return NextResponse.json({
      success: true,
      processed: dueReports.length,
      results,
    });
  } catch (error) {
    console.error("Failed to process scheduled reports:", error);
    return NextResponse.json(
      { success: false, error: "Failed to process scheduled reports" },
      { status: 500 }
    );
  }
}

// Helper function to calculate the next run date based on frequency
function calculateNextRunDate(currentDate: Date, frequency: string): Date {
  const nextDate = new Date(currentDate);

  switch (frequency.toLowerCase()) {
    case "daily":
      nextDate.setDate(nextDate.getDate() + 1);
      break;
    case "weekly":
      nextDate.setDate(nextDate.getDate() + 7);
      break;
    case "monthly":
      nextDate.setMonth(nextDate.getMonth() + 1);
      break;
    case "quarterly":
      nextDate.setMonth(nextDate.getMonth() + 3);
      break;
    case "yearly":
      nextDate.setFullYear(nextDate.getFullYear() + 1);
      break;
    default:
      // Default to monthly if frequency is unknown
      nextDate.setMonth(nextDate.getMonth() + 1);
  }

  return nextDate;
} 