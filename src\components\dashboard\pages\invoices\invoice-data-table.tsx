'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import {
  useRouter,
  usePathname,
  useSearchParams,
} from 'next/navigation';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Pagination } from '@/components/dashboard/pages/invoices/pagination';
import { formatCurrency, formatDate } from '@/lib/utils';
import {
  MoreHorizontal,
  Eye,
  FileText,
  Trash2,
  CheckCircle2,
  AlertCircle,
  ArrowUpDown,
  FolderPlus,
} from 'lucide-react';
import { InvoiceStatusBadge } from '@/components/dashboard/pages/invoices/invoice-status-badge';
import { InvoiceTableSkeleton } from '@/components/dashboard/pages/invoices/invoice-table-skeleton';
import { Checkbox } from '@/components/ui/checkbox';
import { BatchSelection } from '@/components/dashboard/pages/invoices/batch-selection';
import { ExportDialog } from '@/components/dashboard/pages/invoices/export-dialog';
import { toast } from 'sonner';
import {
  updateInvoiceStatus,
  deleteInvoice,
} from '@/actions/invoice-actions';
import { InvoiceStatus } from '@/lib/types';

interface Invoice {
  id: string;
  invoiceNumber?: string | null;
  status: string;
  issueDate?: Date | null;
  dueDate?: Date | null;
  amount?: number | null;
  currency?: string | null;
  category?: {
    id: string;
    name: string;
  } | null;
  vendorName?: string | null;
  thumbnailUrl?: string | null;
  originalFileUrl?: string | null;
  [key: string]: unknown;
}



interface InvoiceDataTableProps {
  initialData: {
    invoices: Invoice[];
    totalCount: number;
    pageCount: number;
  };
  page: number;
  limit: number;
  status?: string;
  vendor?: string;
  category?: string;
  dateRange?: string;
  sort: string;
  order: string;
  search: string;
}

export function InvoiceDataTable({
  initialData,
  page,
  limit,
  status,
  vendor,
  category,
  dateRange,
  sort,
  order,
  search,
}: InvoiceDataTableProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [data, setData] = useState<{
    invoices: Invoice[];
    totalCount: number;
    pageCount: number;
  }>(initialData);

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedInvoices, setSelectedInvoices] = useState<Invoice[]>(
    []
  );
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState<
    string | null
  >(null);

  // Memoize the fetch parameters to prevent unnecessary re-renders
  const fetchParams = useMemo(
    () => ({
      page,
      limit,
      status,
      vendor,
      category,
      dateRange,
      sort,
      order,
      search,
    }),
    [
      page,
      limit,
      status,
      vendor,
      category,
      dateRange,
      sort,
      order,
      search,
    ]
  );

  const fetchData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const searchParams = new URLSearchParams();
      Object.entries(fetchParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, String(value));
        }
      });

      const response = await fetch(
        `/api/invoices?${searchParams.toString()}`
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || 'Failed to fetch invoices'
        );
      }

      const result = await response.json();
      setData(result);
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : 'Failed to load invoices. Please try again.'
      );
    } finally {
      setLoading(false);
    }
  }, [fetchParams]);

  useEffect(() => {
    fetchData();
    // Clear selection when filters change
    setSelectedInvoices([]);
  }, [fetchData]);

  const createQueryString = useCallback(
    (params: Record<string, string | number | null>) => {
      const newParams = new URLSearchParams(searchParams.toString());

      Object.entries(params).forEach(([key, value]) => {
        if (value === null) {
          newParams.delete(key);
        } else {
          newParams.set(key, String(value));
        }
      });

      return newParams.toString();
    },
    [searchParams]
  );

  const handleSort = useCallback(
    (column: string) => {
      const newOrder =
        sort === column && order === 'asc' ? 'desc' : 'asc';
      router.push(
        `${pathname}?${createQueryString({
          sort: column,
          order: newOrder,
        })}`
      );
    },
    [sort, order, router, pathname, createQueryString]
  );

  const handlePageChange = useCallback(
    (newPage: number) => {
      router.push(
        `${pathname}?${createQueryString({
          page: newPage,
        })}`
      );
    },
    [router, pathname, createQueryString]
  );

  const handleSelectInvoice = useCallback(
    (invoice: Invoice, checked: boolean) => {
      if (checked) {
        setSelectedInvoices((prev) => [...prev, invoice]);
      } else {
        setSelectedInvoices((prev) =>
          prev.filter((i) => i.id !== invoice.id)
        );
      }
    },
    []
  );

  const handleSelectAll = useCallback(
    (checked: boolean) => {
      if (checked) {
        setSelectedInvoices(data.invoices);
      } else {
        setSelectedInvoices([]);
      }
    },
    [data.invoices]
  );

  const handleStatusUpdate = useCallback(
    async (invoiceId: string, newStatus: InvoiceStatus) => {
      try {
        setIsUpdatingStatus(invoiceId);
        await updateInvoiceStatus(invoiceId, newStatus);
        toast.success(
          `Invoice has been marked as ${newStatus.toLowerCase()}.`
        );
        await fetchData();
      } catch {
        toast.error('Failed to update invoice status.');
      } finally {
        setIsUpdatingStatus(null);
      }
    },
    [fetchData]
  );

  const handleDelete = useCallback(
    async (invoiceId: string) => {
      try {
        setIsDeleting(invoiceId);
        await deleteInvoice(invoiceId);
        toast.success('Invoice has been deleted.');
        await fetchData();
      } catch {
        toast.error('Failed to delete the invoice.');
      } finally {
        setIsDeleting(null);
      }
    },
    [fetchData]
  );

  // Memoize expensive calculations
  const allSelected = useMemo(
    () =>
      selectedInvoices.length === data.invoices.length &&
      data.invoices.length > 0,
    [selectedInvoices.length, data.invoices.length]
  );

  if (loading) {
    return <InvoiceTableSkeleton />;
  }

  if (error) {
    return (
      <div className="rounded-md bg-destructive/10 p-6 text-center">
        <AlertCircle className="mx-auto h-10 w-10 text-destructive" />
        <h3 className="mt-2 text-lg font-medium">
          Error Loading Invoices
        </h3>
        <p className="mt-1 text-sm text-muted-foreground">{error}</p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => fetchData()}
        >
          Try Again
        </Button>
      </div>
    );
  }

  if (data.invoices.length === 0) {
    return (
      <div className="rounded-md bg-muted/50 p-8 text-center">
        <FileText className="mx-auto h-10 w-10 text-muted-foreground" />
        <h3 className="mt-4 text-lg font-medium">
          No invoices found
        </h3>
        <p className="mt-2 text-sm text-muted-foreground">
          {search
            ? 'Try adjusting your search or filters'
            : 'Upload some invoices to get started'}
        </p>
        <div className="flex justify-center mt-4">
          <Button
            variant="outline"
            size="default"
            className="flex items-center gap-2"
            onClick={() => router.push('/dashboard/upload')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2"><path strokeLinecap="round" strokeLinejoin="round" d="M4 16v2a2 2 0 002 2h12a2 2 0 002-2v-2M7 10l5-5m0 0l5 5m-5-5v12" /></svg>
            Upload Invoices
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {selectedInvoices.length > 0 ? (
        <BatchSelection
          selectedInvoices={selectedInvoices}
          onClearSelection={() => setSelectedInvoices([])}
          onRefresh={fetchData}
        />
      ) : (
        <div className="flex justify-end">
          <Button
            variant="outline"
            onClick={() => setIsExportDialogOpen(true)}
            disabled={data.invoices.length === 0}
          >
            <FolderPlus className="mr-2 h-4 w-4" />
            Export All
          </Button>
        </div>
      )}

      <Card>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[40px]">
                  <Checkbox
                    checked={allSelected}
                    onCheckedChange={handleSelectAll}
                    aria-label="Select all invoices"
                  />
                </TableHead>
                <TableHead className="w-[100px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('invoiceNumber')}
                    className="flex items-center gap-1 font-medium"
                  >
                    Invoice #
                    <ArrowUpDown className="h-3 w-3" />
                  </Button>
                </TableHead>
                <TableHead>
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('vendorName')}
                    className="flex items-center gap-1 font-medium"
                  >
                    Vendor
                    <ArrowUpDown className="h-3 w-3" />
                  </Button>
                </TableHead>
                <TableHead>
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('issueDate')}
                    className="flex items-center gap-1 font-medium"
                  >
                    Date
                    <ArrowUpDown className="h-3 w-3" />
                  </Button>
                </TableHead>
                <TableHead>
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('amount')}
                    className="flex items-center gap-1 font-medium"
                  >
                    Amount
                    <ArrowUpDown className="h-3 w-3" />
                  </Button>
                </TableHead>
                <TableHead>
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('status')}
                    className="flex items-center gap-1 font-medium"
                  >
                    Status
                    <ArrowUpDown className="h-3 w-3" />
                  </Button>
                </TableHead>
                <TableHead>Category</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.invoices.map((invoice) => (
                <TableRow key={invoice.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedInvoices.some(
                        (i) => i.id === invoice.id
                      )}
                      onCheckedChange={(checked) =>
                        handleSelectInvoice(
                          invoice,
                          checked as boolean
                        )
                      }
                      aria-label={`Select invoice ${invoice.invoiceNumber || invoice.id}`}
                    />
                  </TableCell>
                  <TableCell className="font-medium">
                    {invoice.invoiceNumber || '-'}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {invoice.vendorName || 'Unknown Vendor'}
                    </div>
                  </TableCell>
                  <TableCell>
                    {invoice.issueDate
                      ? formatDate(invoice.issueDate)
                      : '-'}
                  </TableCell>
                  <TableCell>
                    {invoice.amount
                      ? formatCurrency(invoice.amount)
                      : '-'}
                  </TableCell>
                  <TableCell>
                    <InvoiceStatusBadge status={invoice.status} />
                  </TableCell>
                  <TableCell>
                    {invoice.category ? (
                      <Badge variant="outline" className="capitalize">
                        {invoice.category.name}
                      </Badge>
                    ) : (
                      '-'
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem
                          onClick={() =>
                            router.push(
                              `/dashboard/invoices/${invoice.id}`
                            )
                          }
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() =>
                            invoice.originalFileUrl &&
                            window.open(
                              invoice.originalFileUrl,
                              '_blank'
                            )
                          }
                          disabled={!invoice.originalFileUrl}
                        >
                          <FileText className="mr-2 h-4 w-4" />
                          View Original
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() =>
                            handleStatusUpdate(invoice.id, 'PAID')
                          }
                          disabled={
                            invoice.status === 'PAID' ||
                            isUpdatingStatus === invoice.id
                          }
                        >
                          <CheckCircle2 className="mr-2 h-4 w-4" />
                          Mark as Paid
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() =>
                            handleStatusUpdate(invoice.id, 'PENDING')
                          }
                          disabled={
                            invoice.status === 'PENDING' ||
                            isUpdatingStatus === invoice.id
                          }
                        >
                          <AlertCircle className="mr-2 h-4 w-4 text-amber-500" />
                          Mark as Pending
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() =>
                            handleStatusUpdate(invoice.id, 'OVERDUE')
                          }
                          disabled={
                            invoice.status === 'OVERDUE' ||
                            isUpdatingStatus === invoice.id
                          }
                        >
                          <AlertCircle className="mr-2 h-4 w-4 text-red-500" />
                          Mark as Overdue
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => handleDelete(invoice.id)}
                          className="text-destructive focus:text-destructive"
                          disabled={isDeleting === invoice.id}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </Card>

      <Pagination
        currentPage={page}
        pageCount={data.pageCount}
        totalItems={data.totalCount}
        itemsPerPage={limit}
        onPageChange={handlePageChange}
      />

      <ExportDialog
        isOpen={isExportDialogOpen}
        onOpenChange={setIsExportDialogOpen}
        invoices={data.invoices}
      />
    </div>
  );
}
