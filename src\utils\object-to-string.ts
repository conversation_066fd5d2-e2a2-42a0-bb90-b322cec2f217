/**
 * Safely converts any value to a string representation
 * Handles objects, arrays, null, undefined, and primitive types
 * 
 * @param value - The value to convert to string
 * @returns A string representation of the value
 */
export function safeToString(value: unknown): string {
  if (value === null || value === undefined) {
    return 'N/A';
  }
  
  if (typeof value === 'object') {
    try {
      return JSON.stringify(value);
    } catch {
      return '[Complex Object]';
    }
  }
  
  return String(value);
}

/**
 * Recursively checks if an object contains any nested objects
 * that could cause rendering issues
 * 
 * @param obj - The object to check
 * @returns True if the object contains nested objects, false otherwise
 */
export function containsNestedObjects(obj: unknown): boolean {
  if (obj === null || obj === undefined || typeof obj !== 'object') {
    return false;
  }
  
  if (Array.isArray(obj)) {
    return obj.some(item => containsNestedObjects(item));
  }
  
  return Object.values(obj).some(value => 
    typeof value === 'object' && value !== null
  );
}

/**
 * Recursively converts all nested objects in an object to string representations
 * 
 * @param obj - The object to process
 * @returns A new object with all nested objects converted to strings
 */
export function flattenObjectsToStrings(obj: unknown): unknown {
  if (obj === null || obj === undefined || typeof obj !== 'object') {
    return obj;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => flattenObjectsToStrings(item));
  }
  
  const result: Record<string, unknown> = {};
  
  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === 'object' && value !== null) {
      if (containsNestedObjects(value)) {
        // If it contains further nested objects, stringify the whole thing
        result[key] = JSON.stringify(value);
      } else {
        // Otherwise, process each property
        result[key] = flattenObjectsToStrings(value);
      }
    } else {
      result[key] = value;
    }
  }
  
  return result;
}
