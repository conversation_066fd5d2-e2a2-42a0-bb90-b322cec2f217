'use client';

import { formatCurrency } from '@/lib/utils';

interface InvoiceStatsProps {
  stats: {
    total: {
      count: number;
      amount: number;
    };
    paid?: {
      count: number;
      amount: number;
    };
    pending?: {
      count: number;
      amount: number;
    };
    overdue?: {
      count: number;
      amount: number;
    };
    cancelled?: {
      count: number;
      amount: number;
    };
    currencyBreakdown?: Record<string, {
      count: number;
      amount: number;
    }>;
    percentages?: {
      paid: number;
      pending: number;
      overdue: number;
      cancelled: number;
    };
    timeframe?: {
      startDate: string | null;
      endDate: string | null;
    };
    message?: string;
  };
}

export function InvoiceStats({ stats }: InvoiceStatsProps) {
  const statusKeys = ['paid', 'pending', 'overdue', 'cancelled'] as const;
  
  return (
    <div className="bg-card/50 border border-border/40 px-5 py-3.5 rounded-2xl shadow-sm">
      <div className="flex flex-col gap-2">
        {/* If there's a message property, show it first */}
        {stats.message && (
          <p>{stats.message}</p>
        )}
        
        {/* Show the total */}
        <div className="flex items-center justify-between">
          <span className="font-medium text-lg">Total Invoices:</span>
          <span className="font-medium text-lg">
            {stats.total.count} 
            <span className="text-muted-foreground ml-1">
              ({formatCurrency(stats.total.amount)})
            </span>
          </span>
        </div>
        
        {/* Status breakdown */}
        <div className="grid grid-cols-2 gap-x-4 gap-y-2 mt-2">
          {statusKeys.map(statusKey => {
            const statusData = stats[statusKey];
            if (!statusData) return null;

            return (
              <div key={statusKey} className="flex justify-between">
                <span className="capitalize">{statusKey}:</span>
                <span>
                  {statusData.count}
                  <span className="text-muted-foreground ml-1">
                    ({formatCurrency(statusData.amount)})
                    {stats.percentages && (
                      <span className="ml-1">
                        {stats.percentages[statusKey]}%
                      </span>
                    )}
                  </span>
                </span>
              </div>
            );
          })}
        </div>
        
        {/* Currency breakdown if available */}
        {stats.currencyBreakdown && Object.keys(stats.currencyBreakdown).length > 0 && (
          <div className="mt-3">
            <p className="text-sm font-medium mb-1">Currency Breakdown:</p>
            <div className="grid grid-cols-2 gap-x-4 gap-y-1">
              {Object.entries(stats.currencyBreakdown).map(([currency, data]) => (
                <div key={currency} className="flex justify-between">
                  <span>{currency}:</span>
                  <span>
                    {data.count} 
                    <span className="text-muted-foreground ml-1">
                      ({formatCurrency(data.amount)})
                    </span>
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Timeframe if available */}
        {stats.timeframe && (stats.timeframe.startDate || stats.timeframe.endDate) && (
          <div className="mt-3 text-sm text-muted-foreground">
            <p>
              Timeframe: 
              {stats.timeframe.startDate 
                ? ` From ${new Date(stats.timeframe.startDate).toLocaleDateString()}` 
                : ' All time'} 
              {stats.timeframe.endDate 
                ? ` to ${new Date(stats.timeframe.endDate).toLocaleDateString()}` 
                : ' to present'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
