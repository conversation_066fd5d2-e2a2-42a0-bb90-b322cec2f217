import { Check } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface VisualizationCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  selected?: boolean;
  onClick?: () => void;
}

export function VisualizationCard({
  title,
  description,
  icon,
  selected,
  onClick,
}: VisualizationCardProps) {
  return (
    <Card 
      className={`cursor-pointer transition-all ${selected ? "border-primary" : "hover:border-primary"}`}
      onClick={onClick}
    >
      <CardHeader className="pb-2">
        <div className="flex justify-center">{icon}</div>
        <CardTitle className="mt-2 text-center">{title}</CardTitle>
        <CardDescription className="text-center">{description}</CardDescription>
      </CardHeader>
      <CardFooter>
        <Button 
          variant={selected ? "default" : "outline"} 
          size="sm" 
          className="w-full"
          onClick={onClick}
        >
          {selected ? (
            <>
              <Check className="mr-2 h-4 w-4" />
              Selected
            </>
          ) : "Select"}
        </Button>
      </CardFooter>
    </Card>
  );
} 