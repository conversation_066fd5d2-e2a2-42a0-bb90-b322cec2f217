"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  FolderPlus,
  Trash2,
  CheckCircle2,
  AlertCircle,
  XCircle,
  MoreHorizontal,
  Tag,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { ExportDialog } from "@/components/dashboard/pages/invoices/export-dialog";
import { toast } from "sonner";
import { updateInvoiceStatus, deleteInvoice, updateMultipleInvoicesCategory } from "@/actions/invoice-actions";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { InvoiceStatus } from "@/lib/types";
import { getCategories } from "@/lib/actions/analytics";

interface BatchSelectionProps {
  selectedInvoices: { id: string; }[];
  onClearSelection: () => void;
  onRefresh: () => void;
}

export function BatchSelection({
  selectedInvoices,
  onClearSelection,
  onRefresh,
}: BatchSelectionProps) {
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [categories, setCategories] = useState<{ id: string; name: string }[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);

  // Fetch categories when component mounts
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setIsLoadingCategories(true);
        const categoriesData = await getCategories();
        setCategories(categoriesData);
      } catch {
        toast.error("Failed to load categories");
      } finally {
        setIsLoadingCategories(false);
      }
    };

    fetchCategories();
  }, []);

  const handleBulkStatusUpdate = async (status: InvoiceStatus) => {
    try {
      setIsProcessing(true);

      // Process invoices sequentially to avoid overwhelming the server
      for (const invoice of selectedInvoices) {
        await updateInvoiceStatus(invoice.id, status);
      }

      toast.success(
        `${selectedInvoices.length} invoices marked as ${status.toLowerCase()}.`
      );
      onClearSelection();
      onRefresh();
    } catch {
      toast.error("Failed to update invoice status.");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleBulkCategoryUpdate = async (categoryName: string) => {
    try {
      setIsProcessing(true);

      // Get all invoice IDs
      const invoiceIds = selectedInvoices.map(invoice => invoice.id);

      // Update all invoices with the selected category
      const result = await updateMultipleInvoicesCategory(invoiceIds, categoryName);

      if (result.success) {
        toast.success(
          `${result.updatedCount} invoices categorized as "${categoryName}".`
        );

        if (result.failedCount && result.failedCount > 0) {
          toast.warning(`Failed to update ${result.failedCount} invoices.`);
        }

        onClearSelection();
        onRefresh();
      } else {
        throw new Error(result.error || "Failed to update categories");
      }
    } catch {
      toast.error("Failed to update invoice categories.");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleBulkDelete = async () => {
    try {
      setIsProcessing(true);

      // Process invoices sequentially to avoid overwhelming the server
      for (const invoice of selectedInvoices) {
        await deleteInvoice(invoice.id);
      }

      toast.success(`${selectedInvoices.length} invoices deleted.`);
      onClearSelection();
      onRefresh();
      setIsDeleteDialogOpen(false);
    } catch {
      toast.error("Failed to delete invoices.");
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <>
      <div className="flex items-center gap-2 bg-muted/50 p-2 rounded-md">
        <Checkbox checked={true} disabled />
        <span className="text-sm font-medium">
          {selectedInvoices.length} invoice
          {selectedInvoices.length !== 1 ? "s" : ""} selected
        </span>

        <div className="ml-auto flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsExportDialogOpen(true)}
            disabled={isProcessing}
          >
            <FolderPlus className="h-4 w-4 mr-2" />
            Export
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" disabled={isProcessing}>
                <CheckCircle2 className="h-4 w-4 mr-2" />
                Status
                <MoreHorizontal className="h-4 w-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Update Status</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleBulkStatusUpdate("PAID")}>
                <CheckCircle2 className="h-4 w-4 mr-2 text-green-500" />
                Mark as Paid
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleBulkStatusUpdate("PENDING")}
              >
                <AlertCircle className="h-4 w-4 mr-2 text-amber-500" />
                Mark as Pending
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleBulkStatusUpdate("OVERDUE")}
              >
                <AlertCircle className="h-4 w-4 mr-2 text-red-500" />
                Mark as Overdue
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleBulkStatusUpdate("CANCELLED")}
              >
                <XCircle className="h-4 w-4 mr-2 text-gray-500" />
                Mark as Cancelled
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" disabled={isProcessing || isLoadingCategories}>
                <Tag className="h-4 w-4 mr-2" />
                Category
                <MoreHorizontal className="h-4 w-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Assign Category</DropdownMenuLabel>
              {isLoadingCategories ? (
                <DropdownMenuItem disabled>
                  Loading categories...
                </DropdownMenuItem>
              ) : categories.length > 0 ? (
                categories.map((category) => (
                  <DropdownMenuItem
                    key={category.id}
                    onClick={() => handleBulkCategoryUpdate(category.name)}
                  >
                    <Tag className="h-4 w-4 mr-2 text-purple-500" />
                    {category.name}
                  </DropdownMenuItem>
                ))
              ) : (
                <DropdownMenuItem disabled>
                  No categories available
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleBulkCategoryUpdate("Uncategorized")}>
                <Tag className="h-4 w-4 mr-2 text-gray-500" />
                Mark as Uncategorized
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsDeleteDialogOpen(true)}
            disabled={isProcessing}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={onClearSelection}
            disabled={isProcessing}
          >
            Cancel
          </Button>
        </div>
      </div>

      <ExportDialog
        isOpen={isExportDialogOpen}
        onOpenChange={setIsExportDialogOpen}
        invoices={selectedInvoices}
      />

      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete {selectedInvoices.length} invoice
              {selectedInvoices.length !== 1 ? "s" : ""} and all associated
              data. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isProcessing}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleBulkDelete();
              }}
              disabled={isProcessing}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isProcessing ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
