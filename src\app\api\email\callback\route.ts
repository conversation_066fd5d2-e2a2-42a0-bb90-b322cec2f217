import { NextRequest, NextResponse } from "next/server";
import { connectGmailAccount } from "@/lib/services/gmail-service";

export async function GET(request: NextRequest) {
  try {
    // Get the authorization code and state from the query parameters
    const searchParams = request.nextUrl.searchParams;
    const code = searchParams.get("code");
    const stateParam = searchParams.get("state");
    const error = searchParams.get("error");

    // Redirect to the settings page in case of error or cancellation
    if (error || !code || !stateParam) {
      console.error("OAuth callback error:", error || "Missing code or state");
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings/email?error=oauth_failed`);
    }

    try {
      // Decode the state parameter to get the userId
      const { userId, provider } = JSON.parse(Buffer.from(stateParam, "base64").toString());

      if (!userId || provider !== "gmail") {
        throw new Error("Invalid state parameter");
      }

      // Connect the Gmail account
      await connectGmailAccount(userId, code);

      // Redirect to the settings page with success message
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings/email?success=connected`);
    } catch (parseError) {
      console.error("Error parsing state parameter:", parseError);
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings/email?error=invalid_state`);
    }
  } catch (error) {
    console.error("Error handling OAuth callback:", error);
    return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings/email?error=server_error`);
  }
} 