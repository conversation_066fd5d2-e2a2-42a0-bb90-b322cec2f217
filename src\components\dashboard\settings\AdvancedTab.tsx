'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';

const AdvancedTab = () => {

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Advanced Settings</CardTitle>
          <CardDescription>
            Manage advanced application settings.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Data Preferences</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="data-export">Export All Data</Label>
                  <p className="text-sm text-muted-foreground">
                    Download a copy of all your account data and
                    invoices.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Removed Developer Options section as per request */}
        </CardContent>
      </Card>
    </div>
  );
};

export default AdvancedTab;
