'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';
import { MailIcon, SendIcon } from './icons';

interface ReportEmailerProps {
  reportId: string;
  title: string;
  reportType: string;
  format: string;
  fileUrl?: string;
  onSend: (emailData: EmailData) => void;
  onCancel: () => void;
}

export interface EmailData {
  reportId: string;
  emailAddresses: string[];
  subject: string;
  message: string;
  includeExcel: boolean;
}

export function ReportEmailer({
  reportId,
  title,
  reportType,
  format,
  fileUrl,
  onSend,
  onCancel,
}: ReportEmailerProps) {
  const [emailInput, setEmailInput] = useState<string>('');
  const [emailAddresses, setEmailAddresses] = useState<string[]>([]);
  const [subject, setSubject] = useState<string>(`Your ${title} Report`);
  const [message, setMessage] = useState<string>(`Here is your ${reportType} report "${title}" that you requested.`);
  const [includeExcel, setIncludeExcel] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleAddEmail = () => {
    if (!emailInput) return;
    
    // Simple email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(emailInput)) {
      toast.error('Please enter a valid email address');
      return;
    }
    
    if (!emailAddresses.includes(emailInput)) {
      setEmailAddresses([...emailAddresses, emailInput]);
      setEmailInput('');
    } else {
      toast.error('Email already added');
    }
  };

  const handleRemoveEmail = (email: string) => {
    setEmailAddresses(emailAddresses.filter(e => e !== email));
  };

  const handleSubmit = () => {
    if (emailAddresses.length === 0) {
      toast.error('Please add at least one email address');
      return;
    }

    if (!fileUrl) {
      toast.error('Report file is not available for sending');
      return;
    }

    setIsSubmitting(true);

    const emailData: EmailData = {
      reportId,
      emailAddresses,
      subject,
      message,
      includeExcel,
    };

    // Simulate API call
    setTimeout(() => {
      onSend(emailData);
      setIsSubmitting(false);
      toast.success('Report sent successfully');
    }, 1000);
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MailIcon className="h-5 w-5" />
          Email Report
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="report-title">Report</Label>
          <div id="report-title" className="text-sm font-medium mt-1">
            {title} ({reportType}) - {format}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Recipients</Label>
          <div className="flex gap-2">
            <Input
              id="email"
              type="email"
              placeholder="Enter email address"
              value={emailInput}
              onChange={(e) => setEmailInput(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  handleAddEmail();
                }
              }}
            />
            <Button type="button" onClick={handleAddEmail} variant="secondary">
              Add
            </Button>
          </div>
          
          {emailAddresses.length > 0 && (
            <div className="mt-2 space-y-2">
              {emailAddresses.map((email) => (
                <div
                  key={email}
                  className="flex items-center justify-between bg-secondary/50 p-2 rounded-md"
                >
                  <span className="text-sm truncate">{email}</span>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveEmail(email)}
                  >
                    &times;
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>

        <div>
          <Label htmlFor="subject">Subject</Label>
          <Input
            id="subject"
            value={subject}
            onChange={(e) => setSubject(e.target.value)}
          />
        </div>

        <div>
          <Label htmlFor="message">Message</Label>
          <Textarea
            id="message"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            rows={4}
          />
        </div>

        {format === 'PDF' && (
          <div className="flex items-center space-x-2">
            <Checkbox
              id="include-excel"
              checked={includeExcel}
              onCheckedChange={(checked) => setIncludeExcel(checked as boolean)}
            />
            <Label htmlFor="include-excel">
              Include Excel version
            </Label>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="ghost" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={handleSubmit} disabled={isSubmitting || emailAddresses.length === 0 || !fileUrl}>
          {isSubmitting ? (
            <>
              <span className="animate-spin mr-2">⟳</span>
              Sending...
            </>
          ) : (
            <>
              <SendIcon className="mr-2 h-4 w-4" />
              Send Report
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
