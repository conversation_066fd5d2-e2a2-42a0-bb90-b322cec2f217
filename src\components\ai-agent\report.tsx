'use client';

import { useArtifact } from '@/hooks/use-artifact';
import { cn } from '@/lib/utils';
import { Button } from '../ui/button';
import { FileIcon, LoaderIcon, CheckIcon } from './icons';
import { toast } from 'sonner';

interface ReportToolCallProps {
  type: 'create' | 'update' | 'email';
  args: {
    title: string;
    reportType?: string;
  };
  progress?: {
    step: number;
    totalSteps: number;
    message: string;
    percentage: number;
  };
  isReadonly?: boolean;
}

export function ReportToolCall({
  type,
  args,
  progress,
}: ReportToolCallProps) {
  return (
    <Button
      type="button"
      className="bg-background cursor-pointer border py-2 px-3 rounded-xl w-fit flex flex-row gap-3 items-start"
      disabled={true}
    >
      <div className="flex flex-row gap-2 items-center">
        <LoaderIcon className="animate-spin" />
        <span className="text-sm">
          {type === 'create'
            ? `Creating "${args.title}"`
            : type === 'update'
            ? `Updating "${args.title}"`
            : `Emailing "${args.title}"`}
        </span>
        {progress && (
          <span className="text-xs text-muted-foreground ml-2">
            {progress.percentage}%
          </span>
        )}
      </div>
    </Button>
  );
}

interface ReportToolResultProps {
  type: 'create' | 'update' | 'email';
  result: {
    id: string;
    title: string;
    reportType: string;
    format: string;
    fileUrl?: string;
  };
  isReadonly?: boolean;
}

export function ReportToolResult({
  type,
  result,
  isReadonly,
}: ReportToolResultProps) {
  const { setArtifact } = useArtifact();

  const handleClick = () => {
    if (isReadonly) {
      toast.error(
        'Viewing reports in shared chats is currently not supported.',
      );
      return;
    }

    if (result.fileUrl) {
      // If we have a file URL, open it in a new tab
      window.open(result.fileUrl, '_blank');
    } else {
      // Otherwise, set the artifact to be visible
      setArtifact({
        reportId: result.id,
        title: result.title,
        reportType: result.reportType,
        format: result.format,
        fileUrl: result.fileUrl,
        isVisible: true,
        status: 'idle',
        content: '',
        kind: 'text',
        documentId: '',
        boundingBox: {
          top: 0,
          left: 0,
          width: 0,
          height: 0,
        },
      });
    }
  };

  return (
    <Button
      type="button"
      className="bg-background cursor-pointer border py-2 px-3 rounded-xl w-fit flex flex-row gap-3 items-start"
      onClick={handleClick}
    >
      <div className="flex flex-row gap-2 items-center">
        {type === 'create' ? (
          <FileIcon className={cn("h-4 w-4", result.format === 'PDF' ? "text-red-500" : "text-green-500")} />
        ) : type === 'update' ? (
          <CheckIcon className="h-4 w-4 text-green-500" />
        ) : (
          <CheckIcon className="h-4 w-4 text-blue-500" />
        )}
        <span className="text-sm">
          {type === 'create'
            ? `Created "${result.title}"`
            : type === 'update'
            ? `Updated "${result.title}"`
            : `Emailed "${result.title}"`}
        </span>
      </div>
    </Button>
  );
}

export function ReportEmailToolCall({
  reportTitle,
  recipients,
}: {
  reportTitle: string;
  recipients: string[];
}) {
  return (
    <Button
      type="button"
      className="bg-background cursor-pointer border py-2 px-3 rounded-xl w-fit flex flex-row gap-3 items-start"
      disabled={true}
    >
      <div className="flex flex-row gap-2 items-center">
        <LoaderIcon className="animate-spin" />
        <span className="text-sm">
          Sending &quot;{reportTitle}&quot; to {recipients.length} recipient{recipients.length !== 1 ? 's' : ''}
        </span>
      </div>
    </Button>
  );
}

export function ReportEmailToolResult({
  reportTitle,
  recipients,
}: {
  reportTitle: string;
  recipients: string[];
}) {
  return (
    <Button
      type="button"
      className="bg-background cursor-pointer border py-2 px-3 rounded-xl w-fit flex flex-row gap-3 items-start"
    >
      <div className="flex flex-row gap-2 items-center">
        <CheckIcon className="h-4 w-4 text-blue-500" />
        <span className="text-sm">
          Sent &quot;{reportTitle}&quot; to {recipients.length} recipient{recipients.length !== 1 ? 's' : ''}
        </span>
      </div>
    </Button>
  );
}
