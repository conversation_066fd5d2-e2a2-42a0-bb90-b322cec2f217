'use client';

import type {
  Attachment,
  ChatRequestOptions,
  CreateMessage,
  Message,
} from 'ai';
import {
  useRef,
  useEffect,
  useState,
  useCallback,
  type Dispatch,
  type SetStateAction,
  type ChangeEvent,
  memo,
} from 'react';
import { toast } from 'sonner';
import { useLocalStorage, useWindowSize } from 'usehooks-ts';

import { sanitizeUIMessages } from '@/lib/utils';

import { ArrowUpIcon, PaperclipIcon, StopIcon } from './icons';
import { PreviewAttachment } from './preview-attachment';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { SuggestedActions } from './suggested-actions';
import equal from 'fast-deep-equal';
import { cx } from 'class-variance-authority';
import Colors from '../theme/Colors';
import { useUsageMonitoring } from '@/hooks/use-usage-monitoring';
import { ChatUsageLimitModal } from '@/components/dashboard/usage/ChatUsageLimitModal';
import { Progress } from '@/components/ui/progress';
import { UsageStats } from '@/lib/services/usage-service';

function PureMultimodalInput({
  chatId,
  input,
  setInput,
  isLoading,
  stop,
  attachments,
  setAttachments,
  messages,
  setMessages,
  append,
  handleSubmit,
  className,
}: {
  chatId: string;
  input: string;
  setInput: (value: string) => void;
  isLoading: boolean;
  stop: () => void;
  attachments: Array<Attachment>;
  setAttachments: Dispatch<SetStateAction<Array<Attachment>>>;
  messages: Array<Message>;
  setMessages: Dispatch<SetStateAction<Array<Message>>>;
  append: (
    message: Message | CreateMessage,
    chatRequestOptions?: ChatRequestOptions
  ) => Promise<string | null | undefined>;
  handleSubmit: (
    event?: {
      preventDefault?: () => void;
    },
    chatRequestOptions?: ChatRequestOptions
  ) => void;
  className?: string;
}) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const { width } = useWindowSize();
  const { checkUsage } = useUsageMonitoring();
  const [showUsageLimitModal, setShowUsageLimitModal] =
    useState(false);
  const [usageLimitData, setUsageLimitData] = useState<{
    message: string;
    stats?: UsageStats;
  } | null>(null);

  const adjustHeight = useCallback(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight + 2, 200)}px`; // Max height limit for performance
    }
  }, []);

  useEffect(() => {
    if (textareaRef.current) {
      adjustHeight();
    }
  }, [adjustHeight]);

  const resetHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = '98px';
    }
  };

  const [localStorageInput, setLocalStorageInput] = useLocalStorage(
    'input',
    ''
  );

  useEffect(() => {
    if (textareaRef.current) {
      const domValue = textareaRef.current.value;
      // Prefer DOM value over localStorage to handle hydration
      const finalValue = domValue || localStorageInput || '';
      setInput(finalValue);
      adjustHeight();
    }
    // Only run once after hydration
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    setLocalStorageInput(input);
  }, [input, setLocalStorageInput]);

  // Listen for custom event to set input text from suggestion chips
  useEffect(() => {
    const handleSetInputText = (event: Event) => {
      const customEvent = event as CustomEvent<{ text: string }>;
      setInput(customEvent.detail.text);
      if (textareaRef.current) {
        textareaRef.current.focus();
        adjustHeight();
      }
    };

    window.addEventListener('set-input-text', handleSetInputText);

    return () => {
      window.removeEventListener(
        'set-input-text',
        handleSetInputText
      );
    };
  }, [setInput, adjustHeight]);

  const handleInput = (
    event: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    setInput(event.target.value);
    adjustHeight();
  };

  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadQueue, setUploadQueue] = useState<Array<string>>([]);
  // Track upload progress for each file
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});

  const submitForm = useCallback(async () => {
    // Check usage limits before submitting
    const usageResult = await checkUsage('chat');

    if (!usageResult.allowed && usageResult.message) {
      // Show usage limit modal instead of submitting
      setUsageLimitData({
        message: usageResult.message,
        stats: usageResult.stats,
      });
      setShowUsageLimitModal(true);
      return;
    }

    window.history.replaceState({}, '', `/chat/${chatId}`);

    handleSubmit(undefined, {
      experimental_attachments: attachments,
    });

    setAttachments([]);
    setLocalStorageInput('');
    resetHeight();

    if (width && width > 768) {
      textareaRef.current?.focus();
    }
  }, [
    attachments,
    handleSubmit,
    setAttachments,
    setLocalStorageInput,
    width,
    chatId,
    checkUsage,
  ]);

  const uploadFile = async (file: File) => {
    return new Promise((resolve, reject) => {
      const formData = new FormData();
      formData.append('file', file);
      const xhr = new XMLHttpRequest();
      xhr.open('POST', '/api/files/upload');
      xhr.upload.onprogress = (event) => {
        if (event.lengthComputable) {
          setUploadProgress((prev) => ({ ...prev, [file.name]: Math.round((event.loaded / event.total) * 100) }));
        }
      };
      xhr.onload = () => {
        setUploadProgress((prev) => ({ ...prev, [file.name]: 100 }));
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const data = JSON.parse(xhr.responseText);
            resolve({
              url: data.url,
              name: data.pathname,
              contentType: data.contentType,
            });
          } catch (e) {
            toast.error('Failed to parse upload response');
            reject(e);
          }
        } else {
          try {
            const { error } = JSON.parse(xhr.responseText);
            toast.error(error);
          } catch {
            toast.error('Upload failed');
          }
          reject(xhr.statusText);
        }
      };
      xhr.onerror = () => {
        toast.error('Failed to upload file, please try again!');
        reject(xhr.statusText);
      };
      xhr.send(formData);
    });
  };

  const handleFileChange = useCallback(
    async (event: ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(event.target.files || []);
      setUploadQueue(files.map((file) => file.name));
      try {
        const uploadPromises = files.map((file) => uploadFile(file));
        // Ensure uploadedAttachments is typed as (Attachment | undefined | null)[]
        const uploadedAttachments = await Promise.all(uploadPromises) as (Attachment | null | undefined)[];
        // Use a type predicate to filter only valid Attachment objects
        const successfullyUploadedAttachments =
          uploadedAttachments.filter((attachment): attachment is Attachment => Boolean(attachment));
        setAttachments((currentAttachments) => [
          ...currentAttachments,
          ...successfullyUploadedAttachments,
        ]);
      } catch {
        toast.error('Failed to upload file, please try again!');
      } finally {
        setUploadQueue([]);
        setUploadProgress({});
      }
    },
    [setAttachments]
  );

  return (
    <div className="relative w-full flex flex-col gap-2 max-w-full">
      {messages.length === 0 &&
        attachments.length === 0 &&
        uploadQueue.length === 0 && (
          <div className="mb-0">
            <SuggestedActions append={append} chatId={chatId} />
          </div>
        )}

      <input
        type="file"
        className="fixed -top-4 -left-4 size-0.5 opacity-0 pointer-events-none"
        ref={fileInputRef}
        multiple
        onChange={handleFileChange}
        tabIndex={-1}
      />

      {(attachments.length > 0 || uploadQueue.length > 0) && (
        <div className="flex flex-row gap-2 overflow-x-scroll items-end">
          {attachments.map((attachment) => (
            <PreviewAttachment
              key={attachment.url}
              attachment={attachment}
            />
          ))}

          {uploadQueue.map((filename) => (
            <div key={filename} className="flex flex-col items-center min-w-[100px]">
              <PreviewAttachment
                attachment={{ url: '', name: filename, contentType: '' }}
                isUploading={true}
              />
              <Progress value={uploadProgress[filename] || 0} className="w-20 mt-1" />
              <span className="text-xs text-muted-foreground">{uploadProgress[filename] || 0}%</span>
            </div>
          ))}
        </div>
      )}

      <div className="relative w-full">
        <Textarea
          ref={textareaRef}
          placeholder="Send a message to Billix Agent..."
          value={input}
          onChange={handleInput}
          className={cx(
            'min-h-[24px] max-h-[calc(30dvh)] w-full overflow-hidden resize-none rounded-2xl !text-base bg-white/95 dark:bg-[#22304d]/95 pb-12 border-blue-200/60 dark:border-[#0097B1]/40 focus:border-blue-500/60 dark:focus:border-[#0097B1]/80 focus:ring-2 focus:ring-blue-500/20 dark:focus:ring-[#0097B1]/20 shadow-lg hover:shadow-xl hover:shadow-blue-500/10 dark:hover:shadow-[#0097B1]/10 backdrop-blur-sm transition-all duration-300',
            className
          )}
          rows={2}
          autoFocus
          onKeyDown={async (event) => {
            if (event.key === 'Enter' && !event.shiftKey) {
              event.preventDefault();

              if (isLoading) {
                toast.error(
                  'Please wait for the model to finish its response!'
                );
              } else {
                await submitForm();
              }
            }
          }}
        />

        <div className="absolute bottom-0 left-0 right-0 h-12 bg-gradient-to-t from-white/95 dark:from-[#22304d]/95 to-transparent pointer-events-none rounded-b-2xl"></div>

        <div className="absolute bottom-0 p-2.5 w-fit flex flex-row justify-start">
          <AttachmentsButton
            fileInputRef={fileInputRef}
            isLoading={isLoading}
          />
        </div>

        <div className="absolute bottom-0 right-0 p-2.5 w-fit flex flex-row justify-end">
          {isLoading ? (
            <StopButton stop={stop} setMessages={setMessages} />
          ) : (
            <SendButton
              input={input}
              submitForm={submitForm}
              uploadQueue={uploadQueue}
            />
          )}
        </div>
      </div>

      {/* Usage Limit Modal */}
      {usageLimitData && (
        <ChatUsageLimitModal
          isOpen={showUsageLimitModal}
          onClose={() => {
            setShowUsageLimitModal(false);
            setUsageLimitData(null);
          }}
          feature="chat"
          message={usageLimitData.message}
          stats={usageLimitData.stats}
        />
      )}
    </div>
  );
}

export const MultimodalInput = memo(
  PureMultimodalInput,
  (prevProps, nextProps) => {
    if (prevProps.input !== nextProps.input) return false;
    if (prevProps.isLoading !== nextProps.isLoading) return false;
    if (!equal(prevProps.attachments, nextProps.attachments))
      return false;

    return true;
  }
);

function PureAttachmentsButton({
  fileInputRef,
  isLoading,
}: {
  fileInputRef: React.RefObject<HTMLInputElement | null>;
  isLoading: boolean;
}) {
  return (
    <Button
      className="rounded-full p-2.5 h-fit border-blue-200/50 dark:border-[#0097B1]/40 hover:border-blue-500/60 dark:hover:border-[#0097B1]/80 hover:bg-blue-50 dark:hover:bg-[#0097B1]/10 text-blue-600 dark:text-[#0097B1] hover:text-blue-700 dark:hover:text-[#0097B1] transition-all duration-300 shadow-sm hover:shadow-md"
      onClick={(event) => {
        event.preventDefault();
        fileInputRef.current?.click();
      }}
      disabled={isLoading}
      variant="ghost"
    >
      <PaperclipIcon size={16} />
    </Button>
  );
}

const AttachmentsButton = memo(PureAttachmentsButton);

function PureStopButton({
  stop,
  setMessages,
}: {
  stop: () => void;
  setMessages: Dispatch<SetStateAction<Array<Message>>>;
}) {
  return (
    <Button
      className="rounded-full p-2.5 h-fit border border-red-500/60 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30 hover:border-red-500/80 transition-all duration-300 shadow-sm hover:shadow-md hover:shadow-red-500/20"
      onClick={(event) => {
        event.preventDefault();
        stop();
        setMessages((messages) => sanitizeUIMessages(messages));
      }}
    >
      <StopIcon size={16} />
    </Button>
  );
}

const StopButton = memo(PureStopButton);

function PureSendButton({
  submitForm,
  input,
  uploadQueue,
}: {
  submitForm: () => Promise<void>;
  input: string;
  uploadQueue: Array<string>;
}) {
  return (
    <Button
      className="rounded-full p-2.5 h-fit border-blue-500/50 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:from-gray-400 disabled:to-gray-500 text-white shadow-lg hover:shadow-xl hover:shadow-blue-500/25 transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100"
      style={{
        background: input.length > 0 && uploadQueue.length === 0 
          ? `linear-gradient(135deg, ${Colors.gradients.blueToPurple[0]}, ${Colors.gradients.blueToPurple[1]})`
          : undefined
      }}
      onClick={async (event) => {
        event.preventDefault();
        await submitForm();
      }}
      disabled={input.length === 0 || uploadQueue.length > 0}
    >
      <ArrowUpIcon size={16} />
    </Button>
  );
}

const SendButton = memo(PureSendButton, (prevProps, nextProps) => {
  if (prevProps.uploadQueue.length !== nextProps.uploadQueue.length)
    return false;
  if (prevProps.input !== nextProps.input) return false;
  return true;
});
