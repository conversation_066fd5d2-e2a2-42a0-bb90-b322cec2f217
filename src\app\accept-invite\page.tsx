import React from 'react';
import { acceptInvite } from '@/lib/actions/team';
import { redirect } from 'next/navigation';
import { currentUser } from '@clerk/nextjs/server';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, XCircle } from 'lucide-react';
import Link from 'next/link';

type SearchParams = { token?: string };

export default async function AcceptInvitePage(
  props: {
    params?: Promise<Record<string, string>>;
    searchParams?: Promise<SearchParams>;
  }
) {
  const searchParams = await props.searchParams;
  // Check if token is provided
  const token = searchParams?.token;
  if (!token) {
    return (
      <InviteError message="Invalid invitation link. Please check the URL and try again." />
    );
  }

  // Check if user is signed in
  const user = await currentUser();
  if (!user) {
    // Redirect to sign in with a return URL
    redirect(`/sign-in?redirect_url=${encodeURIComponent(`/accept-invite?token=${token}`)}`);
  }

  // Process the invitation
  const result = await acceptInvite(token);

  if (result.error) {
    return <InviteError message={result.error} />;
  }

  return (
    <div className="flex min-h-screen items-center justify-center p-4 bg-muted/30">
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <CheckCircle className="h-12 w-12 text-green-500" />
          </div>
          <CardTitle className="text-xl">Invitation Accepted!</CardTitle>
          <CardDescription>
            You&apos;ve successfully joined {result.organizationName} as a {result.role?.toLowerCase()}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-center text-muted-foreground mb-4">
            You now have access to the organization&apos;s dashboard and resources based on your role permissions.
          </p>
        </CardContent>
        <CardFooter className="flex justify-center">
          <Button asChild>
            <Link href="/dashboard">
              Go to Dashboard
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}

function InviteError({ message }: { message: string }) {
  return (
    <div className="flex min-h-screen items-center justify-center p-4 bg-muted/30">
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <XCircle className="h-12 w-12 text-red-500" />
          </div>
          <CardTitle className="text-xl">Invalid Invitation</CardTitle>
          <CardDescription>
            {message}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-center text-muted-foreground mb-4">
            If you were expecting an invitation, please ask the organization admin to send a new one.
          </p>
        </CardContent>
        <CardFooter className="flex justify-center">
          <Button asChild variant="outline">
            <Link href="/">
              Return to Home
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
} 