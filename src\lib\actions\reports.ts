"use server";
import { auth } from "@clerk/nextjs/server";
// Use string type for report types to avoid Prisma enum issues
import { revalidatePath } from "next/cache";
import { uploadToVercelBlob } from "@/lib/blob";
import { generateReportPDF, generateReportExcel } from "@/lib/generators/report-generator";
import db from "@/db/db";
import { InputJsonValue } from "@prisma/client/runtime/library";
import { ReportData, ReportType } from "@prisma/client";



// Types
type DateRange = {
  startDate: string;
  endDate: string;
};

type JsonObject = Record<string, unknown>;

type CreateReportParams = {
  name: string;
  description?: string;
  type: string; // Use string instead of enum
  dateRange: DateRange;
  filters?: JsonObject;
  visualizationType: string;
  isTemplate?: boolean;
  templateId?: string;
  organizationId?: string;
};

type UpdateReportParams = {
  id: string;
  name?: string;
  title?: string; // New field name for name
  description?: string;
  type?: string; // Old field name for reportType
  reportType?: string; // New field name for type
  dateRange?: DateRange;
  filters?: JsonObject;
  visualizationType?: string;
};

// Define interfaces for type safety
interface InvoiceData {
  id: string;
  amount?: number | null;
  issueDate?: Date | null;
  createdAt?: Date | null;
  invoiceNumber?: string | null;
  category?: { name?: string | null } | null;
  vendor?: { name?: string | null } | null;
  lineItems?: unknown[];
}

// Define interface for report data items
interface ReportDataItem extends Omit<ReportData, 'id' | 'createdAt' | 'reportId' | 'invoiceId'> {
  id?: string;
  reportId?: string;
  invoiceId?: string;
}

// ReportWithData interface removed - using new unified generator format

// For the generateReportData function
interface ReportInfo {
  id: string;
  filters?: JsonObject;
  type: string;
}

interface GeneratedReportDataItem {
  reportId: string;
  dataPoint: string;
  value: number;
  label: string;
  category: string;
  invoiceId?: string;
}

export async function createReport(params: CreateReportParams) {
  const { userId: clerkUserId } = await auth();

  if (!clerkUserId) {
    throw new Error("Unauthorized");
  }

  try {
    // Get database user ID from Clerk ID
    const dbUser = await db.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true }
    });

    if (!dbUser) {
      throw new Error("User not found in database");
    }

    // Store visualization type in fileUrl field as a workaround
    const visualizationTypeStr = params.visualizationType ?
      `visualization:${params.visualizationType}` :
      'visualization:bar';

    const report = await db.report.create({
      data: {
        title: params.name, // Map name to title field in Prisma schema
        reportType: params.type, // Map type to reportType field in Prisma schema
        description: params.description,
        // Store dateRange as startDate and endDate
        startDate: params.dateRange ? new Date(params.dateRange.startDate) : null,
        endDate: params.dateRange ? new Date(params.dateRange.endDate) : null,
        // Store visualization type in fileUrl as a workaround
        fileUrl: visualizationTypeStr,
        format: "PDF", // Default format
        isTemplate: params.isTemplate || false,
        templateId: params.templateId,
        userId: dbUser.id, // Use the database user ID
        organizationId: params.organizationId,
      },
    });

    revalidatePath("/dashboard/reports");
    return report;
  } catch {
    throw new Error("Failed to create report");
  }
}

export async function getReports(filters?: {
  type?: ReportType;
  isTemplate?: boolean;
}) {
  const { userId: clerkUserId } = await auth();

  if (!clerkUserId) {
    throw new Error("Unauthorized");
  }

  try {
    // Get database user ID from Clerk ID
    const dbUser = await db.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true }
    });

    if (!dbUser) {
      throw new Error("User not found in database");
    }

    const reports = await db.report.findMany({
      where: {
        userId: dbUser.id,
        ...filters,
      },
      include: {
        data: true,
        scheduledReports: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Process reports for backward compatibility
    const processedReports = reports.map(report => ({
      ...report,
      // Add name field for backward compatibility
      name: report.title || "",
      // Add type field for backward compatibility
      type: report.reportType || "",
      // Extract visualization type from fileUrl
      visualizationType: report.fileUrl?.startsWith('visualization:')
        ? report.fileUrl.split(':')[1]
        : "bar",
      // Create dateRange object from startDate and endDate
      dateRange: {
        startDate: report.startDate?.toISOString() || new Date().toISOString(),
        endDate: report.endDate?.toISOString() || new Date().toISOString(),
      },
    }));

    return processedReports;
  } catch {
    throw new Error("Failed to fetch reports");
  }
}

export async function getReportById(id: string) {
  const { userId: clerkUserId } = await auth();

  if (!clerkUserId) {
    throw new Error("Unauthorized");
  }

  try {
    // Get database user ID from Clerk ID
    const dbUser = await db.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true }
    });

    if (!dbUser) {
      throw new Error("User not found in database");
    }

    const report = await db.report.findUnique({
      where: {
        id,
        userId: dbUser.id,
      },
      include: {
        data: true,
        template: true,
        scheduledReports: true,
      },
    });

    if (!report) {
      throw new Error("Report not found");
    }

    // Process the report to add backward compatibility fields
    const processedReport = {
      ...report,
      // Add name field for backward compatibility
      name: report.title || "",
      // Add type field for backward compatibility
      type: report.reportType || "",
      // Extract visualization type from fileUrl
      visualizationType: report.fileUrl?.startsWith('visualization:')
        ? report.fileUrl.split(':')[1]
        : "bar",
      // Create dateRange object from startDate and endDate
      dateRange: {
        startDate: report.startDate?.toISOString() || new Date().toISOString(),
        endDate: report.endDate?.toISOString() || new Date().toISOString(),
      },
    };

    return processedReport;
  } catch {
    throw new Error("Failed to fetch report");
  }
}

export async function updateReport(params: UpdateReportParams) {
  const { userId: clerkUserId } = await auth();

  if (!clerkUserId) {
    throw new Error("Unauthorized");
  }

  try {
    // Get database user ID from Clerk ID
    const dbUser = await db.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true }
    });

    if (!dbUser) {
      throw new Error("User not found in database");
    }

    // Store visualization type in fileUrl field as a workaround if provided
    const updateData: Partial<Record<string, unknown>> = {};

    // Handle both old and new field names
    if (params.name || params.title) {
      updateData.title = params.title || params.name; // Map name/title to title field
    }

    if (params.description) {
      updateData.description = params.description;
    }

    if (params.type || params.reportType) {
      updateData.reportType = params.reportType || params.type; // Map type/reportType to reportType field
    }

    // Update startDate and endDate if dateRange is provided
    if (params.dateRange) {
      updateData.startDate = new Date(params.dateRange.startDate);
      updateData.endDate = new Date(params.dateRange.endDate);
    }

    // Update visualization type if provided
    if (params.visualizationType) {
      updateData.fileUrl = `visualization:${params.visualizationType}`;
    }

    const report = await db.report.update({
      where: {
        id: params.id,
        userId: dbUser.id,
      },
      data: updateData,
    });

    revalidatePath("/dashboard/reports");
    return report;
  } catch {
    throw new Error("Failed to update report");
  }
}

export async function deleteReport(id: string) {
  const { userId: clerkUserId } = await auth();

  if (!clerkUserId) {
    throw new Error("Unauthorized");
  }

  try {
    // Get database user ID from Clerk ID
    const dbUser = await db.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true }
    });

    if (!dbUser) {
      throw new Error("User not found in database");
    }

    // Delete related data first
    await db.reportData.deleteMany({
      where: {
        reportId: id,
      },
    });

    // Delete scheduled reports
    await db.scheduledReport.deleteMany({
      where: {
        reportId: id,
      },
    });

    // Delete the report itself
    await db.report.delete({
      where: {
        id,
        userId: dbUser.id,
      },
    });

    revalidatePath("/dashboard/reports");
    return { success: true };
  } catch {
    throw new Error("Failed to delete report");
  }
}

export async function generateReportData(reportId: string) {
  const { userId: clerkUserId } = await auth();

  if (!clerkUserId) {
    throw new Error("Unauthorized");
  }

  try {
    // Get database user ID from Clerk ID
    const dbUser = await db.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true }
    });

    if (!dbUser) {
      throw new Error("User not found in database");
    }

    // Get the report
    const report = await db.report.findUnique({
      where: {
        id: reportId,
        userId: dbUser.id,
      },
    });

    if (!report) {
      throw new Error("Report not found");
    }

    // Get date range from report
    let startDate = report.startDate;
    let endDate = report.endDate;

    // If startDate or endDate is null, try to get from filters
    if (!startDate || !endDate) {
      const filtersObj = (report as Partial<{ filters: Record<string, unknown> }>).filters || {};
      const dateRange = filtersObj.dateRange as DateRange;
      if (dateRange) {
        startDate = startDate || new Date(dateRange.startDate);
        endDate = endDate || new Date(dateRange.endDate);
      }
    }

    // Fallback to default date range if still null
    if (!startDate) {
      const now = new Date();
      startDate = new Date(now.getFullYear(), now.getMonth(), 1); // First day of current month
    }

    if (!endDate) {
      endDate = new Date(); // Today
    }

    // Create database filters object
    const databaseFilters = {};

    // Note: reportMetadata contains custom report settings like selectedFields, sorting, etc.
    // These are not used directly in the database query but can be used for custom data handling

    // Get invoices based on report type and date range
    const invoices = await db.invoice.findMany({
      where: {
        userId: dbUser.id,
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
        ...databaseFilters,
      },
      include: {
        lineItems: true,
        category: true,
        vendor: true,
      },
    });

    // Clear existing report data
    await db.reportData.deleteMany({
      where: {
        reportId,
      },
    });

    // Generate data based on report type
    let reportData: ReportDataItem[] = [];

    switch (report.reportType) {
      case "EXPENSES":
        reportData = generateExpensesData(invoices as InvoiceData[], { id: report.id, type: report.reportType });
        break;
      case "VENDOR_ANALYSIS":
        reportData = generateVendorAnalysisData(invoices as InvoiceData[], { id: report.id, type: report.reportType });
        break;
      case "CATEGORY_ANALYSIS":
        reportData = generateCategoryAnalysisData(invoices as InvoiceData[], { id: report.id, type: report.reportType });
        break;
      case "CASH_FLOW":
        reportData = generateCashFlowData(invoices as InvoiceData[], { id: report.id, type: report.reportType });
        break;
      default:
        reportData = generateDefaultData(invoices as InvoiceData[], { id: report.id, type: report.reportType });
    }

    // Save the generated data
    for (const data of reportData) {
      await db.reportData.create({
        data: {
          reportId,
          invoiceId: data.invoiceId,
          dataPoint: data.dataPoint,
          value: data.value,
          label: data.label,
          category: data.category,
        },
      });
    }

    revalidatePath("/dashboard/reports");
    return { success: true, dataCount: reportData.length };
  } catch {
    throw new Error("Failed to generate report data");
  }
}

// Helper functions for data generation based on report type
function generateExpensesData(invoices: InvoiceData[], report: ReportInfo): GeneratedReportDataItem[] {
  const basicExpenseData = invoices.map(invoice => ({
    reportId: report.id,
    invoiceId: invoice.id,
    dataPoint: "expense",
    value: invoice.amount || 0,
    label: invoice.createdAt ? new Date(invoice.createdAt).toLocaleDateString() : '',
    category: invoice.category?.name || "Uncategorized",
  }));

  // Group by date for time-series analysis
  const dateGroups: Record<string, { total: number; count: number; date: Date }> = {};

  invoices.forEach(invoice => {
    if (!invoice.createdAt) return;

    const date = new Date(invoice.createdAt);
    const dateKey = date.toISOString().split('T')[0];

    if (!dateGroups[dateKey]) {
      dateGroups[dateKey] = { total: 0, count: 0, date };
    }

    dateGroups[dateKey].total += invoice.amount || 0;
    dateGroups[dateKey].count += 1;
  });

  const sortedDates = Object.entries(dateGroups)
    .sort(([dateA], [dateB]) => new Date(dateA).getTime() - new Date(dateB).getTime());

  // Create time-series data points
  const timeSeriesData = sortedDates.map(([dateKey, data]) => ({
    reportId: report.id,
    dataPoint: "daily_expense",
    value: data.total,
    label: new Date(dateKey).toLocaleDateString(),
    category: "Daily Expenses",
  }));

  const totalExpense = invoices.reduce((sum, invoice) => sum + (invoice.amount || 0), 0);
  const averageExpense = invoices.length > 0 ? totalExpense / invoices.length : 0;

  const averageData = {
    reportId: report.id,
    dataPoint: "average_expense",
    value: averageExpense,
    label: "Average Expense",
    category: "Metrics",
  };

  return [...basicExpenseData, ...timeSeriesData, averageData];
}

function generateVendorAnalysisData(invoices: InvoiceData[], report: ReportInfo): GeneratedReportDataItem[] {
  // Group by vendor with enhanced metrics
  const vendorGroups: Record<string, {
    total: number;
    count: number;
    average: number;
    maxInvoice: number;
    minInvoice: number;
    lastInvoiceDate: Date | null;
  }> = invoices.reduce((acc, invoice) => {
    const vendorName = invoice.vendor?.name || "Unknown Vendor";
    const amount = invoice.amount || 0;

    if (!acc[vendorName]) {
      acc[vendorName] = {
        total: 0,
        count: 0,
        average: 0,
        maxInvoice: 0,
        minInvoice: Number.MAX_VALUE,
        lastInvoiceDate: null
      };
    }

    acc[vendorName].total += amount;
    acc[vendorName].count += 1;
    acc[vendorName].average = acc[vendorName].total / acc[vendorName].count;
    acc[vendorName].maxInvoice = Math.max(acc[vendorName].maxInvoice, amount);
    acc[vendorName].minInvoice = amount > 0 ? Math.min(acc[vendorName].minInvoice, amount) : acc[vendorName].minInvoice;

    if (invoice.createdAt) {
      const invoiceDate = new Date(invoice.createdAt);
      if (!acc[vendorName].lastInvoiceDate ||
          invoiceDate > acc[vendorName].lastInvoiceDate) {
        acc[vendorName].lastInvoiceDate = invoiceDate;
      }
    }

    return acc;
  }, {} as Record<string, {
    total: number;
    count: number;
    average: number;
    maxInvoice: number;
    minInvoice: number;
    lastInvoiceDate: Date | null;
  }>);

  const sortedVendors = Object.entries(vendorGroups)
    .sort(([, dataA], [, dataB]) => dataB.total - dataA.total);

  // Generate comprehensive vendor data
  const result: GeneratedReportDataItem[] = [];

  // Total spend by vendor
  sortedVendors.forEach(([vendor, data]) => {
    result.push({
      reportId: report.id,
      dataPoint: "vendor_total",
      value: data.total,
      label: vendor,
      category: "Vendor Spend",
    });
  });

  // Invoice count by vendor
  sortedVendors.forEach(([vendor, data]) => {
    result.push({
      reportId: report.id,
      dataPoint: "vendor_count",
      value: data.count,
      label: vendor,
      category: "Invoice Count",
    });
  });

  // Average invoice amount by vendor
  sortedVendors.forEach(([vendor, data]) => {
    result.push({
      reportId: report.id,
      dataPoint: "vendor_average",
      value: data.average,
      label: vendor,
      category: "Average Invoice",
    });
  });

  sortedVendors.slice(0, 5).forEach(([vendor, data], index) => {
    result.push({
      reportId: report.id,
      dataPoint: "top_vendor",
      value: data.total,
      label: `#${index + 1}: ${vendor}`,
      category: "Top Vendors",
    });
  });

  return result;
}

function generateCategoryAnalysisData(invoices: InvoiceData[], report: ReportInfo): GeneratedReportDataItem[] {
  // Group by category with enhanced metrics
  const categoryGroups: Record<string, {
    total: number;
    count: number;
    average: number;
    percentage: number;
  }> = invoices.reduce((acc, invoice) => {
    const categoryName = invoice.category?.name || "Uncategorized";
    const amount = invoice.amount || 0;

    if (!acc[categoryName]) {
      acc[categoryName] = {
        total: 0,
        count: 0,
        average: 0,
        percentage: 0
      };
    }

    acc[categoryName].total += amount;
    acc[categoryName].count += 1;
    acc[categoryName].average = acc[categoryName].total / acc[categoryName].count;

    return acc;
  }, {} as Record<string, {
    total: number;
    count: number;
    average: number;
    percentage: number;
  }>);

  const totalSpend = Object.values(categoryGroups).reduce((sum, data) => sum + data.total, 0);

  Object.values(categoryGroups).forEach(data => {
    data.percentage = totalSpend > 0 ? (data.total / totalSpend) * 100 : 0;
  });

  const sortedCategories = Object.entries(categoryGroups)
    .sort(([, dataA], [, dataB]) => dataB.total - dataA.total);

  // Generate comprehensive category data
  const result: GeneratedReportDataItem[] = [];

  // Total spend by category
  sortedCategories.forEach(([category, data]) => {
    result.push({
      reportId: report.id,
      dataPoint: "category_total",
      value: data.total,
      label: category,
      category: "Category Spend",
    });
  });

  // Percentage of total spend by category
  sortedCategories.forEach(([category, data]) => {
    result.push({
      reportId: report.id,
      dataPoint: "category_percentage",
      value: data.percentage,
      label: category,
      category: "Spend Percentage",
    });
  });

  // Invoice count by category
  sortedCategories.forEach(([category, data]) => {
    result.push({
      reportId: report.id,
      dataPoint: "category_count",
      value: data.count,
      label: category,
      category: "Invoice Count",
    });
  });

  // Average invoice amount by category
  sortedCategories.forEach(([category, data]) => {
    result.push({
      reportId: report.id,
      dataPoint: "category_average",
      value: data.average,
      label: category,
      category: "Average Invoice",
    });
  });

  sortedCategories.slice(0, 5).forEach(([category, data], index) => {
    result.push({
      reportId: report.id,
      dataPoint: "top_category",
      value: data.total,
      label: `#${index + 1}: ${category}`,
      category: "Top Categories",
    });
  });

  return result;
}

function generateCashFlowData(invoices: InvoiceData[], report: ReportInfo): GeneratedReportDataItem[] {
  // Group by month with enhanced metrics
  const monthlyData: Record<string, {
    total: number;
    count: number;
    label: string;
    month: number;
    year: number;
  }> = invoices.reduce((acc, invoice) => {
    if (!invoice.createdAt) return acc;

    const date = new Date(invoice.createdAt);
    const month = date.getMonth() + 1;
    const year = date.getFullYear();
    const monthKey = `${year}-${month.toString().padStart(2, '0')}`;

    if (!acc[monthKey]) {
      acc[monthKey] = {
        total: 0,
        count: 0,
        label: date.toLocaleDateString('default', { month: 'short', year: 'numeric' }),
        month,
        year
      };
    }

    acc[monthKey].total += invoice.amount || 0;
    acc[monthKey].count += 1;
    return acc;
  }, {} as Record<string, {
    total: number;
    count: number;
    label: string;
    month: number;
    year: number;
  }>);

  const sortedMonths = Object.entries(monthlyData)
    .sort(([keyA], [keyB]) => keyA.localeCompare(keyB));

  // Generate comprehensive cash flow data
  const result: GeneratedReportDataItem[] = [];

  sortedMonths.forEach(([, data]) => {
    result.push({
      reportId: report.id,
      dataPoint: "monthly_cashflow",
      value: data.total,
      label: data.label,
      category: "Monthly Expenses",
    });
  });

  let runningTotal = 0;
  sortedMonths.forEach(([, data]) => {
    runningTotal += data.total;
    result.push({
      reportId: report.id,
      dataPoint: "cumulative_cashflow",
      value: runningTotal,
      label: data.label,
      category: "Cumulative Expenses",
    });
  });

  for (let i = 1; i < sortedMonths.length; i++) {
    const [, currentData] = sortedMonths[i];
    const [, previousData] = sortedMonths[i - 1];

    const change = currentData.total - previousData.total;
    const percentChange = previousData.total !== 0
      ? (change / previousData.total) * 100
      : 0;

    result.push({
      reportId: report.id,
      dataPoint: "monthly_change",
      value: change,
      label: currentData.label,
      category: "Monthly Change",
    });

    result.push({
      reportId: report.id,
      dataPoint: "monthly_change_percent",
      value: percentChange,
      label: currentData.label,
      category: "Monthly Change (%)",
    });
  }

  const quarterlyData: Record<string, { total: number; label: string }> = {};

  sortedMonths.forEach(([, data]) => {
    const quarter = Math.ceil(data.month / 3);
    const quarterKey = `${data.year}-Q${quarter}`;

    if (!quarterlyData[quarterKey]) {
      quarterlyData[quarterKey] = {
        total: 0,
        label: `Q${quarter} ${data.year}`
      };
    }

    quarterlyData[quarterKey].total += data.total;
  });

  Object.entries(quarterlyData).forEach(([, data]) => {
    result.push({
      reportId: report.id,
      dataPoint: "quarterly_cashflow",
      value: data.total,
      label: data.label,
      category: "Quarterly Expenses",
    });
  });

  return result;
}

function generateDefaultData(invoices: InvoiceData[], report: ReportInfo): GeneratedReportDataItem[] {
  // Basic invoice data
  const basicData = invoices.map(invoice => ({
    reportId: report.id,
    invoiceId: invoice.id,
    dataPoint: "amount",
    value: invoice.amount || 0,
    label: invoice.invoiceNumber || invoice.id,
    category: invoice.category?.name || "Uncategorized",
  }));

  const totalAmount = invoices.reduce((sum, invoice) => sum + (invoice.amount || 0), 0);
  const averageAmount = invoices.length > 0 ? totalAmount / invoices.length : 0;
  const maxAmount = invoices.reduce((max, invoice) => Math.max(max, invoice.amount || 0), 0);

  // Add summary data points
  const summaryData = [
    {
      reportId: report.id,
      dataPoint: "total_amount",
      value: totalAmount,
      label: "Total Amount",
      category: "Summary",
    },
    {
      reportId: report.id,
      dataPoint: "average_amount",
      value: averageAmount,
      label: "Average Amount",
      category: "Summary",
    },
    {
      reportId: report.id,
      dataPoint: "invoice_count",
      value: invoices.length,
      label: "Invoice Count",
      category: "Summary",
    },
    {
      reportId: report.id,
      dataPoint: "max_amount",
      value: maxAmount,
      label: "Maximum Amount",
      category: "Summary",
    }
  ];

  return [...basicData, ...summaryData];
}

// Report Templates
export async function getReportTemplates() {
  const { userId: clerkUserId } = await auth();

  if (!clerkUserId) {
    throw new Error("Unauthorized");
  }

  try {
    // Get database user ID from Clerk ID
    const dbUser = await db.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true }
    });

    if (!dbUser) {
      throw new Error("User not found in database");
    }

    const templates = await db.reportTemplate.findMany({
      where: {
        userId: dbUser.id,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return templates;
  } catch {
    throw new Error("Failed to fetch report templates");
  }
}

export async function createReportTemplate(params: {
  name: string;
  description?: string;
  type: ReportType;
  filters?: JsonObject;
  visualizationType: string;
  isAIGenerated?: boolean;
  organizationId?: string;
}) {
  const { userId: clerkUserId } = await auth();

  if (!clerkUserId) {
    throw new Error("Unauthorized");
  }

  try {
    // Get database user ID from Clerk ID
    const dbUser = await db.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true }
    });

    if (!dbUser) {
      throw new Error("User not found in database");
    }

    const template = await db.reportTemplate.create({
      data: {
        name: params.name,
        description: params.description,
        reportType: params.type, // Map type to reportType field
        visualizationType: params.visualizationType,
        isAIGenerated: params.isAIGenerated || false,
        userId: dbUser.id,
        organizationId: params.organizationId,
      },
    });

    revalidatePath("/dashboard/reports");
    return template;
  } catch {
    throw new Error("Failed to create report template");
  }
}

// Create default templates if they don't exist
async function ensureDefaultTemplatesExist(userId: string) {
  // Check if default templates already exist for this user
  const existingTemplates = await db.reportTemplate.findMany({
    where: {
      userId,
      id: {
        in: ['default-cashflow', 'default-expenses', 'default-vendor', 'default-category']
      }
    }
  });

  const existingIds = existingTemplates.map(t => t.id);

  // Define default templates
  const defaultTemplates = [
    {
      id: 'default-cashflow',
      name: 'Cash Flow Analysis',
      description: 'Track cash flow over time',
      type: 'CASH_FLOW' as ReportType,
      visualizationType: 'line',
      filters: {}
    },
    {
      id: 'default-expenses',
      name: 'Expense Report',
      description: 'Overview of all expenses',
      type: 'EXPENSES' as ReportType,
      visualizationType: 'bar',
      filters: {}
    },
    {
      id: 'default-vendor',
      name: 'Vendor Analysis',
      description: 'Analyze spending by vendor',
      type: 'VENDOR_ANALYSIS' as ReportType,
      visualizationType: 'pie',
      filters: {}
    },
    {
      id: 'default-category',
      name: 'Category Analysis',
      description: 'Analyze spending by category',
      type: 'CATEGORY_ANALYSIS' as ReportType,
      visualizationType: 'doughnut',
      filters: {}
    }
  ];

  // Create missing templates
  for (const template of defaultTemplates) {
    if (!existingIds.includes(template.id)) {
      await db.reportTemplate.create({
        data: {
          id: template.id,
          name: template.name,
          description: template.description,
          reportType: template.type,
          visualizationType: template.visualizationType,
          filters: template.filters as InputJsonValue,
          userId
        }
      });
    }
  }
}

export async function applyTemplate(templateId: string, params: {
  name: string;
  description?: string;
  dateRange: DateRange;
  organizationId?: string;
}) {
  const { userId: clerkUserId } = await auth();

  if (!clerkUserId) {
    throw new Error("Unauthorized");
  }

  try {
    // Get database user ID from Clerk ID
    const dbUser = await db.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true }
    });

    if (!dbUser) {
      throw new Error("User not found in database");
    }

    // Check if this is a default template ID
    const isDefaultTemplate = templateId.startsWith('default-');

    // If it's a default template, ensure defaults exist
    if (isDefaultTemplate) {
      await ensureDefaultTemplatesExist(dbUser.id);
    }

    // Try to find the template with expanded search criteria
    // First check if it's the user's own template, then look for shared/system templates
    let template = await db.reportTemplate.findUnique({
      where: {
        id: templateId,
        userId: dbUser.id,
      },
    });

    // If not found as a user template, check if it's a shared template
    if (!template) {
      template = await db.reportTemplate.findUnique({
        where: {
          id: templateId,
        },
      });
    }

    if (!template) {
      throw new Error("Template not found. Please verify the template ID is correct.");
    }

    // Store visualization type in fileUrl field as a workaround
    const visualizationTypeStr = template.visualizationType ?
      `visualization:${template.visualizationType}` :
      'visualization:bar';

    // Create a report based on the template
    const report = await db.report.create({
      data: {
        title: params.name, // Map name to title field
        description: params.description,
        reportType: template.reportType, // Map type to reportType field
        // Store dateRange as startDate and endDate
        startDate: params.dateRange ? new Date(params.dateRange.startDate) : null,
        endDate: params.dateRange ? new Date(params.dateRange.endDate) : null,
        // Store visualization type in fileUrl as a workaround
        fileUrl: visualizationTypeStr,
        format: "PDF", // Default format
        isTemplate: false,
        templateId: template.id,
        userId: dbUser.id,
        organizationId: params.organizationId,
      },
    });

    // Generate data for the report
    await generateReportData(report.id);

    revalidatePath("/dashboard/reports");
    return report;
  } catch {
    throw new Error("Failed to use template");
  }
}

// Wrapper function for applyTemplate that isn't a hook (to avoid linting errors)
export async function createReportFromTemplate(templateId: string, params: {
  name: string;
  description?: string;
  dateRange: DateRange;
  organizationId?: string;
}) {
  return applyTemplate(templateId, params);
}

// Report Export
export async function exportReport(reportId: string, format: 'pdf' | 'excel') {
  const { userId: clerkUserId } = await auth();

  if (!clerkUserId) {
    throw new Error("Unauthorized");
  }

  try {
    // Get database user ID from Clerk ID
    const dbUser = await db.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true }
    });

    if (!dbUser) {
      throw new Error("User not found in database");
    }

    // Get the report with data
    const report = await db.report.findUnique({
      where: {
        id: reportId,
        userId: dbUser.id,
      },
      include: {
        data: true,
      },
    });

    if (!report) {
      throw new Error("Report not found");
    }

    let fileBuffer: Buffer;
    let contentType: string;
    let fileExtension: string;

    const adaptedReport = {
      id: report.id,
      title: report.title || '',
      description: report.description || undefined,
      reportType: report.reportType || '',
      startDate: report.startDate,
      endDate: report.endDate,
      createdAt: report.createdAt,
      data: report.data.map(item => ({
        dataPoint: item.dataPoint || '',
        value: item.value || 0,
        label: item.label || '',
        category: item.category || '',
        invoiceId: item.invoiceId,
      }))
    };

    // Generate the report file
    if (format === 'pdf') {
      // Use the new unified report generator
      fileBuffer = await generateReportPDF(adaptedReport);
      contentType = 'application/pdf';
      fileExtension = 'pdf';
    } else {
      // Use the new unified report generator
      fileBuffer = await generateReportExcel(adaptedReport);
      contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      fileExtension = 'xlsx';
    }

    // Upload to Vercel Blob
    const fileName = `report_${report.id}_${Date.now()}.${fileExtension}`;
    const fileUrl = await uploadToVercelBlob(fileName, fileBuffer, contentType);

    // Create export record
    const exportRecord = await db.exportHistory.create({
      data: {
        exportId: `export_${Date.now()}`,
        fileName,
        fileUrl,
        format: fileExtension.toUpperCase(),
        count: report.data.length,
        reportId: report.id,
        userId: dbUser.id,
        organizationId: report.organizationId,
      },
    });

    return {
      success: true,
      fileUrl,
      exportId: exportRecord.id,
    };
  } catch (error) {
    console.error("Export report error:", error);
    throw new Error("Failed to export report");
  }
}

// Scheduled Reports
export async function scheduleReport(params: {
  reportId: string;
  frequency: string;
  nextRunDate: Date;
  emailRecipients: string[];
  organizationId?: string;
}) {
  const { userId: clerkUserId } = await auth();

  if (!clerkUserId) {
    throw new Error("Unauthorized");
  }

  try {
    // Get database user ID from Clerk ID
    const dbUser = await db.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true }
    });

    if (!dbUser) {
      throw new Error("User not found in database");
    }

    // Calculate the next run time based on the frequency and provided date
    let nextRunTime: Date;

    if (params.nextRunDate) {
      // Use the provided date as the base
      nextRunTime = new Date(params.nextRunDate);

      // Ensure the next run time is in the future
      const now = new Date();
      if (nextRunTime <= now) {
        // If the next run time is in the past, calculate a new one based on frequency
        switch (params.frequency) {
          case 'daily':
            // Set to tomorrow at the same time
            nextRunTime = new Date(now);
            nextRunTime.setDate(nextRunTime.getDate() + 1);
            break;
          case 'weekly':
            // Set to next week at the same time
            nextRunTime = new Date(now);
            nextRunTime.setDate(nextRunTime.getDate() + 7);
            break;
          case 'monthly':
            // Set to next month at the same time
            nextRunTime = new Date(now);
            nextRunTime.setMonth(nextRunTime.getMonth() + 1);
            break;
          default:
            // Default to tomorrow
            nextRunTime = new Date(now);
            nextRunTime.setDate(nextRunTime.getDate() + 1);
        }
      }
    } else {
      // Default to calculating based on frequency if no date is provided
      const now = new Date();

      switch (params.frequency) {
        case 'daily':
          // Set to tomorrow at the same time
          nextRunTime = new Date(now);
          nextRunTime.setDate(nextRunTime.getDate() + 1);
          break;
        case 'weekly':
          // Set to next week at the same time
          nextRunTime = new Date(now);
          nextRunTime.setDate(nextRunTime.getDate() + 7);
          break;
        case 'monthly':
          // Set to next month at the same time
          nextRunTime = new Date(now);
          nextRunTime.setMonth(nextRunTime.getMonth() + 1);
          break;
        default:
          // Default to tomorrow
          nextRunTime = new Date(now);
          nextRunTime.setDate(nextRunTime.getDate() + 1);
      }
    }

    const scheduledReport = await db.scheduledReport.create({
      data: {
        reportId: params.reportId,
        frequency: params.frequency,
        nextRunTime: nextRunTime,
        emailAddresses: params.emailRecipients.join(','), // Changed from emailRecipients to emailAddresses
        active: true, // Changed from isActive to active
        userId: dbUser.id,
        organizationId: params.organizationId,
      },
    });

    revalidatePath("/dashboard/reports");
    return scheduledReport;
  } catch {
    throw new Error("Failed to schedule report");
  }
}

export async function updateSchedule(params: {
  id: string;
  frequency?: string;
  nextRunDate?: Date;
  emailRecipients?: string[];
  isActive?: boolean;
}) {
  const { userId: clerkUserId } = await auth();

  if (!clerkUserId) {
    throw new Error("Unauthorized");
  }

  try {
    // Get database user ID from Clerk ID
    const dbUser = await db.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true }
    });

    if (!dbUser) {
      throw new Error("User not found in database");
    }

    // Get the current scheduled report to calculate the next run time
    const currentSchedule = await db.scheduledReport.findUnique({
      where: {
        id: params.id,
        userId: dbUser.id,
      },
    });

    if (!currentSchedule) {
      throw new Error("Scheduled report not found");
    }

    // Create update data object
    const updateData: Partial<Record<string, unknown>> = {
      frequency: params.frequency,
      active: params.isActive, // Changed from isActive to active
    };

    // If emailRecipients is provided, update emailAddresses
    if (params.emailRecipients) {
      updateData.emailAddresses = params.emailRecipients.join(','); // Changed from emailRecipients to emailAddresses
    }

    // Calculate the next run time based on the frequency and provided date
    let nextRunTime: Date;

    if (params.nextRunDate) {
      // Use the provided date as the base
      nextRunTime = new Date(params.nextRunDate);
    } else if (currentSchedule.nextRunTime) {
      // Use the current next run time as the base
      nextRunTime = new Date(currentSchedule.nextRunTime);
    } else {
      // Default to now if no date is available
      nextRunTime = new Date();
    }

    // Ensure the next run time is in the future
    const now = new Date();
    if (nextRunTime <= now) {
      // If the next run time is in the past, calculate a new one based on frequency
      const frequency = params.frequency || currentSchedule.frequency;

      switch (frequency) {
        case 'daily':
          // Set to tomorrow at the same time
          nextRunTime = new Date(now);
          nextRunTime.setDate(nextRunTime.getDate() + 1);
          break;
        case 'weekly':
          // Set to next week at the same time
          nextRunTime = new Date(now);
          nextRunTime.setDate(nextRunTime.getDate() + 7);
          break;
        case 'monthly':
          // Set to next month at the same time
          nextRunTime = new Date(now);
          nextRunTime.setMonth(nextRunTime.getMonth() + 1);
          break;
        default:
          // Default to tomorrow
          nextRunTime = new Date(now);
          nextRunTime.setDate(nextRunTime.getDate() + 1);
      }
    }

    // Update the next run time
    updateData.nextRunTime = nextRunTime;

    const scheduledReport = await db.scheduledReport.update({
      where: {
        id: params.id,
        userId: dbUser.id,
      },
      data: updateData,
    });

    revalidatePath("/dashboard/reports");
    return scheduledReport;
  } catch {
    throw new Error("Failed to update schedule");
  }
}

export async function deleteSchedule(id: string) {
  const { userId: clerkUserId } = await auth();

  if (!clerkUserId) {
    throw new Error("Unauthorized");
  }

  try {
    // Get database user ID from Clerk ID
    const dbUser = await db.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true }
    });

    if (!dbUser) {
      throw new Error("User not found in database");
    }

    await db.scheduledReport.delete({
      where: {
        id,
        userId: dbUser.id,
      },
    });

    revalidatePath("/dashboard/reports");
    return { success: true };
  } catch {
    throw new Error("Failed to delete schedule");
  }
}

export async function toggleScheduleStatus(id: string) {
  const { userId: clerkUserId } = await auth();

  if (!clerkUserId) {
    throw new Error("Unauthorized");
  }

  try {
    // Get database user ID from Clerk ID
    const dbUser = await db.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true }
    });

    if (!dbUser) {
      throw new Error("User not found in database");
    }

    // Get current status
    const schedule = await db.scheduledReport.findUnique({
      where: {
        id,
        userId: dbUser.id,
      },
    });

    if (!schedule) {
      throw new Error("Schedule not found");
    }

    // Toggle status
    const updatedSchedule = await db.scheduledReport.update({
      where: {
        id,
      },
      data: {
        active: !schedule.active, // Changed from isActive to active
      },
    });

    revalidatePath("/dashboard/reports");
    return updatedSchedule;
  } catch {
    throw new Error("Failed to toggle schedule status");
  }
}

/**
 * Get report metrics for the dashboard
 * @returns Object containing report metrics
 */
export async function getReportMetrics() {
  const { userId: clerkUserId } = await auth();

  if (!clerkUserId) {
    throw new Error("Unauthorized");
  }

  try {
    const dbUser = await db.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true }
    });

    if (!dbUser) {
      throw new Error("User not found in database");
    }

    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();
    const firstDayOfMonth = new Date(currentYear, currentMonth, 1);

    const prevMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    const prevMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;
    const firstDayOfPrevMonth = new Date(prevMonthYear, prevMonth, 1);

    // Use try-catch for each database operation to prevent one failure from breaking everything
    let totalReports = 0;
    try {
      totalReports = await db.report.count({
        where: {
          userId: dbUser.id,
        },
      });
    } catch {
      // Add a placeholder to maintain the array length
      totalReports = 0;
    }                                                 

    let totalReportsPrevMonth = 0;
    try {
      totalReportsPrevMonth = await db.report.count({
        where: {
          userId: dbUser.id,
          createdAt: {
            lt: firstDayOfMonth,
          },
        },
      });
    } catch {
      // Add a placeholder to maintain the array length
      totalReportsPrevMonth = 0;
    }

    const totalReportsChange = totalReportsPrevMonth > 0
      ? ((totalReports - totalReportsPrevMonth) / totalReportsPrevMonth) * 100
      : 0;

    let scheduledReports = 0;
    try {
      scheduledReports = await db.scheduledReport.count({
        where: {
          userId: dbUser.id,
          active: true, // Changed from isActive to active
        },
      });
    } catch {
      // Add a placeholder to maintain the array length
      scheduledReports = 0;
    }

    let scheduledReportsPrevMonth = 0;
    try {
      scheduledReportsPrevMonth = await db.scheduledReport.count({
        where: {
          userId: dbUser.id,
          active: true, // Changed from isActive to active
          createdAt: {
            lt: firstDayOfMonth,
          },
        },
      });
    } catch {
      // Add a placeholder to maintain the array length
      scheduledReportsPrevMonth = 0;
    }

    const scheduledReportsChange = scheduledReportsPrevMonth > 0
      ? ((scheduledReports - scheduledReportsPrevMonth) / scheduledReportsPrevMonth) * 100
      : 0;

    let customReports = 0;
    try {
      customReports = await db.report.count({
        where: {
          userId: dbUser.id,
          reportType: "CUSTOM", // Changed from type to reportType
        },
      });
    } catch {
      // Add a placeholder to maintain the array length
      customReports = 0;
    }

    let customReportsPrevMonth = 0;
    try {
      customReportsPrevMonth = await db.report.count({
        where: {
          userId: dbUser.id,
          reportType: "CUSTOM", // Changed from type to reportType
          createdAt: {
            lt: firstDayOfMonth,
          },
        },
      });
    } catch {
      // Add a placeholder to maintain the array length
      customReportsPrevMonth = 0;
    }

    const customReportsChange = customReportsPrevMonth > 0
      ? ((customReports - customReportsPrevMonth) / customReportsPrevMonth) * 100
      : 0;

    let generatedThisMonth = 0;
    try {
      generatedThisMonth = await db.report.count({
        where: {
          userId: dbUser.id,
          createdAt: {
            gte: firstDayOfMonth,
          },
        },
      });
    } catch {
      // Add a placeholder to maintain the array length
      generatedThisMonth = 0;
    }

    let generatedPrevMonth = 0;
    try {
      generatedPrevMonth = await db.report.count({
        where: {
          userId: dbUser.id,
          createdAt: {
            gte: firstDayOfPrevMonth,
            lt: firstDayOfMonth,
          },
        },
      });
    } catch {
      // Add a placeholder to maintain the array length
      generatedPrevMonth = 0;
    }

    const generatedThisMonthChange = generatedPrevMonth > 0
      ? ((generatedThisMonth - generatedPrevMonth) / generatedPrevMonth) * 100
      : 0;

    const monthlyTrends = [];
    for (let i = 6; i >= 0; i--) {
      try {
        const monthDate = new Date(currentYear, currentMonth - i, 1);
        const monthEnd = new Date(currentYear, currentMonth - i + 1, 0);
        const monthName = monthDate.toLocaleDateString('default', { month: 'short' });

        let count = 0;
        try {
          count = await db.report.count({
            where: {
              userId: dbUser.id,
              createdAt: {
                gte: monthDate,
                lte: monthEnd,
              },
            },
          });
        } catch {
          // Add a placeholder to maintain the array length
          count = 0;
        }

        monthlyTrends.push({ month: monthName, reports: count });
      } catch {
        // Add a placeholder to maintain the array length
        monthlyTrends.push({ month: `Month ${i}`, reports: 0 });
      }
    }

    return {
      totalReports,
      scheduledReports,
      customReports,
      generatedThisMonth,
      monthlyTrends,
      totalReportsChange,
      scheduledReportsChange,
      customReportsChange,
      generatedThisMonthChange,
    };
  } catch {
    throw new Error("Failed to get report metrics");
  }
}

// Get scheduled reports
export async function getScheduledReports() {
  const { userId: clerkUserId } = await auth();

  if (!clerkUserId) {
    throw new Error("Unauthorized");
  }

  try {
    // Get database user ID from Clerk ID
    const dbUser = await db.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true }
    });

    if (!dbUser) {
      throw new Error("User not found in database");
    }

    const scheduledReports = await db.scheduledReport.findMany({
      where: {
        userId: dbUser.id,
      },
      include: {
        report: true,
      },
      orderBy: {
        nextRunTime: 'asc', // Changed from nextRunDate to nextRunTime
      },
    });

    return scheduledReports;
  } catch {
    throw new Error("Failed to fetch scheduled reports");
  }
}

// Get a specific scheduled report by ID
export async function getScheduledReportById(id: string) {
  const { userId: clerkUserId } = await auth();

  if (!clerkUserId) {
    throw new Error("Unauthorized");
  }

  try {
    // Get database user ID from Clerk ID
    const dbUser = await db.user.findUnique({
      where: { clerkId: clerkUserId },
      select: { id: true }
    });

    if (!dbUser) {
      throw new Error("User not found in database");
    }

    const scheduledReport = await db.scheduledReport.findUnique({
      where: {
        id,
        userId: dbUser.id,
      },
      include: {
        report: true,
      },
    });

    if (!scheduledReport) {
      throw new Error("Scheduled report not found");
    }

    return scheduledReport;
  } catch {
    throw new Error("Failed to fetch scheduled report");
  }
}