import db from '@/db/db';
import { generateUUID } from '@/lib/utils';

interface ActionResult {
  success: boolean;
  data?: any;
  message: string;
  type: 'invoice' | 'report' | 'analytics' | 'general';
}

/**
 * Smart Action Engine - Executes actions based on AI intent detection
 * This replaces the tool-based system with intelligent action execution
 */
export class ActionEngine {
  
  /**
   * Detect and execute actions from AI response
   */
  static async executeAction(
    intent: string, 
    parameters: Record<string, any>, 
    userId: string
  ): Promise<ActionResult> {
    
    try {
      switch (intent.toLowerCase()) {
        case 'create_invoice':
          return await this.createInvoice(parameters, userId);
          
        case 'list_invoices':
          return await this.listInvoices(parameters, userId);
          
        case 'get_invoice_stats':
          return await this.getInvoiceStats(userId);
          
        case 'generate_report':
          return await this.generateReport(parameters, userId);

        case 'analyze_spending':
          return await this.analyzeSpending(parameters, userId);

        case 'generate_document':
          return await this.generateDocument(parameters, userId);

        case 'create_invoice_pdf':
          return await this.createInvoicePDF(parameters, userId);

        case 'export_data':
          return await this.exportData(parameters, userId);

        case 'navigate_to_page':
          return await this.navigateToPage(parameters, userId);

        case 'execute_page_action':
          return await this.executePageAction(parameters, userId);

        default:
          return {
            success: false,
            message: 'Action not recognized',
            type: 'general'
          };
      }
    } catch (error) {
      console.error('Action Engine Error:', error);
      return {
        success: false,
        message: 'Failed to execute action',
        type: 'general'
      };
    }
  }

  private static async createInvoice(params: any, userId: string): Promise<ActionResult> {
    const invoice = await db.invoice.create({
      data: {
        id: generateUUID(),
        invoiceNumber: params.invoiceNumber || `INV-${Date.now()}`,
        vendorName: params.vendorName,
        amount: parseFloat(params.amount) || 0,
        currency: params.currency || 'USD',
        status: 'PENDING',
        issueDate: params.issueDate ? new Date(params.issueDate) : new Date(),
        dueDate: params.dueDate ? new Date(params.dueDate) : null,
        notes: params.notes,
        userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
    });

    return {
      success: true,
      data: invoice,
      message: `Invoice ${invoice.invoiceNumber} created successfully`,
      type: 'invoice'
    };
  }

  private static async listInvoices(params: any, userId: string): Promise<ActionResult> {
    const limit = Math.min(params.limit || 10, 50);
    const status = params.status;
    
    const invoices = await db.invoice.findMany({
      where: {
        userId,
        ...(status && { status: status.toUpperCase() })
      },
      orderBy: { createdAt: 'desc' },
      take: limit,
      select: {
        id: true,
        invoiceNumber: true,
        vendorName: true,
        amount: true,
        currency: true,
        status: true,
        issueDate: true,
        dueDate: true,
        createdAt: true,
      }
    });

    return {
      success: true,
      data: invoices,
      message: `Found ${invoices.length} invoices`,
      type: 'invoice'
    };
  }

  private static async getInvoiceStats(userId: string): Promise<ActionResult> {
    const [total, pending, paid, overdue] = await Promise.all([
      db.invoice.aggregate({
        where: { userId },
        _count: { id: true },
        _sum: { amount: true }
      }),
      db.invoice.aggregate({
        where: { userId, status: 'PENDING' },
        _count: { id: true },
        _sum: { amount: true }
      }),
      db.invoice.aggregate({
        where: { userId, status: 'PAID' },
        _count: { id: true },
        _sum: { amount: true }
      }),
      db.invoice.aggregate({
        where: { userId, status: 'OVERDUE' },
        _count: { id: true },
        _sum: { amount: true }
      })
    ]);

    const stats = {
      total: { count: total._count.id, amount: total._sum.amount || 0 },
      pending: { count: pending._count.id, amount: pending._sum.amount || 0 },
      paid: { count: paid._count.id, amount: paid._sum.amount || 0 },
      overdue: { count: overdue._count.id, amount: overdue._sum.amount || 0 }
    };

    return {
      success: true,
      data: stats,
      message: 'Invoice statistics retrieved',
      type: 'analytics'
    };
  }

  private static async generateReport(params: any, userId: string): Promise<ActionResult> {
    const reportType = params.type || 'summary';
    const period = params.period || 'month';
    
    // Generate report based on type
    const reportData = await this.buildReportData(reportType, period, userId);
    
    const report = await db.report.create({
      data: {
        id: generateUUID(),
        title: `${reportType.charAt(0).toUpperCase() + reportType.slice(1)} Report`,
        reportType: reportType.toUpperCase(),
        period,
        status: 'COMPLETED',
        userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
    });

    return {
      success: true,
      data: { report, data: reportData },
      message: `${reportType} report generated successfully`,
      type: 'report'
    };
  }

  private static async analyzeSpending(params: any, userId: string): Promise<ActionResult> {
    const period = params.period || 30; // days
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - period);

    const [vendorSpending, categorySpending, trends] = await Promise.all([
      // Vendor spending analysis
      db.invoice.groupBy({
        by: ['vendorName'],
        where: {
          userId,
          createdAt: { gte: startDate },
          vendorName: { not: null }
        },
        _sum: { amount: true },
        _count: { id: true },
        orderBy: { _sum: { amount: 'desc' } },
        take: 10
      }),
      
      // Category spending (if categories exist)
      db.invoice.groupBy({
        by: ['categoryId'],
        where: {
          userId,
          createdAt: { gte: startDate },
          categoryId: { not: null }
        },
        _sum: { amount: true },
        orderBy: { _sum: { amount: 'desc' } }
      }),
      
      // Daily spending trends
      db.$queryRaw`
        SELECT DATE(createdAt) as date, SUM(amount) as total
        FROM Invoice 
        WHERE userId = ${userId} AND createdAt >= ${startDate}
        GROUP BY DATE(createdAt)
        ORDER BY date DESC
        LIMIT 30
      `
    ]);

    const analysis = {
      topVendors: vendorSpending.map(v => ({
        vendor: v.vendorName,
        amount: v._sum.amount,
        invoiceCount: v._count.id
      })),
      categoryBreakdown: categorySpending,
      dailyTrends: trends,
      period: `${period} days`,
      totalSpent: vendorSpending.reduce((sum, v) => sum + (v._sum.amount || 0), 0)
    };

    return {
      success: true,
      data: analysis,
      message: `Spending analysis for the last ${period} days`,
      type: 'analytics'
    };
  }

  private static async buildReportData(type: string, period: string, userId: string) {
    // Simplified report data building
    const endDate = new Date();
    const startDate = new Date();
    
    switch (period) {
      case 'week':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(endDate.getMonth() - 1);
        break;
      case 'quarter':
        startDate.setMonth(endDate.getMonth() - 3);
        break;
      case 'year':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
    }

    const invoices = await db.invoice.findMany({
      where: {
        userId,
        createdAt: { gte: startDate, lte: endDate }
      }
    });

    return {
      period: `${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`,
      totalInvoices: invoices.length,
      totalAmount: invoices.reduce((sum, inv) => sum + (inv.amount || 0), 0),
      averageAmount: invoices.length > 0 ? invoices.reduce((sum, inv) => sum + (inv.amount || 0), 0) / invoices.length : 0,
      statusBreakdown: {
        pending: invoices.filter(i => i.status === 'PENDING').length,
        paid: invoices.filter(i => i.status === 'PAID').length,
        overdue: invoices.filter(i => i.status === 'OVERDUE').length,
      }
    };
  }

  /**
   * Generate documents (PDF, Excel, etc.)
   */
  private static async generateDocument(params: any, userId: string): Promise<ActionResult> {
    try {
      const documentType = params.type || 'report';
      const format = params.format || 'pdf';

      return {
        success: true,
        message: `${documentType} ${format} generation requested`,
        type: 'general',
        data: {
          documentType,
          format,
          action: 'generate_document',
          parameters: params
        }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to generate document',
        type: 'general'
      };
    }
  }

  /**
   * Create invoice PDF
   */
  private static async createInvoicePDF(params: any, userId: string): Promise<ActionResult> {
    try {
      const invoiceId = params.invoiceId;

      if (invoiceId) {
        const invoice = await db.invoice.findUnique({
          where: { id: invoiceId, userId }
        });
        if (!invoice) {
          return {
            success: false,
            message: 'Invoice not found',
            type: 'invoice'
          };
        }
      }

      return {
        success: true,
        message: 'Invoice PDF creation requested',
        type: 'invoice',
        data: {
          action: 'create_invoice_pdf',
          invoiceId,
          template: params.template || 'modern'
        }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to create invoice PDF',
        type: 'invoice'
      };
    }
  }

  /**
   * Export data to Excel
   */
  private static async exportData(params: any, userId: string): Promise<ActionResult> {
    try {
      const dataType = params.dataType || 'invoices';

      return {
        success: true,
        message: `${dataType} export requested`,
        type: 'general',
        data: {
          action: 'export_data',
          dataType,
          format: 'excel'
        }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to export data',
        type: 'general'
      };
    }
  }

  /**
   * Navigate to a specific page
   */
  private static async navigateToPage(params: any, userId: string): Promise<ActionResult> {
    const targetPage = params.page || params.target;

    if (!targetPage) {
      return {
        success: false,
        message: 'No target page specified',
        type: 'general'
      };
    }

    return {
      success: true,
      message: `Navigate to ${targetPage}`,
      type: 'general',
      data: {
        action: 'navigate',
        page: targetPage
      }
    };
  }

  /**
   * Execute a page-specific action
   */
  private static async executePageAction(params: any, userId: string): Promise<ActionResult> {
    try {
      return {
        success: true,
        message: 'Page action execution requested',
        type: 'general',
        data: {
          action: 'execute_page_action',
          page: params.page || '/dashboard',
          actionType: params.actionType,
          parameters: params.parameters || {}
        }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to execute page action',
        type: 'general'
      };
    }
  }
}

/**
 * Intent detection from AI responses
 */
export function detectIntent(message: string): { intent: string; confidence: number; parameters: Record<string, any> } {
  const lowerMessage = message.toLowerCase();
  
  // Enhanced intent detection patterns with new capabilities
  const patterns = [
    { intent: 'create_invoice', keywords: ['create', 'new', 'add', 'invoice'], confidence: 0.8 },
    { intent: 'list_invoices', keywords: ['list', 'show', 'display', 'invoices'], confidence: 0.8 },
    { intent: 'get_invoice_stats', keywords: ['stats', 'statistics', 'summary', 'overview'], confidence: 0.7 },
    { intent: 'generate_report', keywords: ['report', 'generate', 'create report'], confidence: 0.8 },
    { intent: 'analyze_spending', keywords: ['analyze', 'spending', 'analysis', 'breakdown'], confidence: 0.7 },
    { intent: 'generate_document', keywords: ['document', 'pdf', 'excel', 'generate document'], confidence: 0.8 },
    { intent: 'create_invoice_pdf', keywords: ['invoice pdf', 'pdf invoice', 'download invoice'], confidence: 0.8 },
    { intent: 'export_data', keywords: ['export', 'download', 'excel export', 'csv'], confidence: 0.8 },
    { intent: 'navigate_to_page', keywords: ['go to', 'navigate', 'open', 'show me'], confidence: 0.7 },
    { intent: 'execute_page_action', keywords: ['click', 'submit', 'execute', 'run'], confidence: 0.6 },
  ];
  
  for (const pattern of patterns) {
    const matches = pattern.keywords.filter(keyword => lowerMessage.includes(keyword));
    if (matches.length > 0) {
      return {
        intent: pattern.intent,
        confidence: pattern.confidence * (matches.length / pattern.keywords.length),
        parameters: extractParameters(message, pattern.intent)
      };
    }
  }
  
  return { intent: 'general', confidence: 0.1, parameters: {} };
}

function extractParameters(message: string, intent: string): Record<string, any> {
  // Simple parameter extraction based on intent
  const params: Record<string, any> = {};
  
  // Extract common parameters
  const amountMatch = message.match(/\$?(\d+(?:\.\d{2})?)/);
  if (amountMatch) params.amount = amountMatch[1];
  
  const vendorMatch = message.match(/(?:vendor|company|from)\s+([A-Za-z\s]+)/i);
  if (vendorMatch) params.vendorName = vendorMatch[1].trim();
  
  const statusMatch = message.match(/(?:status|state)\s+(pending|paid|overdue)/i);
  if (statusMatch) params.status = statusMatch[1];
  
  return params;
}
