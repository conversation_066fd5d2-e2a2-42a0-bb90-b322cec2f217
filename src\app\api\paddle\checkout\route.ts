import { NextRequest, NextResponse } from 'next/server';
import { auth, currentUser } from '@clerk/nextjs/server';
import db from '@/db/db';

const PADDLE_API_URL =
  (process.env.NEXT_PUBLIC_PADDLE_ENVIRONMENT === 'production' || process.env.NEXT_PUBLIC_PADDLE_ENVIRONMENT === 'sandbox')
    ? 'https://sandbox-api.paddle.com'
    : 'https://sandbox-api.paddle.com';

const PADDLE_HEADERS = {
  Authorization: `Bearer ${process.env.PADDLE_API_KEY}`,
  'Content-Type': 'application/json',
};

export async function POST(request: NextRequest) {
  try {
    // Environment validation
    const paddleEnv = process.env.NEXT_PUBLIC_PADDLE_ENVIRONMENT;
    const apiKey = process.env.PADDLE_API_KEY;
    const appUrl = process.env.NEXT_PUBLIC_APP_URL;

    // Debug all environment variables
    console.log('Environment Debug:', {
      paddleEnv,
      hasApiKey: !!apiKey,
      appUrl,
      nodeEnv: process.env.NODE_ENV,
      allEnvVars: {
        NEXT_PUBLIC_PADDLE_ENVIRONMENT: process.env.NEXT_PUBLIC_PADDLE_ENVIRONMENT,
        PADDLE_API_KEY: process.env.PADDLE_API_KEY ? 'SET' : 'NOT_SET',
        NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
      }
    });

    console.log('Paddle Environment Check:', {
      environment: paddleEnv,
      apiUrl: PADDLE_API_URL,
      hasApiKey: !!apiKey,
      apiKeyPrefix: apiKey?.substring(0, 10) + '...',
      apiKeyLength: apiKey?.length || 0,
    });

    if (!apiKey) {
      console.error('PADDLE_API_KEY environment variable is not set');
      return NextResponse.json(
        { error: 'Paddle API key not configured' },
        { status: 500 }
      );
    }

    // Log the actual API key format for debugging (first 10 chars only)
    console.log('API Key Debug:', {
      hasApiKey: !!apiKey,
      apiKeyPrefix: apiKey?.substring(0, 10),
      apiKeyLength: apiKey?.length,
      environment: process.env.NEXT_PUBLIC_PADDLE_ENVIRONMENT,
      apiUrl: PADDLE_API_URL,
    });

    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const user = await currentUser();
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Get user from database
    const dbUser = await db.user.findUnique({
      where: { clerkId: userId },
    });

    if (!dbUser) {
      return NextResponse.json(
        { error: 'Database user not found' },
        { status: 404 }
      );
    }

    const body = await request.json();
    const { priceId, planId, billingCycle, returnUrl } = body;

    if (!priceId) {
      return NextResponse.json(
        { error: 'Price ID is required' },
        { status: 400 }
      );
    }

    // Create checkout session with Paddle
    const checkoutData = {
      items: [
        {
          price_id: priceId,
          quantity: 1,
        },
      ],
      customer_email: user.emailAddresses[0]?.emailAddress,
      custom_data: {
        user_id: dbUser.id,
        clerk_id: userId,
        plan_id: planId,
        billing_cycle: billingCycle,
        return_url: returnUrl,
      },
      success_url: returnUrl
        ? `${process.env.NEXT_PUBLIC_APP_URL}/confirmation?return_url=${encodeURIComponent(returnUrl)}`
        : `${process.env.NEXT_PUBLIC_APP_URL}/confirmation`,
      discount_id: null, // Can be used for promotional codes
    };

    console.log('Creating Paddle checkout with data:', {
      priceId,
      planId,
      billingCycle,
      returnUrl,
      userEmail: user.emailAddresses[0]?.emailAddress,
      userId: dbUser.id,
      apiUrl: PADDLE_API_URL,
      environment: process.env.NEXT_PUBLIC_PADDLE_ENVIRONMENT,
      successUrl: checkoutData.success_url,
      appUrl: process.env.NEXT_PUBLIC_APP_URL,
    });

    console.log('Making Paddle API request:', {
      url: `${PADDLE_API_URL}/transactions`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ***' + (process.env.PADDLE_API_KEY?.substring(-4) || ''),
      },
      bodyPreview: JSON.stringify(checkoutData).substring(0, 200) + '...',
    });

    const response = await fetch(`${PADDLE_API_URL}/transactions`, {
      method: 'POST',
      headers: PADDLE_HEADERS,
      body: JSON.stringify(checkoutData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Paddle API error:', {
        status: response.status,
        statusText: response.statusText,
        error: errorData,
        requestData: {
          apiUrl: PADDLE_API_URL,
          priceId,
          userEmail: user.emailAddresses[0]?.emailAddress,
          environment: process.env.NEXT_PUBLIC_PADDLE_ENVIRONMENT,
          hasApiKey: !!process.env.PADDLE_API_KEY,
        }
      });
      return NextResponse.json(
        {
          success: false,
          error: `Paddle API error: ${response.status}`,
          details: errorData,
        },
        { status: 400 }
      );
    }

    const result = await response.json();
    console.log(
      'Paddle checkout created successfully:',
      result.data?.id
    );
    console.log('Full Paddle API response:', {
      id: result.data?.id,
      checkoutUrl: result.data?.checkout?.url,
      hasCheckout: !!result.data?.checkout,
      fullResponse: result,
    });

    return NextResponse.json({
      success: true,
      data: {
        ...result.data,
        // Always use the correct domain for checkout URL
        checkout: {
          url: `https://checkout.paddle.com/transaction/${result.data?.id}`,
        },
      },
    });
  } catch (error) {
    console.error('Error creating Paddle checkout:', error);
    return NextResponse.json(
      {
        success: false,
        error:
          error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
