import type { Metada<PERSON> } from "next";
import { Toaster } from "sonner";

import "@/app/globals.css";


export const metadata: Metadata = {
  title: "Complete Your Onboarding | Billix",
  description: "Complete your onboarding to start using Billix invoice management system",
};

export default function OnboardingLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="dashboard-gradient-bg min-h-screen">
      {children}
      <Toaster position="top-center" richColors />
    </div>
  );
} 