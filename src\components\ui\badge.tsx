import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import Colors from '../theme/Colors';

import { cn } from "@/lib/utils";

const badgeVariants = cva(
  "inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",
  {
    variants: {
      variant: {
        default:
          `border-transparent bg-[${Colors.primary}] text-[${Colors.textLight}] [a&]:hover:bg-[${Colors.primaryDark}]`,
        secondary:
          `border-transparent bg-[${Colors.accent}] text-[${Colors.textLight}] [a&]:hover:bg-[${Colors.primary}]`,
        destructive:
          `border-transparent bg-[${Colors.error}] text-[${Colors.textLight}] [a&]:hover:bg-[${Colors.error}] focus-visible:ring-[${Colors.error}] dark:focus-visible:ring-[${Colors.error}] dark:bg-[${Colors.error}]/60`,
        outline:
          `text-[${Colors.text}] [a&]:hover:bg-[${Colors.accent}] [a&]:hover:text-[${Colors.textLight}]`,
        blueGlow:
          `relative bg-gradient-to-r from-[${Colors.primary}] to-[${Colors.primaryDark}] text-[${Colors.textLight}] before:absolute before:inset-0 before:-z-10 before:rounded-md before:bg-gradient-to-r before:from-[${Colors.primary}] before:to-[${Colors.primaryDark}] before:blur-lg before:opacity-70 before:transition-opacity hover:before:opacity-100`,
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

function Badge({
  className,
  variant,
  asChild = false,
  ...props
}: React.ComponentProps<"span"> &
  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {
  const Comp = asChild ? Slot : "span";

  return (
    <Comp
      data-slot="badge"
      className={cn(badgeVariants({ variant }), className)}
      {...props}
    />
  );
}

export { Badge, badgeVariants };
