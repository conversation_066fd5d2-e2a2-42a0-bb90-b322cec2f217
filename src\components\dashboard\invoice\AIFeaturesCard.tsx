"use client";

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ReceiptText } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";

interface AIFeaturesCardProps {
  onConfigureAI: () => void;
}

const AIFeaturesCard = ({ onConfigureAI }: AIFeaturesCardProps) => {
  return (
    <Card className="h-full flex flex-col border border-purple-200/30 dark:border-purple-700/20 rounded-xl bg-gradient-to-br from-white via-purple-50/20 to-indigo-50/20 dark:from-gray-800/50 dark:via-purple-950/10 dark:to-indigo-950/10">
      <CardHeader className="border-b border-purple-200/30 dark:border-purple-700/20 bg-gradient-to-r from-purple-50/50 via-white to-indigo-50/50 dark:from-purple-950/20 dark:via-gray-800/50 dark:to-indigo-950/20 rounded-t-xl p-6">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center text-xl font-bold text-gray-900 dark:text-gray-100">
            <div className="p-2 rounded-xl bg-gradient-to-br from-purple-100 to-indigo-100 dark:from-purple-900/40 dark:to-indigo-900/40 shadow-sm mr-3">
              <Sparkles className="h-5 w-5 text-purple-600 dark:text-purple-400" />
            </div>
            AI Processing
          </CardTitle>
          <Badge
            variant="outline"
            className="bg-gradient-to-r from-purple-100 to-indigo-100 text-purple-800 dark:from-purple-900/40 dark:to-indigo-900/40 dark:text-purple-300 border-purple-300 dark:border-purple-600 font-medium px-3 py-1"
          >
            AI-Powered
          </Badge>
        </div>
        <CardDescription className="text-gray-600 dark:text-gray-400 font-medium mt-2">
          Smart invoice processing with advanced AI features
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6 flex-1 p-6 bg-gradient-to-br from-white via-purple-50/20 to-indigo-50/20 dark:from-gray-800/50 dark:via-purple-950/10 dark:to-indigo-950/10">
        <div className="space-y-6">
          <AIFeature
            icon={
              <ReceiptText className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            }
            title="Intelligent Data Extraction"
            description="Our AI extracts all invoice data with detailed line items and automatically validates and corrects information."
            bgColor="bg-blue-100 dark:bg-blue-900/50"
            gradientColor="from-blue-50 to-blue-50/30 dark:from-blue-950/50 dark:to-blue-950/20"
          />

          <AIFeature
            icon={
              <PieChart className="h-4 w-4 text-purple-600 dark:text-purple-400" />
            }
            title="Smart Categorization"
            description="AI suggests categories and vendor types with confidence scores based on invoice content. Suggestions improve over time as you select them."
            bgColor="bg-purple-100 dark:bg-purple-900/50"
            gradientColor="from-purple-50 to-purple-50/30 dark:from-purple-950/50 dark:to-purple-950/20"
            showProgress={true}
            confidenceValue={85}
          />

          <AIFeature
            icon={
              <BarChart className="h-4 w-4 text-green-600 dark:text-green-400" />
            }
            title="Automated Tax Calculation"
            description="Missing tax information is automatically calculated based on country-specific rates and verified against invoice totals."
            bgColor="bg-green-100 dark:bg-green-900/50"
            gradientColor="from-green-50 to-green-50/30 dark:from-green-950/50 dark:to-green-950/20"
          />

          <AIFeature
            icon={
              <BarChart className="h-4 w-4 text-amber-600 dark:text-amber-400" />
            }
            title="Intelligent Invoice Type Detection"
            description="Automatically detects if an invoice is for payment (expense) or from customers (income) based on invoice content and patterns."
            bgColor="bg-amber-100 dark:bg-amber-900/50"
            gradientColor="from-amber-50 to-amber-50/30 dark:from-amber-950/50 dark:to-amber-950/20"
          />
        </div>
      </CardContent>
      <CardFooter className="p-6 pt-0 mt-auto">
        <Button 
          onClick={onConfigureAI}
          className="w-full bg-gradient-to-r from-blue-500 to-indigo-500 text-white border-0 shadow-lg rounded-xl font-semibold py-3 text-base cursor-pointer"
        >
          <Sparkles className="mr-3 h-5 w-5" />
          Configure AI Settings
        </Button>
      </CardFooter>
    </Card>
  );
};

interface AIFeatureProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  bgColor: string;
  gradientColor: string;
  showProgress?: boolean;
  confidenceValue?: number;
}

const AIFeature = ({
  icon,
  title,
  description,
  bgColor,
  gradientColor,
  showProgress = false,
  confidenceValue,
}: AIFeatureProps) => {
  return (
    <div className={`border border-purple-200/30 dark:border-purple-700/20 rounded-xl p-5 bg-gradient-to-r ${gradientColor}`}>
      <div className="flex items-start gap-4">
        <div className={`h-10 w-10 rounded-xl ${bgColor} flex items-center justify-center flex-shrink-0 shadow-sm`}>
          {icon}
        </div>
        <div className="flex-1">
          <h3 className="font-semibold text-base text-gray-900 dark:text-gray-100 mb-2">{title}</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">{description}</p>

          {showProgress && confidenceValue && (
            <div className="mt-4">
              <div className="flex justify-between items-center text-xs text-gray-600 dark:text-gray-400 mb-2">
                <span className="font-medium">Confidence Score</span>
                <span className="font-semibold text-blue-600 dark:text-blue-400">{confidenceValue}%</span>
              </div>
              <div className="h-2 w-full overflow-hidden rounded-full bg-gradient-to-r from-purple-100 to-blue-100 dark:from-purple-900/20 dark:to-blue-900/20">
                <div
                  className={`h-full rounded-full ${
                    confidenceValue > 80
                      ? "bg-gradient-to-r from-green-500 to-emerald-500"
                      : confidenceValue > 60
                      ? "bg-gradient-to-r from-blue-500 to-cyan-500"
                      : "bg-gradient-to-r from-orange-500 to-yellow-500"
                  }`}
                  style={{ width: `${confidenceValue}%` }}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AIFeaturesCard;
