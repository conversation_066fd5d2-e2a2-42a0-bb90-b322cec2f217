import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user from Clerk
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the provider from query parameters
    const provider = request.nextUrl.searchParams.get("provider");
    if (!provider || provider !== "gmail") {
      return NextResponse.json({ error: "Invalid or unsupported provider" }, { status: 400 });
    }

    // Create OAuth URL for Gmail
    const redirectUri = process.env.GOOGLE_REDIRECT_URI;
    const scope = "https://www.googleapis.com/auth/gmail.readonly";
    const clientId = process.env.GOOGLE_CLIENT_ID;

    if (!clientId || !redirectUri) {
      return NextResponse.json({ error: "Google OAuth configuration incomplete" }, { status: 500 });
    }

    // Generate a state parameter to prevent CSRF attacks
    // Include the userId to identify the user after auth callback
    const state = Buffer.from(JSON.stringify({ userId, provider })).toString("base64");

    const authUrl = `https://accounts.google.com/o/oauth2/v2/auth?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&response_type=code&scope=${encodeURIComponent(scope)}&access_type=offline&prompt=consent&state=${state}`;

    return NextResponse.json({ authUrl });
  } catch (error) {
    console.error("Error initiating Gmail connection:", error);
    return NextResponse.json(
      { error: "Failed to initiate connection" },
      { status: 500 }
    );
  }
}
