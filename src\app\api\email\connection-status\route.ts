import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import db from "@/db/db";
import { isGmailConnected } from "@/lib/services/gmail-service";

export async function GET(request: NextRequest) {
  try {
    // Get the authenticated user from Clerk
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the provider from query parameters
    const provider = request.nextUrl.searchParams.get("provider");
    if (!provider || !["gmail", "outlook"].includes(provider)) {
      return NextResponse.json({ error: "Invalid provider" }, { status: 400 });
    }

    if (provider === "gmail") {
      // Check Gmail connection status
      const status = await isGmailConnected(userId);
      
      // Get sync settings if connected
      let syncSettings = null;
      if (status.connected) {
        const emailSyncJob = await db.emailSyncJob.findFirst({
          where: { userId }
        });
        
        if (emailSyncJob) {
          syncSettings = {
            autoSync: emailSyncJob.autoProcess,
            importAttachments: true,
            frequency: emailSyncJob.frequency,
            includeRead: emailSyncJob.includeRead
          };
        }
      }
      
      return NextResponse.json({
        connected: status.connected,
        email: status.email || null,
        lastSync: status.connected ? await getLastSyncDate(userId) : null,
        settings: syncSettings
      });
    } else {
      // Outlook not implemented yet
      return NextResponse.json({
        connected: false,
        email: null,
        lastSync: null,
        settings: null
      });
    }
  } catch (error) {
    console.error("Error checking email connection status:", error);
    return NextResponse.json(
      { error: "Failed to check connection status" },
      { status: 500 }
    );
  }
}

async function getLastSyncDate(userId: string): Promise<Date | null> {
  try {
    const emailSyncJob = await db.emailSyncJob.findFirst({
      where: { userId },
      orderBy: { lastRun: 'desc' }
    });
    
    return emailSyncJob?.lastRun || null;
  } catch (error) {
    console.error("Error getting last sync date:", error);
    return null;
  }
} 