'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';

interface UseSubscriptionAccessResult {
  hasAccess: boolean;
  isLoading: boolean;
  subscription: any;
}

export function useSubscriptionAccess(): UseSubscriptionAccessResult {
  const [hasAccess, setHasAccess] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [subscription, setSubscription] = useState(null);
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    const checkAccess = async () => {
      try {
        // Always allow access to subscription-related pages
        const isSubscriptionRelated = 
          pathname === '/dashboard/subscription' || 
          pathname === '/confirmation' ||
          searchParams.get('success') === 'true' ||
          searchParams.get('session_id');

        if (isSubscriptionRelated) {
          setHasAccess(true);
          setIsLoading(false);
          return;
        }

        // Check actual subscription status for other pages
        const response = await fetch('/api/subscriptions/active');
        
        if (!response.ok) {
          throw new Error('Failed to check subscription');
        }

        const data = await response.json();
        setSubscription(data);

        // Check if user has active subscription
        const isActive = data.isActive !== undefined ? data.isActive : (data.status === 'active');

        if (isActive) {
          setHasAccess(true);
        } else {
          // Redirect to pricing
          const returnUrl = encodeURIComponent(pathname);
          router.push(`/pricing?return_url=${returnUrl}`);
          return;
        }
      } catch (error) {
        console.error('Subscription access check failed:', error);
        
        // On error, be permissive for subscription-related pages
        const isSubscriptionRelated = 
          pathname === '/dashboard/subscription' || 
          pathname === '/confirmation' ||
          searchParams.get('success') === 'true';

        if (isSubscriptionRelated) {
          setHasAccess(true);
        } else {
          const returnUrl = encodeURIComponent(pathname);
          router.push(`/pricing?return_url=${returnUrl}`);
          return;
        }
      } finally {
        setIsLoading(false);
      }
    };

    checkAccess();
  }, [pathname, router, searchParams]);

  return { hasAccess, isLoading, subscription };
}
