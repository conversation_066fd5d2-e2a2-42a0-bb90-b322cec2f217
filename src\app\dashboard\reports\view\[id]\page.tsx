"use client";

import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { getReportById, exportReport } from "@/lib/actions/reports";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Pie<PERSON>hart, Share, Download, ArrowLeft, Loader2, RefreshCw } from "lucide-react";
import { Bar<PERSON><PERSON> as BarChartComponent } from "@/components/ui/bar-chart";
import { LineChart as LineChartComponent } from "@/components/ui/line-chart";
import { <PERSON><PERSON><PERSON> as PieChartComponent } from "@/components/ui/pie-chart";
import { toast } from "sonner";
import { Report } from "@prisma/client";

interface ReportDataItem {
  id?: string;
  reportId?: string;
  invoiceId?: string | null;
  dataPoint: string;
  value: number;
  label?: string | null;
  category?: string | null;
  createdAt?: Date;
}

interface ReportTemplate {
  id: string;
  name: string;
  description?: string | null;
  type: string;
  filters?: Record<string, unknown> | null;
  visualizationType: string;
  isAIGenerated: boolean;
}

interface ReportWithData extends Report {
  data?: ReportDataItem[];
  template?: ReportTemplate | null;
  visualizationType?: string | null;
  dateRange?: string | Record<string, string> | null;
  name?: string;
  type?: string;
}

// Helper function to extract visualization type from report
function getVisualizationType(report: ReportWithData): string {
  if (report.fileUrl && report.fileUrl.startsWith('visualization:')) {
    return report.fileUrl.split(':')[1];
  }

  if (report.visualizationType) {
    return report.visualizationType;
  }

  switch (report.reportType) {
    case 'CASH_FLOW':
    case 'PROFIT_LOSS':
      return 'line';
    case 'BALANCE_SHEET':
    case 'CATEGORY_BREAKDOWN':
      return 'pie';
    default:
      return 'bar';
  }
}

// Helper function to get appropriate X-axis label based on report type
function getXAxisLabel(reportType: string): string {
  switch (reportType) {
    case 'CASH_FLOW':
    case 'PROFIT_LOSS':
    case 'SALES':
      return 'Time Period';
    case 'TAX':
      return 'Month';
    case 'BALANCE_SHEET':
      return 'Category';
    case 'VENDOR_ANALYSIS':
      return 'Vendor';
    case 'CATEGORY_ANALYSIS':
    case 'CATEGORY_BREAKDOWN':
      return 'Category';
    default:
      return 'Data Points';
  }
}

// Helper function to get appropriate Y-axis label based on report type
function getYAxisLabel(reportType: string): string {
  switch (reportType) {
    case 'CASH_FLOW':
      return 'Cash Flow Amount';
    case 'PROFIT_LOSS':
      return 'Profit/Loss Amount';
    case 'SALES':
      return 'Sales Amount';
    case 'TAX':
      return 'Tax Amount';
    case 'BALANCE_SHEET':
      return 'Balance Amount';
    case 'EXPENSES':
      return 'Expense Amount';
    default:
      return 'Value';
  }
}

// Helper function to determine if a reference line should be shown
function shouldShowReferenceLine(reportType: string): boolean {
  return ['CASH_FLOW', 'PROFIT_LOSS', 'SALES'].includes(reportType);
}

// Helper function to calculate reference line value (e.g., average)
function getReferenceLineValue(data: ReportDataItem[]): number {
  const relevantData = data.filter(item =>
    ['monthly_income', 'monthly_expense', 'monthly_profit', 'monthly_sales'].includes(item.dataPoint)
  );

  if (relevantData.length === 0) return 0;

  const sum = relevantData.reduce((acc, item) => acc + item.value, 0);
  return sum / relevantData.length;
}

// Helper function to get reference line label
function getReferenceLineLabel(reportType: string): string {
  switch (reportType) {
    case 'CASH_FLOW':
      return 'Average Cash Flow';
    case 'PROFIT_LOSS':
      return 'Average Profit/Loss';
    case 'SALES':
      return 'Average Sales';
    default:
      return 'Average';
  }
}

// Helper function to get total label for pie charts
function getTotalLabel(reportType: string): string {
  switch (reportType) {
    case 'BALANCE_SHEET':
      return 'Total Assets';
    case 'CATEGORY_BREAKDOWN':
      return 'Total';
    case 'EXPENSES':
      return 'Total Expenses';
    case 'SALES':
      return 'Total Sales';
    case 'TAX':
      return 'Total Tax';
    default:
      return 'Total';
  }
}

// Helper function to format data point names for display
function formatDataPoint(dataPoint: string): string {
  return dataPoint
    .replace(/_/g, ' ')
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Helper function to format values based on data point type
function formatValue(value: number, dataPoint?: string): string {
  if (dataPoint && (
    dataPoint.includes('percent') ||
    dataPoint.includes('margin') ||
    dataPoint.includes('ratio') ||
    dataPoint.includes('growth') ||
    dataPoint.includes('rate')
  )) {
    return `${value.toLocaleString(undefined, {
      minimumFractionDigits: 1,
      maximumFractionDigits: 2
    })}%`;
  }

  if (dataPoint && (
    dataPoint.includes('amount') ||
    dataPoint.includes('income') ||
    dataPoint.includes('expense') ||
    dataPoint.includes('revenue') ||
    dataPoint.includes('cost') ||
    dataPoint.includes('profit') ||
    dataPoint.includes('loss') ||
    dataPoint.includes('assets') ||
    dataPoint.includes('liabilities') ||
    dataPoint.includes('equity') ||
    dataPoint.includes('sales') ||
    dataPoint.includes('tax') ||
    dataPoint.includes('cashflow')
  )) {
    return value.toLocaleString(undefined, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  }

  return value.toLocaleString();
}

export default function ReportViewPage() {
  const params = useParams();
  const router = useRouter();
  const [report, setReport] = useState<ReportWithData | null>(null);
  const [loading, setLoading] = useState(true);
  const [exportLoading, setExportLoading] = useState(false);
  const [selectedDataType, setSelectedDataType] = useState<string>('');
  const reportId = params.id as string;

  useEffect(() => {
    const fetchReport = async () => {
      try {
        const data = await getReportById(reportId);
        const reportData = data as unknown as ReportWithData;
        setReport(reportData);

        if (reportData.data && reportData.data.length > 0) {
          const dataByType: Record<string, ReportDataItem[]> = {};
          reportData.data.forEach(item => {
            if (!dataByType[item.dataPoint]) {
              dataByType[item.dataPoint] = [];
            }
            dataByType[item.dataPoint].push(item);
          });

          if (Object.keys(dataByType).length > 0) {
            setSelectedDataType(Object.keys(dataByType)[0]);
          }
        }
      } catch {
        toast.error("Failed to load report");
      } finally {
        setLoading(false);
      }
    };

    fetchReport();
  }, [reportId]);

  const handleExport = async (format: 'pdf' | 'excel') => {
    setExportLoading(true);
    try {
      const result = await exportReport(reportId, format);
      if (result && result.fileUrl) {
        window.open(result.fileUrl, "_blank");
        toast.success(`Report exported as ${format.toUpperCase()}`);
      }
    } catch {
      toast.error(`Failed to export as ${format}`);
    } finally {
      setExportLoading(false);
    }
  };

  const formatData = (reportData: ReportDataItem[]) => {
    const categoryData = reportData.reduce((acc, item) => {
      if (item.value < 0) return acc;

      const category = item.category || 'Uncategorized';
      if (!acc[category]) {
        acc[category] = 0;
      }
      acc[category] += item.value;
      return acc;
    }, {} as Record<string, number>);

    const pieData = Object.entries(categoryData)
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      .filter(([_, value]) => value > 0)
      .map(([name, value]) => ({
        name,
        value,
      }));

    const dataByType: Record<string, ReportDataItem[]> = {};

    reportData.forEach(item => {
      if (!dataByType[item.dataPoint]) {
        dataByType[item.dataPoint] = [];
      }
      dataByType[item.dataPoint].push(item);
    });

    let bestTimeSeriesData: ReportDataItem[] = [];

    let timeSeriesPriority = [
      'monthly_income', 'monthly_expense', 'monthly_net', 'monthly_cashflow',
      'daily_sales', 'daily_expense', 'category_total', 'vendor_total'
    ];

    const reportType = report?.reportType?.toUpperCase();

    if (reportType === 'CASH_FLOW') {
      timeSeriesPriority = [
        'monthly_cashflow', 'monthly_net', 'cumulative_cashflow', 'monthly_income', 'monthly_expense'
      ];
    } else if (reportType === 'PROFIT_LOSS') {
      timeSeriesPriority = [
        'monthly_profit', 'monthly_income', 'monthly_expense', 'category_profit'
      ];
    } else if (reportType === 'SALES') {
      timeSeriesPriority = [
        'daily_sales', 'total_sales', 'average_sale'
      ];
    } else if (reportType === 'TAX') {
      timeSeriesPriority = [
        'monthly_tax_amount', 'monthly_taxable_amount', 'total_tax_amount', 'total_taxable_amount'
      ];
    } else if (reportType === 'BALANCE_SHEET') {
      timeSeriesPriority = [
        'assets', 'liabilities', 'equity'
      ];
    }

    for (const dataType of timeSeriesPriority) {
      if (dataByType[dataType] && dataByType[dataType].length > 0) {
        bestTimeSeriesData = dataByType[dataType];
        break;
      }
    }

    if (bestTimeSeriesData.length === 0) {
      bestTimeSeriesData = reportData;
    }

    const timeSeriesData = bestTimeSeriesData
      .map(item => ({
        name: item.label || '',
        value: item.value,
        category: item.category || 'Uncategorized',
      }))
      .sort((a, b) => {
        if (a.name.match(/^\d{4}-\d{2}(-\d{2})?$/) && b.name.match(/^\d{4}-\d{2}(-\d{2})?$/)) {
          return a.name.localeCompare(b.name);
        }
        return 0;
      });

    return {
      pieData,
      timeSeriesData,
      dataByType,
    };
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex h-screen items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </DashboardLayout>
    );
  }

  if (!report) {
    return (
      <DashboardLayout>
        <div className="flex h-screen flex-col items-center justify-center space-y-4">
          <h2 className="text-2xl font-bold">Report not found</h2>
          <Button onClick={() => router.push('/dashboard/reports')}>
            Back to Reports
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  const { pieData, timeSeriesData, dataByType } = report.data ? formatData(report.data) : { pieData: [], timeSeriesData: [], dataByType: {} };

  let startDateStr = '';
  let endDateStr = '';

  if (report.startDate) {
    startDateStr = new Date(report.startDate).toLocaleDateString();
  }

  if (report.endDate) {
    endDateStr = new Date(report.endDate).toLocaleDateString();
  }

  if ((!startDateStr || !endDateStr) && report.dateRange) {
    try {
      const parsedDateRange = typeof report.dateRange === 'string'
        ? JSON.parse(report.dateRange)
        : report.dateRange;

      if (parsedDateRange) {
        if (!startDateStr && parsedDateRange.startDate) {
          startDateStr = new Date(parsedDateRange.startDate).toLocaleDateString();
        }

        if (!endDateStr && parsedDateRange.endDate) {
          endDateStr = new Date(parsedDateRange.endDate).toLocaleDateString();
        }
      }
    } catch {
      // Remove all console.error statements
    }
  }

  if (!startDateStr) {
    startDateStr = 'Not specified';
  }

  if (!endDateStr) {
    endDateStr = 'Not specified';
  }

  return (
    <DashboardLayout>
      <div className="flex min-h-screen w-full flex-col">
        <header className="sticky top-0 z-10 border-b bg-background">
          <div className="flex h-16 items-center justify-between px-6">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => router.push('/dashboard/reports')}
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <h1 className="text-xl font-semibold">{report.title || report.name}</h1>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={() => handleExport('excel')}
                disabled={exportLoading}
              >
                {exportLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Download className="mr-2 h-4 w-4" />
                )}
                Export Excel
              </Button>
              <Button
                onClick={() => handleExport('pdf')}
                disabled={exportLoading}
              >
                {exportLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Share className="mr-2 h-4 w-4" />
                )}
                Export PDF
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  toast.loading("Regenerating report data...");
                  fetch(`/api/reports/${reportId}/regenerate`, {
                    method: 'POST',
                  }).then(response => {
                    if (response.ok) {
                      toast.success("Report data regenerated. Refreshing...");
                      setTimeout(() => {
                        window.location.reload();
                      }, 1000);
                    } else {
                      toast.error("Failed to regenerate report data");
                    }
                  }).catch(() => {
                    toast.error("Error regenerating report data");
                  });
                }}
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Regenerate
              </Button>
            </div>
          </div>
        </header>

        <main className="flex-1 space-y-6 p-6 md:p-8">
          <Card className="overflow-hidden border-none bg-gradient-to-br from-slate-50 to-gray-50 shadow-md dark:from-slate-950/30 dark:to-gray-950/30">
            <CardHeader className="border-b bg-white/50 pb-4 dark:bg-white/5">
              <CardTitle className="flex items-center gap-2 text-slate-800 dark:text-slate-200">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-clipboard-list">
                  <rect width="8" height="4" x="8" y="2" rx="1" ry="1"></rect>
                  <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                  <path d="M12 11h4"></path>
                  <path d="M12 16h4"></path>
                  <path d="M8 11h.01"></path>
                  <path d="M8 16h.01"></path>
                </svg>
                Report Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6 p-6">
              <div className="grid gap-6 sm:grid-cols-3">
                <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-slate-900/50">
                  <div className="mb-2 flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-500 dark:text-blue-400">
                      <path d="M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z"></path>
                      <path d="M13 5v2"></path>
                      <path d="M13 17v2"></path>
                      <path d="M13 11v2"></path>
                    </svg>
                    <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Report Type</p>
                  </div>
                  <p className="text-lg font-semibold text-slate-800 dark:text-slate-200">
                    {(report.reportType || report.type || 'Unknown').replace(/_/g, ' ')}
                  </p>
                </div>
                <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-slate-900/50">
                  <div className="mb-2 flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-purple-500 dark:text-purple-400">
                      <rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect>
                      <line x1="16" x2="16" y1="2" y2="6"></line>
                      <line x1="8" x2="8" y1="2" y2="6"></line>
                      <line x1="3" x2="21" y1="10" y2="10"></line>
                    </svg>
                    <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Date Range</p>
                  </div>
                  <p className="text-lg font-semibold text-slate-800 dark:text-slate-200">{startDateStr} - {endDateStr}</p>
                </div>
                <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-slate-900/50">
                  <div className="mb-2 flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-emerald-500 dark:text-emerald-400">
                      <circle cx="12" cy="12" r="10"></circle>
                      <polyline points="12 6 12 12 16 14"></polyline>
                    </svg>
                    <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Created</p>
                  </div>
                  <p className="text-lg font-semibold text-slate-800 dark:text-slate-200">
                    {new Date(report.createdAt).toLocaleString()}
                  </p>
                </div>
              </div>
              {report.description && (
                <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-slate-900/50">
                  <div className="mb-2 flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-amber-500 dark:text-amber-400">
                      <path d="M14 9a2 2 0 0 1-2 2H6l-4 4V4c0-1.1.9-2 2-2h8a2 2 0 0 1 2 2v5Z"></path>
                      <path d="M18 9h2a2 2 0 0 1 2 2v11l-4-4h-6a2 2 0 0 1-2-2v-1"></path>
                    </svg>
                    <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Description</p>
                  </div>
                  <p className="text-slate-700 dark:text-slate-300">{report.description}</p>
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="overflow-hidden border-none bg-white shadow-md dark:bg-slate-900">
            <CardHeader className="border-b bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30">
              <CardTitle className="flex items-center gap-2 text-slate-800 dark:text-slate-200">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-bar-chart-3">
                  <path d="M3 3v18h18"></path>
                  <path d="M18 17V9"></path>
                  <path d="M13 17V5"></path>
                  <path d="M8 17v-3"></path>
                </svg>
                Report Visualization
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <Tabs defaultValue={getVisualizationType(report)} className="space-y-6">
                <TabsList className="tabs-container grid w-full grid-cols-3 rounded-lg p-1">
                  <TabsTrigger value="bar" className="tab-trigger flex items-center gap-2 rounded-md">
                    <BarChart className="h-4 w-4" />
                    Bar Chart
                  </TabsTrigger>
                  <TabsTrigger value="line" className="tab-trigger flex items-center gap-2 rounded-md">
                    <LineChart className="h-4 w-4" />
                    Line Chart
                  </TabsTrigger>
                  <TabsTrigger value="pie" className="tab-trigger flex items-center gap-2 rounded-md">
                    <PieChart className="h-4 w-4" />
                    Pie Chart
                  </TabsTrigger>
                </TabsList>

                <div className="h-[400px] w-full">
                  <TabsContent value="bar" className="h-full">
                    {report.data && report.data.length > 0 ? (
                      <div className="h-full w-full">
                        <BarChartComponent
                          data={timeSeriesData}
                          x="name"
                          y="value"
                          xLabel={getXAxisLabel(report.reportType)}
                          yLabel={getYAxisLabel(report.reportType)}
                          showLegend={true}
                          gradientBars={true}
                          showValues={true}
                          animationDuration={1200}
                          roundedBars={true}
                        />
                      </div>
                    ) : (
                      <div className="flex h-full items-center justify-center">
                        <p className="text-muted-foreground">No data available</p>
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="line" className="h-full">
                    {report.data && report.data.length > 0 ? (
                      <div className="h-full w-full">
                        <LineChartComponent
                          data={timeSeriesData}
                          x="name"
                          y="value"
                          xLabel={getXAxisLabel(report.reportType)}
                          yLabel={getYAxisLabel(report.reportType)}
                          showLegend={true}
                          showDots={true}
                          fillArea={true}
                          gradientFill={true}
                          animationDuration={1500}
                          curveType="monotone"
                          strokeWidth={3}
                          showReferenceLine={shouldShowReferenceLine(report.reportType)}
                          referenceLineValue={getReferenceLineValue(report.data)}
                          referenceLineLabel={getReferenceLineLabel(report.reportType)}
                        />
                      </div>
                    ) : (
                      <div className="flex h-full items-center justify-center">
                        <p className="text-muted-foreground">No data available</p>
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="pie" className="h-full">
                    {report.data && report.data.length > 0 ? (
                      <div className="h-full w-full">
                        <PieChartComponent
                          data={pieData}
                          nameKey="name"
                          valueKey="value"
                          donut={true}
                          showLegend={true}
                          paddingAngle={3}
                          gradientColors={true}
                          showActiveShape={true}
                          showTotal={true}
                          totalLabel={getTotalLabel(report.reportType)}
                          animationDuration={1200}
                          outerRadius={130}
                        />
                      </div>
                    ) : (
                      <div className="flex h-full items-center justify-center">
                        <p className="text-muted-foreground">No data available</p>
                      </div>
                    )}
                  </TabsContent>
                </div>
              </Tabs>
            </CardContent>
          </Card>

          {report.data && report.data.length > 0 ? (
            <Card className="overflow-hidden border-none bg-white shadow-md dark:bg-slate-900">
              <CardHeader className="border-b bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950/30 dark:to-pink-950/30">
                <CardTitle className="flex items-center gap-2 text-slate-800 dark:text-slate-200">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-table">
                    <path d="M12 3v18"></path>
                    <rect width="18" height="18" x="3" y="3" rx="2"></rect>
                    <path d="M3 9h18"></path>
                    <path d="M3 15h18"></path>
                  </svg>
                  Data Points
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <Tabs defaultValue="all" className="space-y-6">
                  <TabsList className="tabs-container inline-flex rounded-lg p-1">
                    <TabsTrigger value="all" className="tab-trigger rounded-md">All Data</TabsTrigger>
                    <TabsTrigger value="summary" className="tab-trigger rounded-md">Summary</TabsTrigger>
                    {Object.keys(dataByType).length > 0 && (
                      <TabsTrigger value="byType" className="tab-trigger rounded-md">By Type</TabsTrigger>
                    )}
                  </TabsList>

                  <TabsContent value="all">
                    <div className="overflow-x-auto rounded-lg border shadow-sm">
                      <table className="w-full table-auto">
                        <thead>
                          <tr className="bg-muted/50">
                            <th className="px-4 py-3 text-left font-medium text-muted-foreground">Label</th>
                            <th className="px-4 py-3 text-left font-medium text-muted-foreground">Category</th>
                            <th className="px-4 py-3 text-left font-medium text-muted-foreground">Data Point</th>
                            <th className="px-4 py-3 text-right font-medium text-muted-foreground">Value</th>
                          </tr>
                        </thead>
                        <tbody>
                          {report.data?.map((item: ReportDataItem, index: number) => (
                            <tr
                              key={index}
                              className={`border-b transition-colors hover:bg-muted/20 ${index % 2 === 0 ? 'bg-background' : 'bg-muted/10'}`}
                            >
                              <td className="px-4 py-3 text-sm">{item.label || '-'}</td>
                              <td className="px-4 py-3 text-sm">
                                <span className="inline-flex items-center rounded-full bg-primary/10 px-2.5 py-0.5 text-xs font-medium text-primary">
                                  {item.category || 'Uncategorized'}
                                </span>
                              </td>
                              <td className="px-4 py-3 text-sm font-medium">{formatDataPoint(item.dataPoint)}</td>
                              <td className="px-4 py-3 text-right text-sm font-semibold">
                                {formatValue(item.value, item.dataPoint)}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </TabsContent>

                  <TabsContent value="summary">
                    <div className="space-y-4">
                      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                        <Card className="overflow-hidden border-none bg-gradient-to-br from-blue-50 to-indigo-50 shadow-md dark:from-blue-950/30 dark:to-indigo-950/30">
                          <CardHeader className="pb-2">
                            <CardTitle className="flex items-center gap-2 text-sm font-medium text-blue-700 dark:text-blue-300">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-bar-chart-2">
                                <line x1="18" x2="18" y1="20" y2="10"></line>
                                <line x1="12" x2="12" y1="20" y2="4"></line>
                                <line x1="6" x2="6" y1="20" y2="14"></line>
                              </svg>
                              Total Data Points
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <p className="text-3xl font-bold text-blue-700 dark:text-blue-300">{report.data.length}</p>
                            <p className="mt-1 text-xs text-blue-600/70 dark:text-blue-400/70">Data points in this report</p>
                          </CardContent>
                        </Card>

                        <Card className="overflow-hidden border-none bg-gradient-to-br from-purple-50 to-pink-50 shadow-md dark:from-purple-950/30 dark:to-pink-950/30">
                          <CardHeader className="pb-2">
                            <CardTitle className="flex items-center gap-2 text-sm font-medium text-purple-700 dark:text-purple-300">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-layers">
                                <polygon points="12 2 2 7 12 12 22 7 12 2"></polygon>
                                <polyline points="2 17 12 22 22 17"></polyline>
                                <polyline points="2 12 12 17 22 12"></polyline>
                              </svg>
                              Data Types
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <p className="text-3xl font-bold text-purple-700 dark:text-purple-300">{Object.keys(dataByType).length}</p>
                            <p className="mt-1 text-xs text-purple-600/70 dark:text-purple-400/70">Different types of data metrics</p>
                          </CardContent>
                        </Card>

                        <Card className="overflow-hidden border-none bg-gradient-to-br from-emerald-50 to-teal-50 shadow-md dark:from-emerald-950/30 dark:to-teal-950/30">
                          <CardHeader className="pb-2">
                            <CardTitle className="flex items-center gap-2 text-sm font-medium text-emerald-700 dark:text-emerald-300">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-tag">
                                <path d="M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2Z"></path>
                                <path d="M7 7h.01"></path>
                              </svg>
                              Categories
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <p className="text-3xl font-bold text-emerald-700 dark:text-emerald-300">
                              {new Set(report.data.map(item => item.category || 'Uncategorized')).size}
                            </p>
                            <p className="mt-1 text-xs text-emerald-600/70 dark:text-emerald-400/70">Unique categories in the data</p>
                          </CardContent>
                        </Card>
                      </div>

                      <div className="mt-6">
                        <h3 className="mb-3 flex items-center gap-2 font-medium text-muted-foreground">
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-list-filter">
                            <path d="M3 6h18"></path>
                            <path d="M7 12h10"></path>
                            <path d="M10 18h4"></path>
                          </svg>
                          Data Point Types
                        </h3>
                        <ul className="grid gap-2 sm:grid-cols-2 lg:grid-cols-3">
                          {Object.entries(dataByType).map(([type, items]) => (
                            <li key={type} className="rounded-md border bg-card p-3 shadow-sm transition-all hover:shadow-md">
                              <div className="flex items-center justify-between">
                                <span className="font-medium text-primary">{formatDataPoint(type)}</span>
                                <span className="rounded-full bg-primary/10 px-2 py-1 text-xs font-semibold text-primary">
                                  {items.length} items
                                </span>
                              </div>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </TabsContent>

                  {Object.keys(dataByType).length > 0 && (
                    <TabsContent value="byType">
                      <div className="space-y-4">
                        <div className="mb-6 flex flex-col space-y-2">
                          <label className="text-sm font-medium text-muted-foreground">Select Data Type</label>
                          <Select
                            value={selectedDataType}
                            onValueChange={setSelectedDataType}
                          >
                            <SelectTrigger className="w-full bg-card shadow-sm">
                              <SelectValue placeholder="Select data type" />
                            </SelectTrigger>
                            <SelectContent>
                              {Object.keys(dataByType).map(type => (
                                <SelectItem key={type} value={type}>{formatDataPoint(type)}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        {selectedDataType && dataByType[selectedDataType] && (
                          <div className="data-type-section mt-6 rounded-lg border bg-card/50 p-4 shadow-sm">
                            <h3 className="mb-4 flex items-center gap-2 text-lg font-medium text-primary">
                              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-database">
                                <ellipse cx="12" cy="5" rx="9" ry="3"></ellipse>
                                <path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"></path>
                                <path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"></path>
                              </svg>
                              {formatDataPoint(selectedDataType)}
                              <span className="ml-2 rounded-full bg-primary/10 px-2.5 py-0.5 text-sm font-medium text-primary">
                                {dataByType[selectedDataType].length} items
                              </span>
                            </h3>
                            <div className="overflow-x-auto rounded-lg border shadow-sm">
                              <table className="w-full table-auto">
                                <thead>
                                  <tr className="bg-muted/50">
                                    <th className="px-4 py-3 text-left font-medium text-muted-foreground">Label</th>
                                    <th className="px-4 py-3 text-left font-medium text-muted-foreground">Category</th>
                                    <th className="px-4 py-3 text-right font-medium text-muted-foreground">Value</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {dataByType[selectedDataType].map((item, index) => (
                                    <tr
                                      key={index}
                                      className={`border-b transition-colors hover:bg-muted/20 ${index % 2 === 0 ? 'bg-background' : 'bg-muted/10'}`}
                                    >
                                      <td className="px-4 py-3 text-sm">{item.label || '-'}</td>
                                      <td className="px-4 py-3 text-sm">
                                        <span className="inline-flex items-center rounded-full bg-primary/10 px-2.5 py-0.5 text-xs font-medium text-primary">
                                          {item.category || 'Uncategorized'}
                                        </span>
                                      </td>
                                      <td className="px-4 py-3 text-right text-sm font-semibold">
                                        {formatValue(item.value, item.dataPoint)}
                                      </td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        )}
                      </div>
                    </TabsContent>
                  )}
                </Tabs>
              </CardContent>
            </Card>
          ) : (
            <Card className="overflow-hidden border-none bg-gradient-to-br from-gray-50 to-slate-50 shadow-md dark:from-gray-950/30 dark:to-slate-950/30">
              <CardHeader className="border-b bg-white/50 dark:bg-white/5">
                <CardTitle className="flex items-center gap-2 text-slate-700 dark:text-slate-300">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-alert-circle">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="12" x2="12" y1="8" y2="12"></line>
                    <line x1="12" x2="12.01" y1="16" y2="16"></line>
                  </svg>
                  No Data Available
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="flex flex-col items-center justify-center space-y-4 text-center">
                  <div className="rounded-full bg-slate-100 p-4 dark:bg-slate-800">
                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" className="text-slate-500 dark:text-slate-400">
                      <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                      <line x1="9" y1="10" x2="15" y2="10"></line>
                      <line x1="12" y1="7" x2="12" y2="13"></line>
                    </svg>
                  </div>
                  <div className="max-w-md">
                    <p className="mb-4 text-slate-600 dark:text-slate-400">
                      This report doesn&apos;t contain any data points. Try regenerating the report or selecting a different date range.
                    </p>
                    <Button
                      className="mt-2 gap-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 dark:from-blue-700 dark:to-indigo-700 dark:hover:from-blue-800 dark:hover:to-indigo-800"
                      onClick={() => {
                        toast.loading("Regenerating report data...");
                        fetch(`/api/reports/${reportId}/regenerate`, {
                          method: 'POST',
                        }).then(response => {
                          if (response.ok) {
                            toast.success("Report data regenerated. Refreshing...");
                            setTimeout(() => {
                              window.location.reload();
                            }, 1000);
                          } else {
                            toast.error("Failed to regenerate report data");
                          }
                        }).catch(() => {
                          toast.error("Error regenerating report data");
                        });
                      }}
                    >
                      <RefreshCw className="h-4 w-4" />
                      Regenerate Report Data
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </main>
      </div>
    </DashboardLayout>
  );
}