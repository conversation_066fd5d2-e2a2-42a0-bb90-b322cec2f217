import { NextResponse } from 'next/server';
import { currentUser } from '@clerk/nextjs/server';
import db from '@/db/db';

export async function GET() {
  try {
    const user = await currentUser();
    
    if (!user || !user.emailAddresses || user.emailAddresses.length === 0) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }
    
    const email = user.emailAddresses[0].emailAddress;
    
    // Try to find the user in the database
    let dbUser = await db.user.findUnique({
      where: { email }
    });
    
    // If user doesn't exist in the database, create it
    if (!dbUser) {
      dbUser = await db.user.create({
        data: {
          email,
          clerkId: user.id,
        }
      });
    }
    
    return NextResponse.json({
      id: dbUser.id,
      email: dbUser.email,
      clerkId: user.id,
    });
  } catch (error) {
    console.error('Error getting current user:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
