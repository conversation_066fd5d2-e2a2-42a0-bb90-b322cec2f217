'use client';

import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ChartContainer, ChartTooltip } from '@/components/ui/chart';
import {
  CartesianGrid,
  XAxis,
  YAxis,
  ResponsiveContainer,
  Line,
  LineChart,
} from 'recharts';
import {
  Brain,
  TrendingUp,
  AlertTriangle,
  Lightbulb,
  Target,
  DollarSign,
  Activity,
  Clock,
  Sparkles,
  Shield,
  Rocket,
  Wallet,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import {
  getFinancialMetrics,
  getRevenueAnalysis,
  getCashFlowAnalysis,
} from '@/lib/actions/analytics';
import type { FilterValues } from './data-filters';
import type { FinancialMetrics } from '@/types/analytics';
import { formatCurrency } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';
import { motion } from 'motion/react';

interface AIInsightsProps {
  dateRange: {
    from: Date;
    to: Date;
  };
  filters: FilterValues;
}

interface AIInsight {
  id: string;
  type:
    | 'PREDICTION'
    | 'RECOMMENDATION'
    | 'ALERT'
    | 'OPPORTUNITY'
    | 'RISK';
  title: string;
  description: string;
  impact: 'HIGH' | 'MEDIUM' | 'LOW';
  confidence: number;
  actionable: boolean;
  estimatedValue?: number;
  timeframe?: string;
  category:
    | 'REVENUE'
    | 'EXPENSES'
    | 'CASH_FLOW'
    | 'PROFITABILITY'
    | 'RISK'
    | 'GROWTH';
}

interface FinancialHealthScore {
  overall: number;
  revenue: number;
  expenses: number;
  cashFlow: number;
  profitability: number;
  risk: number;
}

interface PredictionData {
  period: string;
  predicted: number;
  confidence: number;
  actual?: number;
}

export function AIInsights({ dateRange, filters }: AIInsightsProps) {
  const [financialData, setFinancialData] =
    useState<FinancialMetrics | null>(null);
  const [insights, setInsights] = useState<AIInsight[]>([]);
  const [healthScore, setHealthScore] =
    useState<FinancialHealthScore | null>(null);
  const [predictions, setPredictions] = useState<PredictionData[]>(
    []
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function loadAIInsights() {
      setLoading(true);
      setError(null);
      try {
        // Load all financial data for AI analysis
        const [financial] = await Promise.all([
          getFinancialMetrics(dateRange.from, dateRange.to, filters),
          getRevenueAnalysis(dateRange.from, dateRange.to, filters),
          getCashFlowAnalysis(dateRange.from, dateRange.to, filters),
        ]);

        setFinancialData(financial);

        // Generate AI insights based on real data
        const generatedInsights = generateAIInsights(financial);
        setInsights(generatedInsights);

        // Calculate financial health score
        const healthScore = calculateFinancialHealth(financial);
        setHealthScore(healthScore);

        // Generate predictions
        const predictions = generatePredictions(financial);
        setPredictions(predictions);
      } catch {
        setError(
          'Unable to generate AI insights. Please try again later.'
        );
      } finally {
        setLoading(false);
      }
    }

    loadAIInsights();
  }, [dateRange, filters]);

  // AI Insight Generation Functions
  function generateAIInsights(
    financial: FinancialMetrics
  ): AIInsight[] {
    const insights: AIInsight[] = [];

    // Revenue Growth Opportunities
    if (financial.monthOverMonthRevenue < 5) {
      insights.push({
        id: 'revenue-growth',
        type: 'OPPORTUNITY',
        title: 'Revenue Growth Opportunity',
        description: `Your revenue growth is ${financial.monthOverMonthRevenue.toFixed(1)}%. Consider expanding marketing efforts or introducing new services to accelerate growth.`,
        impact: 'HIGH',
        confidence: 85,
        actionable: true,
        estimatedValue: financial.totalRevenue * 0.15,
        timeframe: '3-6 months',
        category: 'REVENUE',
      });
    }

    // Cash Flow Optimization
    if (financial.cashFlow < financial.totalRevenue * 0.1) {
      insights.push({
        id: 'cash-flow-optimization',
        type: 'RECOMMENDATION',
        title: 'Improve Cash Flow Management',
        description:
          'Your cash flow could be optimized. Consider implementing faster payment terms or offering early payment discounts.',
        impact: 'HIGH',
        confidence: 90,
        actionable: true,
        estimatedValue: financial.totalRevenue * 0.05,
        timeframe: '1-2 months',
        category: 'CASH_FLOW',
      });
    }

    // Expense Reduction Opportunities
    if (financial.monthOverMonthExpenses > 10) {
      insights.push({
        id: 'expense-reduction',
        type: 'ALERT',
        title: 'Rising Expense Trend Detected',
        description: `Expenses increased by ${financial.monthOverMonthExpenses.toFixed(1)}% this month. Review recurring costs and negotiate better rates with suppliers.`,
        impact: 'MEDIUM',
        confidence: 95,
        actionable: true,
        estimatedValue: financial.totalExpenses * 0.1,
        timeframe: '1 month',
        category: 'EXPENSES',
      });
    }

    // Profitability Analysis
    if (financial.grossMargin < 30) {
      insights.push({
        id: 'profitability-improvement',
        type: 'RECOMMENDATION',
        title: 'Enhance Profit Margins',
        description: `Your gross margin is ${financial.grossMargin.toFixed(1)}%. Consider raising prices or reducing direct costs to improve profitability.`,
        impact: 'HIGH',
        confidence: 80,
        actionable: true,
        estimatedValue: financial.totalRevenue * 0.1,
        timeframe: '2-3 months',
        category: 'PROFITABILITY',
      });
    }

    // Payment Terms Optimization
    if (financial.averageDaysToPayment > 45) {
      insights.push({
        id: 'payment-terms',
        type: 'RECOMMENDATION',
        title: 'Optimize Payment Terms',
        description: `Average payment time is ${financial.averageDaysToPayment} days. Implement stricter payment terms and follow-up procedures.`,
        impact: 'MEDIUM',
        confidence: 85,
        actionable: true,
        timeframe: '1 month',
        category: 'CASH_FLOW',
      });
    }

    return insights;
  }

  function calculateFinancialHealth(
    financial: FinancialMetrics
  ): FinancialHealthScore {
    // Revenue health (0-100)
    const revenueScore = Math.min(
      100,
      Math.max(0, (financial.monthOverMonthRevenue + 50) * 2)
    );

    // Expense health (0-100) - lower expense growth is better
    const expenseScore = Math.min(
      100,
      Math.max(0, 100 - financial.monthOverMonthExpenses * 5)
    );

    // Cash flow health (0-100)
    const cashFlowScore = Math.min(
      100,
      Math.max(
        0,
        financial.cashFlow > 0
          ? 80 + (financial.cashFlow / financial.totalRevenue) * 100
          : 20
      )
    );

    // Profitability health (0-100)
    const profitabilityScore = Math.min(
      100,
      Math.max(0, financial.grossMargin * 2)
    );

    // Risk assessment (0-100) - lower is better, so we invert
    const riskScore = Math.min(
      100,
      Math.max(
        0,
        100 -
          (financial.overdueInvoiceCount / financial.totalInvoices) *
            100
      )
    );

    // Overall score
    const overall =
      (revenueScore +
        expenseScore +
        cashFlowScore +
        profitabilityScore +
        riskScore) /
      5;

    return {
      overall: Math.round(overall),
      revenue: Math.round(revenueScore),
      expenses: Math.round(expenseScore),
      cashFlow: Math.round(cashFlowScore),
      profitability: Math.round(profitabilityScore),
      risk: Math.round(riskScore),
    };
  }

  function generatePredictions(
    financial: FinancialMetrics
  ): PredictionData[] {
    const predictions: PredictionData[] = [];

    // Generate next 6 months predictions based on trends
    const currentDate = new Date();
    for (let i = 1; i <= 6; i++) {
      const futureDate = new Date(currentDate);
      futureDate.setMonth(futureDate.getMonth() + i);

      // Simple trend-based prediction
      const growthFactor = 1 + financial.monthOverMonthRevenue / 100;
      const predictedRevenue =
        financial.totalRevenue * Math.pow(growthFactor, i);

      predictions.push({
        period: futureDate.toLocaleDateString('en-US', {
          month: 'short',
          year: 'numeric',
        }),
        predicted: predictedRevenue,
        confidence: Math.max(60, 95 - i * 5), // Confidence decreases over time
      });
    }

    return predictions;
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gradient-to-r from-purple-500 to-blue-500">
              <Brain className="h-6 w-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold">
                AI Financial Intelligence
              </h2>
              <p className="text-muted-foreground">
                Advanced insights powered by artificial intelligence
              </p>
            </div>
          </div>
          <div className="text-sm text-muted-foreground animate-pulse">
            Analyzing your financial data...
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Card
              key={i}
              className="border border-primary/10 bg-card/50 backdrop-blur-sm shadow-lg"
            >
              <CardContent className="p-6">
                <Skeleton className="h-4 w-3/4 mb-3" />
                <Skeleton className="h-3 w-full mb-2" />
                <Skeleton className="h-3 w-5/6" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="border-destructive/50 bg-destructive/5">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Error Loading AI Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p>{error}</p>
          <p className="mt-2 text-sm text-muted-foreground">
            You can still explore your financial data through the
            other tabs.
          </p>
        </CardContent>
      </Card>
    );
  }

  if (!financialData || !healthScore) {
    return (
      <div className="grid place-items-center h-64 bg-card/50 backdrop-blur-sm rounded-lg border border-primary/10 shadow-lg">
        <div className="text-center p-6">
          <Brain className="h-12 w-12 text-primary/40 mx-auto mb-4" />
          <p className="text-lg font-medium text-foreground">
            Insufficient data for AI analysis
          </p>
          <p className="text-sm text-muted-foreground mt-1">
            Upload more invoices to unlock powerful AI insights and
            predictions.
          </p>
        </div>
      </div>
    );
  }

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'PREDICTION':
        return <Sparkles className="h-5 w-5" />;
      case 'RECOMMENDATION':
        return <Lightbulb className="h-5 w-5" />;
      case 'ALERT':
        return <AlertTriangle className="h-5 w-5" />;
      case 'OPPORTUNITY':
        return <Target className="h-5 w-5" />;
      case 'RISK':
        return <Shield className="h-5 w-5" />;
      default:
        return <Brain className="h-5 w-5" />;
    }
  };

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'PREDICTION':
        return 'from-purple-500 to-blue-500';
      case 'RECOMMENDATION':
        return 'from-green-500 to-emerald-500';
      case 'ALERT':
        return 'from-red-500 to-orange-500';
      case 'OPPORTUNITY':
        return 'from-blue-500 to-cyan-500';
      case 'RISK':
        return 'from-orange-500 to-red-500';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-gradient-to-r from-purple-500 to-blue-500">
            <Brain className="h-6 w-6 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold">
              AI Financial Intelligence
            </h2>
            <p className="text-muted-foreground">
              Advanced insights powered by artificial intelligence
            </p>
          </div>
        </div>
        <Badge variant="outline" className="text-xs">
          Analysis Period: {dateRange.from.toLocaleDateString()} -{' '}
          {dateRange.to.toLocaleDateString()}
        </Badge>
      </div>

      {/* Financial Health Score */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card className="border border-primary/10 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-primary" />
              Financial Health Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-2">
              <div className="space-y-4">
                <div className="text-center">
                  <div
                    className={`text-6xl font-bold ${getHealthScoreColor(healthScore.overall)}`}
                  >
                    {healthScore.overall}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Overall Health Score
                  </p>
                </div>
                <div className="space-y-3">
                  {[
                    {
                      label: 'Revenue',
                      score: healthScore.revenue,
                      icon: <TrendingUp className="h-4 w-4" />,
                    },
                    {
                      label: 'Expenses',
                      score: healthScore.expenses,
                      icon: <DollarSign className="h-4 w-4" />,
                    },
                    {
                      label: 'Cash Flow',
                      score: healthScore.cashFlow,
                      icon: <Wallet className="h-4 w-4" />,
                    },
                    {
                      label: 'Profitability',
                      score: healthScore.profitability,
                      icon: <Target className="h-4 w-4" />,
                    },
                    {
                      label: 'Risk',
                      score: healthScore.risk,
                      icon: <Shield className="h-4 w-4" />,
                    },
                  ].map((item) => (
                    <div
                      key={item.label}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center gap-2">
                        {item.icon}
                        <span className="text-sm font-medium">
                          {item.label}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-20 h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className={`h-full bg-gradient-to-r ${
                              item.score >= 80
                                ? 'from-green-400 to-green-600'
                                : item.score >= 60
                                  ? 'from-yellow-400 to-yellow-600'
                                  : 'from-red-400 to-red-600'
                            }`}
                            style={{ width: `${item.score}%` }}
                          />
                        </div>
                        <span
                          className={`text-sm font-bold ${getHealthScoreColor(item.score)}`}
                        >
                          {item.score}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Revenue Prediction Chart */}
              <div>
                <h4 className="font-semibold mb-4 flex items-center gap-2">
                  <Rocket className="h-4 w-4" />
                  Revenue Predictions (Next 6 Months)
                </h4>
                <ChartContainer config={{}} className="h-[200px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={predictions}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="period" />
                      <YAxis
                        tickFormatter={(value) =>
                          formatCurrency(value)
                        }
                      />
                      <ChartTooltip
                        formatter={(value) => [
                          formatCurrency(value as number),
                          'Predicted Revenue',
                        ]}
                      />
                      <Line
                        type="monotone"
                        dataKey="predicted"
                        stroke="#8884d8"
                        strokeWidth={2}
                        dot={{ fill: '#8884d8' }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* AI Insights Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {insights.map((insight, index) => (
          <motion.div
            key={insight.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <Card className="border border-primary/10 bg-card/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
              <div
                className={`h-1 w-full bg-gradient-to-r ${getInsightColor(insight.type)}`}
              />
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div
                    className={`p-3 rounded-lg bg-gradient-to-r ${getInsightColor(insight.type)} text-white`}
                  >
                    {getInsightIcon(insight.type)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className="font-semibold text-lg">
                        {insight.title}
                      </h4>
                      <Badge
                        variant={
                          insight.impact === 'HIGH'
                            ? 'destructive'
                            : insight.impact === 'MEDIUM'
                              ? 'default'
                              : 'secondary'
                        }
                        className="text-xs"
                      >
                        {insight.impact}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">
                      {insight.description}
                    </p>
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>Confidence: {insight.confidence}%</span>
                      {insight.estimatedValue && (
                        <span className="font-medium text-green-600">
                          {formatCurrency(insight.estimatedValue)}
                        </span>
                      )}
                    </div>
                    {insight.timeframe && (
                      <div className="mt-2 flex items-center gap-1 text-xs text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        <span>{insight.timeframe}</span>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  );
}
