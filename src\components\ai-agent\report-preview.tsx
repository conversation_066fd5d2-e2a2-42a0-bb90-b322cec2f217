'use client';

import { memo, MouseEvent, useCallback, useEffect, useRef, RefObject, Dispatch, SetStateAction } from 'react';
import { FileIcon, LoaderIcon, DownloadIcon } from './icons';
import { useArtifact } from '@/hooks/use-artifact';
import { UIArtifact } from '@/components/ai-agent/artifact';
import { ReportToolCall, ReportToolResult } from './report';

export interface ReportPreviewProps {
  isReadonly?: boolean;
  result?: {
    id: string;
    title: string;
    reportType: string;
    format: string;
    fileUrl?: string;
  };
  args?: {
    title: string;
    reportType: string;
  };
  progress?: {
    step: number;
    totalSteps: number;
    message: string;
    percentage: number;
  };
}

export function ReportPreview({
  isReadonly,
  result,
  args,
  progress,
}: ReportPreviewProps) {
  const { artifact, setArtifact } = useArtifact();
  const hitboxRef = useRef<HTMLDivElement>(null);

  // Automatically open the preview when a report is generated
  useEffect(() => {
    // Only open automatically if:
    // 1. We have a result
    // 2. The preview is not already visible
    // 3. The user hasn't manually closed it
    if (result && !artifact.isVisible && !artifact.manuallyClosed) {
      // Set a small delay to ensure the UI has updated
      const timer = setTimeout(() => {
        setArtifact(currentArtifact => ({
          ...currentArtifact,
          reportId: result.id,
          title: result.title,
          reportType: result.reportType,
          format: result.format,
          fileUrl: result.fileUrl,
          isVisible: true,
          isMinimized: false,
          status: 'idle',
          content: '',
          kind: 'text',
          documentId: '',
          boundingBox: {
            top: 0,
            left: 0,
            width: 0,
            height: 0,
          },
        }));
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [result, artifact.isVisible, artifact.manuallyClosed, setArtifact]);

  useEffect(() => {
    const boundingBox = hitboxRef.current?.getBoundingClientRect();

    if (artifact.reportId && boundingBox) {
      setArtifact((artifact) => ({
        ...artifact,
        boundingBox: {
          left: boundingBox.x,
          top: boundingBox.y,
          width: boundingBox.width,
          height: boundingBox.height,
        },
      }));
    }
  }, [artifact.reportId, setArtifact]);

  if (artifact.isVisible) {
    if (result) {
      return (
        <ReportToolResult
          type="create"
          result={{
            id: result.id,
            title: result.title,
            reportType: result.reportType,
            format: result.format,
            fileUrl: result.fileUrl
          }}
          isReadonly={isReadonly}
        />
      );
    }

    if (args) {
      return (
        <ReportToolCall
          type="create"
          args={{
            title: args.title,
            reportType: args.reportType
          }}
          progress={progress}
          isReadonly={isReadonly}
        />
      );
    }
  }

  // Show progress if we're generating a report
  if (artifact.status === 'streaming' && progress) {
    return (
      <div className="flex flex-col space-y-2 p-4 border rounded-lg bg-background">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <LoaderIcon className="animate-spin" />
            <h3 className="text-sm font-medium">{args?.title || 'Generating Report...'}</h3>
          </div>
          <span className="text-xs text-muted-foreground">{progress.percentage}%</span>
        </div>
        <div className="w-full bg-secondary rounded-full h-2">
          <div
            className="bg-primary h-2 rounded-full transition-all duration-300 ease-in-out"
            style={{ width: `${progress.percentage}%` }}
          />
        </div>
        <p className="text-xs text-muted-foreground">{progress.message}</p>
        <HitboxLayer hitboxRef={hitboxRef} result={result} setArtifact={setArtifact} />
      </div>
    );
  }

  // Check if we have a minimized report
  if (artifact.isMinimized && artifact.reportId) {
    return (
      <div className="flex items-center p-2 border rounded-lg bg-background cursor-pointer"
        onClick={() => {
          setArtifact((currentArtifact) => ({
            ...currentArtifact,
            isVisible: true,
            isMinimized: false,
            manuallyClosed: false, // Reset the manually closed flag
          }));
        }}>
        <div className="flex items-center space-x-2">
          {artifact.format === 'PDF' ? (
            <FileIcon className="h-5 w-5 text-red-500" />
          ) : (
            <FileIcon className="h-5 w-5 text-green-500" />
          )}
          <span className="text-sm font-medium">{artifact.title}</span>
        </div>
      </div>
    );
  }

  // Regular report preview
  if (result) {
    return (
      <div className="flex flex-col space-y-2 p-4 border rounded-lg bg-background">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {result.format === 'PDF' ? (
              <FileIcon className="h-5 w-5 text-red-500" />
            ) : (
              <FileIcon className="h-5 w-5 text-green-500" />
            )}
            <h3 className="text-sm font-medium">{result.title}</h3>
          </div>
          <DownloadIcon className="h-4 w-4 text-muted-foreground" />
        </div>
        <p className="text-xs text-muted-foreground">{result.reportType} Report</p>
        <HitboxLayer hitboxRef={hitboxRef} result={result} setArtifact={setArtifact} />
      </div>
    );
  }

  return null;
}

interface HitboxLayerProps {
  hitboxRef: RefObject<HTMLDivElement | null>;
  result: ReportPreviewProps['result'];
  setArtifact: Dispatch<SetStateAction<UIArtifact>>;
}

const HitboxLayer = memo(({ hitboxRef, result, setArtifact }: HitboxLayerProps) => {
  const handleClick = useCallback(
    (event: MouseEvent<HTMLElement>) => {
      const boundingBox = event.currentTarget.getBoundingClientRect();

      setArtifact((artifact: UIArtifact) => {
        // Always reset the manually closed flag when clicking
        if (artifact.status === 'streaming') {
          return {
            ...artifact,
            isVisible: true,
            manuallyClosed: false
          };
        } else {
          return {
            ...artifact,
            title: result?.title || '',
            reportId: result?.id || '',
            reportType: result?.reportType || '',
            format: result?.format || 'PDF',
            fileUrl: result?.fileUrl || '',
            isVisible: true,
            isMinimized: false,
            manuallyClosed: false, // Reset the manually closed flag
            boundingBox: {
              left: boundingBox.x,
              top: boundingBox.y,
              width: boundingBox.width,
              height: boundingBox.height,
            },
          };
        }
      });
    },
    [setArtifact, result],
  );

  return (
    <div
      ref={hitboxRef}
      className="absolute inset-0 cursor-pointer"
      onClick={handleClick}
    />
  );
});

HitboxLayer.displayName = 'HitboxLayer';
