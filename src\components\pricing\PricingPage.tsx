'use client';

import React, { useState, useEffect } from 'react';
import {
  FileText,
  BarChart2,
  Building2,
  ArrowRight,
  Sparkles,
} from 'lucide-react';
import { initializePaddle, Paddle } from '@paddle/paddle-js';
import { useUser } from '@clerk/nextjs';
import { toast } from 'sonner';

interface PricingPageProps {
  returnUrl?: string;
  onGetStarted?: (planId: string) => void;
}

interface PlanFeature {
  text: string;
}

interface Plan {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  monthlyPrice: number;
  yearlyPrice: number;
  monthlyPriceId: string;
  yearlyPriceId: string;
  features: PlanFeature[];
  highlighted?: boolean;
}

export default function PricingPageComponent({
  returnUrl,
  onGetStarted,
}: PricingPageProps) {
  const [isMonthly, setIsMonthly] = useState(true);
  const [paddle, setPaddle] = useState<Paddle>();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [checkoutLoading, setCheckoutLoading] = useState<
    string | null
  >(null);
  const { isSignedIn } = useUser();

  useEffect(() => {
    const paddleEnv = process.env.NEXT_PUBLIC_PADDLE_ENVIRONMENT;
    const clientKey = process.env.NEXT_PUBLIC_PADDLE_CLIENT_KEY;

    console.log('Paddle Client Environment Check:', {
      environment: paddleEnv,
      hasClientKey: !!clientKey,
      clientKeyPrefix: clientKey?.substring(0, 10) + '...',
      windowLocation: typeof window !== 'undefined' ? window.location.origin : 'server',
    });

    if (!clientKey) {
      console.error('Paddle client key not configured');
      setError('Paddle client key not configured');
      setLoading(false);
      return;
    }

    console.log('Initializing Paddle client...');
    initializePaddle({
      environment: 'sandbox', // Always use sandbox for now
      token: process.env.NEXT_PUBLIC_PADDLE_CLIENT_KEY!,
    })
      .then((paddle) => {
        console.log('Paddle client initialized successfully:', paddle);
        setPaddle(paddle);
        setLoading(false);
      })
      .catch((error) => {
        console.error('Failed to initialize Paddle client:', error);
        setError('Failed to initialize payment system');
        setLoading(false);
      });
  }, []);

  // Pricing data matching the exact structure from Plans2.tsx
  const plans: Plan[] = [
    {
      id: 'starter',
      title: 'Starter',
      description:
        'Perfect for small businesses processing fewer than 100 invoices per month.',
      icon: <FileText className="w-5 h-5 sm:w-6 sm:h-6" />,
      monthlyPrice: 29,
      yearlyPrice: 290,
      monthlyPriceId: 'pri_01k12da62m0ra9fm88yz4dvr0d',
      yearlyPriceId: 'pri_01k1frnhwtja6p57ws7kd1m8vy',
      features: [
        { text: 'AI-powered data extraction' },
        { text: 'Basic invoice categorization' },
        { text: 'Up to 100 invoice uploads/month' },
        { text: 'CSV & Excel export formats' },
        { text: 'Basic financial dashboard' },
        { text: 'Email support' },
      ],
    },
    {
      id: 'business',
      title: 'Business',
      description:
        'Ideal for growing businesses with advanced analytics needs and higher volume.',
      icon: <BarChart2 className="w-5 h-5 sm:w-6 sm:h-6" />,
      monthlyPrice: 79,
      yearlyPrice: 790,
      monthlyPriceId: 'pri_01k1frpv2hfq8m2we693e53s24',
      yearlyPriceId: 'pri_01k1frqf35j3fb6pbmcdnbzear',
      features: [
        { text: 'Advanced data extraction & OCR' },
        { text: 'Custom invoice categorization' },
        { text: 'Up to 1,000 invoice uploads/month' },
        { text: 'All export formats + integrations' },
        { text: 'Full financial analytics suite' },
        { text: 'Anomaly & fraud detection' },
      ],
      highlighted: true,
    },
    {
      id: 'enterprise',
      title: 'Enterprise',
      description:
        'Comprehensive solution for large organizations with custom requirements.',
      icon: <Building2 className="w-5 h-5 sm:w-6 sm:h-6" />,
      monthlyPrice: 199,
      yearlyPrice: 1990,
      monthlyPriceId: 'pri_01jz83r7fw92rb1pb9a3qt7ty3',
      yearlyPriceId: 'pri_01jz83zhw7065b2dzyhcbx77h2',
      features: [
        { text: 'Enterprise-grade data extraction' },
        { text: 'Custom AI model training' },
        { text: 'Unlimited invoice processing' },
        { text: 'Advanced API access' },
        { text: 'Predictive financial analytics' },
        { text: 'Dedicated account manager' },
      ],
    },
  ];

  // Calculate price based on billing cycle
  const getPrice = (plan: {
    monthlyPrice: number;
    yearlyPrice: number;
  }) => {
    return isMonthly ? plan.monthlyPrice : plan.yearlyPrice;
  };

  // Apply yearly discount message if applicable
  const getPriceLabel = () => {
    return isMonthly ? '/month' : '/year';
  };

  const switchBilling = () => {
    setIsMonthly(!isMonthly);
  };

  const handleCheckout = async (priceId: string, planId: string) => {
    if (!paddle) {
      console.error('Paddle client not initialized, trying direct redirect...');
      // Try direct checkout without Paddle client
      try {
        const response = await fetch('/api/paddle/checkout', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            priceId,
            planId,
            billingCycle: isMonthly ? 'monthly' : 'yearly',
            returnUrl: returnUrl || '/dashboard',
          }),
        });

        const result = await response.json();
        if (result.success && result.data?.checkout?.url) {
          console.log('Redirecting directly to checkout URL:', result.data.checkout.url);
          window.location.href = result.data.checkout.url;
          return;
        }
      } catch (error) {
        console.error('Direct checkout failed:', error);
      }
      
      toast.error('Payment system not initialized');
      return;
    }

    if (!isSignedIn) {
      toast.error('Please sign in to subscribe');
      return;
    }

    setCheckoutLoading(planId);

    try {
      console.log('Starting checkout process for:', { priceId, planId, returnUrl });
      
      // Create checkout session on server side for better security
      const response = await fetch('/api/paddle/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priceId,
          planId,
          billingCycle: isMonthly ? 'monthly' : 'yearly',
          returnUrl: returnUrl || '/dashboard',
        }),
      });

      const result = await response.json();
      console.log('Checkout API response:', result);

      if (!result.success) {
        throw new Error(
          result.error || 'Failed to create checkout session'
        );
      }

      console.log('Checkout created successfully, attempting to open Paddle checkout...');

      // Try to open Paddle checkout with the transaction data
      try {
        console.log('Opening Paddle checkout with transaction ID:', result.data.id);
        await paddle.Checkout.open({
          transactionId: result.data.id,
          settings: {
            displayMode: 'overlay',
            theme: 'dark',
            successUrl: returnUrl
              ? `${window.location.origin}/dashboard/subscription?success=true&session_id=${result.data.id}&return_url=${encodeURIComponent(returnUrl)}`
              : `${window.location.origin}/dashboard/subscription?success=true&session_id=${result.data.id}`,
          },
        });
        console.log('Paddle checkout opened successfully');
      } catch (paddleError) {
        console.error('Paddle checkout open failed:', paddleError);
        
        // Fallback to direct URL redirect
        if (result.data?.checkout?.url) {
          console.log('Falling back to direct URL redirect:', result.data.checkout.url);
          window.location.href = result.data.checkout.url;
          return;
        }

        // If no checkout URL, try to redirect to the success URL directly
        console.log('No checkout URL available, redirecting to success URL');
        const successUrl = returnUrl
          ? `${window.location.origin}/dashboard/subscription?success=true&session_id=${result.data.id}&return_url=${encodeURIComponent(returnUrl)}`
          : `${window.location.origin}/dashboard/subscription?success=true&session_id=${result.data.id}`;
        window.location.href = successUrl;
      }
    } catch (error) {
      console.error('Checkout error:', error);
      toast.error(
        error instanceof Error
          ? error.message
          : 'Failed to start checkout'
      );
    } finally {
      setCheckoutLoading(null);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-black">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
          <span className="text-white">
            Loading payment options...
          </span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-black">
        <div className="text-center">
          <span className="text-red-500 mb-4 block">{error}</span>
          <button
            onClick={() => window.location.reload()}
            className="text-white underline"
          >
            Try again
          </button>
        </div>
      </div>
    );
  }

  return (
    <section className="py-12 sm:py-16 md:py-20 px-4 bg-black overflow-hidden">
      <div className="max-w-7xl mx-auto">
        <div className="relative mb-8 sm:mb-12">
          <div className="inline-flex items-center gap-2 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full bg-neutral-900 border border-neutral-700 mb-3 sm:mb-4">
            <Sparkles className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
            <span className="text-xs sm:text-sm text-white font-medium">
              Pricing Plans
            </span>
          </div>
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-2">
            Choose Your Plan
          </h2>
          <p className="text-sm md:text-base text-gray-400 max-w-lg">
            Select the right plan for your invoice management and
            financial analytics needs.
            {returnUrl && (
              <span className="block mt-2 text-sm text-blue-400">
                Complete your subscription to access the dashboard.
              </span>
            )}
          </p>
          <div
            className="absolute top-20 right-0 text-[180px] font-bold select-none hidden lg:block z-0 overflow-hidden"
            style={{
              background:
                'linear-gradient(to bottom, rgba(255,255,255,0.2), transparent)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
              color: 'transparent',
              maxWidth: '100%',
            }}
          >
            PRICING
          </div>
        </div>
        <div className="flex justify-center gap-4 mb-8 sm:mb-12">
          <div className="bg-neutral-800 rounded-full p-1 inline-flex">
            <div className="relative flex w-48 h-10">
              <div
                className="absolute top-0 left-0 w-1/2 h-full bg-black rounded-full transition-all duration-300"
                style={{
                  transform: `translateX(${isMonthly ? '0%' : '100%'})`,
                }}
              />
              <button
                className={`w-1/2 px-4 sm:px-6 py-1.5 sm:py-2 text-xs sm:text-sm rounded-full relative z-10 transition-colors duration-300 ${isMonthly ? 'text-white' : 'text-gray-400'}`}
                onClick={switchBilling}
              >
                Monthly
              </button>
              <button
                className={`w-1/2 px-4 sm:px-6 py-1.5 sm:py-2 text-xs sm:text-sm rounded-full relative z-10 transition-colors duration-300 ${!isMonthly ? 'text-white' : 'text-gray-400'}`}
                onClick={switchBilling}
              >
                Annual
              </button>
            </div>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-white">
          {plans.map((plan) => (
            <div
              key={plan.id}
              className="relative flex flex-col justify-between p-6 min-h-[500px] bg-neutral-900/60 hover:bg-neutral-800/60 backdrop-blur-xl rounded-[20px] shadow-[0_0_0_0,inset_0_0_30px_rgba(200,200,200,0.1)] border border-neutral-700 overflow-hidden transition-colors duration-300"
              style={{ zIndex: 2 }}
            >
              <div className="flex flex-col gap-2 mb-6">
                <div className="flex items-center gap-2 mb-2">
                  <div className="bg-black p-2 rounded-lg inline-block">
                    {plan.icon}
                  </div>
                  <h3 className="text-lg sm:text-xl font-bold">
                    {plan.title}
                  </h3>
                  {plan.highlighted && (
                    <span className="ml-2 px-2 py-0.5 rounded-full text-xs font-semibold bg-blue-500/10 text-blue-300 border border-blue-400/30">
                      Most popular
                    </span>
                  )}
                </div>
                <div className="flex items-end gap-2">
                  <span className="text-4xl font-bold text-white">
                    ${getPrice(plan)}
                  </span>
                  <span className="text-base text-gray-400">
                    {getPriceLabel()}
                  </span>
                </div>
                {!isMonthly && (
                  <div className="text-xs text-green-400">
                    Save ${plan.monthlyPrice * 2} annually
                  </div>
                )}
                <p className="text-sm text-gray-400 mt-2">
                  {plan.description}
                </p>
              </div>
              <div className="flex-1 flex flex-col justify-between">
                <button
                  className={`w-full text-base font-semibold cursor-pointer transition-all duration-200 py-3 rounded-lg mb-6 flex items-center justify-center gap-2 ${plan.highlighted ? 'bg-white text-black hover:bg-gray-100 group' : 'bg-black text-white hover:bg-neutral-800 group'}`}
                  onClick={() =>
                    onGetStarted
                      ? onGetStarted(plan.id)
                      : handleCheckout(
                          isMonthly
                            ? plan.monthlyPriceId
                            : plan.yearlyPriceId,
                          plan.id
                        )
                  }
                  disabled={checkoutLoading === plan.id}
                >
                  {checkoutLoading === plan.id
                    ? 'Processing...'
                    : 'Get started'}{' '}
                  <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
                </button>
                <div>
                  <ul className="space-y-3 mt-2">
                    {plan.features.map((feature, index) => (
                      <li
                        key={index}
                        className="flex items-center gap-3"
                      >
                        <span className="inline-flex items-center justify-center bg-neutral-800 border border-neutral-600 rounded-lg p-1.5">
                          <svg
                            className="w-3 h-3 text-white"
                            viewBox="0 0 12 12"
                            fill="none"
                          >
                            <path
                              d="M10 3L4.5 8.5L2 6"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </span>
                        <span className="text-base text-gray-300">
                          {feature.text}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Footer */}
        <div className="text-center mt-16">
          <p className="text-gray-400 text-sm">
            All plans include 30-day money-back guarantee • Cancel
            anytime • No setup fees
          </p>
          {returnUrl && (
            <p className="text-blue-400 text-sm mt-2">
              After subscribing, you&apos;ll be redirected to continue
              where you left off.
            </p>
          )}
        </div>
      </div>
    </section>
  );
}
