const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updatePlanLimits() {
  try {
    // Define the usage limits for each plan tier
    const planUpdates = [
      // Starter Plans
      {
        productName: 'Starter',
        chatLimit: 5,
        invoiceLimit: 10,
      },
      // Business Plans
      {
        productName: 'Business',
        chatLimit: 50,
        invoiceLimit: 100,
      },
      // Enterprise Plans
      {
        productName: 'Enterprise',
        chatLimit: 500,
        invoiceLimit: 1000,
      },
    ];

    let updatedCount = 0;

    for (const update of planUpdates) {
      const result = await prisma.plan.updateMany({
        where: {
          productName: {
            contains: update.productName,
            mode: 'insensitive',
          },
        },
        data: {
          chatLimit: update.chatLimit,
          invoiceLimit: update.invoiceLimit,
        },
      });

      updatedCount += result.count;
    }

    // Check final state
    const allPlans = await prisma.plan.findMany({
      select: {
        id: true,
        productName: true,
        name: true,
        chatLimit: true,
        invoiceLimit: true,
        price: true,
      },
    });

    return { success: true, updated: updatedCount };
  } catch (error) {
    return { success: false, error: error.message };
  } finally {
    await prisma.$disconnect();
  }
}

// Run the update
updatePlanLimits()
  .then((result) => {
    if (result.success) {
      process.exit(0);
    } else {
      process.exit(1);
    }
  })
  .catch((error) => {
    process.exit(1);
  });
