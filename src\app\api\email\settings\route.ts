import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import db from "@/db/db";
import { updateEmailSyncSettings } from "@/lib/services/gmail-service";

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const { userId: clerkUserId } = await auth();
    if (!clerkUserId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the user ID from query params or use authenticated user
    const searchParams = request.nextUrl.searchParams;
    const queryUserId = searchParams.get("userId");
    
    // User is always allowed to access their own settings
    const targetUserId = queryUserId || clerkUserId;

    // Fetch email sync settings
    const syncSettings = await db.emailSyncJob.findFirst({
      where: {
        userId: targetUserId,
        provider: "gmail",
      },
    });

    return NextResponse.json({ syncSettings });
  } catch (error) {
    console.error("Error fetching email settings:", error);
    return NextResponse.json(
      { error: "Failed to fetch email settings" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const { userId: clerkUserId } = await auth();
    if (!clerkUserId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get request body
    const body = await request.json();
    const { userId, settings } = body;

    // User is always allowed to update their own settings
    const targetUserId = userId || clerkUserId;

    // Validate settings
    if (!settings || typeof settings !== "object") {
      return NextResponse.json(
        { error: "Invalid settings format" },
        { status: 400 }
      );
    }

    // Update email sync settings
    await updateEmailSyncSettings(targetUserId, settings);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error updating email settings:", error);
    return NextResponse.json(
      { error: "Failed to update email settings" },
      { status: 500 }
    );
  }
} 