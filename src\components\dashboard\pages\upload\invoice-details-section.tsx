"use client";

import { useState } from "react";
import type { InvoiceData } from "@/types/invoice";
import { safeToString } from "@/utils/object-to-string";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Globe, Clock } from "lucide-react";

interface InvoiceDetailsSectionProps {
  data: InvoiceData & { id?: string };
  editedData: InvoiceData & { id?: string };
  isEditing: boolean;
  onInputChange: (section: string, field: string, value: string) => void;
}

export function InvoiceDetailsSection({
  data,
  editedData,
  isEditing,
  onInputChange
}: InvoiceDetailsSectionProps) {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    vendor: true,
    customer: true,
    financials: true,
    payment: true
  });

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  return (
    <div className="space-y-6">
      {/* Vendor Information */}
      {editedData.vendor && (
        <Card className="overflow-hidden border border-slate-200 dark:border-slate-800 shadow-sm">
          <div
            className="flex items-center justify-between p-4 bg-gradient-to-r from-slate-50 to-white dark:from-slate-900 dark:to-slate-800 cursor-pointer"
            onClick={() => toggleSection('vendor')}
          >
            <div className="flex items-center">
              <div className="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900/40 flex items-center justify-center mr-3">
                <Globe className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="text-lg font-semibold">Vendor Information</h3>
            </div>
            <div className="text-sm text-muted-foreground">
              {expandedSections.vendor ? '▼' : '▶'}
            </div>
          </div>

          {expandedSections.vendor && (
            <CardContent className="p-4 pt-0 mt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {(data.vendor?.name && safeToString(data.vendor?.name) !== 'N/A' && safeToString(data.vendor?.name) !== 'Not available') && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Vendor Name</label>
                    {isEditing ? (
                      <Input
                        value={editedData.vendor?.name || ""}
                        onChange={(e) => onInputChange("vendor", "name", e.target.value)}
                        placeholder="Vendor name"
                      />
                    ) : (
                      <div className="p-2 bg-slate-50 dark:bg-slate-900 rounded border border-slate-200 dark:border-slate-800">
                        {safeToString(data.vendor?.name)}
                      </div>
                    )}
                  </div>
                )}

                {(data.vendor?.address && safeToString(data.vendor?.address) !== 'N/A' && safeToString(data.vendor?.address) !== 'Not available') && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Vendor Address</label>
                    {isEditing ? (
                      <Input 
                        value={editedData.vendor?.address || ""}
                        onChange={(e) => onInputChange("vendor", "address", e.target.value)}
                        placeholder="Vendor address"
                      />
                    ) : (
                      <div className="p-2 bg-slate-50 dark:bg-slate-900 rounded border border-slate-200 dark:border-slate-800">
                        {safeToString(data.vendor?.address)}
                      </div>
                    )}
                  </div>
                )}

                {(data.vendor?.phone && safeToString(data.vendor?.phone) !== 'N/A' && safeToString(data.vendor?.phone) !== 'Not available') && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Vendor Phone</label>
                    {isEditing ? (
                      <Input
                        value={editedData.vendor?.phone || ""}
                        onChange={(e) => onInputChange("vendor", "phone", e.target.value)}
                        placeholder="Vendor phone"
                      />
                    ) : (
                      <div className="p-2 bg-slate-50 dark:bg-slate-900 rounded border border-slate-200 dark:border-slate-800">
                        {safeToString(data.vendor?.phone)}
                      </div>
                    )}
                  </div>
                )}

                {(data.vendor?.email && safeToString(data.vendor?.email) !== 'N/A' && safeToString(data.vendor?.email) !== 'Not available') && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Vendor Email</label>
                    {isEditing ? (
                      <Input
                        value={editedData.vendor?.email || ""}
                        onChange={(e) => onInputChange("vendor", "email", e.target.value)}
                        placeholder="Vendor email"
                      />
                    ) : (
                      <div className="p-2 bg-slate-50 dark:bg-slate-900 rounded border border-slate-200 dark:border-slate-800">
                        {safeToString(data.vendor?.email)}
                      </div>
                    )}
                  </div>
                )}

                {(data.vendor?.website && safeToString(data.vendor?.website) !== 'N/A' && safeToString(data.vendor?.website) !== 'Not available') && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Vendor Website</label>
                    {isEditing ? (
                      <Input
                        value={editedData.vendor?.website || ""}
                        onChange={(e) => onInputChange("vendor", "website", e.target.value)}
                        placeholder="Vendor website"
                      />
                    ) : (
                      <div className="p-2 bg-slate-50 dark:bg-slate-900 rounded border border-slate-200 dark:border-slate-800">
                        {safeToString(data.vendor?.website)}
                      </div>
                    )}
                  </div>
                )}

                {(data.vendor?.taxId && safeToString(data.vendor?.taxId) !== 'N/A' && safeToString(data.vendor?.taxId) !== 'Not available') && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Tax ID / VAT Number</label>
                    {isEditing ? (
                      <Input
                        value={editedData.vendor?.taxId || ""}
                        onChange={(e) => onInputChange("vendor", "taxId", e.target.value)}
                        placeholder="Tax ID / VAT Number"
                      />
                    ) : (
                      <div className="p-2 bg-slate-50 dark:bg-slate-900 rounded border border-slate-200 dark:border-slate-800">
                        {safeToString(data.vendor?.taxId)}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          )}
        </Card>
      )}

      {/* Customer Information */}
      {editedData.customer && (
        <Card className="overflow-hidden border border-slate-200 dark:border-slate-800 shadow-sm">
          <div
            className="flex items-center justify-between p-4 bg-gradient-to-r from-slate-50 to-white dark:from-slate-900 dark:to-slate-800 cursor-pointer"
            onClick={() => toggleSection('customer')}
          >
            <div className="flex items-center">
              <div className="h-8 w-8 rounded-full bg-green-100 dark:bg-green-900/40 flex items-center justify-center mr-3">
                <svg className="h-4 w-4 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold">Customer Information</h3>
            </div>
            <div className="text-sm text-muted-foreground">
              {expandedSections.customer ? '▼' : '▶'}
            </div>
          </div>

          {expandedSections.customer && (
            <CardContent className="p-4 pt-0 mt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {(data.customer?.name && safeToString(data.customer?.name) !== 'N/A' && safeToString(data.customer?.name) !== 'Not available') && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Customer Name</label>
                    {isEditing ? (
                      <Input
                        value={editedData.customer?.name || ""}
                        onChange={(e) => onInputChange("customer", "name", e.target.value)}
                        placeholder="Customer name"
                      />
                    ) : (
                      <div className="p-2 bg-slate-50 dark:bg-slate-900 rounded border border-slate-200 dark:border-slate-800">
                        {safeToString(data.customer?.name)}
                      </div>
                    )}
                  </div>
                )}

                {(data.customer?.address && safeToString(data.customer?.address) !== 'N/A' && safeToString(data.customer?.address) !== 'Not available') && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Customer Address</label>
                    {isEditing ? (
                      <Input
                        value={editedData.customer?.address || ""}
                        onChange={(e) => onInputChange("customer", "address", e.target.value)}
                        placeholder="Customer address"
                      />
                    ) : (
                      <div className="p-2 bg-slate-50 dark:bg-slate-900 rounded border border-slate-200 dark:border-slate-800">
                        {safeToString(data.customer?.address)}
                      </div>
                    )}
                  </div>
                )}

                {(data.customer?.email && safeToString(data.customer?.email) !== 'N/A' && safeToString(data.customer?.email) !== 'Not available') && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Customer Email</label>
                    {isEditing ? (
                      <Input
                        value={editedData.customer?.email || ""}
                        onChange={(e) => onInputChange("customer", "email", e.target.value)}
                        placeholder="Customer email"
                      />
                    ) : (
                      <div className="p-2 bg-slate-50 dark:bg-slate-900 rounded border border-slate-200 dark:border-slate-800">
                        {safeToString(data.customer?.email)}
                      </div>
                    )}
                  </div>
                )}

                {(data.customer?.phone && safeToString(data.customer?.phone) !== 'N/A' && safeToString(data.customer?.phone) !== 'Not available') && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Customer Phone</label>
                    {isEditing ? (
                      <Input
                        value={editedData.customer?.phone || ""}
                        onChange={(e) => onInputChange("customer", "phone", e.target.value)}
                        placeholder="Customer phone"
                      />
                    ) : (
                      <div className="p-2 bg-slate-50 dark:bg-slate-900 rounded border border-slate-200 dark:border-slate-800">
                        {safeToString(data.customer?.phone)}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          )}
        </Card>
      )}

      {/* Invoice Date Information */}
      <Card className="overflow-hidden border border-slate-200 dark:border-slate-800 shadow-sm">
        <div
          className="flex items-center justify-between p-4 bg-gradient-to-r from-slate-50 to-white dark:from-slate-900 dark:to-slate-800 cursor-pointer"
        >
          <div className="flex items-center">
            <div className="h-8 w-8 rounded-full bg-purple-100 dark:bg-purple-900/40 flex items-center justify-center mr-3">
              <Clock className="h-4 w-4 text-purple-600 dark:text-purple-400" />
            </div>
            <h3 className="text-lg font-semibold">Invoice Details</h3>
          </div>
        </div>

        <CardContent className="p-4 pt-0 mt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {(data.invoiceNumber && safeToString(data.invoiceNumber) !== 'N/A' && safeToString(data.invoiceNumber) !== 'Not available') && (
              <div className="space-y-2">
                <label className="text-sm font-medium">Invoice Number</label>
                {isEditing ? (
                  <Input
                    value={editedData.invoiceNumber || ""}
                    onChange={(e) => onInputChange("", "invoiceNumber", e.target.value)}
                    placeholder="Invoice number"
                  />
                ) : (
                  <div className="p-2 bg-slate-50 dark:bg-slate-900 rounded border border-slate-200 dark:border-slate-800">
                    {safeToString(data.invoiceNumber)}
                  </div>
                )}
              </div>
            )}
            {(data.invoiceDate && safeToString(data.invoiceDate) !== 'N/A' && safeToString(data.invoiceDate) !== 'Not available') && (
              <div className="space-y-2">
                <label className="text-sm font-medium">Invoice Date</label>
                {isEditing ? (
                  <Input
                    type="date"
                    value={safeToString(editedData.invoiceDate) || ""}
                    onChange={(e) => onInputChange("", "invoiceDate", e.target.value)}
                    placeholder="Invoice date"
                  />
                ) : (
                  <div className="p-2 bg-slate-50 dark:bg-slate-900 rounded border border-slate-200 dark:border-slate-800">
                    {safeToString(data.invoiceDate)}
                  </div>
                )}
              </div>
            )}
            {(data.dueDate && safeToString(data.dueDate) !== 'N/A' && safeToString(data.dueDate) !== 'Not available') && (
              <div className="space-y-2">
                <label className="text-sm font-medium">Due Date</label>
                {isEditing ? (
                  <Input
                    type="date"
                    value={editedData.dueDate || ""}
                    onChange={(e) => onInputChange("", "dueDate", e.target.value)}
                    placeholder="Due date"
                  />
                ) : (
                  <div className="p-2 bg-slate-50 dark:bg-slate-900 rounded border border-slate-200 dark:border-slate-800">
                    {safeToString(data.dueDate)}
                  </div>
                )}
              </div>
            )}
            {(data.currency && safeToString(data.currency) !== 'N/A' && safeToString(data.currency) !== 'Not available') && (
              <div className="space-y-2">
                <label className="text-sm font-medium">Currency</label>
                {isEditing ? (
                  <Input
                    value={safeToString(editedData.currency) || ""}
                    onChange={(e) => onInputChange("", "currency", e.target.value)}
                    placeholder="Currency"
                  />
                ) : (
                  <div className="p-2 bg-slate-50 dark:bg-slate-900 rounded border border-slate-200 dark:border-slate-800">
                    {safeToString(data.currency)}
                  </div>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
