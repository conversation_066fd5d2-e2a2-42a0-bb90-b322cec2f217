'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { formatCurrency, formatDate } from '@/lib/utils';
import {
  Calendar,
  CreditCard,
  Tag,
  Building,
} from 'lucide-react';

interface InvoiceSummaryCardProps {
  invoice: {
    issueDate?: Date | null;
    dueDate?: Date | null;
    amount?: number | null;
    category?: { id: string; name: string; color?: string } | null;
    extractedData?: {
      vendor?: Record<string, string | null>;
      customer?: Record<string, string | null>;
    };
  };
}

// Utility function to render nested object fields
function renderObjectFields(obj: Record<string, any>) {
  return Object.entries(obj).map(([key, value]) => {
    if (value && typeof value === 'object' && !Array.isArray(value)) {
      const nested = renderObjectFields(value);
      if (!nested || nested.length === 0) return null;
      return (
        <div key={key} className="space-y-1 ml-4">
          <p className="text-xs text-muted-foreground capitalize">
            {key.replace(/([A-Z])/g, ' $1').trim()}:
          </p>
          {nested}
        </div>
      );
    }
    if (
      value !== null &&
      value !== undefined &&
      value !== '' &&
      typeof value !== 'object'
    ) {
      return (
        <div key={key} className="mb-2">
          <p className="text-sm text-muted-foreground capitalize">
            {key.replace(/([A-Z])/g, ' $1').trim()}
          </p>
          <p className="font-medium">{String(value)}</p>
        </div>
      );
    }
    return null;
  });
}

export function InvoiceSummaryCard({ invoice }: InvoiceSummaryCardProps) {
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">Invoice Summary</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground flex items-center">
              <Calendar className="mr-1 h-4 w-4" />
              Issue Date
            </p>
            <p className="font-medium">
              {invoice.issueDate ? formatDate(invoice.issueDate) : 'N/A'}
            </p>
          </div>
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground flex items-center">
              <Calendar className="mr-1 h-4 w-4" />
              Due Date
            </p>
            <p className="font-medium">
              {invoice.dueDate ? formatDate(invoice.dueDate) : 'N/A'}
            </p>
          </div>
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground flex items-center">
              <CreditCard className="mr-1 h-4 w-4" />
              Amount
            </p>
            <p className="font-medium">
              {invoice.amount ? formatCurrency(invoice.amount) : 'N/A'}
            </p>
          </div>
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground flex items-center">
              <Tag className="mr-1 h-4 w-4" />
              Category
            </p>
            <p className="font-medium">
              {invoice.category ? (
                <span className="flex items-center">
                  <span
                    className="h-3 w-3 rounded-full mr-1.5"
                    style={{
                      backgroundColor: invoice.category.color || '#6366F1',
                    }}
                  />
                  {invoice.category.name}
                </span>
              ) : (
                'Uncategorized'
              )}
            </p>
          </div>
        </div>

        <Separator className="my-4" />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <p className="text-sm font-medium flex items-center">
              <Building className="mr-1 h-4 w-4" />
              Vendor Information
            </p>
            <div className="bg-muted/50 p-3 rounded-md">
              {invoice.extractedData?.vendor ? (
                Object.entries(invoice.extractedData.vendor).map(([key, value]) =>
                  value &&
                  typeof value === 'object' &&
                  !Array.isArray(value) ? (
                    <div key={key} className="mb-2">
                      <p className="text-sm text-muted-foreground capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </p>
                      {renderObjectFields(value)}
                    </div>
                  ) : value && value !== '' ? (
                    <div key={key} className="mb-2">
                      <p className="text-sm text-muted-foreground capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </p>
                      <p className="font-medium">{String(value)}</p>
                    </div>
                  ) : null
                )
              ) : (
                <p className="font-medium">Unknown Vendor</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <p className="text-sm font-medium flex items-center">
              <Building className="mr-1 h-4 w-4" />
              Customer Information
            </p>
            <div className="bg-muted/50 p-3 rounded-md">
              {invoice.extractedData?.customer ? (
                Object.entries(invoice.extractedData.customer).map(([key, value]) =>
                  value &&
                  typeof value === 'object' &&
                  !Array.isArray(value) ? (
                    <div key={key} className="mb-2">
                      <p className="text-sm text-muted-foreground capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </p>
                      {renderObjectFields(value)}
                    </div>
                  ) : value && value !== '' ? (
                    <div key={key} className="mb-2">
                      <p className="text-sm text-muted-foreground capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </p>
                      <p className="font-medium">{String(value)}</p>
                    </div>
                  ) : null
                )
              ) : (
                <p className="text-sm text-muted-foreground">
                  No customer information available
                </p>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}