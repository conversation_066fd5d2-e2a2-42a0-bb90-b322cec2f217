import { NextRequest, NextResponse } from "next/server";
import { updateInvoiceMetadata } from "@/actions/invoice-actions";
import { auth } from "@clerk/nextjs/server";

export async function PUT(request: NextRequest, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const id = params.id;
    const data = await request.json();
    
    // Extract the selected category and vendor type
    const { selectedCategory, selectedVendorType } = data;
    
    // Call the server action to update the invoice
    const result = await updateInvoiceMetadata(id, {
      selectedCategory,
      selectedVendorType,
    });
    
    if (!result.success) {
      return NextResponse.json(
        { error: result.error || "Failed to update invoice metadata" },
        { status: 400 }
      );
    }
    
    return NextResponse.json(result.data);
  } catch (error) {
    console.error("Error updating invoice metadata:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 