"use client";

import type React from "react";

import { type <PERSON>actN<PERSON>, useMemo, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";

import {
  CheckCircleFillIcon,
  ChevronDownIcon,
  GlobeIcon,
  LockIcon,
} from "./icons";
import { useChatVisibility } from "@/hooks/use-chat-visibility";

export type VisibilityType = "private" | "public";

const visibilities: Array<{
  id: VisibilityType;
  label: string;
  description: string;
  icon: ReactNode;
}> = [
  {
    id: "private",
    label: "Private",
    description: "Only you can access this chat",
    icon: <LockIcon />,
  },
  {
    id: "public",
    label: "Public",
    description: "Anyone with the link can access this chat",
    icon: <GlobeIcon />,
  },
];

export function VisibilitySelector({
  chatId,
  className,
  selectedVisibilityType,
}: {
  chatId: string;
  selectedVisibilityType: VisibilityType;
} & React.ComponentProps<typeof Button>) {
  const [open, setOpen] = useState(false);

  const { visibilityType, setVisibilityType } = useChatVisibility({
    chatId,
    initialVisibility: selectedVisibilityType,
  });

  const selectedVisibility = useMemo(
    () => visibilities.find((visibility) => visibility.id === visibilityType),
    [visibilityType]
  );

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger
        asChild
        className={cn(
          "w-fit data-[state=open]:bg-accent/20 data-[state=open]:text-accent-foreground data-[state=open]:border-primary/50 data-[state=open]:shadow-md",
          className
        )}
      >
        <Button
          variant="outline"
          className="hidden md:flex md:px-4 md:h-[38px] rounded-full border-border/60 bg-background/80 hover:bg-background/90 hover:border-primary/40 hover:shadow-sm transition-all duration-200 backdrop-blur-md"
        >
          <span className="text-primary mr-1.5">{selectedVisibility?.icon}</span>
          <span className="font-medium">{selectedVisibility?.label}</span>
          <ChevronDownIcon />
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        align="start"
        className="min-w-[320px] rounded-xl p-2 border-border/60 shadow-lg bg-popover/95 backdrop-blur-md"
      >
        {visibilities.map((visibility) => {
          const isActive = visibility.id === visibilityType;

          return (
            <DropdownMenuItem
              key={visibility.id}
              onSelect={() => {
                setVisibilityType(visibility.id);
                setOpen(false);
              }}
              className="gap-4 group/item flex flex-row justify-between items-center rounded-lg p-3.5 hover:bg-accent/20 transition-all duration-200"
              data-active={isActive}
            >
              <div className="flex flex-col gap-1.5 items-start">
                <div className="font-medium flex items-center gap-2">
                  <span className={`text-primary ${visibility.id === 'private' ? 'text-amber-500' : 'text-emerald-500'}`}>
                    {visibility.icon}
                  </span>
                  {visibility.label}
                </div>
                {visibility.description && (
                  <div className="text-xs text-muted-foreground">
                    {visibility.description}
                  </div>
                )}
              </div>
              <div className="text-primary opacity-0 group-data-[active=true]/item:opacity-100 transition-opacity duration-200">
                <CheckCircleFillIcon />
              </div>
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
