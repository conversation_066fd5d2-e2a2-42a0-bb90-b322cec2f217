import * as React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>n,
  Con<PERSON>er,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>r,
  Html,
  Img,
  Link,
  Preview,
  Row,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";
import Colors from "../../../components/theme/Colors";

export const WaitlistEmailTemplate = () => {
  const currentYear = new Date().getFullYear();

  return (
    <Html>
      <Head />
      <Preview>
        Welcome to the Billix AI Waitlist - We&apos;re excited to have you join
        us!
      </Preview>
      <Tailwind>
        <Body style={{ backgroundColor: Colors.backgroundDark }} className="font-sans py-[40px]">
          <Container style={{ backgroundColor: Colors.backgroundDark, borderColor: Colors.textLight + '20' }} className="rounded-[8px] mx-auto p-[20px] max-w-[600px] border">
            {/* Header with Logo */}
            <Section className="mt-[10px] mb-[32px] text-center">
              <Img
                src="https://billix.io/billix_logo.jpg"
                alt="Billix AI Logo"
                width="120"
                height="50"
                className="mx-auto w-[120px] h-auto object-cover"
              />
            </Section>

            {/* Main Heading */}
            <Heading style={{ color: Colors.textLight }} className="text-[24px] font-bold text-center mb-[16px]">
              Thanks for joining our waitlist!
            </Heading>

            <Text style={{ color: Colors.textLight }} className="text-[16px] leading-[24px] mb-[24px] text-center">
              We&apos;re thrilled to have you on board as we prepare to launch
              Billix AI. You&apos;re now part of an exclusive group that will be
              first to experience our innovative AI solutions.
            </Text>

            {/* What's Next Section */}
            <Section style={{ backgroundColor: Colors.textLight + '10', borderColor: Colors.textLight + '20' }} className="backdrop-blur-sm rounded-[8px] p-[24px] mb-[32px] border">
              <Heading style={{ color: Colors.textLight }} className="text-[20px] font-bold mt-0 mb-[16px]">
                What happens next?
              </Heading>

              <Text style={{ color: Colors.textLight + 'dd' }} className="text-[16px] leading-[24px] mb-[16px]">
                You&apos;ll be among the first to know when we launch.
                Here&apos;s what you can expect:
              </Text>

              <ul className="pl-[24px] mb-[16px]">
                <li style={{ color: Colors.textLight + 'dd' }} className="text-[16px] leading-[24px] mb-[8px]">
                  <strong>Early access</strong> to our platform
                </li>
                <li style={{ color: Colors.textLight + 'dd' }} className="text-[16px] leading-[24px] mb-[8px]">
                  <strong>Exclusive updates</strong> on our progress
                </li>
                <li style={{ color: Colors.textLight + 'dd' }} className="text-[16px] leading-[24px] mb-[8px]">
                  <strong>Special launch offers</strong> for waitlist members
                </li>
              </ul>
            </Section>

            {/* CTA Section */}
            <Section className="text-center mb-[32px]">
              <Button
                href="https://www.billix.io"
                style={{ backgroundColor: Colors.textLight, color: Colors.text, fontWeight: 'bold' }}
                className="py-[12px] px-[24px] rounded-[4px] no-underline text-[16px] box-border"
              >
                Learn More About Billix AI
              </Button>
            </Section>

            {/* Social Proof */}
            <Section className="mb-[32px]">
              <Text style={{ color: Colors.textLight + 'aa' }} className="text-[16px] leading-[24px] text-center italic">
                &quot;Join thousands of forward-thinking professionals who are
                excited about the future of AI with Billix.&quot;
              </Text>
            </Section>

            <Hr style={{ borderColor: Colors.textLight + '20' }} className="my-[32px]" />

            {/* Footer */}
            <Section>
              <Text style={{ color: Colors.textLight + 'aa' }} className="text-[14px] leading-[24px] text-center">
                Follow us on social media to stay updated:
              </Text>

              <Row className="text-center my-[16px]">
                <Column align="center">
                  <Link
                    href="https://x.com/Billix_io"
                    style={{ color: Colors.textLight }}
                    className="no-underline"
                  >
                    Twitter
                  </Link>
                </Column>
                <Column align="center">
                  <Link
                    href="https://www.instagram.com/billix.io"
                    style={{ color: Colors.textLight }}
                    className="no-underline"
                  >
                    Instagram
                  </Link>
                </Column>
              </Row>

              <Text style={{ color: Colors.textLight + '80' }} className="text-[12px] leading-[20px] text-center m-0">
                © {currentYear} Billix AI. All rights reserved.
              </Text>

              <Text style={{ color: Colors.textLight + '80' }} className="text-[12px] leading-[20px] text-center m-0">
                Hebron, Palestine
              </Text>

              <Text style={{ color: Colors.textLight + '80' }} className="text-[12px] leading-[20px] text-center mt-[8px]">
                <Link
                  href="https://billix.io/privacy"
                  style={{ color: Colors.textLight, textDecoration: 'underline' }}
                >
                  Privacy Policy
                </Link>
              </Text>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

export default WaitlistEmailTemplate;
