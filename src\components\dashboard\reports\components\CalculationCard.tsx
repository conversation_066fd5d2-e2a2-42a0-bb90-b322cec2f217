import { Calculator, Trash } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface CalculationCardProps {
  name: string;
  formula: string;
  type: string;
  selected?: boolean;
  onSelect?: () => void;
  onDelete?: () => void;
}

export function CalculationCard({ 
  name, 
  formula, 
  type, 
  selected = false, 
  onSelect, 
  onDelete 
}: CalculationCardProps) {
  return (
    <div className={`flex items-center justify-between p-3 border rounded-md ${selected ? "border-primary" : ""}`}>
      <div className="flex items-center gap-3">
        <Calculator className="h-5 w-5 text-muted-foreground" />
        <div>
          <div className="font-medium">{name}</div>
          <div className="text-sm text-muted-foreground">{formula}</div>
        </div>
      </div>
      <div className="flex items-center gap-2">
        <Badge variant="outline">{type}</Badge>
        {selected ? (
          onDelete && (
            <Button variant="ghost" size="icon" onClick={onDelete}>
              <Trash className="h-4 w-4 text-red-500" />
            </Button>
          )
        ) : (
          onSelect && (
            <Button variant="outline" size="sm" onClick={onSelect}>
              Add
            </Button>
          )
        )}
      </div>
    </div>
  );
} 