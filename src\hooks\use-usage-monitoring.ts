'use client';

import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';

interface UsageStats {
  chatUsage: number;
  invoiceUsage: number;
  chatLimit: number;
  invoiceLimit: number;
  resetDate: Date;
  daysUntilReset: number;
}

interface UsageCheckResult {
  allowed: boolean;
  stats?: UsageStats;
  message?: string;
}

export function useUsageMonitoring() {
  const [isChecking, setIsChecking] = useState(false);
  const router = useRouter();

  const checkUsage = useCallback(
    async (feature: 'chat' | 'upload'): Promise<UsageCheckResult> => {
      setIsChecking(true);

      try {
        const response = await fetch('/api/usage/stats');

        if (!response.ok) {
          // If API fails, allow action (fail open)
          return { allowed: true };
        }

        const result = await response.json();

        if (!result.success) {
          // If we can't get usage stats, allow action (fail open)
          return { allowed: true };
        }

        const stats: UsageStats = result.stats;

        let allowed = false;
        let message = '';

        if (feature === 'chat') {
          allowed = stats.chatUsage < stats.chatLimit;
          if (!allowed) {
            message = `You've reached your monthly chat limit of ${stats.chatLimit}. ${stats.chatLimit === 0 ? 'Subscribe to get more chats' : 'Upgrade your plan'} or wait ${stats.daysUntilReset} days for reset.`;
          }
        } else if (feature === 'upload') {
          allowed = stats.invoiceUsage < stats.invoiceLimit;
          if (!allowed) {
            message = `You've reached your monthly invoice upload limit of ${stats.invoiceLimit}. ${stats.invoiceLimit === 0 ? 'Subscribe to get more uploads' : 'Upgrade your plan'} or wait ${stats.daysUntilReset} days for reset.`;
          }
        }

        return {
          allowed,
          stats,
          message: allowed ? undefined : message,
        };
      } catch {
        return { allowed: true };
      } finally {
        setIsChecking(false);
      }
    },
    []
  );

  const redirectToSubscription = useCallback(
    (feature: 'chat' | 'upload', message: string) => {
      const encodedMessage = encodeURIComponent(message);
      router.push(
        `/dashboard/subscription?limit_exceeded=${feature}&message=${encodedMessage}`
      );
    },
    [router]
  );

  return {
    checkUsage,
    redirectToSubscription,
    isChecking,
  };
}
