import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Loader2 } from "lucide-react";
import { 
  <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter 
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Select, SelectContent, SelectItem, SelectTrigger, SelectValue 
} from "@/components/ui/select";
import { VisualizationCard } from "./VisualizationCard";
import { FormData } from "../custom-report-builder";

interface VisualizationStepProps {
  formData: FormData;
  loading: boolean;
  handleSelectChange: (id: string, value: string) => void;
  onBack: () => void;
  onSubmit: () => void;
}

export function VisualizationStep({
  formData,
  loading,
  handleSelectChange,
  onBack,
  onSubmit
}: VisualizationStepProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Visualization</CardTitle>
        <CardDescription>
          Choose how to visualize your report data
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Tabs defaultValue="charts" onValueChange={(value) => handleSelectChange('visualizationType', value === 'tables' ? 'table' : value === 'charts' ? 'bar' : 'layout')}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="charts">Charts</TabsTrigger>
            <TabsTrigger value="tables">Tables</TabsTrigger>
            <TabsTrigger value="layout">Layout</TabsTrigger>
          </TabsList>

          <TabsContent value="charts" className="mt-4 space-y-4">
            <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
              <VisualizationCard
                title="Bar Chart"
                description="Compare values across categories"
                icon={<BarChart3 className="h-10 w-10 text-blue-500" />}
                selected={formData.visualizationType === 'bar'}
                onClick={() => handleSelectChange('visualizationType', 'bar')}
              />
              <VisualizationCard
                title="Line Chart"
                description="Show trends over time"
                icon={<LineChart className="h-10 w-10 text-emerald-500" />}
                selected={formData.visualizationType === 'line'}
                onClick={() => handleSelectChange('visualizationType', 'line')}
              />
              <VisualizationCard
                title="Pie Chart"
                description="Show proportion of a whole"
                icon={<PieChart className="h-10 w-10 text-purple-500" />}
                selected={formData.visualizationType === 'pie'}
                onClick={() => handleSelectChange('visualizationType', 'pie')}
              />
            </div>
          </TabsContent>

          <TabsContent value="tables" className="mt-4 space-y-4">
            <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
              <VisualizationCard
                title="Standard Table"
                description="Simple tabular data display"
                icon={<Table2 className="h-10 w-10 text-blue-500" />}
                selected={formData.visualizationType === 'table'}
                onClick={() => handleSelectChange('visualizationType', 'table')}
              />
              <VisualizationCard
                title="Pivot Table"
                description="Cross-tabulate data for analysis"
                icon={<Layers className="h-10 w-10 text-emerald-500" />}
                selected={formData.visualizationType === 'pivot'}
                onClick={() => handleSelectChange('visualizationType', 'pivot')}
              />
              <VisualizationCard
                title="Summary Table"
                description="Show aggregated data with totals"
                icon={<Calculator className="h-10 w-10 text-purple-500" />}
                selected={formData.visualizationType === 'summary'}
                onClick={() => handleSelectChange('visualizationType', 'summary')}
              />
            </div>
          </TabsContent>

          <TabsContent value="layout" className="mt-4 space-y-4">
            <div className="space-y-2">
              <Label htmlFor="report-layout">Report Layout</Label>
              <Select>
                <SelectTrigger id="report-layout">
                  <SelectValue placeholder="Select layout" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="standard">Standard</SelectItem>
                  <SelectItem value="dashboard">Dashboard</SelectItem>
                  <SelectItem value="compact">Compact</SelectItem>
                  <SelectItem value="detailed">Detailed</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="report-theme">Color Theme</Label>
              <Select>
                <SelectTrigger id="report-theme">
                  <SelectValue placeholder="Select theme" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="default">Default</SelectItem>
                  <SelectItem value="blue">Blue</SelectItem>
                  <SelectItem value="green">Green</SelectItem>
                  <SelectItem value="purple">Purple</SelectItem>
                  <SelectItem value="custom">Custom</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={onBack} disabled={loading}>
          Back
        </Button>
        <Button onClick={onSubmit} disabled={loading}>
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating Report...
            </>
          ) : (
            "Generate Report"
          )}
        </Button>
      </CardFooter>
    </Card>
  );
} 