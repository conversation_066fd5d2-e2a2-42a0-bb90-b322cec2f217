import db from '@/db/db';
import { currentUser } from '@clerk/nextjs/server';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const documentId = searchParams.get('documentId');

  if (!documentId) {
    return new Response('Not Found', { status: 404 });
  }

  const user = await currentUser();

  if (!user) {
    return new Response('Unauthorized', { status: 401 });
  }

  // Get user from database by email
  const dbUser = await db.user.findUnique({
    where: { email: user.emailAddresses[0].emailAddress }
  });

  if (!dbUser) {
    return new Response('User not found', { status: 404 });
  }

  const suggestions = await db.suggestion.findMany({
    where: { documentId }
  });

  if (suggestions.length === 0) {
    return Response.json([], { status: 200 });
  }

  const [suggestion] = suggestions;

  if (suggestion.userId !== dbUser.id) {
    return new Response('Unauthorized', { status: 401 });
  }

  return Response.json(suggestions, { status: 200 });
}
