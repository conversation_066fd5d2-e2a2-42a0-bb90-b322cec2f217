'use client';

import { cn } from '@/lib/utils';
import { Menu, X } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { useAuth } from '@clerk/nextjs';

const Logo = () => {
  return (
    <div className="flex items-center gap-1 sm:gap-2">
      <div className="relative size-5 sm:size-6 md:size-8">
        {/* Logo content */}
      </div>
      <span className="text-lg sm:text-xl md:text-3xl font-medium">
        Billx
      </span>
    </div>
  );
};

const menuItems = [
  { name: 'Services', href: '#services' },
  { name: 'Benefits', href: '#benefits' },
  { name: 'Pricing', href: '#pricing' },
  { name: 'Contact', href: '#contact' },
];

const HeaderHero = () => {
  const [menuState, setMenuState] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const { isSignedIn } = useAuth();
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Check if in production mode
  // const isProduction = process.env.NODE_ENV === 'production';

  // Handle mounting to prevent hydration mismatch
  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (menuState) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [menuState]);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        menuRef.current &&
        buttonRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setMenuState(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [menuRef, buttonRef]);

  // Close menu on escape key
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setMenuState(false);
      }
    };

    document.addEventListener('keydown', handleEscKey);
    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, []);

  return (
    <header className="relative text-white z-[100]">
      <nav
        data-state={menuState ? 'active' : 'inactive'}
        className="fixed z-20 w-full px-1 sm:px-2"
        aria-label="Main navigation"
      >
        <div
          className={cn(
            'mx-auto mt-1 sm:mt-2 max-w-6xl px-2 sm:px-4 md:px-6 transition-all duration-300 lg:px-12',
            isScrolled &&
              'bg-black max-w-4xl rounded-xl sm:rounded-2xl border-[1px] shadow-lg border-neutral-600 backdrop-blur-xl lg:px-5'
          )}
        >
          <div className="relative flex flex-wrap items-center justify-between gap-2 sm:gap-4 md:gap-6 py-2 sm:py-3 lg:gap-0 lg:py-4">
            <div className="flex w-full justify-between lg:w-auto">
              <Link
                href="/"
                aria-label="home"
                className="flex items-center -ml-1"
              >
                <Logo />
              </Link>

              <button
                ref={buttonRef}
                onClick={() => setMenuState(!menuState)}
                aria-label={menuState ? 'Close Menu' : 'Open Menu'}
                aria-expanded={menuState}
                aria-controls="mobile-menu"
                className="relative z-20 -m-1 sm:-m-2 -mr-1 sm:-mr-2 block cursor-pointer p-1.5 sm:p-2 rounded-md hover:bg-white/10 transition-colors lg:hidden"
              >
                <Menu
                  className={cn(
                    'm-auto size-4 sm:size-5 md:size-6 transition-all duration-300',
                    menuState && 'opacity-0 rotate-180 scale-0'
                  )}
                />
                <X
                  className={cn(
                    'absolute inset-0 m-auto size-4 sm:size-5 md:size-6 transition-all duration-300 -rotate-180 scale-0 opacity-0',
                    menuState && 'opacity-100 rotate-0 scale-100'
                  )}
                />
              </button>
            </div>

            {/* Desktop menu */}
            <div className="absolute inset-0 m-auto hidden size-fit lg:block">
              <ul className="flex gap-4 md:gap-6 lg:gap-8 text-sm">
                {menuItems.map((item, index) => (
                  <li key={index}>
                    <a
                      href={item.href}
                      className="group text-muted-foreground relative block overflow-hidden px-2 sm:px-3 py-2 transition-colors hover:text-foreground"
                    >
                      <span className="relative">{item.name}</span>
                      <span className="absolute bottom-0 left-0 h-0.5 w-full transform bg-gradient-to-r from-primary to-primary/50 transition-transform duration-300 -translate-x-full group-hover:translate-x-0" />
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Mobile menu */}
            <div
              id="mobile-menu"
              ref={menuRef}
              className={cn(
                'bg-black fixed inset-x-0 top-0 z-10 mt-10 sm:mt-12 md:mt-14 h-screen w-full border-t border-primary/90 p-6 sm:p-8 md:p-10 transition-all duration-300 backdrop-blur-xl lg:relative lg:inset-x-auto lg:mt-0 lg:h-auto lg:w-auto lg:border-0 lg:bg-transparent lg:p-0',
                menuState
                  ? 'translate-y-0 opacity-100'
                  : 'translate-y-4 opacity-0 pointer-events-none',
                'lg:translate-y-0 lg:opacity-100 lg:pointer-events-auto'
              )}
            >
              <div className="lg:hidden pt-8 sm:pt-10">
                <ul className="space-y-4 sm:space-y-6 text-base sm:text-lg">
                  {menuItems.map((item, index) => (
                    <li key={index}>
                      <a
                        href={item.href}
                        onClick={() => setMenuState(false)}
                        className="text-muted-foreground hover:text-accent-foreground block py-2 duration-150 relative overflow-hidden group"
                      >
                        <span className="relative inline-block transition-transform duration-300 group-hover:translate-x-1">
                          {item.name}
                        </span>
                        <span className="absolute bottom-0 left-0 h-0.5 w-0 bg-gradient-to-r from-primary to-primary/50 transition-all duration-300 group-hover:w-full" />
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="flex w-full flex-col mt-8 lg:mt-0 space-y-3 sm:space-y-4 lg:flex-row lg:items-center lg:gap-3 lg:space-y-0 md:w-fit">
                {!isMounted ? (
                  // Show loading state while component is mounting
                  <div className="flex w-full lg:w-auto py-5 sm:py-6 lg:py-2 bg-white/10 text-white font-semibold rounded-md shadow animate-pulse">
                    <div className="w-full h-6 bg-white/20 rounded"></div>
                  </div>
                ) : isSignedIn ? (
                  <Button
                    asChild
                    variant="default"
                    size="sm"
                    className={cn(
                      'text-sm sm:text-base w-full lg:w-auto py-5 sm:py-6 lg:py-2 bg-white text-black font-semibold rounded-md shadow hover:bg-neutral-200 transition-colors'
                    )}
                  >
                    <Link
                      href="/dashboard-access"
                      onClick={() => setMenuState(false)}
                    >
                      <span>Dashboard</span>
                    </Link>
                  </Button>
                ) : (
                  // WAITLIST DISABLED: Always show sign-up flow, even in production
                  // ) : isProduction ? (
                  //   // In production mode, show Join Waitlist button that links to waitlist page
                  //   <Button
                  //     asChild
                  //     variant="default"
                  //     size="sm"
                  //     className={cn(
                  //       "text-sm sm:text-base w-full lg:w-auto py-5 sm:py-6 lg:py-2 bg-white text-black font-semibold rounded-md shadow hover:bg-neutral-200 transition-colors"
                  //     )}
                  //   >
                  //     <Link href="/waitlist" onClick={() => setMenuState(false)}>
                  //       <span>Join Waitlist</span>
                  //     </Link>
                  //   </Button>
                  // ) : (
                  // In development mode, show Login and Sign Up buttons
                  <>
                    <Button
                      asChild
                      variant="outline"
                      size="sm"
                      className={cn(
                        'text-sm sm:text-base w-full lg:w-auto py-5 sm:py-6 lg:py-2 bg-black text-white border border-white/30 hover:bg-neutral-900 hover:text-white transition-colors',
                        isScrolled && 'lg:hidden'
                      )}
                    >
                      <Link
                        href="/sign-in"
                        onClick={() => setMenuState(false)}
                      >
                        <span>Login</span>
                      </Link>
                    </Button>
                    <Button
                      asChild
                      size="sm"
                      className={cn(
                        'text-sm sm:text-base w-full lg:w-auto py-5 sm:py-6 lg:py-2 bg-white text-black font-semibold rounded-md shadow hover:bg-neutral-200 transition-colors',
                        isScrolled && 'lg:hidden'
                      )}
                    >
                      <Link
                        href="/sign-up"
                        onClick={() => setMenuState(false)}
                      >
                        <span>Sign Up</span>
                      </Link>
                    </Button>
                    <Button
                      asChild
                      size="sm"
                      className={cn(
                        'text-sm sm:text-base w-full lg:w-auto py-5 sm:py-6 lg:py-2 bg-white text-black font-semibold rounded-md shadow hover:bg-neutral-200 transition-colors',
                        isScrolled ? 'lg:inline-flex' : 'hidden'
                      )}
                    >
                      <Link
                        href="/sign-up"
                        onClick={() => {
                          setMenuState(false);
                        }}
                      >
                        <span>Get Started</span>
                        {/* WAITLIST DISABLED: Always show "Get Started"
                        <span>{isProduction ? "Join Waitlist" : "Get Started"}</span> */}
                      </Link>
                    </Button>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </nav>
    </header>
  );
};

export default HeaderHero;
