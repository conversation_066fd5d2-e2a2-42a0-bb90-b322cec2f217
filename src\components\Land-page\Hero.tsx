"use client";

import { motion } from "motion/react";
import {
  ArrowRight,
} from "lucide-react";
import HeaderHero from "../Land-page/HeaderHero";
import { CircularLines } from "../ui/CircularLines_Landing";
import Link from "next/link";
import { HeroVideoDialog } from "./HeroVideoDialog";

export default function Hero() {
  // Check if in production mode
  // const isProduction = process.env.NODE_ENV === 'production';
  
  return (
    <>
      <HeaderHero />
      <div className="min-h-screen bg-gradient-to-br from-[#000000] via-[#10272a] to-[#000000] text-white overflow-hidden relative">
        <CircularLines />
        {/* Decorative blob - adjusted positioning and visibility */}
        <div className="absolute -top-50 left-1/2 -translate-x-1/2 w-full max-w-[900px] h-[350px] rounded-full bg-gradient-to-br from-[#395F71] to-[#395F71] opacity-30 blur-3xl pointer-events-none z-[1]" />

        <div className="container mx-auto px-4 py-12 md:py-20">
          {/* Announcement banner */}
          <div className="flex justify-center mb-10 md:mb-16 mt-10 md:mt-20 relative z-10">
            <div className="bg-white/10 backdrop-blur-sm px-4 md:px-6 py-2 md:py-3 rounded-full border border-white/20">
              <p className="text-xs sm:text-sm md:text-base">
                Introducing AI Knowledge Management.{" "}
                <a href="#" className="underline underline-offset-2">
                  Learn more
                </a>
              </p>
            </div>
          </div>

          {/* Hero content */}
          <div className="mx-auto text-center mb-10 md:mb-16 relative z-10">
            <h1 className="text-3xl sm:text-5xl md:text-6xl lg:text-7xl font-medium mb-4 md:mb-6 tracking-tight bg-gradient-to-r from-[#ffffff] via-[#ffffff] to-[#ffffff] bg-clip-text">
              Intelligent Invoice Management
              <br className="hidden sm:block" />
              <span className="pt-2 md:pt-4 inline-block">
                for Modern Business
              </span>
              <br />
              <span className="bg-gradient-to-r from-[#ffffff] via-[#ffffff] to-[#ffffff] bg-clip-text text-transparent"></span>
            </h1>
            <p className="text-base sm:text-lg md:text-xl text-[#ffffff] mb-6 md:mb-10 max-w-2xl mx-auto px-4">
              Manage invoices with AI-powered financial analytics, fraud
              detection, and predictive insights. Upload, analyze, and
              export—all in one secure platform.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 md:gap-4 justify-center px-4">
              {/* WAITLIST DISABLED: Always show "Get Started" button, even in production
              {isProduction ? (
                <Link href="/waitlist">
                  <button className="bg-white text-black hover:bg-gray-100 group py-2 md:py-3 px-6 md:px-9 rounded-xl transition-all flex items-center justify-center gap-2">
                    Join Waitlist{" "}
                    <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
                  </button>
                </Link>
              ) : ( */}
                <Link href="/sign-up">
                  <button className="bg-white text-black hover:bg-gray-100 hover:shadow-[0_0_20px_rgba(255,255,255,0.45)] group py-2 md:py-3 px-6 md:px-9 rounded-xl transition-all flex items-center justify-center gap-2">
                    Get Started{" "}
                    <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
                  </button>
                </Link>
              {/* )} */}
              <button className="bg-black text-white border border-white/30 hover:bg-neutral-800 duration-300 group py-2 md:py-3 px-4 md:px-6 rounded-xl transition-all flex items-center justify-center gap-2">
                Talk to an expert
              </button>
            </div>
          </div>

          {/* Decorative blob - adjusted positioning and visibility */}
          <div className="absolute top-90 left-1/2 -translate-x-1/2 w-full max-w-[1150px] h-[400px] rounded-full bg-gradient-to-br from-[#395F71] to-[#395F71] opacity-30 blur-3xl pointer-events-none z-[1]" />

          {/* YouTube Video Section (replacing Dashboard Image) */}
          <div className="max-w-6xl mx-auto mt-10 md:mt-20 relative z-10 px-4">
            <motion.div
              className="bg-gradient-to-br from-white/5 to-white/10 rounded-xl shadow-2xl overflow-hidden p-1 backdrop-blur-sm border border-white/10"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <HeroVideoDialog 
                videoSrc="https://www.youtube.com/embed/qQ54y54EpUE?si=RegdVHnxnEfz14XH"
                thumbnailSrc="../app-dark.png"
                thumbnailAlt="Product Demo Video"
                animationStyle="from-bottom"
                className="w-full rounded-lg overflow-hidden"
              />
            </motion.div>
          </div>

          {/* Bottom gradient overlay */}
          <div className="absolute bottom-0 left-0 right-0 w-full h-5/6 bg-gradient-to-t from-black to-transparent pointer-events-none z-1" />
        </div>
      </div>
    </>
  );
}
