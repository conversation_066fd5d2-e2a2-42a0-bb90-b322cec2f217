"use client";

import { useEffect, useState } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { ChartContainer, ChartTooltip } from "@/components/ui/chart";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer } from "recharts";
import { formatCurrency } from "@/lib/utils";
import { getAccountsAnalysis } from "@/lib/actions/analytics";
import type { FilterValues } from "./data-filters";
import type { AccountsAnalysis } from "@/types/analytics";
import { Skeleton } from "@/components/ui/skeleton";
import { motion } from "motion/react";
import {
  CreditCard,
  Receipt,
  Clock,
  AlertTriangle,
  Calendar,
} from "lucide-react";
import Colors from '@/components/theme/Colors';

interface AccountsAnalysisProps {
  dateRange: {
    from: Date;
    to: Date;
  };
  filters: FilterValues;
}

export function AccountsAnalysisComponent({
  dateRange,
  filters,
}: AccountsAnalysisProps) {
  const [accountsData, setAccountsData] = useState<AccountsAnalysis | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function loadAccountsData() {
      setLoading(true);
      setError(null);
      try {
        const data = await getAccountsAnalysis(
          dateRange.from,
          dateRange.to,
          filters
        );
        setAccountsData(data);
      } catch (error) {
        setError("Failed to load accounts data" + error);
      } finally {
        setLoading(false);
      }
    }

    loadAccountsData();
  }, [dateRange, filters]);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2">
          {Array.from({ length: 2 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-48" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-[300px] w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error || !accountsData) {
    return (
      <Card className="border-destructive/20">
        <CardHeader>
          <CardTitle className="text-destructive">Error</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-destructive/80">
            {error || "Failed to load accounts data"}
          </p>
        </CardContent>
      </Card>
    );
  }

  const arColors = [Colors.success, Colors.warning, Colors.accent, Colors.error, Colors.error];
  const apColors = [Colors.primary, Colors.accent, Colors.info, Colors.warning, Colors.success];

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card className={`border border-[${Colors.primary}]/10 bg-[${Colors.background}]/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300`}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Accounts Receivable
              </CardTitle>
              <CreditCard className={`h-4 w-4 text-[${Colors.success}]`} />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold text-[${Colors.success}]`}>
                {formatCurrency(accountsData.accountsReceivable.total)}
              </div>
              <p className="text-xs text-muted-foreground">Money owed to you</p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Card className={`border border-[${Colors.primary}]/10 bg-[${Colors.background}]/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300`}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Accounts Payable
              </CardTitle>
              <Receipt className={`h-4 w-4 text-[${Colors.warning}]`} />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold text-[${Colors.warning}]`}>
                {formatCurrency(accountsData.accountsPayable.total)}
              </div>
              <p className="text-xs text-muted-foreground">Money you owe</p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Card className={`border border-[${Colors.primary}]/10 bg-[${Colors.background}]/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300`}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">A/R Overdue</CardTitle>
              <AlertTriangle className={`h-4 w-4 text-[${Colors.error}]`} />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold text-[${Colors.error}]`}>
                {formatCurrency(accountsData.accountsReceivable.overdueAmount)}
              </div>
              <p className="text-xs text-muted-foreground">
                Past due receivables
              </p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Card className={`border border-[${Colors.primary}]/10 bg-[${Colors.background}]/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300`}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">A/P Overdue</CardTitle>
              <Clock className={`h-4 w-4 text-[${Colors.accent}]`} />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold text-[${Colors.accent}]`}>
                {formatCurrency(accountsData.accountsPayable.overdueAmount)}
              </div>
              <p className="text-xs text-muted-foreground">Past due payables</p>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Aging Analysis */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Accounts Receivable Aging */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className={`border border-[${Colors.primary}]/10 bg-[${Colors.background}]/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300`}>
            <CardHeader>
              <CardTitle className="text-xl font-bold flex items-center gap-2">
                <CreditCard className={`h-5 w-5 text-[${Colors.primary}]`} />
                Accounts Receivable Aging
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Age distribution of outstanding receivables
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {accountsData.accountsReceivable.aging.length > 0 ? (
                  <>
                    <ChartContainer config={{}} className="h-[200px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={accountsData.accountsReceivable.aging}
                            cx="50%"
                            cy="50%"
                            outerRadius={80}
                            dataKey="amount"
                            nameKey="range"
                          >
                            {accountsData.accountsReceivable.aging.map(
                              (_, index) => (
                                <Cell
                                  key={`cell-${index}`}
                                  fill={arColors[index % arColors.length]}
                                />
                              )
                            )}
                          </Pie>
                          <ChartTooltip
                            formatter={(value) => [
                              formatCurrency(value as number),
                              "Amount",
                            ]}
                          />
                        </PieChart>
                      </ResponsiveContainer>
                    </ChartContainer>

                    <div className="space-y-2">
                      {accountsData.accountsReceivable.aging.map(
                        (item, index) => (
                          <div
                            key={item.range}
                            className="flex items-center justify-between"
                          >
                            <div className="flex items-center space-x-2">
                              <div
                                className="w-3 h-3 rounded-full"
                                style={{
                                  backgroundColor:
                                    arColors[index % arColors.length],
                                }}
                              />
                              <span className="text-sm font-medium">
                                {item.range}
                              </span>
                            </div>
                            <div className="text-right">
                              <div className="text-sm font-bold">
                                {formatCurrency(item.amount)}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {item.percentage.toFixed(1)}%
                              </div>
                            </div>
                          </div>
                        )
                      )}
                    </div>
                  </>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    No receivables data available
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Accounts Payable Aging */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className={`border border-[${Colors.primary}]/10 bg-[${Colors.background}]/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300`}>
            <CardHeader>
              <CardTitle className="text-xl font-bold flex items-center gap-2">
                <Receipt className={`h-5 w-5 text-[${Colors.primary}]`} />
                Accounts Payable Aging
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Age distribution of outstanding payables
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {accountsData.accountsPayable.aging.length > 0 ? (
                  <>
                    <ChartContainer config={{}} className="h-[200px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={accountsData.accountsPayable.aging}
                            cx="50%"
                            cy="50%"
                            outerRadius={80}
                            dataKey="amount"
                            nameKey="range"
                          >
                            {accountsData.accountsPayable.aging.map(
                              (_, index) => (
                                <Cell
                                  key={`cell-${index}`}
                                  fill={apColors[index % apColors.length]}
                                />
                              )
                            )}
                          </Pie>
                          <ChartTooltip
                            formatter={(value) => [
                              formatCurrency(value as number),
                              "Amount",
                            ]}
                          />
                        </PieChart>
                      </ResponsiveContainer>
                    </ChartContainer>

                    <div className="space-y-2">
                      {accountsData.accountsPayable.aging.map((item, index) => (
                        <div
                          key={item.range}
                          className="flex items-center justify-between"
                        >
                          <div className="flex items-center space-x-2">
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{
                                backgroundColor:
                                  apColors[index % apColors.length],
                              }}
                            />
                            <span className="text-sm font-medium">
                              {item.range}
                            </span>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-bold">
                              {formatCurrency(item.amount)}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {item.percentage.toFixed(1)}%
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    No payables data available
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Collection & Payment Metrics */}
      <div className="grid gap-6 md:grid-cols-2">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <Card className={`border border-[${Colors.primary}]/10 bg-[${Colors.background}]/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300`}>
            <CardHeader>
              <CardTitle className="text-lg font-bold flex items-center gap-2">
                <Calendar className={`h-5 w-5 text-[${Colors.primary}]`} />
                Collection Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">
                    Average Collection Days
                  </span>
                  <span className="text-lg font-bold text-blue-600">
                    {accountsData.accountsReceivable.averageCollectionDays} days
                  </span>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Total A/R</span>
                    <span className="font-medium">
                      {formatCurrency(accountsData.accountsReceivable.total)}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Overdue Amount</span>
                    <span className="font-medium text-red-600">
                      {formatCurrency(
                        accountsData.accountsReceivable.overdueAmount
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Overdue Percentage</span>
                    <span className="font-medium">
                      {accountsData.accountsReceivable.total > 0
                        ? (
                            (accountsData.accountsReceivable.overdueAmount /
                              accountsData.accountsReceivable.total) *
                            100
                          ).toFixed(1)
                        : 0}
                      %
                    </span>
                  </div>
                </div>

                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-green-600 h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${
                        accountsData.accountsReceivable.total > 0
                          ? ((accountsData.accountsReceivable.total -
                              accountsData.accountsReceivable.overdueAmount) /
                              accountsData.accountsReceivable.total) *
                            100
                          : 0
                      }%`,
                    }}
                  />
                </div>
                <p className="text-xs text-muted-foreground">
                  Collection efficiency:{" "}
                  {accountsData.accountsReceivable.total > 0
                    ? (
                        ((accountsData.accountsReceivable.total -
                          accountsData.accountsReceivable.overdueAmount) /
                          accountsData.accountsReceivable.total) *
                        100
                      ).toFixed(1)
                    : 0}
                  % current
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <Card className={`border border-[${Colors.primary}]/10 bg-[${Colors.background}]/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300`}>
            <CardHeader>
              <CardTitle className="text-lg font-bold flex items-center gap-2">
                <Clock className={`h-5 w-5 text-[${Colors.primary}]`} />
                Payment Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">
                    Average Payment Days
                  </span>
                  <span className="text-lg font-bold text-orange-600">
                    {accountsData.accountsPayable.averagePaymentDays} days
                  </span>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Total A/P</span>
                    <span className="font-medium">
                      {formatCurrency(accountsData.accountsPayable.total)}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Overdue Amount</span>
                    <span className="font-medium text-red-600">
                      {formatCurrency(
                        accountsData.accountsPayable.overdueAmount
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Overdue Percentage</span>
                    <span className="font-medium">
                      {accountsData.accountsPayable.total > 0
                        ? (
                            (accountsData.accountsPayable.overdueAmount /
                              accountsData.accountsPayable.total) *
                            100
                          ).toFixed(1)
                        : 0}
                      %
                    </span>
                  </div>
                </div>

                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-orange-600 h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${
                        accountsData.accountsPayable.total > 0
                          ? ((accountsData.accountsPayable.total -
                              accountsData.accountsPayable.overdueAmount) /
                              accountsData.accountsPayable.total) *
                            100
                          : 0
                      }%`,
                    }}
                  />
                </div>
                <p className="text-xs text-muted-foreground">
                  Payment efficiency:{" "}
                  {accountsData.accountsPayable.total > 0
                    ? (
                        ((accountsData.accountsPayable.total -
                          accountsData.accountsPayable.overdueAmount) /
                          accountsData.accountsPayable.total) *
                        100
                      ).toFixed(1)
                    : 0}
                  % current
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
