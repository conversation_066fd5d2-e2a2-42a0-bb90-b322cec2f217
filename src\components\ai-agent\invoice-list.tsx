'use client';

import { useState } from 'react';
import { motion } from 'motion/react';
import { InvoiceDisplay } from './invoice';
import { InvoiceStatus } from '@prisma/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from '@/components/ui/pagination';
import {
  FilterIcon,
  SearchIcon,
} from 'lucide-react';

interface InvoiceLineItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  amount: number;
  // Add other relevant fields like taxRate, discount, etc. if needed
}

interface InvoiceListProps {
  invoices: Array<{
    id: string;
    number: string;
    vendorName: string;
    amount: number;
    currency: string;
    status: InvoiceStatus;
    category?: string;
    issueDate: string;
    dueDate: string;
    paidDate?: string | null;
    lineItems?: InvoiceLineItem[];
  }>;
  total: number;
  page?: number;
  totalPages?: number;
  onPageChange?: (page: number) => void;
  onFilterChange?: (filters: {
    status?: InvoiceStatus;
    searchTerm?: string;
    category?: string;
  }) => void;
  onStatusChange?: (invoiceId: string, newStatus: InvoiceStatus) => void;
  onDelete?: (invoiceId: string) => void;
  onAddToFolder?: (invoiceId: string) => void;
  onCategorize?: (invoiceId: string) => void;
}

export function InvoiceList({
  invoices,
  total,
  page = 1,
  totalPages = 1,
  onPageChange,
  onFilterChange,
  onStatusChange,
  onDelete,
  onAddToFolder,
  onCategorize
}: InvoiceListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<InvoiceStatus | ''>('');
  const [isFilterVisible, setIsFilterVisible] = useState(false);

  const handleSearch = () => {
    onFilterChange?.({
      searchTerm: searchTerm || undefined,
      status: statusFilter || undefined,
    });
  };

  const clearFilters = () => {
    setSearchTerm('');
    setStatusFilter('');
    onFilterChange?.({});
  };

  if (invoices.length === 0) {
    return (
      <div className="p-6 text-center bg-card/50 border border-border/40 rounded-xl shadow-sm">
        <p className="text-muted-foreground">No invoices found matching your criteria.</p>
        {onFilterChange && (
          <Button
            variant="outline"
            className="mt-4"
            onClick={clearFilters}
          >
            Clear Filters
          </Button>
        )}
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="space-y-4"
    >
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-4">
        <h3 className="text-lg font-medium">Invoices</h3>

        {onFilterChange && (
          <div className="flex flex-col md:flex-row gap-2 w-full md:w-auto">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsFilterVisible(!isFilterVisible)}
              className="flex items-center gap-2"
            >
              <FilterIcon className="h-4 w-4" />
              <span>Filters</span>
            </Button>

            {isFilterVisible && (
              <div className="flex flex-col md:flex-row gap-2 w-full">
                <div className="relative">
                  <Input
                    placeholder="Search invoices..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8 w-full"
                  />
                  <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                </div>

                <Select
                  value={statusFilter}
                  onValueChange={(value) => setStatusFilter(value as InvoiceStatus | '')}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Statuses</SelectItem>
                    <SelectItem value="PENDING">Pending</SelectItem>
                    <SelectItem value="PAID">Paid</SelectItem>
                    <SelectItem value="OVERDUE">Overdue</SelectItem>
                    <SelectItem value="CANCELLED">Cancelled</SelectItem>
                  </SelectContent>
                </Select>

                <div className="flex gap-2">
                  <Button
                    variant="default"
                    size="sm"
                    onClick={handleSearch}
                  >
                    Apply
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={clearFilters}
                  >
                    Clear
                  </Button>
                </div>
              </div>
            )}
          </div>
        )}

        <span className="text-sm text-muted-foreground whitespace-nowrap">
          Showing {invoices.length} of {total} invoices
        </span>
      </div>

      <div className="space-y-4">
        {invoices.map((invoice) => (
          <motion.div
            key={invoice.id}
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            <InvoiceDisplay
              invoice={invoice}
              onStatusChange={onStatusChange}
              onDelete={onDelete}
              onAddToFolder={onAddToFolder}
              onCategorize={onCategorize}
            />
          </motion.div>
        ))}
      </div>

      {totalPages > 1 && onPageChange && (
        <Pagination className="mt-8">
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={() => page > 1 && onPageChange(page - 1)}
                className={page <= 1 ? 'pointer-events-none opacity-50' : ''}
              />
            </PaginationItem>

            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              // Show first page, last page, current page, and pages around current
              let pageNum: number | null = null;

              if (i === 0) {
                pageNum = 1;
              } else if (i === 4) {
                pageNum = totalPages;
              } else if (totalPages <= 5) {
                pageNum = i + 1;
              } else if (page <= 3) {
                pageNum = i + 1;
              } else if (page >= totalPages - 2) {
                pageNum = totalPages - 4 + i;
              } else {
                pageNum = page - 1 + i;
              }

              // Show ellipsis for gaps
              if (i === 1 && page > 3 && totalPages > 5) {
                return (
                  <PaginationItem key="ellipsis-start">
                    <PaginationEllipsis />
                  </PaginationItem>
                );
              }

              if (i === 3 && page < totalPages - 2 && totalPages > 5) {
                return (
                  <PaginationItem key="ellipsis-end">
                    <PaginationEllipsis />
                  </PaginationItem>
                );
              }

              if (pageNum !== null) {
                return (
                  <PaginationItem key={pageNum}>
                    <PaginationLink
                      isActive={page === pageNum}
                      onClick={() => onPageChange(pageNum!)}
                    >
                      {pageNum}
                    </PaginationLink>
                  </PaginationItem>
                );
              }

              return null;
            })}

            <PaginationItem>
              <PaginationNext
                onClick={() => page < totalPages && onPageChange(page + 1)}
                className={page >= totalPages ? 'pointer-events-none opacity-50' : ''}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}
    </motion.div>
  );
}
