import { AuditIssue, InvoiceData, LineItem } from "./invoice";

/**
 * Vendor Profile for storing vendor-specific extraction patterns
 */
export interface VendorProfile {
  id?: string;
  vendorId: string;
  userId: string;
  documentFormat?: string;
  extractionPatterns?: Record<string, unknown>;
  fieldMappings?: Record<string, string>;
  commonLineItems?: LineItem[];
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Invoice Audit information
 */
export interface InvoiceAudit {
  id?: string;
  invoiceId: string;
  status: 'PASS' | 'WARNING' | 'FAIL';
  fraudScore?: number;
  issues?: AuditIssue[];
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Invoice History for tracking changes
 */
export interface InvoiceHistory {
  id?: string;
  invoiceId: string;
  userId: string;
  action: 'CREATE' | 'UPDATE' | 'DELETE' | 'APPROVE' | 'REJECT' | 'PAY';
  changes?: Record<string, unknown>;
  timestamp?: Date;
}

/**
 * Currency Exchange Rate
 */
export interface CurrencyExchangeRate {
  id?: string;
  fromCurrency: string;
  toCurrency: string;
  rate: number;
  date: Date;
  source?: string;
  createdAt?: Date;
}

/**
 * Enhanced AI Settings
 */
export interface EnhancedAISettings {
  vendorProfiles?: Record<string, unknown>;
  fraudDetectionRules?: FraudDetectionRule[];
  learningFeedback?: LearningFeedback[];
  historicalPatterns?: HistoricalPattern[];
}

/**
 * Fraud Detection Rule
 */
export interface FraudDetectionRule {
  id: string;
  name: string;
  description: string;
  condition: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH';
  enabled: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Learning Feedback
 */
export interface LearningFeedback {
  id: string;
  invoiceId: string;
  field: string;
  originalValue: string;
  correctedValue: string;
  confidence: number;
  timestamp: Date;
}

/**
 * Historical Pattern
 */
export interface HistoricalPattern {
  id: string;
  vendorId?: string;
  patternType: 'AMOUNT' | 'FREQUENCY' | 'LINE_ITEMS' | 'PAYMENT_TERMS';
  pattern: Record<string, unknown>;
  confidence: number;
  occurrences: number;
  lastDetected: Date;
}

/**
 * Related Document
 */
export interface RelatedDocument {
  id: string;
  type: 'PURCHASE_ORDER' | 'RECEIPT' | 'DELIVERY_NOTE' | 'CONTRACT';
  documentNumber: string;
  fileUrl?: string;
  date?: Date;
  amount?: number;
  currency?: string;
}

/**
 * Payment Prediction
 */
export interface PaymentPrediction {
  predictedPayDate: Date;
  confidence: number;
  factors: string[];
  historicalAvgDays?: number;
  riskLevel?: 'LOW' | 'MEDIUM' | 'HIGH';
}

/**
 * Enhanced Invoice with additional fields
 */
export type EnhancedInvoice = InvoiceData & {
  auditStatus?: 'PASS' | 'WARNING' | 'FAIL';
  fraudScore?: number;
  relatedDocuments?: RelatedDocument[];
  paymentPrediction?: PaymentPrediction;
  exchangeRates?: Record<string, number>;
  audit?: InvoiceAudit;
  history?: InvoiceHistory[];
};

/**
 * Multi-Document Correlation Result
 */
export interface DocumentCorrelation {
  primaryDocument: EnhancedInvoice;
  relatedDocuments: RelatedDocument[];
  discrepancies?: {
    field: string;
    primaryValue: unknown;
    relatedValue: unknown;
    documentId: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH';
  }[];
  correlationScore: number;
}

/**
 * Vendor Analysis
 */
export interface VendorAnalysis {
  vendorId: string;
  vendorName: string;
  totalSpend: number;
  invoiceCount: number;
  averageAmount: number;
  paymentHistory: {
    onTimePercentage: number;
    averageDaysLate: number;
    latePayments: number;
  };
  pricingTrends: {
    period: string;
    averageAmount: number;
    changePercentage: number;
  }[];
  riskScore?: number;
}
