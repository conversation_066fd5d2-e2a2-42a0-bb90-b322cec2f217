import db from '@/db/db';

async function fixSubscriptionPlans() {
  // Find subscriptions using the placeholder plan
  const placeholderPlan = await db.plan.findFirst({
    where: { name: { contains: 'Paddle Plan undefined' } },
  });

  if (!placeholderPlan) {
    return { success: true, updated: 0 };
  }

  const subscriptions = await db.subscription.findMany({
    where: { planId: placeholderPlan.id },
    include: { user: true },
  });

  let updatedCount = 0;

  for (const subscription of subscriptions) {
    // If subscription has a Paddle price ID, try to match it to a real plan
    if (subscription.paddlePriceId) {
      const correctPlan = await db.plan.findFirst({
        where: { paddlePriceId: subscription.paddlePriceId },
      });

      if (correctPlan) {
        await db.subscription.update({
          where: { id: subscription.id },
          data: { planId: correctPlan.id },
        });
        updatedCount++;
      }
    } else {
      // If no Paddle price ID, try to guess based on price
      const price = parseInt(subscription.price);
      let matchingPlan = null;

      // Try to match by price
      const allPlans = await db.plan.findMany({
        where: { paddlePriceId: { not: null } },
      });

      for (const plan of allPlans) {
        if (parseInt(plan.price) === price) {
          matchingPlan = plan;
          break;
        }
      }

      if (matchingPlan) {
        await db.subscription.update({
          where: { id: subscription.id },
          data: {
            planId: matchingPlan.id,
            paddlePriceId: matchingPlan.paddlePriceId, // Also set the price ID for future reference
          },
        });
        updatedCount++;
      }
    }
  }

  // Try to delete the placeholder plan again
  const remainingSubscriptions = await db.subscription.count({
    where: { planId: placeholderPlan.id },
  });

  if (remainingSubscriptions === 0) {
    await db.plan.delete({
      where: { id: placeholderPlan.id },
    });
  }

  return { success: true, updated: updatedCount };
}

// Function to run the fix if this file is executed directly
if (require.main === module) {
  fixSubscriptionPlans()
    .then(() => {
      process.exit(0);
    })
    .catch(() => {
      process.exit(1);
    });
}
