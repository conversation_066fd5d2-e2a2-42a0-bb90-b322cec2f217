"use client";

import React, { useState, Suspense } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import Categories from '../management/Categories';
import Vendors from '../management/Vendors';
import { Grid, Store, Loader2 } from 'lucide-react';

// Loading component for suspense
function LoadingComponent() {
  return (
    <div className="flex items-center justify-center h-48">
      <Loader2 className="h-8 w-8 animate-spin text-primary" />
    </div>
  );
}                                                     

export default function Management() {
  const [activeTab, setActiveTab] = useState<string>('categories');

  return (
    <DashboardLayout>
      <div className="min-h-screen flex-col bg-background dark:bg-[#0B1739] w-full flex space-y-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Manage Your Business</h1>
          <p className="text-muted-foreground mt-1">
            Create and manage categories and vendors for your invoices.
          </p>
        </div>

        <Tabs defaultValue="categories" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full max-w-md grid-cols-2">
            <TabsTrigger value="categories" className="flex items-center">
              <Grid className="mr-2 h-4 w-4" />
              Categories
            </TabsTrigger>
            <TabsTrigger value="vendors" className="flex items-center">
              <Store className="mr-2 h-4 w-4" />
              Vendors
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="categories" className="pt-4">
            <Suspense fallback={<LoadingComponent />}>
              <Categories />
            </Suspense>
          </TabsContent>
          
          <TabsContent value="vendors" className="pt-4">
            <Suspense fallback={<LoadingComponent />}>
              <Vendors />
            </Suspense>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
} 