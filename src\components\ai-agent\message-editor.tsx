'use client';

import { ChatRequestOptions, Message } from 'ai';
import { Button } from '@/components/ui/button';
import { Dispatch, SetStateAction, useEffect, useRef, useState } from 'react';
import { Textarea } from '@/components/ui/textarea';
import { deleteTrailingMessages } from '@/app/dashboard/chat/actions';

export type MessageEditorProps = {
  message: Message;
  setMode: Dispatch<SetStateAction<'view' | 'edit'>>;
  setMessages: (
    messages: Message[] | ((messages: Message[]) => Message[]),
  ) => void;
  reload: (
    chatRequestOptions?: ChatRequestOptions,
  ) => Promise<string | null | undefined>;
};

export function MessageEditor({
  message,
  setMode,
  setMessages,
  reload,
}: MessageEditorProps) {
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  // Import the parseMessageContent function from message.tsx
  const parseContent = (content: unknown): string => {
    // If it's already a string, check if it's JSON
    if (typeof content === 'string') {
      try {
        // Try to parse it as JSON
        const parsed = JSON.parse(content);

        // If it's an array of objects with text property
        if (Array.isArray(parsed)) {
          return parsed.reduce((acc, item) => {
            if (item && typeof item === 'object' && 'type' in item && (item as { type: string }).type === 'text') {
              return acc + ((item as { text?: string }).text || '');
            }
            return acc;
          }, '');
        }

        // If it's an object with a text property
        if (parsed && typeof parsed === 'object' && 'text' in parsed) {
          const textValue = (parsed as { text: unknown }).text;
          return typeof textValue === 'string' ? textValue : '';
        }

        // If it's just a string in JSON format
        if (typeof parsed === 'string') {
          return parsed;
        }

        // Fallback to stringifying the parsed object
        return JSON.stringify(parsed);
      } catch {
        // If it's not valid JSON, just return the string
        return content;
      }
    }

    // If it's an array (like from the AI's response)
    if (Array.isArray(content)) {
      return content.reduce((acc, item) => {
        if (item && typeof item === 'object' && 'type' in item && (item as {type: string}).type === 'text') {
          // Ensure item.text is treated as potentially undefined string
          return acc + ((item as {text?: string}).text || '');
        }
        return acc;
      }, '');
    }

    // If it's an object, try to extract text
    if (content && typeof content === 'object') {
      if ('text' in content) {
        const textValue = (content as { text: unknown }).text;
        return typeof textValue === 'string' ? textValue : '';
      }
    }

    // Fallback to string conversion
    return String(content || '');
  };

  const [draftContent, setDraftContent] = useState<string>(parseContent(message.content));
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (textareaRef.current) {
      adjustHeight();
    }
  }, []);

  const adjustHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight + 2}px`;
    }
  };

  const handleInput = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setDraftContent(event.target.value);
    adjustHeight();
  };

  return (
    <div className="flex flex-col gap-2 w-full">
      <Textarea
        ref={textareaRef}
        className="bg-transparent outline-none overflow-hidden resize-none !text-base rounded-xl w-full"
        value={draftContent}
        onChange={handleInput}
      />

      <div className="flex flex-row gap-2 justify-end">
        <Button
          variant="outline"
          className="h-fit py-2 px-3"
          onClick={() => {
            setMode('view');
          }}
        >
          Cancel
        </Button>
        <Button
          variant="default"
          className="h-fit py-2 px-3"
          disabled={isSubmitting}
          onClick={async () => {
            setIsSubmitting(true);

            await deleteTrailingMessages({
              id: message.id,
            });

            setMessages((messages) => {
              const index = messages.findIndex((m) => m.id === message.id);

              if (index !== -1) {
                const updatedMessage = {
                  ...message,
                  content: draftContent,
                };

                return [...messages.slice(0, index), updatedMessage];
              }

              return messages;
            });

            setMode('view');
            reload();
          }}
        >
          {isSubmitting ? 'Sending...' : 'Send'}
        </Button>
      </div>
    </div>
  );
}
