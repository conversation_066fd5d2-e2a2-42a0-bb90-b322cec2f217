import type { InvoiceData, LineItem } from '@/types/invoice';

// Simplified currency mapping - only common currencies (covers ~80% of global usage)
const CURRENCY_TO_COUNTRY: Record<
  string,
  { country: string; countryCode: string; region: string }
> = {
  // Major currencies
  USD: {
    country: 'United States',
    countryCode: 'US',
    region: 'Americas',
  },
  EUR: {
    country: 'European Union',
    countryCode: 'EU',
    region: 'Europe',
  },
  GBP: {
    country: 'United Kingdom',
    countryCode: 'GB',
    region: 'Europe',
  },
  JPY: { country: 'Japan', countryCode: 'JP', region: 'Asia' },
  CNY: { country: 'China', countryCode: 'CN', region: 'Asia' },
  CAD: { country: 'Canada', countryCode: 'CA', region: 'Americas' },
  AUD: { country: 'Australia', countryCode: 'AU', region: 'Oceania' },
  CHF: {
    country: 'Switzerland',
    countryCode: 'CH',
    region: 'Europe',
  },

  // Additional common currencies
  INR: { country: 'India', countryCode: 'IN', region: 'Asia' },
  BRL: { country: 'Brazil', countryCode: 'BR', region: 'Americas' },
  KRW: { country: 'South Korea', countryCode: 'KR', region: 'Asia' },
  MXN: { country: 'Mexico', countryCode: 'MX', region: 'Americas' },
  SEK: { country: 'Sweden', countryCode: 'SE', region: 'Europe' },
  NOK: { country: 'Norway', countryCode: 'NO', region: 'Europe' },
  SGD: { country: 'Singapore', countryCode: 'SG', region: 'Asia' },
};

// Simplified tax rates - only essential countries
const COUNTRY_TAX_RATES: Record<
  string,
  {
    taxName: string;
    defaultRate: number;
  }
> = {
  // Major markets
  US: { taxName: 'Sales Tax', defaultRate: 0.08 },
  EU: { taxName: 'VAT', defaultRate: 0.2 },
  GB: { taxName: 'VAT', defaultRate: 0.2 },
  CA: { taxName: 'GST/HST', defaultRate: 0.13 },
  AU: { taxName: 'GST', defaultRate: 0.1 },
  JP: { taxName: 'Consumption Tax', defaultRate: 0.1 },
  CN: { taxName: 'VAT', defaultRate: 0.13 },
  IN: { taxName: 'GST', defaultRate: 0.18 },
  BR: { taxName: 'ICMS/IPI', defaultRate: 0.17 },
  CH: { taxName: 'MWST', defaultRate: 0.077 },
};

// Common language codes
const LANGUAGE_CODES: Record<string, string> = {
  en: 'English',
  es: 'Spanish',
  fr: 'French',
  de: 'German',
  it: 'Italian',
  pt: 'Portuguese',
  ja: 'Japanese',
  zh: 'Chinese',
  ar: 'Arabic',
  ru: 'Russian',
};

// Essential business categories
const BUSINESS_CATEGORIES = {
  Technology: [
    'software',
    'computer',
    'it services',
    'digital',
    'tech',
  ],
  'Office Supplies': [
    'office',
    'supplies',
    'stationery',
    'equipment',
  ],
  'Food & Beverage': ['restaurant', 'catering', 'food', 'grocery'],
  Healthcare: ['medical', 'health', 'doctor', 'pharmacy'],
  'Legal Services': ['law', 'legal', 'attorney'],
  Marketing: ['advertising', 'media', 'design'],
  Transportation: ['shipping', 'delivery', 'transport'],
  Utilities: ['electricity', 'water', 'gas', 'internet'],
  'Financial Services': ['banking', 'finance', 'accounting'],
  Consulting: ['consultant', 'advisory', 'business services'],
};

/**
 * Parse invoice text into structured data with enhanced capabilities
 */
export async function parseInvoiceData(
  text: string
): Promise<InvoiceData> {
  const startTime = Date.now();

  try {
    // Use external invoice extraction API with backend URL from environment
    const backendUrl = process.env.BACKEND_URL;

    if (!backendUrl) {
      throw new Error(
        'BACKEND_URL environment variable is not configured'
      );
    }

    const response = await fetch(
      `${backendUrl}/api/v1/invoice/extract/invoice`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: text,
        }),
      }
    );

    if (!response.ok) {
      throw new Error(
        `API call failed: ${response.status} ${response.statusText}`
      );
    }

    const apiResponseText = await response.text();

    // Check if this is a backend error response (JSON parsing failure)
    if (
      apiResponseText.includes('"detail":') &&
      apiResponseText.includes('Invalid json output')
    ) {
      // Try to extract the malformed JSON from the error message
      const jsonMatch = apiResponseText.match(
        /```json\n([\s\S]*?)\n```/
      );
      if (jsonMatch && jsonMatch[1]) {
        try {
          // Try to fix common JSON issues
            const fixedJson = jsonMatch[1]
            .replace(/\n/g, ' ') // Remove newlines
            .replace(/\s+/g, ' ') // Normalize whitespace
            .replace(/,\s*}/g, '}') // Remove trailing commas
            .replace(/,\s*]/g, ']') // Remove trailing commas in arrays
            .replace(/"\s*"/g, '""') // Fix empty quotes
            .replace(/"\s*([^"]*?)\s*"/g, '"$1"') // Trim quoted strings
            .trim();

          const parsed = JSON.parse(fixedJson);
          
          // Map the extracted data to InvoiceData format
          const parsedData: InvoiceData = {
            ...parsed,
            // Map "items" to "lineItems" if present
            lineItems: parsed.items || parsed.lineItems || []
          };
          
          // Remove the duplicate "items" field if it exists
          if ('items' in parsedData) {
            const { items: _items, ...dataWithoutItems } = parsedData as InvoiceData & { items?: unknown };
            Object.assign(parsedData, dataWithoutItems);
          }

          // Ensure lineItems exists
          if (!parsedData.lineItems) {
            parsedData.lineItems = [];
          }

          // Add metadata about the recovery
          if (!parsedData.meta) parsedData.meta = {};
          parsedData.meta.backendParsingIssue = true;
          parsedData.meta.recoveredFromError = true;

          return processInvoiceData(parsedData, text, startTime);
        } catch {
          throw new Error(
            'Failed to parse the extracted data from backend. Backend response may be malformed.'
          );
        }
      }

      // If we can't fix the JSON, throw a descriptive error
      throw new Error(
        'Backend failed to parse invoice structure. This often happens with complex line item tables. The invoice basic data may still be extractable, but line items extraction failed.'
      );
    }

    // Normal processing for successful responses
    const jsonStr = apiResponseText
      .trim()
      .replace(/```json|```/g, '')
      .trim();

    // Parse the JSON response
    try {
      const extractedData = JSON.parse(jsonStr);
      
      // Map the extracted data to InvoiceData format
      const invoiceData: InvoiceData = {
        ...extractedData,
        // Map "items" to "lineItems" if present
        lineItems: extractedData.items || extractedData.lineItems || []
      };
      

      
      // Remove the duplicate "items" field if it exists
      if ('items' in invoiceData) {
        const { items: _items, ...dataWithoutItems } = invoiceData as InvoiceData & { items?: unknown };
        Object.assign(invoiceData, dataWithoutItems);
      }

      // Ensure lineItems exists as an array
      if (!invoiceData.lineItems) {
        invoiceData.lineItems = [];
      }

      return processInvoiceData(invoiceData, text, startTime);
    } catch {
      // If parsing fails, try to extract a JSON object from the text
      const jsonMatch = apiResponseText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        try {
          const parsed = JSON.parse(jsonMatch[0]);
          
          // Map the extracted data to InvoiceData format
          const extractedData: InvoiceData = {
            ...parsed,
            // Map "items" to "lineItems" if present
            lineItems: parsed.items || parsed.lineItems || []
          };
          
          // Remove the duplicate "items" field if it exists
          if ('items' in extractedData) {
            const { items: _items, ...dataWithoutItems } = extractedData as InvoiceData & { items?: unknown };
            Object.assign(extractedData, dataWithoutItems);
          }

          // Ensure lineItems exists
          if (!extractedData.lineItems) {
            extractedData.lineItems = [];
          }

          // Add metadata about the recovery
          if (!extractedData.meta) extractedData.meta = {};
          extractedData.meta.backendParsingIssue = true;
          extractedData.meta.recoveredFromRegex = true;

          return processInvoiceData(extractedData, text, startTime);
        } catch {
          throw new Error(
            'Failed to parse the extracted data from backend. Backend response may be malformed.'
          );
        }
      }

      throw new Error(
        'Failed to parse the extracted data from backend. No valid JSON found in response.'
      );
    }
  } catch (error) {
    throw error;
  }
}

/**
 * Process the invoice data after successful parsing
 */
function processInvoiceData(
  invoiceData: InvoiceData,
  originalText: string,
  startTime: number
): InvoiceData {
  // Process country and tax information
  if (invoiceData.financials) {
    let countryCode = invoiceData.meta?.countryCode;

    // Enhance country detection based on currency if not already detected
    if (
      invoiceData.financials?.currency &&
      (!invoiceData.meta?.country || !invoiceData.meta?.countryCode)
    ) {
      const currencyCode = invoiceData.financials.currency
        .trim()
        .toUpperCase();
      const extractedCurrency = Object.keys(CURRENCY_TO_COUNTRY).find(
        (code) =>
          currencyCode.includes(code) ||
          currencyCode.includes(code.toLowerCase())
      );

      if (
        extractedCurrency &&
        CURRENCY_TO_COUNTRY[extractedCurrency]
      ) {
        if (!invoiceData.meta) invoiceData.meta = {};
        invoiceData.meta.country =
          invoiceData.meta.country ||
          CURRENCY_TO_COUNTRY[extractedCurrency].country;
        invoiceData.meta.countryCode =
          invoiceData.meta.countryCode ||
          CURRENCY_TO_COUNTRY[extractedCurrency].countryCode;
        countryCode = invoiceData.meta.countryCode;
      }
    }

    // If we have a tax amount but no tax rate, or vice versa, calculate the missing value
    if (countryCode && COUNTRY_TAX_RATES[countryCode]) {
      const defaultTaxInfo = COUNTRY_TAX_RATES[countryCode];

      // Add tax name if missing
      if (!invoiceData.financials.taxName && defaultTaxInfo.taxName) {
        invoiceData.financials.taxName = defaultTaxInfo.taxName;
      }

      // Calculate tax rate or amount if one is missing
      if (invoiceData.financials.subtotal) {
        const subtotal = parseFloat(
          String(invoiceData.financials.subtotal).replace(
            /[^0-9.-]+/g,
            ''
          )
        );

        if (!isNaN(subtotal)) {
          // If we have tax amount but no rate
          if (
            invoiceData.financials.tax &&
            !invoiceData.financials.taxRate
          ) {
            const taxAmount = parseFloat(
              String(invoiceData.financials.tax).replace(
                /[^0-9.-]+/g,
                ''
              )
            );
            if (!isNaN(taxAmount)) {
              const calculatedRate = (taxAmount / subtotal) * 100;
              invoiceData.financials.taxRate = `${calculatedRate.toFixed(2)}%`;
            }
          }
          // If we have tax rate but no amount
          else if (
            invoiceData.financials.taxRate &&
            !invoiceData.financials.tax
          ) {
            const rateStr = String(
              invoiceData.financials.taxRate
            ).replace(/[^0-9.-]+/g, '');
            let rate = parseFloat(rateStr);

            // Convert to decimal if it's a percentage
            if (
              rate > 1 &&
              !String(invoiceData.financials.taxRate).includes('.')
            ) {
              rate = rate / 100;
            }

            if (!isNaN(rate)) {
              const calculatedTax = subtotal * rate;
              // Format to match the currency format in the subtotal
              const formatter = new Intl.NumberFormat(
                invoiceData.meta?.language
                  ? `${invoiceData.meta.language}-${countryCode}`
                  : undefined,
                {
                  style: 'currency',
                  currency: invoiceData.financials.currency || 'USD',
                }
              );
              invoiceData.financials.tax =
                formatter.format(calculatedTax);
            }
          }
          // If neither tax rate nor amount, use default rate
          else if (
            !invoiceData.financials.tax &&
            !invoiceData.financials.taxRate
          ) {
            const defaultRate = defaultTaxInfo.defaultRate;
            const calculatedTax = subtotal * defaultRate;

            // Update only if we have a total to compare with
            const total = invoiceData.financials.total
              ? parseFloat(
                  String(invoiceData.financials.total).replace(
                    /[^0-9.-]+/g,
                    ''
                  )
                )
              : NaN;

            // Only set calculated tax if it's reasonably close to the difference between total and subtotal
            if (
              isNaN(total) ||
              Math.abs(total - subtotal - calculatedTax) <
                subtotal * 0.05
            ) {
              invoiceData.financials.taxRate = `${(defaultRate * 100).toFixed(2)}%`;

              // Format tax amount to match currency
              const formatter = new Intl.NumberFormat(
                invoiceData.meta?.language
                  ? `${invoiceData.meta.language}-${countryCode}`
                  : undefined,
                {
                  style: 'currency',
                  currency: invoiceData.financials.currency || 'USD',
                }
              );
              invoiceData.financials.tax =
                formatter.format(calculatedTax);
            }
          }
        }
      }
    }
  }

  // Enhance language name if we have the code
  if (invoiceData.meta?.language && !invoiceData.meta.languageName) {
    const langCode = invoiceData.meta.language
      .substring(0, 2)
      .toLowerCase();
    if (LANGUAGE_CODES[langCode]) {
      invoiceData.meta.languageName = LANGUAGE_CODES[langCode];
    }
  }

  // Ensure suggestions are present
  if (!invoiceData.meta) invoiceData.meta = {};
  if (!invoiceData.meta.suggestions)
    invoiceData.meta.suggestions = {};

  // Add category suggestions if missing
  if (!invoiceData.meta.suggestions.categories) {
    invoiceData.meta.suggestions.categories =
      inferMultipleCategories(invoiceData);
  }

  // Add vendor type suggestions if missing
  if (!invoiceData.meta.suggestions.vendorTypes) {
    invoiceData.meta.suggestions.vendorTypes =
      inferVendorTypes(invoiceData);
  }

  // Record processing time
  invoiceData.meta.processingTime = Date.now() - startTime;

  // Remove undefined, null, and empty string values
  return cleanInvoiceData(invoiceData, originalText);
}

/**
 * Remove null, undefined, and empty string values from the invoice data
 */
function cleanInvoiceData(
  data: InvoiceData,
  originalText: string
): InvoiceData {
  // Create a deep copy to avoid modifying the original
  const cleanedData: InvoiceData = {
    ...data,
    lineItems: Array.isArray(data.lineItems)
      ? [...data.lineItems]
      : [],
  };

  // Clean top-level fields
  Object.keys(cleanedData).forEach((key) => {
    if (key === 'lineItems') return; // Skip lineItems, we'll handle them separately

    const value = cleanedData[key];

    if (value === null || value === undefined || value === '') {
      delete cleanedData[key];
    } else if (typeof value === 'object') {
      // Clean nested objects
      const cleanedObj = cleanObjectValues(
        value as Record<string, unknown>
      );
      if (Object.keys(cleanedObj).length === 0) {
        delete cleanedData[key];
      } else {
        cleanedData[key] = cleanedObj;
      }
    }
  });

  // Clean line items
  if (Array.isArray(cleanedData.lineItems)) {
    cleanedData.lineItems = cleanedData.lineItems
      .map((item) => cleanObjectValues(item) as LineItem)
      .filter((item) => Object.keys(item).length > 0);
  }

  // If a vendor name is clearly present, remove vendor type suggestions
  if (
    cleanedData.vendor?.name &&
    cleanedData.vendor.name.trim().length > 0 &&
    cleanedData.meta?.suggestions?.vendorTypes &&
    cleanedData.meta.suggestions.vendorTypes.length > 0
  ) {
    // If vendor name is present, we shouldn't suggest vendor types
    if (!cleanedData.meta) cleanedData.meta = {};
    if (!cleanedData.meta.suggestions)
      cleanedData.meta.suggestions = {};

    // Replace vendor suggestions with just the actual vendor from the invoice
    cleanedData.meta.suggestions.vendorTypes = [
      {
        name: 'Vendor: ' + cleanedData.vendor.name,
        confidence: 98,
      },
    ];

    // Auto-select this vendor
    cleanedData.meta.suggestions.selectedVendorType =
      'Vendor: ' + cleanedData.vendor.name;
  }

  // Determine invoice type - add it if not already present
  if (!cleanedData.meta?.suggestions?.invoiceType) {
    // Default to PURCHASE (incoming invoice)
    let invoiceType: 'PURCHASE' | 'SALES' = 'PURCHASE';

    // Check for indications this is a SALES invoice (our org is sending it)
    const orgNames = [
      'your company',
      'our company',
      'your organization',
      'our organization',
    ];
    const orgSignatures = [
      'sincerely',
      'thank you for your business',
      'we appreciate your business',
    ];

    // If customer looks like an external entity and "we/our/your company" is mentioned, it's likely a SALES invoice
    if (
      cleanedData.customer?.name &&
      !orgNames.some((name) =>
        cleanedData.customer?.name?.toLowerCase().includes(name)
      ) &&
      (originalText
        .toLowerCase()
        .includes('invoice from your company') ||
        originalText
          .toLowerCase()
          .includes('invoice from our company') ||
        orgSignatures.some((sig) =>
          originalText.toLowerCase().includes(sig)
        ))
    ) {
      invoiceType = 'SALES';
    }

    if (!cleanedData.meta) cleanedData.meta = {};
    if (!cleanedData.meta.suggestions)
      cleanedData.meta.suggestions = {};

    // Add invoiceType to suggestions
    cleanedData.meta.suggestions.invoiceType = invoiceType;
  }

  return cleanedData;
}

/**
 * Clean object values recursively
 */
function cleanObjectValues(
  obj: Record<string, unknown>
): Record<string, unknown> {
  const cleaned: Record<string, unknown> = {};

  Object.keys(obj).forEach((key) => {
    const value = obj[key];

    if (value === null || value === undefined || value === '') {
      // Skip null, undefined, and empty string values
      return;
    }

    if (typeof value === 'object' && !Array.isArray(value)) {
      // Recursively clean nested objects
      const cleanedNested = cleanObjectValues(
        value as Record<string, unknown>
      );
      if (Object.keys(cleanedNested).length > 0) {
        cleaned[key] = cleanedNested;
      }
    } else if (Array.isArray(value)) {
      // Clean arrays by removing empty items
      const cleanedArray = value.filter(
        (item) => item !== null && item !== undefined && item !== ''
      );
      if (cleanedArray.length > 0) {
        cleaned[key] = cleanedArray;
      }
    } else {
      // Keep non-empty primitive values
      cleaned[key] = value;
    }
  });

  return cleaned;
}

/**
 * Infer multiple categories based on invoice content
 */
function inferMultipleCategories(
  data: InvoiceData
): Array<{ name: string; confidence: number }> {
  const categories: Array<{ name: string; confidence: number }> = [];

  // Check if category is already clearly mentioned
  const existingCategory = findExistingCategory(data);
  if (existingCategory) {
    categories.push({
      name: existingCategory,
      confidence: 95,
    });
    return categories;
  }

  // Analyze text content for category inference
  const textToAnalyze = [
    data.vendor?.name,
    data.vendor?.website,
    data.notes,
    data.additionalFields?.description,
    ...(data.lineItems?.map((item) => item.description) || []),
  ]
    .filter(Boolean)
    .join(' ')
    .toLowerCase();

  // Score each category based on keyword matches
  Object.entries(BUSINESS_CATEGORIES).forEach(
    ([category, keywords]) => {
      let score = 0;
      let matches = 0;

      keywords.forEach((keyword) => {
        if (textToAnalyze.includes(keyword.toLowerCase())) {
          score += 10;
          matches++;
        }
      });

      // Boost confidence for multiple keyword matches
      if (matches > 1) {
        score += matches * 5;
      }

      // Only include categories with reasonable confidence
      if (score > 0) {
        categories.push({
          name: category,
          confidence: Math.min(score, 85), // Cap at 85% for inferred categories
        });
      }
    }
  );

  // Sort by confidence and return top 3
  return categories
    .sort((a, b) => b.confidence - a.confidence)
    .slice(0, 3);
}

// Find if category is already clearly mentioned in the invoice
function findExistingCategory(data: InvoiceData): string | null {
  // Check if category is explicitly mentioned in various fields
  const categoryFields = [
    data.additionalFields?.category,
    data.additionalFields?.invoiceCategory,
    data.additionalFields?.documentCategory,
  ];

  for (const field of categoryFields) {
    if (
      field &&
      typeof field === 'string' &&
      field.trim().length > 0
    ) {
      return field.trim();
    }
  }

  // Check if category is mentioned in specific text fields
  const textToSearch = [
    data.notes,
    data.additionalFields?.description,
  ]
    .filter(Boolean)
    .join(' ')
    .toLowerCase();

  const categoryRegex = /category\s*:\s*([a-zA-Z\s&]+)/i;
  const match = textToSearch.match(categoryRegex);
  if (match && match[1]) {
    return match[1].trim();
  }

  return null;
}

/**
 * Infer vendor types based on invoice content
 */
function inferVendorTypes(
  data: InvoiceData
): Array<{ name: string; confidence: number }> {
  const vendorTypes: Array<{ name: string; confidence: number }> = [];

  // Check if vendor type is already clearly mentioned
  const existingVendorType = findExistingVendorType(data);
  if (existingVendorType) {
    vendorTypes.push({
      name: existingVendorType,
      confidence: 95,
    });
    return vendorTypes;
  }

  // Basic vendor type inference
  const textToAnalyze = [
    data.vendor?.name,
    data.vendor?.website,
    data.notes,
    data.additionalFields?.description,
  ]
    .filter(Boolean)
    .join(' ')
    .toLowerCase();

  // Simple vendor type detection
  if (
    textToAnalyze.includes('supplier') ||
    textToAnalyze.includes('vendor')
  ) {
    vendorTypes.push({ name: 'Supplier', confidence: 70 });
  } else if (
    textToAnalyze.includes('customer') ||
    textToAnalyze.includes('client')
  ) {
    vendorTypes.push({ name: 'Customer', confidence: 70 });
  } else if (
    textToAnalyze.includes('contractor') ||
    textToAnalyze.includes('freelancer')
  ) {
    vendorTypes.push({ name: 'Contractor', confidence: 70 });
  } else {
    // Default fallback
    vendorTypes.push({ name: 'Vendor', confidence: 50 });
  }

  return vendorTypes;
}

// Find if vendor type is already clearly mentioned in the invoice
function findExistingVendorType(data: InvoiceData): string | null {
  // Check if vendor type is explicitly mentioned in various fields
  const vendorTypeFields = [
    data.additionalFields?.vendorType,
    data.additionalFields?.supplierType,
    data.additionalFields?.customerType,
  ];

  for (const field of vendorTypeFields) {
    if (
      field &&
      typeof field === 'string' &&
      field.trim().length > 0
    ) {
      return field.trim();
    }
  }

  return null;
}
