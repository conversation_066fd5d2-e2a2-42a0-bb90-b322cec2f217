import { CacheEngine } from '../cache-engine';

// System control interfaces
interface PageDefinition {
  path: string;
  name: string;
  description: string;
  availableActions: ActionDefinition[];
  requiredPermissions: string[];
  contextData: string[];
  relatedPages: string[];
}

interface ActionDefinition {
  id: string;
  name: string;
  description: string;
  type: 'navigation' | 'form_submit' | 'data_fetch' | 'ui_control' | 'feature_toggle';
  parameters: ParameterDefinition[];
  permissions: string[];
  successMessage: string;
  errorHandling: string;
}

interface ParameterDefinition {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  required: boolean;
  description: string;
  validation?: string;
  defaultValue?: any;
}

interface SystemState {
  currentPage: string;
  userPermissions: string[];
  availableFeatures: string[];
  pageState: Record<string, any>;
  userPreferences: Record<string, any>;
  activeWorkflows: string[];
}

interface ControlAction {
  type: 'navigate' | 'execute' | 'configure' | 'automate';
  target: string;
  parameters: Record<string, any>;
  context: Record<string, any>;
}

interface ControlResult {
  success: boolean;
  message: string;
  data?: any;
  nextActions?: ControlAction[];
  pageUpdate?: Record<string, any>;
}

/**
 * System Controller - AI Agent's interface to control the application
 * Provides context about pages, features, and actions available to the user
 */
export class SystemController {
  
  // Define all pages and their capabilities
  private static readonly PAGE_DEFINITIONS: Record<string, PageDefinition> = {
    '/dashboard': {
      path: '/dashboard',
      name: 'Dashboard',
      description: 'Main overview page with financial summary and quick actions',
      availableActions: [
        {
          id: 'view_financial_summary',
          name: 'View Financial Summary',
          description: 'Display current financial metrics and KPIs',
          type: 'data_fetch',
          parameters: [
            { name: 'period', type: 'string', required: false, description: 'Time period for summary' }
          ],
          permissions: ['read:financial_data'],
          successMessage: 'Financial summary loaded',
          errorHandling: 'Show cached data if available'
        },
        {
          id: 'create_quick_invoice',
          name: 'Create Quick Invoice',
          description: 'Open invoice creation form with pre-filled data',
          type: 'navigation',
          parameters: [
            { name: 'vendor', type: 'string', required: false, description: 'Pre-fill vendor name' },
            { name: 'amount', type: 'number', required: false, description: 'Pre-fill amount' }
          ],
          permissions: ['create:invoice'],
          successMessage: 'Invoice creation started',
          errorHandling: 'Redirect to invoices page'
        }
      ],
      requiredPermissions: ['read:dashboard'],
      contextData: ['financial_summary', 'recent_invoices', 'pending_payments', 'alerts'],
      relatedPages: ['/invoices', '/analytics', '/reports']
    },

    '/invoices': {
      path: '/invoices',
      name: 'Invoices',
      description: 'Invoice management page for creating, viewing, and managing invoices',
      availableActions: [
        {
          id: 'create_invoice',
          name: 'Create New Invoice',
          description: 'Create a new invoice with specified details',
          type: 'form_submit',
          parameters: [
            { name: 'vendorName', type: 'string', required: true, description: 'Vendor or client name' },
            { name: 'amount', type: 'number', required: true, description: 'Invoice amount' },
            { name: 'currency', type: 'string', required: false, description: 'Currency code', defaultValue: 'USD' },
            { name: 'dueDate', type: 'string', required: false, description: 'Due date in YYYY-MM-DD format' },
            { name: 'description', type: 'string', required: false, description: 'Invoice description' }
          ],
          permissions: ['create:invoice'],
          successMessage: 'Invoice created successfully',
          errorHandling: 'Validate required fields and show specific errors'
        },
        {
          id: 'filter_invoices',
          name: 'Filter Invoices',
          description: 'Filter invoices by status, date, or amount',
          type: 'ui_control',
          parameters: [
            { name: 'status', type: 'string', required: false, description: 'Filter by status: PENDING, PAID, OVERDUE' },
            { name: 'dateRange', type: 'object', required: false, description: 'Date range filter' },
            { name: 'minAmount', type: 'number', required: false, description: 'Minimum amount filter' }
          ],
          permissions: ['read:invoices'],
          successMessage: 'Invoices filtered',
          errorHandling: 'Reset to default view if filter fails'
        },
        {
          id: 'export_invoices',
          name: 'Export Invoices',
          description: 'Export filtered invoices to PDF or Excel',
          type: 'data_fetch',
          parameters: [
            { name: 'format', type: 'string', required: true, description: 'Export format: PDF or EXCEL' },
            { name: 'filters', type: 'object', required: false, description: 'Applied filters' }
          ],
          permissions: ['export:invoices'],
          successMessage: 'Export generated successfully',
          errorHandling: 'Provide alternative export options'
        }
      ],
      requiredPermissions: ['read:invoices'],
      contextData: ['invoice_list', 'invoice_stats', 'filters', 'pagination'],
      relatedPages: ['/dashboard', '/vendors', '/reports']
    },

    '/analytics': {
      path: '/analytics',
      name: 'Analytics',
      description: 'Financial analytics and business intelligence dashboard',
      availableActions: [
        {
          id: 'generate_chart',
          name: 'Generate Chart',
          description: 'Create a specific chart or visualization',
          type: 'ui_control',
          parameters: [
            { name: 'chartType', type: 'string', required: true, description: 'Type of chart: bar, line, pie, etc.' },
            { name: 'dataSource', type: 'string', required: true, description: 'Data source for chart' },
            { name: 'timeRange', type: 'string', required: false, description: 'Time range for data' }
          ],
          permissions: ['read:analytics'],
          successMessage: 'Chart generated',
          errorHandling: 'Show default chart if generation fails'
        },
        {
          id: 'export_analytics',
          name: 'Export Analytics',
          description: 'Export analytics data or charts',
          type: 'data_fetch',
          parameters: [
            { name: 'format', type: 'string', required: true, description: 'Export format' },
            { name: 'includeCharts', type: 'boolean', required: false, description: 'Include charts in export' }
          ],
          permissions: ['export:analytics'],
          successMessage: 'Analytics exported',
          errorHandling: 'Provide data-only export if charts fail'
        }
      ],
      requiredPermissions: ['read:analytics'],
      contextData: ['charts', 'metrics', 'trends', 'comparisons'],
      relatedPages: ['/dashboard', '/reports', '/invoices']
    },

    '/reports': {
      path: '/reports',
      name: 'Reports',
      description: 'Financial reports generation and management',
      availableActions: [
        {
          id: 'generate_report',
          name: 'Generate Report',
          description: 'Generate a financial report',
          type: 'form_submit',
          parameters: [
            { name: 'reportType', type: 'string', required: true, description: 'Type of report to generate' },
            { name: 'period', type: 'string', required: true, description: 'Reporting period' },
            { name: 'format', type: 'string', required: false, description: 'Output format', defaultValue: 'PDF' }
          ],
          permissions: ['create:report'],
          successMessage: 'Report generated successfully',
          errorHandling: 'Provide alternative report formats'
        }
      ],
      requiredPermissions: ['read:reports'],
      contextData: ['report_list', 'report_templates', 'generation_status'],
      relatedPages: ['/analytics', '/dashboard']
    },

    '/settings': {
      path: '/settings',
      name: 'Settings',
      description: 'Application settings and preferences',
      availableActions: [
        {
          id: 'update_preferences',
          name: 'Update Preferences',
          description: 'Update user preferences and settings',
          type: 'form_submit',
          parameters: [
            { name: 'category', type: 'string', required: true, description: 'Settings category' },
            { name: 'settings', type: 'object', required: true, description: 'Settings to update' }
          ],
          permissions: ['update:settings'],
          successMessage: 'Settings updated',
          errorHandling: 'Revert to previous settings on error'
        }
      ],
      requiredPermissions: ['read:settings'],
      contextData: ['user_preferences', 'system_settings', 'integrations'],
      relatedPages: ['/dashboard']
    }
  };

  /**
   * Get current system state and available actions
   */
  static async getSystemState(userId: string, currentPage?: string): Promise<SystemState> {
    const cacheKey = CacheEngine.generateKey('system-state', userId, currentPage || 'unknown');
    
    return CacheEngine.getOrSet(
      cacheKey,
      'user-context', // 5-minute cache
      async () => {
        const [userPermissions, userPreferences] = await Promise.all([
          this.getUserPermissions(userId),
          this.getUserPreferences(userId)
        ]);

        return {
          currentPage: currentPage || '/dashboard',
          userPermissions,
          availableFeatures: this.getAvailableFeatures(userPermissions),
          pageState: await this.getPageState(currentPage || '/dashboard', userId),
          userPreferences,
          activeWorkflows: await this.getActiveWorkflows(userId)
        };
      }
    );
  }

  /**
   * Get page definition and available actions
   */
  static getPageDefinition(page: string): PageDefinition | null {
    return this.PAGE_DEFINITIONS[page] || null;
  }

  /**
   * Get all available actions for current context
   */
  static getAvailableActions(page: string, userPermissions: string[]): ActionDefinition[] {
    const pageDefinition = this.getPageDefinition(page);
    if (!pageDefinition) return [];

    return pageDefinition.availableActions.filter(action =>
      action.permissions.every(permission => userPermissions.includes(permission))
    );
  }

  /**
   * Execute a control action
   */
  static async executeAction(
    userId: string,
    action: ControlAction
  ): Promise<ControlResult> {
    try {
      const systemState = await this.getSystemState(userId, action.context.currentPage);
      
      // Validate permissions
      const pageDefinition = this.getPageDefinition(action.target);
      if (!pageDefinition) {
        return {
          success: false,
          message: `Unknown page: ${action.target}`
        };
      }

      // Find the action definition
      const actionDef = pageDefinition.availableActions.find(a => a.id === action.type);
      if (!actionDef) {
        return {
          success: false,
          message: `Unknown action: ${action.type} on page ${action.target}`
        };
      }

      // Check permissions
      const hasPermissions = actionDef.permissions.every(permission =>
        systemState.userPermissions.includes(permission)
      );

      if (!hasPermissions) {
        return {
          success: false,
          message: 'Insufficient permissions for this action'
        };
      }

      // Execute the action based on type
      const result = await this.performAction(actionDef, action.parameters, userId);
      
      // Record the action for learning
      await this.recordActionExecution(userId, action, result);

      return result;
    } catch (error) {
      console.error('Action execution error:', error);
      return {
        success: false,
        message: 'Failed to execute action'
      };
    }
  }

  /**
   * Get contextual help for current page
   */
  static getContextualHelp(page: string, userLevel: 'beginner' | 'intermediate' | 'expert'): string {
    const pageDefinition = this.getPageDefinition(page);
    if (!pageDefinition) return 'Page not found';

    const helpTexts = {
      beginner: `You're on the ${pageDefinition.name} page. ${pageDefinition.description}. Available actions: ${pageDefinition.availableActions.map(a => a.name).join(', ')}.`,
      intermediate: `${pageDefinition.name}: ${pageDefinition.description}. Key features: ${pageDefinition.availableActions.slice(0, 3).map(a => a.name).join(', ')}.`,
      expert: `${pageDefinition.name} - ${pageDefinition.availableActions.length} actions available.`
    };

    return helpTexts[userLevel];
  }

  /**
   * Suggest next actions based on context
   */
  static async suggestNextActions(
    userId: string,
    currentPage: string,
    userIntent?: string
  ): Promise<ControlAction[]> {
    const systemState = await this.getSystemState(userId, currentPage);
    const suggestions: ControlAction[] = [];

    // Get user patterns to suggest relevant actions
    const { LongTermMemory } = await import('../memory/long-term-memory');
    const userHistory = await LongTermMemory.getUserHistory(userId);

    // Suggest based on common workflows
    const commonWorkflow = userHistory.workflowPatterns.find(w => 
      w.steps.some(step => step.page === currentPage)
    );

    if (commonWorkflow) {
      const currentStepIndex = commonWorkflow.steps.findIndex(step => step.page === currentPage);
      const nextStep = commonWorkflow.steps[currentStepIndex + 1];
      
      if (nextStep) {
        suggestions.push({
          type: 'navigate',
          target: nextStep.page,
          parameters: { action: nextStep.action },
          context: { workflow: commonWorkflow.name }
        });
      }
    }

    // Suggest based on page usage patterns
    const pagePattern = userHistory.pageUsage.find(p => p.page === currentPage);
    if (pagePattern) {
      pagePattern.commonActions.forEach(action => {
        suggestions.push({
          type: 'execute',
          target: currentPage,
          parameters: { action },
          context: { frequency: 'common' }
        });
      });
    }

    return suggestions.slice(0, 3); // Top 3 suggestions
  }

  // Private helper methods
  private static async getUserPermissions(userId: string): Promise<string[]> {
    // Get user permissions from database or auth system
    // For now, return default permissions
    return [
      'read:dashboard',
      'read:invoices',
      'create:invoice',
      'read:analytics',
      'read:reports',
      'create:report',
      'export:invoices',
      'export:analytics',
      'read:settings',
      'update:settings'
    ];
  }

  private static async getUserPreferences(userId: string): Promise<Record<string, any>> {
    try {
      const { default: db } = await import('@/db/db');
      const user = await db.user.findUnique({
        where: { id: userId },
        select: { aiSettings: true }
      });
      
      return user?.aiSettings || {};
    } catch (error) {
      return {};
    }
  }

  private static getAvailableFeatures(permissions: string[]): string[] {
    const features: string[] = [];
    
    if (permissions.includes('create:invoice')) features.push('invoice_creation');
    if (permissions.includes('create:report')) features.push('report_generation');
    if (permissions.includes('export:invoices')) features.push('data_export');
    if (permissions.includes('read:analytics')) features.push('analytics_dashboard');
    
    return features;
  }

  private static async getPageState(page: string, userId: string): Promise<Record<string, any>> {
    // Get current state of the page (filters, selections, etc.)
    // This would be enhanced with actual page state tracking
    return {
      filters: {},
      selections: [],
      viewMode: 'default'
    };
  }

  private static async getActiveWorkflows(userId: string): Promise<string[]> {
    // Get any active workflows or processes
    return [];
  }

  private static async performAction(
    actionDef: ActionDefinition,
    parameters: Record<string, any>,
    userId: string
  ): Promise<ControlResult> {
    // This would integrate with actual application APIs
    // For now, return a success response
    return {
      success: true,
      message: actionDef.successMessage,
      data: { actionId: actionDef.id, parameters }
    };
  }

  private static async recordActionExecution(
    userId: string,
    action: ControlAction,
    result: ControlResult
  ): Promise<void> {
    const { LongTermMemory } = await import('../memory/long-term-memory');
    await LongTermMemory.recordUserInteraction(userId, {
      page: action.target,
      action: action.type,
      context: action.parameters,
      outcome: result.success ? 'success' : 'error',
      duration: 0 // Would be tracked in real implementation
    });
  }
}
