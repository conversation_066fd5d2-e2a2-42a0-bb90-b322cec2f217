import { NextRequest, NextResponse } from 'next/server';
import db from '@/db/db';
import { auth } from '@clerk/nextjs/server';

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const query = request.nextUrl.searchParams.get('query');
    if (!query) {
      return NextResponse.json(
        { error: 'Missing query parameter' },
        { status: 400 }
      );
    }

    // Get user's organization
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      include: { organizations: true }
    });
    
    if (!user || !user.organizations.length) {
      return NextResponse.json(
        { error: 'No organization found' },
        { status: 404 }
      );
    }
    
    const organizationId = user.organizations[0].id;

    // Get analytics data based on the query type
    if (query.toLowerCase().includes('spending trends') || query.toLowerCase().includes('quarterly')) {
      // Get spending summary by quarter
      const currentDate = new Date();
      
      // Start of current quarter
      const currentQuarter = new Date(currentDate.getFullYear(), Math.floor(currentDate.getMonth() / 3) * 3, 1);
      
      // Start of last quarter
      const lastQuarter = new Date(currentQuarter);
      lastQuarter.setMonth(lastQuarter.getMonth() - 3);
      
      // Current quarter data
      const currentQuarterInvoices = await db.invoice.findMany({
        where: {
          organizationId,
          createdAt: {
            gte: currentQuarter,
            lt: new Date() // up to now
          }
        },
        select: {
          amount: true,
          status: true,
          category: true
        }
      });
      
      // Last quarter data
      const lastQuarterInvoices = await db.invoice.findMany({
        where: {
          organizationId,
          createdAt: {
            gte: lastQuarter,
            lt: currentQuarter
          }
        },
        select: {
          amount: true,
          status: true,
          category: true
        }
      });
      
      // Calculate quarterly totals
      const currentQuarterTotal = currentQuarterInvoices.reduce(
        (sum, invoice) => sum + (Number(invoice.amount) || 0), 
        0
      );
      
      const lastQuarterTotal = lastQuarterInvoices.reduce(
        (sum, invoice) => sum + (Number(invoice.amount) || 0), 
        0
      );
      
      // Calculate the percentage change
      const quarterlyChange = lastQuarterTotal > 0
        ? ((currentQuarterTotal - lastQuarterTotal) / lastQuarterTotal * 100).toFixed(1) + '%'
        : 'N/A';
      
      // Get categories for the current quarter
      const categoryTotals: Record<string, number> = {};
      
      currentQuarterInvoices.forEach(invoice => {
        // Make sure category is a string
        const categoryName = String(invoice.category || 'Uncategorized');
        categoryTotals[categoryName] = (categoryTotals[categoryName] || 0) + (Number(invoice.amount) || 0);
      });
      
      // Convert to array and sort
      const categories = Object.entries(categoryTotals).map(([name, total]) => ({
        category: name,
        total
      })).sort((a, b) => (b.total as number) - (a.total as number));
      
      // Calculate the monthly breakdown for the current quarter
      const months = ['January', 'February', 'March', 'April', 'May', 'June', 
                     'July', 'August', 'September', 'October', 'November', 'December'];
      
      const monthlyData = [];
      
      // Collect data for each month in the current quarter
      for (let i = 0; i < 3; i++) {
        const monthIndex = (Math.floor(currentDate.getMonth() / 3) * 3) + i;
        const monthName = months[monthIndex % 12];
        
        const startOfMonth = new Date(currentDate.getFullYear(), monthIndex, 1);
        const endOfMonth = new Date(currentDate.getFullYear(), monthIndex + 1, 0);
        
        // Fetch invoices for this month
        const monthInvoices = await db.invoice.findMany({
          where: {
            organizationId,
            createdAt: {
              gte: startOfMonth,
              lte: endOfMonth
            }
          },
          select: {
            amount: true,
            status: true
          }
        });
        
        const monthTotal = monthInvoices.reduce(
          (sum, invoice) => sum + (Number(invoice.amount) || 0), 
          0
        );
        
        monthlyData.push({
          month: monthName,
          amount: monthTotal
        });
      }
      
      return NextResponse.json({
        query,
        results: {
          spendingTrends: monthlyData,
          topCategories: categories.slice(0, 5).map(cat => ({
            category: cat.category,
            percentage: Math.round((cat.total as number) / (currentQuarterTotal || 1) * 100)
          })),
          quarterlyComparison: {
            current: currentQuarterTotal,
            previous: lastQuarterTotal,
            change: quarterlyChange
          }
        }
      });
    } else {
      // Default analytics dashboard
      const invoiceStats = await db.invoice.groupBy({
        by: ['status'],
        where: {
          organizationId
        },
        _count: {
          id: true
        },
        _sum: {
          amount: true
        }
      });
      
      // Calculate totals
      let total = 0;
      let paid = 0;
      let unpaid = 0;
      let overdue = 0;
      
      invoiceStats.forEach(stat => {
        const count = stat._count.id;
        total += count;
        
        if (stat.status === 'PAID') {
          paid = count;
        } else if (stat.status === 'PENDING') { // Changed from UNPAID to PENDING to match schema
          unpaid = count;
        } else if (stat.status === 'OVERDUE') {
          overdue = count;
        }
      });
      
      // Get recent activity
      const recentInvoices = await db.invoice.findMany({
        where: { organizationId },
        orderBy: { createdAt: 'desc' },
        take: 5,
        select: {
          id: true,
          title: true,
          amount: true,
          currency: true,
          status: true,
          dueDate: true,
          createdAt: true
        }
      });
      
      return NextResponse.json({
        query,
        results: {
          summary: {
            total,
            paid,
            unpaid,
            overdue,
            paidPercentage: total ? Math.round((paid / total) * 100) : 0
          },
          recentActivity: recentInvoices
        }
      });
    }
  } catch (error) {
    console.error('Analytics API error:', error);
    return NextResponse.json(
      { error: 'Error processing analytics request' },
      { status: 500 }
    );
  }
} 