'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';

interface InvoiceNotesCardProps {
  invoice: {
    notes?: string | null;
    extractedData?: {
      notes?: string;
      termsAndConditions?: string;
    };
  };
}

export function InvoiceNotesCard({ invoice }: InvoiceNotesCardProps) {
  const hasNotes = invoice.notes || invoice.extractedData?.notes;
  const hasTerms = invoice.extractedData?.termsAndConditions;

  if (!hasNotes && !hasTerms) {
    return null;
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">Notes & Additional Information</CardTitle>
      </CardHeader>
      <CardContent>
        {hasNotes && (
          <div className="mb-4">
            <h3 className="text-sm font-medium mb-2">Notes</h3>
            <p className="text-sm text-muted-foreground">
              {invoice.notes || invoice.extractedData?.notes}
            </p>
          </div>
        )}

        {hasTerms && (
          <div>
            <h3 className="text-sm font-medium mb-2">Terms and Conditions</h3>
            <p className="text-sm text-muted-foreground">
              {invoice.extractedData?.termsAndConditions}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}