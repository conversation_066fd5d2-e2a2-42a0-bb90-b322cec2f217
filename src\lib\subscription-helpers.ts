import { auth } from '@clerk/nextjs/server';
import db from '@/db/db';

export interface SubscriptionStatus {
  hasActiveSubscription: boolean;
  subscription?: {
    id: number;
    status: string;
    planId: number;
    provider: string;
    renewsAt: string | null;
    endsAt: string | null;
  } | null;
}

/**
 * Check if the current user has an active subscription
 */
export async function checkUserSubscription(): Promise<SubscriptionStatus> {
  try {
    const { userId: clerkUserId } = await auth();

    if (!clerkUserId) {
      return { hasActiveSubscription: false };
    }

    // Get the database user
    const dbUser = await db.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    if (!dbUser) {
      return { hasActiveSubscription: false };
    }

    // Check for active subscriptions (including both Paddle and LemonSqueezy)
    const activeSubscription = await db.subscription.findFirst({
      where: {
        userId: dbUser.id,
        status: {
          in: ['active', 'trialing', 'past_due'], // Consider these as active
        },
      },
      orderBy: {
        id: 'desc', // Get the most recent subscription
      },
    });

    if (!activeSubscription) {
      return { hasActiveSubscription: false };
    }

    // Additional check for expired subscriptions
    const now = new Date();
    const isExpired =
      activeSubscription.endsAt &&
      new Date(activeSubscription.endsAt) < now;

    if (isExpired) {
      return { hasActiveSubscription: false };
    }

    return {
      hasActiveSubscription: true,
      subscription: {
        id: activeSubscription.id,
        status: activeSubscription.status,
        planId: activeSubscription.planId,
        provider:
          (activeSubscription as { provider?: string }).provider ||
          'paddle',
        renewsAt: activeSubscription.renewsAt,
        endsAt: activeSubscription.endsAt,
      },
    };
  } catch {
    return { hasActiveSubscription: false };
  }
}

/**
 * Check if user has access to premium features
 */
export async function hasAccessToFeature(): Promise<boolean> {
  const { hasActiveSubscription, subscription } =
    await checkUserSubscription();

  if (!hasActiveSubscription || !subscription) {
    return false;
  }

  // You can extend this logic based on plan tiers
  // For now, any active subscription grants access to all features
  return true;
}

/**
 * Get subscription redirect URL based on user state
 */
export function getSubscriptionRedirectUrl(
  returnUrl?: string
): string {
  const baseUrl =
    process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
  const encodedReturnUrl = returnUrl
    ? encodeURIComponent(returnUrl)
    : '';

  return encodedReturnUrl
    ? `${baseUrl}/pricing?return_url=${encodedReturnUrl}`
    : `${baseUrl}/pricing`;
}
