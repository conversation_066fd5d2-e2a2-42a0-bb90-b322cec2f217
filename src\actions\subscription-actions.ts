'use server';

import { createPaddleCheckout } from '@/actions/paddle-actions';
import { redirect } from 'next/navigation';
import db from '@/db/db';

export async function handleUpgrade(priceId: string) {
  if (!priceId || typeof priceId !== 'string') {
    throw new Error('Please provide a valid priceId.');
  }

  const result = await createPaddleCheckout(priceId);
  if (result.success && result.data?.checkout?.url) {
    redirect(result.data.checkout.url);
  } else {
    throw new Error(
      result.error || 'Failed to create checkout session'
    );
  }
}

export async function reprocessWebhookEvents(limit?: number) {
  try {
    // Get all unprocessed webhook events
    const webhookEvents = await db.webhookEvent.findMany({
      where: { processed: false },
      orderBy: { createdAt: 'asc' },
      take: limit || 10, // Use provided limit or default to 10
    });

    if (webhookEvents.length === 0) {
      return {
        success: true,
        message: 'No unprocessed webhook events found',
      };
    }

    // Mark all events as processed since we no longer have Lemon Squeezy processing
    // This is a cleanup function for old webhook events
    await db.webhookEvent.updateMany({
      where: { processed: false },
      data: {
        processed: true,
        processingError:
          'Lemon Squeezy integration removed - marked as processed',
      },
    });

    return {
      success: true,
      message: `Marked ${webhookEvents.length} old webhook events as processed`,
    };
  } catch (error) {
    return { success: false, message: String(error) };
  }
}

export async function updatePaymentMethod() {
  // TODO: Implement payment method update logic
  // This is a placeholder for the actual implementation
  return;
}
