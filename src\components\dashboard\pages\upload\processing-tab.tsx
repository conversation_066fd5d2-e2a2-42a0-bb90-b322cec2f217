"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ProcessingFileCard } from "./processing-file-card";
import { type InvoiceData } from "@/types/invoice";
import { ProcessingFile } from "./types";
import { AlertCircle, Check, X, RefreshCcw, UploadCloud, Settings2 } from "lucide-react";

interface ProcessingTabProps {
  processingFiles: ProcessingFile[];
  isProcessing: boolean;
  isSaving: boolean;
  error: string | null;
  isApiKeyError: boolean;
  showSuccess: boolean;
  onUpdateData: (index: number, data: InvoiceData) => void;
  onGoToUpload: () => void;
}

export function ProcessingTab({
  processingFiles,
  isProcessing,
  isSaving,
  error,
  isApiKeyError,
  showSuccess,
  onUpdateData,
  onGoToUpload
}: ProcessingTabProps) {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  
  // Calculate summary counts
  const completedCount = processingFiles.filter(file => file.status === 'completed').length;
  const errorCount = processingFiles.filter(file => file.status === 'error').length;
  const pendingCount = processingFiles.filter(file => file.status === 'pending').length;
  const processingCount = processingFiles.filter(file => file.status === 'processing').length;
  const totalCount = processingFiles.length;
  
  const categories = [
    { id: null, label: "All", count: totalCount },
    { id: "completed", label: "Completed", count: completedCount },
    { id: "error", label: "Failed", count: errorCount },
    { id: "processing", label: "Processing", count: processingCount },
    { id: "pending", label: "Pending", count: pendingCount },
  ];
  
  // Filter files based on selected category
  const filteredFiles = selectedCategory 
    ? processingFiles.filter(file => file.status === selectedCategory)
    : processingFiles;
  
  // Calculate overall success rate
  const successRate = totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0;
  
  // Calculate average processing time for completed files
  const completedFiles = processingFiles.filter(file => file.status === 'completed' && file.data?.meta?.processingTime);
  const avgProcessingTime = completedFiles.length > 0
    ? completedFiles.reduce((sum, file) => sum + (file.data?.meta?.processingTime || 0), 0) / completedFiles.length
    : 0;
  
  // Format processing time 
  const formatProcessingTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };
  
  return (
    <div className="space-y-6">
      {/* Status summary cards */}
      {processingFiles.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="bg-gradient-to-br from-blue-50/80 to-indigo-50/80 dark:from-blue-950/20 dark:to-indigo-950/30 border border-blue-100 dark:border-blue-900/50 shadow-sm">
            <CardContent className="pt-6">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Total Files</p>
                  <h3 className="text-2xl font-bold">{totalCount}</h3>
                </div>
                <div className="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900/40 flex items-center justify-center">
                  <UploadCloud className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-br from-green-50/80 to-emerald-50/80 dark:from-green-950/20 dark:to-emerald-950/30 border border-green-100 dark:border-green-900/50 shadow-sm">
            <CardContent className="pt-6">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm font-medium text-green-600 dark:text-green-400">Completed</p>
                  <h3 className="text-2xl font-bold">{completedCount}</h3>
                </div>
                <div className="h-10 w-10 rounded-full bg-green-100 dark:bg-green-900/40 flex items-center justify-center">
                  <Check className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
              </div>
              <div className="mt-2">
                <p className="text-xs text-green-800 dark:text-green-300">{successRate}% success rate</p>
                <div className="h-1.5 w-full bg-green-100 dark:bg-green-900/40 rounded-full mt-1">
                  <div 
                    className="h-full bg-green-500 dark:bg-green-500/70 rounded-full" 
                    style={{ width: `${successRate}%` }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className={`${errorCount > 0 ? 
            'bg-gradient-to-br from-red-50/80 to-rose-50/80 dark:from-red-950/20 dark:to-rose-950/30 border border-red-100 dark:border-red-900/50' : 
            'bg-gradient-to-br from-gray-50/80 to-slate-50/80 dark:from-gray-900/20 dark:to-slate-900/30 border border-gray-100 dark:border-gray-800/50'} shadow-sm`}>
            <CardContent className="pt-6">
              <div className="flex justify-between items-center">
                <div>
                  <p className={`text-sm font-medium ${errorCount > 0 ? 'text-red-600 dark:text-red-400' : 'text-gray-600 dark:text-gray-400'}`}>Failed</p>
                  <h3 className="text-2xl font-bold">{errorCount}</h3>
                </div>
                <div className={`h-10 w-10 rounded-full ${errorCount > 0 ? 'bg-red-100 dark:bg-red-900/40' : 'bg-gray-100 dark:bg-gray-800/40'} flex items-center justify-center`}>
                  <X className={`h-5 w-5 ${errorCount > 0 ? 'text-red-600 dark:text-red-400' : 'text-gray-600 dark:text-gray-400'}`} />
                </div>
              </div>
              {errorCount > 0 && (
                <p className="text-xs text-red-600 dark:text-red-400 mt-2">
                  {Math.round((errorCount / totalCount) * 100)}% of files failed
                </p>
              )}
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-br from-amber-50/80 to-yellow-50/80 dark:from-amber-950/20 dark:to-yellow-950/20 border border-amber-100 dark:border-amber-900/50 shadow-sm">
            <CardContent className="pt-6">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm font-medium text-amber-600 dark:text-amber-400">Processing Time</p>
                  <h3 className="text-2xl font-bold">
                    {avgProcessingTime > 0 ? formatProcessingTime(avgProcessingTime) : '—'}
                  </h3>
                </div>
                <div className="h-10 w-10 rounded-full bg-amber-100 dark:bg-amber-900/40 flex items-center justify-center">
                  <Settings2 className="h-5 w-5 text-amber-600 dark:text-amber-400" />
                </div>
              </div>
              <p className="text-xs text-amber-800 dark:text-amber-300 mt-2">
                {completedFiles.length > 0 ? `Average for ${completedFiles.length} files` : 'No completed files yet'}
              </p>
            </CardContent>
          </Card>
        </div>
      )}
      
      {/* Error messages */}
      {error && (
        <Alert variant="destructive" className="border-red-200 dark:border-red-900 bg-red-50 dark:bg-red-950/50">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {error}
            {isApiKeyError && (
              <div className="mt-2">
                <p className="font-medium">API Key Issue</p>
                <p className="text-sm">Please check your Anthropic API key in the settings.</p>
                <Button 
                  variant="secondary"
                  size="sm"
                  className="mt-2 bg-red-100 dark:bg-red-900/50 hover:bg-red-200 dark:hover:bg-red-900"
                  onClick={() => window.location.href = '/dashboard/settings/ai'}
                >
                  Go to API Settings
                </Button>
              </div>
            )}
          </AlertDescription>
        </Alert>
      )}
      
      {/* Success message */}
      {showSuccess && (
        <Alert className="bg-green-50 dark:bg-green-950/30 text-green-800 dark:text-green-300 border-green-100 dark:border-green-900/50">
          <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
          <AlertTitle className="text-green-800 dark:text-green-300">Processing Complete</AlertTitle>
          <AlertDescription className="text-green-700 dark:text-green-400">
            All files have been processed successfully.
          </AlertDescription>
        </Alert>
      )}
      
      {/* No files message */}
      {processingFiles.length === 0 && (
        <Card className="border border-muted bg-muted/10 dark:bg-muted/5">
          <CardContent className="pt-6 pb-6 flex flex-col items-center justify-center text-center">
            <div className="h-12 w-12 rounded-full bg-muted/30 dark:bg-muted/20 flex items-center justify-center mb-4">
              <UploadCloud className="h-6 w-6 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium mb-1">No Files to Process</h3>
            <p className="text-muted-foreground mb-4">Upload invoices first to start processing</p>
            <Button onClick={onGoToUpload}>Go to Upload</Button>
          </CardContent>
        </Card>
      )}
      
      {/* Filter tabs */}
      {processingFiles.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-2">
          {categories.map((category) => (
            <Button
              key={category.id || 'all'}
              variant={selectedCategory === category.id ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category.id)}
              className="flex items-center"
            >
              {category.label}
              <span className="ml-2 inline-flex items-center justify-center rounded-full bg-muted/50 px-1.5 py-0.5 text-xs">
                {category.count}
              </span>
            </Button>
          ))}
        </div>
      )}
      
      {/* Processing file cards */}
      <div className="space-y-4">
        {filteredFiles.length > 0 ? (
          filteredFiles.map((file, index) => (
            <ProcessingFileCard
              key={index}
              file={file}
              index={processingFiles.indexOf(file)}
              onUpdateData={onUpdateData}
            />
          ))
        ) : selectedCategory ? (
          <Card>
            <CardContent className="py-6 text-center">
              <p className="text-muted-foreground">No {selectedCategory} files found</p>
            </CardContent>
          </Card>
        ) : null}
      </div>
      
      {/* Loading indicator */}
      {(isProcessing || isSaving) && (
        <div className="flex justify-center mt-4">
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <RefreshCcw className="h-4 w-4 animate-spin" />
            <span>{isProcessing ? 'Processing files...' : 'Saving to database...'}</span>
          </div>
        </div>
      )}
    </div>
  );
} 