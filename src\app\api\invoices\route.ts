import { NextRequest, NextResponse } from 'next/server';
import { getInvoices } from '@/actions/invoice-actions';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Extract query parameters
    const page = Number(searchParams.get('page')) || 1;
    const limit = Number(searchParams.get('limit')) || 10;
    const status = searchParams.get('status') || undefined;
    const vendor = searchParams.get('vendor') || undefined;
    const category = searchParams.get('category') || undefined;
    const dateRange = searchParams.get('dateRange') || undefined;
    const sort = searchParams.get('sort') || 'createdAt';
    const order = searchParams.get('order') || 'desc';
    const search = searchParams.get('search') || '';

    const params = {
      page,
      limit,
      status,
      vendor,
      category,
      dateRange,
      sort,
      order,
      search,
    };

    const result = await getInvoices(params);

    if ('success' in result && result.success) {
      return NextResponse.json({
        invoices: result.invoices || [],
        totalCount: result.totalCount || 0,
        pageCount: result.pageCount || 0,
      });
    } else {
      return NextResponse.json(
        {
          error:
            'error' in result
              ? result.error
              : 'Failed to fetch invoices',
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error fetching invoices:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
