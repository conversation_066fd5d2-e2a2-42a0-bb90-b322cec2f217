import { NextRequest, NextResponse } from 'next/server';
import { subscribe, NewsletterFormSchema } from '@/lib/email/send-email';
import { getResend } from '@/lib/email/resend';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    
    // Handle newsletter subscription
    if (body.type === 'newsletter') {
      const validatedData = NewsletterFormSchema.safeParse(body.data);
      
      if (!validatedData.success) {
        return NextResponse.json(
          { success: false, error: 'Invalid email format' },
          { status: 400 }
        );
      }
      
      // Check if email already exists in audience
      try {
        const resend = getResend();
        const audienceId = process.env.RESEND_AUDIENCE_ID as string;
        
        if (!audienceId) {
          throw new Error('RESEND_AUDIENCE_ID is not defined');
        }
        
        // Check if the contact already exists
        const { data: contacts } = await resend.contacts.list({ audienceId });
        const existingContact = contacts?.data?.find(
          (contact: { email: string }) => contact.email === validatedData.data.email
        );
        
        if (existingContact) {
          return NextResponse.json(
            // WAITLIST DISABLED FOR PRODUCTION TESTING
            { success: false, error: 'Email already subscribed' },
            { status: 409 }
          );
        }
      } catch (error) {
        console.error('Error checking existing contact:', error);
        // Continue with subscription attempt even if check fails
      }
      
      const result = await subscribe(validatedData.data);
      
      if ('error' in result) {
        return NextResponse.json(
          { success: false, error: 'Failed to subscribe' },
          { status: 500 }
        );
      }
      
      return NextResponse.json({ success: true });
    }
    
    // Add other email operations as needed (e.g., sending reports, invites)
    
    return NextResponse.json(
      { success: false, error: 'Unsupported operation' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Email API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
} 