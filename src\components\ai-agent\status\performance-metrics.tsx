'use client';

import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

interface PerformanceMetricsProps {
  performance: {
    status: 'excellent' | 'good' | 'fair' | 'poor';
    avgResponseTime: number;
    successRate: number;
    recommendations: string[];
  };
  getStatusColor: (status: string) => string;
}

export function PerformanceMetrics({ performance, getStatusColor }: PerformanceMetricsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Performance Metrics
          <Badge className={getStatusColor(performance.status)}>
            {performance.status}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <div className="flex justify-between mb-2">
            <span>Response Time</span>
            <span>{performance.avgResponseTime}ms</span>
          </div>
          <Progress value={Math.max(0, 100 - (performance.avgResponseTime / 50))} />
        </div>
        <div>
          <div className="flex justify-between mb-2">
            <span>Success Rate</span>
            <span>{(performance.successRate * 100).toFixed(1)}%</span>
          </div>
          <Progress value={performance.successRate * 100} />
        </div>
        {performance.recommendations.length > 0 && (
          <div>
            <h4 className="font-semibold mb-2">Recommendations:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
              {performance.recommendations.map((rec, index) => (
                <li key={index}>{rec}</li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
}