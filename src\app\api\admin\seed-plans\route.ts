import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { seedPaddlePlans } from '@/lib/seed-plans';
import db from '@/db/db';

export async function POST() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Clean up any placeholder plans first
    const placeholderPlans = await db.plan.findMany({
      where: {
        OR: [
          { name: { contains: 'Paddle Plan undefined' } },
          { name: { contains: 'Paddle Plan pri_' } },
          { price: '0' },
          { paddlePriceId: null },
        ],
      },
    });

    if (placeholderPlans.length > 0) {
      console.log(
        `🧹 Cleaning up ${placeholderPlans.length} placeholder plans...`
      );

      // Check if any subscriptions are using these plans
      for (const plan of placeholderPlans) {
        const subscriptionsUsingPlan = await db.subscription.count({
          where: { planId: plan.id },
        });

        if (subscriptionsUsingPlan > 0) {
          console.log(
            `⚠️ Plan ${plan.name} has ${subscriptionsUsingPlan} subscriptions, skipping deletion`
          );
          continue;
        }

        await db.plan.delete({
          where: { id: plan.id },
        });
        console.log(`🗑️ Deleted placeholder plan: ${plan.name}`);
      }
    }

    // Run the seeding
    const result = await seedPaddlePlans();

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Plans seeded successfully',
        plansCreated: result.plansCreated,
        placeholderPlansRemoved: placeholderPlans.length,
      });
    } else {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error seeding plans:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
